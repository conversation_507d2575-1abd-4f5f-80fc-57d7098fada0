<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recebimento de Materiais</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --light-bg: #f8f9fa;
      --white: #ffffff;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
      --border-radius: 12px;
      --transition: all 0.3s ease;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: var(--text-color);
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: var(--white);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      overflow: hidden;
      animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .header {
      background: linear-gradient(135deg, var(--header-bg), var(--primary-color));
      color: var(--white);
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.05) 10px,
        rgba(255,255,255,0.05) 20px
      );
      animation: float 20s linear infinite;
    }

    @keyframes float {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .header h1 {
      font-size: 2rem;
      font-weight: 600;
      position: relative;
      z-index: 1;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .header .btn-secondary {
      position: relative;
      z-index: 1;
    }

    .content {
      padding: 30px;
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 25px;
      color: var(--primary-color);
      padding-bottom: 12px;
      border-bottom: 2px solid var(--primary-color);
      position: relative;
    }

    h2::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    }

    h3 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: var(--text-color);
    }

    .form-section {
      background: linear-gradient(135deg, var(--light-bg), #e9ecef);
      border-radius: var(--border-radius);
      padding: 25px;
      margin-bottom: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid rgba(255,255,255,0.8);
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      color: var(--text-color);
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      font-size: 1rem;
      transition: var(--transition);
      background: var(--white);
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-2px);
    }

    .form-group input::placeholder,
    .form-group textarea::placeholder {
      color: var(--text-secondary);
      font-style: italic;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    .table-container {
      background: var(--white);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow);
      margin: 20px 0;
      border: 1px solid var(--border-color);
    }

    .items-table {
      width: 100%;
      border-collapse: collapse;
    }

    .items-table th {
      background: linear-gradient(135deg, var(--header-bg), #34495e);
      color: var(--white);
      padding: 16px 12px;
      text-align: left;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
    }

    .items-table th::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    }

    .items-table td {
      padding: 14px 12px;
      border-bottom: 1px solid #ecf0f1;
      transition: var(--transition);
      vertical-align: middle;
    }

    .items-table tr:hover {
      background: linear-gradient(135deg, #f8f9fa, #ecf0f1);
      transform: scale(1.01);
    }

    .items-table tr:nth-child(even) {
      background: rgba(8, 84, 160, 0.02);
    }

    button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      min-width: 140px;
    }

    button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.3s, height 0.3s;
    }

    button:hover::before {
      width: 300px;
      height: 300px;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-hover);
    }

    .btn-success {
      background: linear-gradient(135deg, var(--success-color), var(--success-hover));
      color: var(--white);
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d, #5a6268);
      color: var(--white);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: var(--white);
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--warning-color), #e67e22);
      color: var(--white);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--danger-color), var(--danger-hover));
      color: var(--white);
    }

    .qr-scanner {
      background: linear-gradient(135deg, var(--light-bg), #e9ecef);
      border-radius: var(--border-radius);
      padding: 30px;
      margin: 30px 0;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid rgba(255,255,255,0.8);
    }

    .qr-scanner h2 {
      margin-bottom: 20px;
      color: var(--primary-color);
    }

    #reader {
      width: 100%;
      max-width: 400px;
      height: 300px;
      background: #000;
      border-radius: var(--border-radius);
      overflow: hidden;
      margin: 20px auto;
      box-shadow: var(--shadow);
      border: 3px solid var(--primary-color);
    }

    .photo-upload {
      background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
      border: 2px dashed var(--success-color);
      border-radius: var(--border-radius);
      padding: 25px;
      margin: 25px 0;
      text-align: center;
      transition: var(--transition);
    }

    .photo-upload:hover {
      background: linear-gradient(135deg, #c8e6c9, #a5d6a7);
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }

    .photo-upload h3 {
      color: var(--success-color);
      margin-bottom: 15px;
    }

    .photo-upload input[type="file"] {
      margin: 15px 0;
      padding: 10px;
      border: 2px solid var(--success-color);
      border-radius: 8px;
      background: var(--white);
    }

    .photo-preview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .photo-preview img {
      width: 100%;
      max-width: 200px;
      max-height: 200px;
      object-fit: cover;
      border-radius: 8px;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .photo-preview img:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-hover);
    }

    .checklist {
      background: linear-gradient(135deg, #fff3e0, #ffe0b3);
      border: 2px solid var(--warning-color);
      border-radius: var(--border-radius);
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
    }

    .checklist h3 {
      color: var(--warning-color);
      margin-bottom: 20px;
      text-align: center;
    }

    .checklist-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 15px;
      background: var(--white);
      border-radius: 8px;
      border: 1px solid var(--border-color);
      transition: var(--transition);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .checklist-item:hover {
      transform: translateX(5px);
      box-shadow: var(--shadow);
      border-color: var(--warning-color);
    }

    .checklist-item input[type="checkbox"] {
      margin-right: 15px;
      width: 20px;
      height: 20px;
      accent-color: var(--success-color);
      cursor: pointer;
    }

    .checklist-item label {
      font-weight: 500;
      color: var(--text-color);
      cursor: pointer;
      flex: 1;
    }

    .checklist-item input[type="checkbox"]:checked + label {
      color: var(--success-color);
      text-decoration: line-through;
    }

    /* Seção de recebimentos registrados */
    .registered-section {
      background: var(--white);
      border-radius: var(--border-radius);
      padding: 30px;
      margin: 30px auto;
      max-width: 1400px;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
    }

    .registered-section h3 {
      background: linear-gradient(135deg, var(--primary-color), var(--success-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 1.8rem;
      margin-bottom: 25px;
      text-align: center;
    }

    /* Inputs especiais */
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    input[type="number"] {
      -moz-appearance: textfield;
    }

    /* Estados de validação */
    .form-group input:invalid {
      border-color: var(--danger-color);
      box-shadow: 0 0 0 3px rgba(187, 0, 0, 0.1);
    }

    .form-group input:valid {
      border-color: var(--success-color);
    }

    /* Loading states */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
      font-size: 1.2rem;
      color: var(--text-secondary);
    }

    .loading::after {
      content: '';
      width: 20px;
      height: 20px;
      border: 2px solid var(--border-color);
      border-top: 2px solid var(--primary-color);
      border-radius: 50%;
      margin-left: 10px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsividade */
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        margin: 10px auto;
        border-radius: 8px;
      }

      .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header h1 {
        font-size: 1.5rem;
      }

      .content {
        padding: 20px;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .items-table {
        font-size: 0.8rem;
      }

      .items-table th,
      .items-table td {
        padding: 8px 6px;
      }

      button {
        width: 100%;
        margin-bottom: 10px;
      }

      .qr-scanner {
        padding: 20px;
      }

      #reader {
        height: 250px;
      }

      .photo-preview {
        grid-template-columns: 1fr;
      }

      .checklist-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .registered-section {
        margin: 20px 10px;
        padding: 20px;
      }
    }

    @media (max-width: 480px) {
      .header h1 {
        font-size: 1.2rem;
      }

      .content {
        padding: 15px;
      }

      .form-section {
        padding: 15px;
      }

      .items-table {
        font-size: 0.7rem;
      }

      .items-table th,
      .items-table td {
        padding: 6px 4px;
      }
    }

    /* Animações de entrada */
    .fade-in {
      animation: fadeIn 0.5s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Toasts para notificações */
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, var(--success-color), #229954);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: var(--shadow-hover);
      z-index: 1000;
      font-weight: 600;
      animation: slideIn 0.3s ease-out;
    }

    .toast.error {
      background: linear-gradient(135deg, var(--danger-color), #c82333);
    }

    .toast.warning {
      background: linear-gradient(135deg, var(--warning-color), #e67e22);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(100%);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideOut {
      from {
        opacity: 1;
        transform: translateX(0);
      }
      to {
        opacity: 0;
        transform: translateX(100%);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📦 Recebimento de Materiais</h1>
      <button class="btn-secondary" id="backButton">← Voltar</button>
    </div>

    <div class="content">
      <!-- Scanner QR -->
      <div class="qr-scanner">
        <h2>📱 Escanear Nota Fiscal</h2>
        <div id="reader"></div>
        <button onclick="startScanner()" class="btn-primary">
          📷 Iniciar Scanner
        </button>
      </div>

      <!-- Seleção de Pedido -->
      <div class="form-section">
        <h3>🛒 Seleção de Pedido</h3>
        <div class="form-group">
          <label>📋 Selecionar Pedido de Compra</label>
          <select id="orderSelect" onchange="loadOrderDetails()">
            <option value="">Selecione o pedido...</option>
          </select>
        </div>

        <div class="form-row">
          <div class="form-col">
            <div class="form-group">
              <label>🏪 Armazém de Recebimento</label>
              <select id="warehouseSelect" required>
                <option value="">Selecione o armazém...</option>
              </select>
            </div>
          </div>
          <div class="form-col">
            <div class="form-group">
              <label>💼 Centro de Custo</label>
              <select id="costCenterSelect" required>
                <option value="">Selecione o centro de custo...</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Formulário de Recebimento -->
      <form id="receiveForm" onsubmit="receiveMaterials(event)" style="display: none;" class="fade-in">
        <div class="form-section">
          <h2>📝 Registro de Recebimento</h2>

          <!-- Checklist -->
          <div class="checklist">
            <h3>✅ Checklist de Recebimento</h3>
            <div class="checklist-item">
              <input type="checkbox" id="check_nf" required>
              <label for="check_nf">📄 Nota fiscal confere com pedido</label>
            </div>
            <div class="checklist-item">
              <input type="checkbox" id="check_quantidade" required>
              <label for="check_quantidade">📊 Quantidades conferem</label>
            </div>
            <div class="checklist-item">
              <input type="checkbox" id="check_embalagem" required>
              <label for="check_embalagem">📦 Embalagens íntegras</label>
            </div>
            <div class="checklist-item">
              <input type="checkbox" id="check_prazo" required>
              <label for="check_prazo">⏰ Prazo de entrega conforme pedido</label>
            </div>
          </div>

          <!-- Dados da NF -->
          <div class="form-row">
            <div class="form-col">
              <div class="form-group">
                <label>🔢 Número da Nota Fiscal</label>
                <input type="text" id="nfNumero" placeholder="Digite o número da NF..." required>
              </div>
            </div>
            <div class="form-col">
              <div class="form-group">
                <label>🔑 Chave de Acesso (44 dígitos)</label>
                <input type="text" id="nfChave" maxlength="44" placeholder="Chave de 44 dígitos..." required>
              </div>
            </div>
          </div>

          <!-- Upload de Fotos -->
          <div class="photo-upload">
            <h3>📸 Fotos do Recebimento</h3>
            <p style="margin-bottom: 15px; color: var(--text-secondary);">
              Adicione fotos dos materiais recebidos para documentação
            </p>
            <input type="file" id="photoInput" accept="image/*" multiple onchange="previewPhotos(event)">
            <div id="photoPreview" class="photo-preview"></div>
          </div>

          <!-- Data e Valor -->
          <div class="form-row">
            <div class="form-col">
              <div class="form-group">
                <label>📅 Data da Nota Fiscal</label>
                <input type="date" id="nfData" required>
              </div>
            </div>
            <div class="form-col">
              <div class="form-group">
                <label>💰 Valor Total da NF</label>
                <input type="number" id="nfValor" min="0" step="0.01" placeholder="0,00" required>
              </div>
            </div>
          </div>
        </div>

        <!-- Histórico de Recebimentos -->
        <div class="form-section">
          <h3>📋 Histórico de Recebimentos</h3>
          <div class="table-container">
            <table class="items-table">
              <thead>
                <tr>
                  <th>📅 Data</th>
                  <th>📄 NF</th>
                  <th>🔑 Chave</th>
                  <th>💰 Valor</th>
                  <th>📦 Itens</th>
                  <th>🏪 Armazém</th>
                  <th>💼 Centro de Custo</th>
                </tr>
              </thead>
              <tbody id="receiveHistoryBody">
                <tr>
                  <td colspan="7" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                    Nenhum recebimento anterior encontrado
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Itens para Recebimento -->
        <div class="form-section">
          <h3>📦 Itens para Recebimento</h3>
          <div class="table-container">
            <table class="items-table">
              <thead>
                <tr>
                  <th>🏷️ Código</th>
                  <th>📝 Descrição</th>
                  <th>📊 Qtd. Pedida</th>
                  <th>✅ Qtd. Recebida</th>
                  <th>⏳ Saldo Pendente</th>
                  <th>📏 Unidade NF</th>
                  <th>🔄 Fator Conversão</th>
                  <th>📋 Qtd. na NF</th>
                  <th>🔢 Qtd. em Peças</th>
                  <th>🏷️ Lote Fornecedor</th>
                  <th>📐 Unidade Principal</th>
                </tr>
              </thead>
              <tbody id="itemsTableBody">
                <tr>
                  <td colspan="11" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                    Selecione um pedido para visualizar os itens
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Observações -->
        <div class="form-section">
          <div class="form-group">
            <label>📝 Observações</label>
            <textarea id="receiveObservacoes" rows="4" placeholder="Digite observações sobre o recebimento..."></textarea>
          </div>
        </div>

        <!-- Botão de Submissão -->
        <div style="text-align: center; padding: 20px;">
          <button type="submit" class="btn-success" style="font-size: 1.1rem; padding: 15px 40px;">
            ✅ Registrar Recebimento
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Seção de Recebimentos Registrados -->
  <div class="registered-section">
    <h3>📋 Recebimentos Registrados</h3>
    <div class="table-container">
      <table id="recebimentosTable" class="items-table">
        <thead>
          <tr>
            <th>🏷️ Código</th>
            <th>📝 Descrição</th>
            <th>📊 Quantidade</th>
            <th>📄 NF</th>
            <th>📅 Data</th>
            <th>⚙️ Ações</th>
          </tr>
        </thead>
        <tbody id="recebimentosTableBody">
          <tr>
            <td colspan="6" class="loading">Carregando recebimentos...</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <script src="https://unpkg.com/html5-qrcode"></script>
  <script type="module">
    import { db, storage } from './firebase-config.js';
    import {
      collection,
      addDoc,
      getDocs,
      doc,
      getDoc,
      updateDoc,
      Timestamp,
      query,
      where,
      orderBy,
      limit,
      deleteDoc,
      runTransaction
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    import { MaterialEntryService } from './services/material-entry-service.js';
    import { ref, uploadBytes } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

    let html5QrcodeScanner = null;
    let pedidosCompra = [];
    let fornecedores = [];
    let produtos = [];
    let armazens = [];
    let centrosCusto = [];
    let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Usuário Atual' };
    let currentOrder = null;
    let photoFiles = [];

    // Attach event listener for the back button
    document.getElementById('backButton').addEventListener('click', () => {
      window.location.href = 'index.html';
    });

    window.onload = async () => {
      try {
        // Buscar parametros/configuracoes para armazemPadrao
        const configDoc = await getDoc(doc(db, "parametros", "configuracoes"));
        const config = configDoc.exists() ? configDoc.data() : {};
        const armazemPadrao = config.armazemPadrao || '';

        const requiresInspection = config?.inspecaoRecebimento || false;
        const usesQualityWarehouse = config?.armazemQualidade || false;
        const requiresHomologation = config?.homologacaoFornecedor || false;

        await loadInitialData();
        populateOrderSelect();
        populateWarehouseSelect();
        populateCostCenterSelect();
        // Após popular os armazéns, definir o padrão se houver
        setTimeout(() => {
          if (armazemPadrao) {
            const select = document.getElementById('warehouseSelect');
            if (select && !select.value) select.value = armazemPadrao;
          }
        }, 600);
        await loadRecebimentos();
      } catch (error) {
        console.error("Erro ao inicializar a página:", error);
        alert("Erro ao carregar a página. Verifique a conexão com o Firebase.");
      }
    };


    async function loadInitialData() {
      try {
        const [pedidosSnap, fornecedoresSnap, produtosSnap, armazensSnap, centrosCustoSnap] = await Promise.all([
          getDocs(collection(db, "pedidosCompra")),
          getDocs(collection(db, "fornecedores")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "armazens")),
          getDocs(collection(db, "centrosCusto"))
        ]);
        pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais. Verifique a conexão com o Firestore.");
      }
    }

    function populateOrderSelect() {
      const select = document.getElementById('orderSelect');
      select.innerHTML = '<option value="">Selecione o pedido...</option>';
      pedidosCompra
        .filter(p => p.status === 'APROVADO' || p.status === 'RECEBIDO')
        .forEach(pedido => {
          const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
          select.innerHTML += `
            <option value="${pedido.id}">
              ${pedido.numero} - ${fornecedor ? fornecedor.razaoSocial : 'N/A'}
            </option>`;
        });
    }

    function populateWarehouseSelect() {
      const select = document.getElementById('warehouseSelect');
      select.innerHTML = '<option value="">Selecione o armazém...</option>';
      armazens.forEach(armazem => {
        select.innerHTML += `
          <option value="${armazem.codigo}">
            ${armazem.codigo} - ${armazem.descricao}
          </option>`;
      });
    }

    function populateCostCenterSelect() {
      const select = document.getElementById('costCenterSelect');
      select.innerHTML = '<option value="">Selecione o centro de custo...</option>';
      centrosCusto.forEach(centro => {
        select.innerHTML += `
          <option value="${centro.codigo}">
            ${centro.codigo} - ${centro.descricao}
          </option>`;
      });
    }

    window.startScanner = function() {
      if (html5QrcodeScanner) {
        html5QrcodeScanner.clear();
      }

      html5QrcodeScanner = new Html5QrcodeScanner(
        "reader",
        { fps: 10, qrbox: {width: 250, height: 250} },
        false
      );

      html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    };

    function onScanSuccess(decodedText, decodedResult) {
      try {
        document.getElementById('nfChave').value = decodedText;
        html5QrcodeScanner.clear();
        alert('QR Code lido com sucesso!');
      } catch (error) {
        console.error("Erro ao processar QR Code:", error);
        alert("Erro ao processar QR Code: " + error.message);
      }
    }

    function onScanFailure(error) {
      console.warn(`Erro na leitura do QR Code: ${error}`);
    }

    window.previewPhotos = function(event) {
      const preview = document.getElementById('photoPreview');
      preview.innerHTML = '';
      photoFiles = Array.from(event.target.files);

      photoFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
          const img = document.createElement('img');
          img.src = e.target.result;
          img.className = 'photo-preview';
          preview.appendChild(img);
        };
        reader.readAsDataURL(file);
      });
    };

    window.loadOrderDetails = async function() {
      const orderId = document.getElementById('orderSelect').value;
      const form = document.getElementById('receiveForm');
      const itemsTableBody = document.getElementById('itemsTableBody');
      const receiveHistoryBody = document.getElementById('receiveHistoryBody');

      form.style.display = 'none';
      itemsTableBody.innerHTML = '';
      receiveHistoryBody.innerHTML = '';

      if (!orderId) return;

      currentOrder = pedidosCompra.find(p => p.id === orderId);
      if (!currentOrder) return;

      // Verificar se o pedido já foi totalmente recebido
      const recebimentos = currentOrder.recebimentos || [];
      const todosRecebidos = currentOrder.itens.every((item, index) => {
        const totalRecebido = recebimentos.reduce((sum, rec) => 
          sum + (rec.itens[index]?.quantidadeConvertida || 0), 0);
        return totalRecebido >= item.quantidade;
      });

      if (todosRecebidos) {
        alert('Este pedido já foi totalmente recebido.');
        document.getElementById('orderSelect').value = '';
        return;
      }

      // Verificar itens que já foram totalmente recebidos
      const itensComRecebimento = new Set();
      recebimentos.forEach(rec => {
        rec.itens.forEach((item, index) => {
          if (item.quantidadeConvertida > 0) {
            const totalRecebido = recebimentos.reduce((sum, r) => 
              sum + (r.itens[index]?.quantidadeConvertida || 0), 0);
            if (totalRecebido >= currentOrder.itens[index].quantidade) {
              itensComRecebimento.add(index);
            }
          }
        });
      });

      // Preencher histórico de recebimentos
      if (recebimentos.length > 0) {
        recebimentos.forEach(rec => {
          // Calcular saldo restante após este recebimento
          let saldoItens = rec.itens.map((itemRec, idx) => {
            const itemPedido = currentOrder.itens.find(i => i.codigo === itemRec.codigo);
            if (!itemPedido) return '';
            // Soma recebida até este recebimento
            let totalAteAqui = 0;
            for (const r of recebimentos) {
              if (r.dataRecebimento.seconds > rec.dataRecebimento.seconds) continue;
              const found = r.itens.find(i => i.codigo === itemRec.codigo);
              if (found) totalAteAqui += found.quantidadeConvertida || 0;
            }
            const saldoRestante = (itemPedido.quantidade - totalAteAqui).toFixed(3);
            return `${itemRec.codigo}: saldo ${saldoRestante} ${itemPedido.unidade}`;
          }).filter(Boolean).join('<br>');
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${new Date(rec.dataRecebimento.seconds * 1000).toLocaleString()}</td>
            <td>${rec.nfNumero}</td>
            <td>${rec.nfChave}</td>
            <td>R$ ${rec.nfValor.toFixed(2)}</td>
            <td>${rec.itens.length} itens</td>
            <td>${rec.armazem}</td>
            <td>${rec.centroCusto}</td>
            <td style='font-size:12px;color:#666'>${saldoItens}</td>
          `;
          receiveHistoryBody.appendChild(row);
        });
      }

      // Preencher tabela de itens
      currentOrder.itens.forEach((item, index) => {
        if (itensComRecebimento.has(index)) {
          // Item já totalmente recebido - não mostrar na tabela
          return;
        }

        const totalRecebido = recebimentos.reduce((sum, rec) => 
          sum + (rec.itens[index]?.quantidadeConvertida || 0), 0);
        const quantidadePendente = item.quantidade - totalRecebido;

        if (quantidadePendente <= 0) {
          // Item já totalmente recebido - não mostrar na tabela
          return;
        }

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.codigo}</td>
          <td>${item.descricao}</td>
          <td>${item.quantidade} ${item.unidade}</td>
          <td>${totalRecebido.toFixed(3)} ${item.unidade}</td>
          <td><b>${quantidadePendente.toFixed(3)} ${item.unidade}</b></td>
          <td>
            <select class="unidade-recebimento" data-index="${index}">
              <option value="${item.unidade}">${item.unidade}</option>
            </select>
          </td>
          <td>
            <input type="number" class="fator-conversao" data-index="${index}" 
                   value="1" min="0.001" step="0.001" onchange="updateConvertedQuantity(${index})">
          </td>
          <td>
            <input type="number" class="qtd-recebida-nf" data-index="${index}" 
                   value="${quantidadePendente}" min="0" step="0.001" 
                   onchange="updateConvertedQuantity(${index})">
          </td>
          <td>
            <input type="number" class="qtd-convertida-pc" data-index="${index}" 
                   value="${quantidadePendente}" readonly>
          </td>
          <td>
            <input type="text" class="lote-fornecedor" data-index="${index}" required>
          </td>
          <td>${item.unidade}</td>
        `;
        itemsTableBody.appendChild(row);
      });

      form.style.display = 'block';
    };

    window.updateConvertedQuantity = function(index) {
      const unidadeNF = document.querySelector(`.unidade-recebimento[data-index="${index}"]`).value;
      const qtdNFInput = document.querySelector(`.qtd-recebida-nf[data-index="${index}"]`);
      const qtdNF = parseFloat(qtdNFInput.value) || 0;
      const qtdConvertidaInput = document.querySelector(`.qtd-convertida-pc[data-index="${index}"]`);
      const produto = produtos.find(p => p.codigo === currentOrder.itens[index].codigo);

      if (qtdNF < 0) {
        alert('A quantidade na NF não pode ser negativa.');
        qtdNFInput.value = 0;
        qtdConvertidaInput.value = 0;
        return;
      }

      const qtdRestante = currentOrder.itens[index].quantidade - 
        (currentOrder.recebimentos || []).reduce((sum, rec) => 
          sum + (rec.itens[index]?.quantidadeConvertida || 0), 0);
      if (qtdNF > qtdRestante) {
        alert(`A quantidade na NF excede o restante a receber (${qtdRestante.toFixed(3)}).`);
        qtdNFInput.value = qtdRestante;
        return;
      }

      let qtdPC = qtdNF;
      if (unidadeNF === produto.unidadeSecundaria && produto.fatorConversao) {
        qtdPC = qtdNF / produto.fatorConversao;
      } else if (unidadeNF === produto.unidade) {
        qtdPC = qtdNF;
      }
      qtdConvertidaInput.value = qtdPC.toFixed(3);
    };

    window.receiveMaterials = async function(event) {
      event.preventDefault();

      if (!currentOrder) {
        alert('Selecione um pedido antes de registrar o recebimento.');
        return;
      }

      let warehouse = document.getElementById('warehouseSelect').value;
      const costCenter = document.getElementById('costCenterSelect').value;

      if (!warehouse || !costCenter) {
        alert('Selecione o armazém e o centro de custo.');
        return;
      }

      const checklistItems = ['check_nf', 'check_quantidade', 'check_embalagem', 'check_prazo'];
      for (const item of checklistItems) {
        if (!document.getElementById(item).checked) {
          alert('Por favor, complete todos os itens do checklist.');
          return;
        }
      }

      const nfNumero = document.getElementById('nfNumero').value;
      const nfChave = document.getElementById('nfChave').value;
      const nfData = new Date(document.getElementById('nfData').value);
      const nfValor = parseFloat(document.getElementById('nfValor').value);
      const observacoes = document.getElementById('receiveObservacoes').value;
      const dataEmissao = document.getElementById('nfData').value;

      if (nfChave.length !== 44) {
        alert('A chave de acesso deve ter exatamente 44 dígitos.');
        return;
      }

      if (isNaN(nfValor) || nfValor <= 0) {
        alert('O valor total da NF deve ser maior que zero.');
        return;
      }

      const itensRecebidos = [];
      let hasValidItem = false;
      document.querySelectorAll('.qtd-recebida-nf').forEach((input, index) => {
        const qtdRecebidaNF = parseFloat(input.value) || 0;
        const unidadeNF = document.querySelector(`.unidade-recebimento[data-index="${index}"]`).value;
        const qtdConvertidaPC = parseFloat(document.querySelector(`.qtd-convertida-pc[data-index="${index}"]`).value) || 0;
        const loteFornecedor = document.querySelector(`.lote-fornecedor[data-index="${index}"]`).value.trim();

        if (qtdRecebidaNF > 0) {
          if (!loteFornecedor) {
            alert(`Informe o lote do fornecedor para o item ${currentOrder.itens[index].descricao}.`);
            throw new Error("Lote do fornecedor não informado.");
          }
          hasValidItem = true;
          const loteInterno = generateLoteInterno();
          itensRecebidos.push({
            ...currentOrder.itens[index],
            quantidadeRecebida: qtdRecebidaNF,
            unidadeRecebida: unidadeNF,
            quantidadeConvertida: qtdConvertidaPC,
            loteFornecedor,
            loteInterno
          });
        }
      });

      if (!hasValidItem) {
        alert('Informe a quantidade recebida para pelo menos um item.');
        return;
      }

      const recebimentoData = {
        nfNumero,
        nfChave,
        nfData: Timestamp.fromDate(nfData),
        nfValor,
        itens: itensRecebidos,
        dataRecebimento: Timestamp.now(),
        recebidoPor: currentUser.nome,
        observacoes,
        checklist: {
          nfConfere: document.getElementById('check_nf').checked,
          quantidadesConferem: document.getElementById('check_quantidade').checked,
          embalagensIntegras: document.getElementById('check_embalagem').checked,
          prazoConforme: document.getElementById('check_prazo').checked
        },
        fotos: photoFiles.length > 0,
        armazem: warehouse,
        centroCusto: costCenter
      };

      try {
        const pedidoRef = doc(db, "pedidosCompra", currentOrder.id);
        const pedidoSnap = await getDoc(pedidoRef);
        const pedidoData = pedidoSnap.data();
        const recebimentos = pedidoData.recebimentos || [];
        recebimentos.push(recebimentoData);

        const todosRecebidos = currentOrder.itens.every((item, index) => {
          const totalRecebido = recebimentos.reduce((sum, rec) => 
            sum + (rec.itens[index]?.quantidadeConvertida || 0), 0);
          return totalRecebido >= item.quantidade;
        });

        await updateDoc(pedidoRef, {
          recebimentos,
          status: todosRecebidos ? 'RECEBIDO' : 'APROVADO',
          notaFiscal: {
            numero: nfNumero,
            chaveAcesso: nfChave,
            dataEmissao: Timestamp.fromDate(nfData),
            valorTotal: nfValor
          },
          historico: [
            ...(pedidoData.historico || []),
            {
              data: Timestamp.now(),
              acao: `Recebimento registrado (NF ${nfNumero}) - ${todosRecebidos ? 'Total' : 'Parcial'} no armazém ${warehouse}, centro de custo ${costCenter}`,
              usuario: currentUser.nome
            }
          ]
        });

        const recebimentoId = `REC-${Date.now()}`;
        for (const file of photoFiles) {
          try {
            const storageRef = ref(storage, `recebimentos/${recebimentoId}/${file.name}`);
            const metadata = {
              contentType: file.type,
              customMetadata: {
                'recebimentoId': recebimentoId,
                'uploadedBy': currentUser.nome,
                'uploadDate': new Date().toISOString()
              }
            };
            await uploadBytes(storageRef, file, metadata);
          } catch (error) {
            console.error(`Erro ao fazer upload do arquivo ${file.name}:`, error);
            // Continue com o processo mesmo se um arquivo falhar
          }
        }

        // Processar cada item recebido usando o serviço inteligente
        const resultados = [];

        for (const item of itensRecebidos) {
          const produto = produtos.find(p => p.codigo === item.codigo);
          if (!produto) {
            console.warn(`Produto não encontrado: ${item.codigo}`);
            continue;
          }

          try {
            // Preparar dados para o serviço
            const entryData = {
              produtoId: produto.id,
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: item.quantidadeConvertida,
              unidade: produto.unidade,
              armazemId: warehouse, // Armazém selecionado pelo usuário
              numeroNF: nfNumero,
              chaveNF: nfChave,
              loteFornecedor: item.loteFornecedor,
              loteInterno: item.loteInterno,
              recebimentoId: recebimentoId,
              pedidoId: currentOrder.id,
              centroCusto: costCenter,
              recebidoPor: currentUser.nome
            };

            // Processar entrada usando serviço inteligente
            const resultado = await MaterialEntryService.processEntry(entryData);

            // Registrar detalhes do recebimento para rastreabilidade
            await addDoc(collection(db, "recebimentosDetalhes"), {
              pedidoId: currentOrder.id,
              recebimentoId: recebimentoId,
              produtoId: produto.id,
              codigo: item.codigo,
              descricao: item.descricao,
              quantidadeRecebida: item.quantidadeRecebida,
              unidadeRecebida: item.unidadeRecebida,
              quantidadeConvertida: item.quantidadeConvertida,
              unidadeConvertida: produto.unidade,
              notaFiscal: { numero: nfNumero, chaveAcesso: nfChave },
              loteFornecedor: item.loteFornecedor,
              loteInterno: item.loteInterno,
              armazemSelecionado: warehouse,
              armazemDestino: resultado.warehouse.id,
              tipoProcessamento: resultado.type,
              centroCusto: costCenter,
              dataRecebimento: Timestamp.now(),
              recebidoPor: currentUser.nome,
              status: resultado.type === 'QUALITY_INSPECTION' ? 'PENDENTE_INSPECAO' : 'RECEBIDO',
              observacoes: resultado.message
            });

            resultados.push({
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: item.quantidadeConvertida,
              destino: resultado.warehouse.codigo,
              tipo: resultado.type,
              message: resultado.message,
              success: true
            });

            console.log(`✅ ${item.codigo}: ${resultado.message}`);

          } catch (error) {
            console.error(`❌ Erro ao processar item ${item.codigo}:`, error);

            resultados.push({
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: item.quantidadeConvertida,
              error: error.message,
              success: false
            });
          }
        }

        // Gerar conta a pagar
        const contaPagar = {
          fornecedorId: currentOrder.fornecedorId,
          tipoDocumento: 'NF',
          numeroDocumento: nfNumero,
          dataEmissao: Timestamp.fromDate(new Date(dataEmissao)),
          valorTotal: nfValor,
          condicaoPagamentoId: currentOrder.condicaoPagamentoId,
          centroCusto: costCenter,
          contaContabil: 'FORNECEDORES',
          observacoes: `Referente NF ${nfNumero} - Pedido ${currentOrder.numero}`,
          dataCadastro: Timestamp.now(),
          status: 'PENDENTE'
        };

        await addDoc(collection(db, "contasAPagar"), contaPagar);

        // Atualizar status do pedido
        await updateDoc(doc(db, "pedidosCompra", currentOrder.id), {
          status: 'RECEBIDO',
          dataRecebimento: Timestamp.now(),
          recebidoPor: currentUser.nome
        });

        // Mostrar resumo dos resultados
        const sucessos = resultados.filter(r => r.success).length;
        const erros = resultados.filter(r => !r.success).length;
        const paraQualidade = resultados.filter(r => r.tipo === 'QUALITY_INSPECTION').length;
        const paraEstoque = resultados.filter(r => r.tipo === 'DIRECT_STOCK').length;

        let mensagem = `✅ Recebimento processado!\n\n`;
        mensagem += `📊 Resumo:\n`;
        mensagem += `• Total de itens: ${resultados.length}\n`;
        mensagem += `• Sucessos: ${sucessos}\n`;
        mensagem += `• Erros: ${erros}\n\n`;
        mensagem += `📦 Destinos:\n`;
        mensagem += `• Direto para estoque: ${paraEstoque}\n`;
        mensagem += `• Para inspeção qualidade: ${paraQualidade}\n\n`;

        if (paraQualidade > 0) {
          mensagem += `⚠️ ${paraQualidade} item(ns) enviado(s) para inspeção de qualidade.\n`;
          mensagem += `Acesse o módulo de qualidade para aprovar/reprovar.`;
        }

        alert(mensagem);

        document.getElementById('receiveForm').reset();
        document.getElementById('receiveForm').style.display = 'none';
        document.getElementById('orderSelect').value = '';
        document.getElementById('warehouseSelect').value = '';
        document.getElementById('costCenterSelect').value = '';
        document.getElementById('photoPreview').innerHTML = '';
        photoFiles = [];
        await loadRecebimentos();
      } catch (error) {
        console.error("Erro ao registrar recebimento:", error);
        alert("Erro ao registrar recebimento: " + error.message);
      }
    };

    function generateLoteInterno() {
      return `LOT-${Date.now()}`;
    }

    window.loadRecebimentos = async function() {
      const tableBody = document.getElementById('recebimentosTableBody');
      tableBody.innerHTML = '';
      const snap = await getDocs(collection(db, "estoqueQualidade"));
      snap.forEach(docSnap => {
        const rec = docSnap.data();
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${rec.codigo}</td>
          <td>${rec.descricao}</td>
          <td>${rec.quantidade}</td>
          <td>${rec.notaFiscal?.numero || '-'}</td>
          <td>${rec.dataEntrada && typeof rec.dataEntrada.seconds === 'number' ? new Date(rec.dataEntrada.seconds * 1000).toLocaleString() : ''}</td>
          <td><button onclick="excluirRecebimento('${docSnap.id}')">Excluir</button></td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.excluirRecebimento = async function(id) {
      if (!confirm('Tem certeza que deseja excluir este recebimento?')) return;
      try {
        await deleteDoc(doc(db, "estoqueQualidade", id));
        alert('Recebimento excluído!');
        loadRecebimentos();
      } catch (e) {
        alert('Erro ao excluir recebimento.');
        console.error(e);
      }
    }
  </script>
</body>
</html>