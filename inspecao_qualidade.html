<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liberação de Qualidade</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .quality-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .pending-items {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .approved-items {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .rejected-items {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .item-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .btn-approve {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }
        .btn-reject {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pendente {
            background: #ffc107;
            color: #212529;
        }
        .status-aprovado {
            background: #28a745;
            color: white;
        }
        .status-rejeitado {
            background: #dc3545;
            color: white;
        }
        
        /* Estilos para notificações */
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
            max-width: 400px;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .notification i {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .notification.success {
            background-color: #28a745;
        }
        
        .notification.error {
            background-color: #dc3545;
        }
        
        .notification.info {
            background-color: #17a2b8;
        }
        
        .notification.warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        /* Estilos para indicadores de carregamento */
        .loading-toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transition: opacity 0.3s;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1050;
            transition: opacity 0.3s;
        }
        
        .loading-content {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .loading-content p {
            margin-top: 15px;
            color: #495057;
            font-size: 1.1em;
        }
        
        /* Estilos para mensagens de erro */
        .error-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            max-width: 800px;
            margin: 40px auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .error-container h2 {
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .error-container p {
            margin-bottom: 20px;
            color: #6c757d;
        }
        
        .error-container .btn {
            margin-top: 15px;
        }
        
        /* Melhorias na responsividade */
        @media (max-width: 768px) {
            .notification {
                left: 10px;
                right: 10px;
                max-width: none;
            }
            
            .error-container {
                margin: 20px;
                padding: 20px;
            }
        }
        
        .custom-modal .modal-dialog {
            max-width: 800px;
            margin: 1.75rem auto;
        }
        
        .custom-modal .modal-lg {
            max-width: 900px;
        }
        
        .custom-modal .modal-content {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
        }
        
        .custom-modal .modal-header {
            border-bottom: 1px solid #e3e6f0;
            padding: 1.25rem 1.5rem;
        }
        
        .custom-modal .modal-header.bg-primary {
            background-color: #4e73df !important;
        }
        
        .custom-modal .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #fff;
            margin: 0;
        }
        
        .custom-modal .modal-body {
            padding: 1.5rem;
        }
        
        .custom-modal .modal-footer {
            border-top: 1px solid #e3e6f0;
            padding: 1rem 1.5rem;
            background-color: #f8f9fc;
            border-bottom-left-radius: 0.35rem;
            border-bottom-right-radius: 0.35rem;
        }
        
        .custom-modal .btn {
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            border-radius: 0.35rem;
            transition: all 0.2s;
        }
        
        .custom-modal .btn-success {
            background-color: #1cc88a;
            border-color: #1cc88a;
        }
        
        .custom-modal .btn-success:hover {
            background-color: #17a673;
            border-color: #169b6b;
        }
        
        .custom-modal .btn-outline-secondary {
            color: #6e707e;
            border-color: #d1d3e2;
        }
        
        .custom-modal .btn-outline-secondary:hover {
            background-color: #f8f9fc;
            border-color: #bac8f3;
            color: #4e73df;
        }
        
        .custom-modal .table {
            margin-bottom: 1rem;
            color: #6e707e;
        }
        
        .custom-modal .table th {
            background-color: #f8f9fc;
            border-bottom-width: 1px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.7rem;
            letter-spacing: 0.05em;
            color: #b7b9cc;
        }
        
        .custom-modal .table td {
            vertical-align: middle;
            border-color: #e3e6f0;
        }
        
        .custom-modal .alert {
            border: none;
            border-radius: 0.35rem;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }
        
        .custom-modal .alert-info {
            background-color: #f0f7ff;
            color: #2c5282;
            border-left: 4px solid #4299e1;
        }
        
        .custom-modal .form-check {
            margin-bottom: 0;
            padding-left: 1.75em;
        }
        
        .custom-modal .form-check-input {
            margin-top: 0.2em;
            margin-left: -1.75em;
        }
        
        .custom-modal .form-check-label {
            color: #5a5c69;
            font-size: 0.9rem;
        }
        
        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            padding: 20px;
        }
        
        .progress-container-content {
            background: white;
            padding: 2.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 600px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .progress-container h5 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        
        .progress-container .progress {
            height: 10px;
            border-radius: 5px;
            background-color: #f0f3f5;
            margin-bottom: 1.5rem;
            overflow: visible;
        }
        
        .progress-container .progress-bar {
            background-color: #4e73df;
            border-radius: 5px;
            box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);
            transition: width 0.3s ease-in-out;
        }
        
        .progress-details {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            border: 1px solid #e3e6f0;
        }
        
        .progress-text {
            font-size: 1rem;
            font-weight: 500;
            color: #4e73df;
            margin-bottom: 0.5rem;
        }
        
        .progress-status {
            font-size: 0.875rem;
            color: #6c757d;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .progress-status .current-item {
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .progress {
            height: 1.5rem;
            background-color: #e9ecef;
            border-radius: 0.25rem;
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .progress-bar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background-color: #4e73df;
            transition: width 0.6s ease;
            font-size: 0.75rem;
            line-height: 1.5rem;
            font-weight: 600;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15);
        }
        
        .progress-bar-striped {
            background-image: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 1.5rem 1.5rem;
        }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }
        
        .progress-text {
            margin-top: 0;
            font-size: 0.9rem;
            color: #4e73df;
            font-weight: 500;
        }
        
        /* Estilos para o formulário de confirmação */
        .form-check {
            display: block;
            min-height: 1.5rem;
            padding-left: 1.5em;
            margin-bottom: 0.125rem;
        }
        
        .form-check-input {
            width: 1em;
            height: 1em;
            margin-top: 0.25em;
            vertical-align: top;
            background-color: #fff;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            border: 1px solid #ced4da;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact; /* Padrão moderno */
            -webkit-print-color-adjust: exact; /* Para compatibilidade com navegadores mais antigos */
            margin-left: -1.5em;
        }
        
        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .form-check-input:checked[type=checkbox] {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
        }
        
        .form-check-label {
            cursor: pointer;
            user-select: none;
        }
        
        .mt-2 { margin-top: 0.5rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        .mt-4 { margin-top: 1.5rem !important; }
        .me-2 { margin-right: 0.5rem !important; }
        .text-center { text-align: center !important; }
    </style>
</head>
<body>
    <div class="quality-container">
        <h1>🔍 Liberação de Qualidade</h1>
        
        <div class="form-group">
            <button onclick="carregarItens()" class="btn btn-primary">🔄 Atualizar Lista</button>
            <button onclick="aprovarTodos()" class="btn-approve">✅ Aprovar Todos Pendentes</button>
        </div>

        <!-- Itens Pendentes -->
        <div class="pending-items">
            <h3>⏳ Itens Pendentes de Inspeção</h3>
            <div id="itensPendentes"></div>
        </div>

        <!-- Itens Aprovados -->
        <div class="approved-items">
            <h3>✅ Itens Aprovados</h3>
            <div id="itensAprovados"></div>
        </div>

        <!-- Itens Rejeitados -->
        <div class="rejected-items">
            <h3>❌ Itens Rejeitados</h3>
            <div id="itensRejeitados"></div>
        </div>
    </div>

    <!-- Modal para motivo de rejeição -->
    <div id="rejectModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>Motivo da Rejeição</h3>
            <textarea id="motivoRejeicao" placeholder="Digite o motivo da rejeição..." rows="4" style="width: 100%; margin: 10px 0;"></textarea>
            <div style="text-align: right;">
                <button onclick="fecharModalRejeicao()" class="btn btn-secondary">Cancelar</button>
                <button onclick="confirmarRejeicao()" class="btn-reject">Confirmar Rejeição</button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc,
            query,
            where,
            orderBy,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        
        import { MaterialEntryService } from './services/material-entry-service.js';

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let itensQualidade = [];
        let itemParaRejeitar = null;

        // Processa itens recebidos do recebimento de materiais
        async function processarItensRecebidos() {
            const loadingToast = showLoadingToast('Verificando itens recebidos...');
            
            try {
                const itensParaQualidade = JSON.parse(sessionStorage.getItem('itensParaQualidade') || '[]');
                const pedidoId = sessionStorage.getItem('pedidoId');
                
                if (itensParaQualidade.length > 0 && pedidoId) {
                    // Mostrar notificação mais amigável
                    showNotification(
                        `Foram recebidos ${itensParaQualidade.length} itens para inspeção de qualidade do pedido ${pedidoId}.`,
                        'info',
                        5000
                    );
                    
                    // Limpar o sessionStorage após processar
                    sessionStorage.removeItem('itensParaQualidade');
                    sessionStorage.removeItem('pedidoId');
                }
                
                loadingToast.remove();
            } catch (error) {
                console.error('Erro ao processar itens recebidos:', error);
                showNotification('Ocorreu um erro ao processar os itens recebidos', 'error');
                loadingToast.remove();
            }
        }

        window.onload = async function() {
            try {
                if (!currentUser) {
                    window.location.href = 'login.html';
                    return;
                }
                
                // Mostrar indicador de carregamento
                const loadingIndicator = showLoadingIndicator('Carregando módulo de qualidade...');
                
                try {
                    // Processar itens recebidos do recebimento, se houver
                    await processarItensRecebidos();
                    
                    // Carregar itens existentes
                    await carregarItens();
                } finally {
                    // Garantir que o indicador de carregamento seja removido
                    loadingIndicator.remove();
                }
                
            } catch (error) {
                console.error('Erro ao inicializar a página:', error);
                showNotification('Erro ao carregar a página de qualidade', 'error');
                
                // Mostrar mensagem de erro na interface
                const container = document.querySelector('.quality-container');
                if (container) {
                    container.innerHTML = `
                        <div class="error-container">
                            <h2>❌ Erro ao carregar a página</h2>
                            <p>Ocorreu um erro ao carregar a página de qualidade. Por favor, tente novamente.</p>
                            <p>Detalhes técnicos: ${error.message || 'Erro desconhecido'}</p>
                            <button onclick="window.location.reload()" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Tentar novamente
                            </button>
                        </div>
                    `;
                }
            }
        };

        // Função para mostrar indicador de carregamento
        function showLoadingToast(message) {
            const toast = document.createElement('div');
            toast.className = 'loading-toast';
            toast.innerHTML = `
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <span class="ms-2">${message}</span>
            `;
            document.body.appendChild(toast);
            
            // Método para remover o toast
            toast.remove = () => {
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            };
            
            return toast;
        }
        
        // Função para mostrar indicador de carregamento na página
        function showLoadingIndicator(message) {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">${message}</p>
                </div>
            `;
            document.body.appendChild(loadingDiv);
            
            // Método para remover o indicador
            loadingDiv.remove = () => {
                loadingDiv.style.opacity = '0';
                setTimeout(() => loadingDiv.remove(), 300);
            };
            
            return loadingDiv;
        }
        
        // Função para exibir notificações
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 
                                      type === 'success' ? 'fa-check-circle' : 
                                      'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Remover notificação após o tempo definido
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }
        
        window.carregarItens = async function(showLoading = true) {
            let loadingToast;
            
            // Referências aos containers
            const containers = {
                pendentes: document.getElementById('itensPendentes'),
                aprovados: document.getElementById('itensAprovados'),
                rejeitados: document.getElementById('itensRejeitados')
            };
            
            // Verificar se os containers existem
            const containersExistem = Object.values(containers).every(container => container !== null);
            if (!containersExistem) {
                console.error('Erro: Um ou mais containers de itens não foram encontrados no DOM');
                return;
            }
            
            // Mostrar toast de carregamento se necessário
            if (showLoading) {
                loadingToast = showLoadingToast('Atualizando lista de itens...');
            }
            
            // Função para atualizar o estado de carregamento
            const updateLoadingState = (progress = 0, message = 'Carregando itens...') => {
                const progressBars = document.querySelectorAll('.progress-bar');
                const progressTexts = document.querySelectorAll('.progress-text');
                
                progressBars.forEach(bar => {
                    bar.style.width = `${progress}%`;
                    bar.setAttribute('aria-valuenow', progress);
                });
                
                progressTexts.forEach(text => {
                    text.textContent = message;
                });
            };
            
            // Função para mostrar estado de carregamento
            const showLoadingState = () => {
                const loadingHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <div class="mt-3">
                            <h5 class="text-primary">Carregando itens</h5>
                            <div class="progress mt-3" style="height: 6px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" 
                                     style="width: 0%" 
                                     aria-valuenow="0" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <p class="progress-text text-muted mt-2">Preparando para carregar itens...</p>
                        </div>
                    </div>`;
                
                // Aplicar a todos os containers
                Object.values(containers).forEach(container => {
                    container.innerHTML = loadingHTML;
                });
                
                // Atualizar progresso inicial
                updateLoadingState(5, 'Iniciando carregamento...');
            };
            
            // Função para mostrar estado vazio
            const showEmptyState = (message = 'Nenhum item encontrado.') => {
                const emptyHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum item encontrado</h5>
                        <p class="text-muted">${message}</p>
                        <button class="btn btn-outline-primary" onclick="carregarItens()">
                            <i class="fas fa-sync-alt me-2"></i>Atualizar
                        </button>
                    </div>`;
                
                // Aplicar a todos os containers
                Object.values(containers).forEach(container => {
                    container.innerHTML = emptyHTML;
                });
            };
            
            // Função para mostrar erro
            const showErrorState = (error) => {
                console.error('Erro ao carregar itens:', error);
                
                // Mapear erros comuns para mensagens amigáveis
                let errorMessage = 'Ocorreu um erro ao carregar os itens. Por favor, tente novamente.';
                let errorDetails = '';
                
                // Mapear códigos de erro comuns
                const errorMessages = {
                    'permission-denied': 'Você não tem permissão para visualizar estes itens.',
                    'unavailable': 'Serviço indisponível. Verifique sua conexão com a internet.',
                    'not-found': 'O recurso solicitado não foi encontrado.',
                    'invalid-argument': 'Dados inválidos fornecidos.',
                    'failed-precondition': 'Operação não pôde ser concluída no estado atual.',
                    'resource-exhausted': 'Limite de requisições excedido. Tente novamente mais tarde.',
                    'deadline-exceeded': 'Tempo de espera excedido. Verifique sua conexão e tente novamente.'
                };
                
                // Determinar a mensagem de erro com base no código
                if (error.code && errorMessages[error.code]) {
                    errorMessage = errorMessages[error.code];
                } else if (error.message) {
                    errorMessage = error.message;
                }
                
                // Coletar detalhes adicionais para depuração
                if (error.stack) {
                    errorDetails += `Stack: ${error.stack}\n\n`;
                }
                if (error.code) {
                    errorDetails += `Código: ${error.code}\n`;
                }
                if (error.name) {
                    errorDetails += `Nome: ${error.name}\n`;
                }
                
                // Criar um ID único para o erro para permitir copiar os detalhes
                const errorId = 'error-' + Date.now();
                const errorHTML = `
                    <div class="alert alert-danger m-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <h5 class="alert-heading mb-0">Erro ao carregar itens</h5>
                                </div>
                                <p class="mb-2">${errorMessage}</p>
                                    <button class="btn btn-outline-secondary" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#errorDetails" 
                                            aria-expanded="false" 
                                            aria-controls="errorDetails">
                                        <i class="fas fa-bug me-1"></i>Detalhes
                                    </button>
                                    <div class="collapse w-100 mt-2" id="errorDetails">
                                        <div class="card card-body bg-light text-dark small">
                                            <pre class="mb-0">${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}</pre>
                                        </div>
                                    </div>` : ''}
                                </div>
                                <div class="text-end mt-2">
                                    <small class="text-muted">${new Date().toLocaleString()}</small>
                                </div>
                            </div>
                        </div>
                    </div>`;
                
                // Aplicar a todos os containers
                Object.values(containers).forEach(container => {
                    container.innerHTML = errorHTML;
                });
                
                showNotification(`Erro ao carregar itens: ${errorMessage}`, 'error');
                
                // Inicializar tooltips e popovers após inserir o HTML
                setTimeout(() => {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
                    
                    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    popoverTriggerList.map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
                }, 100);
            };
            
            // Iniciar estado de carregamento
            showLoadingState();
            
            // Função para limpar o toast de carregamento
            const clearLoadingToast = () => {
                if (loadingToast) {
                    loadingToast.style.opacity = '0';
                    setTimeout(() => loadingToast.remove(), 300);
                }
            };
            
            try {
                updateLoadingState(10, 'Verificando autenticação...');
                
                // Verificar autenticação
                const user = firebase.auth().currentUser;
                if (!user) {
                    throw new Error('Usuário não autenticado. Por favor, faça login novamente.');
                }
                
                // Verificar se o Firestore está disponível
                if (typeof db === 'undefined' || !getDocs) {
                    throw new Error('Serviço de banco de dados não disponível. Por favor, recarregue a página.');
                }
                
                updateLoadingState(20, 'Preparando consulta...');
                
                // Configurar timeout para a requisição
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        const error = new Error('Tempo de carregamento excedido. Verifique sua conexão e tente novamente.');
                        error.code = 'timeout';
                        reject(error);
                    }, 30000); // 30 segundos de timeout
                });
                
                updateLoadingState(30, 'Buscando itens no banco de dados...');
                
                // Buscar itens do Firestore
                const queryPromise = getDocs(
                    query(
                        collection(db, "estoqueQualidade"),
                        orderBy("dataEntrada", "desc")
                    )
                );
                
                // Usar Promise.race para implementar timeout
                const snapshot = await Promise.race([queryPromise, timeoutPromise]);
                
                // Verificar se há documentos
                if (!snapshot.docs || snapshot.docs.length === 0) {
                    // Nenhum item encontrado
                    itensQualidade = [];
                    renderizarItens();
                    showNotification('Nenhum item encontrado para inspeção de qualidade.', 'info');
                    return;
                }
                
                // Processar itens em lotes para melhor desempenho
                const batchSize = 50;
                const allItems = [];
                let processedItems = 0;
                const totalItems = snapshot.docs.length;
                
                // Atualizar estado para mostrar que começou o processamento
                updateLoadingState(30, `Processando ${totalItems} itens...`);
                
                // Função para processar um lote de itens
                const processBatch = (startIndex) => {
                    const endIndex = Math.min(startIndex + batchSize, totalItems);
                    
                    try {
                        // Processar itens do lote atual
                        for (let i = startIndex; i < endIndex; i++) {
                            const doc = snapshot.docs[i];
                            try {
                                const data = doc.data();
                                
                                // Validar dados obrigatórios
                                if (!data.codigo) {
                                    console.warn(`Documento ${doc.id} não possui código, ignorando...`);
                                    continue;
                                }
                                
                                // Processar dados do documento
                                const item = {
                                    id: doc.id,
                                    codigo: String(data.codigo || '').trim(),
                                    descricao: String(data.descricao || 'Sem descrição').trim(),
                                    quantidade: Number(data.quantidade) || 0,
                                    unidade: String(data.unidade || 'un').trim(),
                                    lote: data.lote ? String(data.lote).trim() : null,
                                    loteFornecedor: data.loteFornecedor ? String(data.loteFornecedor).trim() : null,
                                    fornecedor: data.fornecedor ? String(data.fornecedor).trim() : null,
                                    notaFiscal: data.notaFiscal ? String(data.notaFiscal).trim() : null,
                                    pedidoCompra: data.pedidoCompra ? String(data.pedidoCompra).trim() : null,
                                    observacoes: data.observacoes ? String(data.observacoes).trim() : null,
                                    motivoRejeicao: data.motivoRejeicao ? String(data.motivoRejeicao).trim() : null,
                                    status: ['PENDENTE', 'APROVADO', 'REJEITADO'].includes(data.status) 
                                        ? data.status 
                                        : 'PENDENTE',
                                    // Processar datas
                                    dataEntrada: data.dataEntrada?.toDate ? data.dataEntrada.toDate() : (data.dataEntrada || null),
                                    dataAprovacao: data.dataAprovacao?.toDate ? data.dataAprovacao.toDate() : (data.dataAprovacao || null),
                                    dataRejeicao: data.dataRejeicao?.toDate ? data.dataRejeicao.toDate() : (data.dataRejeicao || null),
                                    validade: data.validade?.toDate ? data.validade.toDate() : (data.validade || null),
                                    // Informações de auditoria
                                    aprovadoPor: data.aprovadoPor ? String(data.aprovadoPor).trim() : null,
                                    rejeitadoPor: data.rejeitadoPor ? String(data.rejeitadoPor).trim() : null
                                };
                                
                                allItems.push(item);
                                processedItems++;
                                
                                // Atualizar progresso a cada 10 itens ou no último item
                                if (processedItems % 10 === 0 || processedItems === totalItems) {
                                    const progress = 70 + Math.floor((processedItems / totalItems) * 25);
                                    updateLoadingState(progress, `Processando itens... (${processedItems}/${totalItems})`);
                                }
                                
                            } catch (error) {
                                console.error(`Erro ao processar documento ${doc.id}:`, error);
                                // Continuar com o próximo item mesmo em caso de erro
                                continue;
                            }
                        }
                        
                        // Processar próximo lote se necessário
                        if (endIndex < totalItems) {
                            // Usar setTimeout para não bloquear a UI
                            setTimeout(() => processBatch(endIndex), 0);
                        } else {
                            // Todos os itens foram processados
                            itensQualidade = allItems;
                            renderizarItens();
                            
                            // Mostrar notificação de sucesso
                            if (showLoading) {
                                const message = totalItems > 0 
                                    ? `${totalItems} itens carregados com sucesso!`
                                    : 'Nenhum item encontrado para inspeção de qualidade.';
                                showNotification(message, 'success');
                            }
                        }
                        
                    } catch (error) {
                        console.error('Erro ao processar lote de itens:', error);
                        showErrorState(error);
                    }
                };
                
                // Iniciar processamento em lotes
                try {
                    processBatch(0);
                } catch (error) {
                    console.error('Erro ao iniciar processamento em lotes:', error);
                    showErrorState(error);
                }
                
            } catch (error) {
                console.error('Erro ao carregar itens:', error);
                showErrorState(error);
            } finally {
                // Remover toast de carregamento
                if (loadingToast) {
                    loadingToast.style.opacity = '0';
                    setTimeout(() => {
                        if (loadingToast && loadingToast.parentNode) {
                            loadingToast.remove();
                        }
                    }, 300);
                }
            }
        };

        function renderizarItens() {
            const pendentes = itensQualidade.filter(item => item.status === 'PENDENTE');
            const aprovados = itensQualidade.filter(item => item.status === 'APROVADO');
            const rejeitados = itensQualidade.filter(item => item.status === 'REJEITADO');

            // Função para criar cabeçalho de seção
            const criarCabecalhoSecao = (titulo, quantidade, cor, icone) => {
                return `
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2" style="background-color: ${cor}15; border-left: 4px solid ${cor}; border-radius: 4px;">
                        <div class="d-flex align-items-center">
                            <i class="${icone} me-2" style="color: ${cor};"></i>
                            <h5 class="mb-0" style="color: ${cor};">${titulo}</h5>
                        </div>
                        <span class="badge rounded-pill" style="background-color: ${cor};">${quantidade} itens</span>
                    </div>`;
            };

            // Renderizar itens pendentes
            const containerPendentes = document.getElementById('itensPendentes');
            if (containerPendentes) {
                if (pendentes.length > 0) {
                    containerPendentes.innerHTML = `
                        ${criarCabecalhoSecao('Itens Pendentes', pendentes.length, '#ffc107', 'fas fa-clock')}
                        <div class="row g-3">
                            ${pendentes.map(item => renderizarItem(item)).join('')}
                        </div>`;
                } else {
                    containerPendentes.innerHTML = `
                        ${criarCabecalhoSecao('Itens Pendentes', 0, '#6c757d', 'fas fa-clock')}
                        <div class="text-center p-4 bg-light rounded">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Nenhum item pendente para inspeção</p>
                            <button class="btn btn-sm btn-outline-secondary mt-2" onclick="carregarItens()">
                                <i class="fas fa-sync-alt me-1"></i> Atualizar
                            </button>
                        </div>`;
                }
            }
            
            // Renderizar itens aprovados
            const containerAprovados = document.getElementById('itensAprovados');
            if (containerAprovados) {
                if (aprovados.length > 0) {
                    containerAprovados.innerHTML = `
                        ${criarCabecalhoSecao('Itens Aprovados', aprovados.length, '#28a745', 'fas fa-check-circle')}
                        <div class="row g-3">
                            ${aprovados.map(item => renderizarItem(item)).join('')}
                        </div>`;
                } else {
                    containerAprovados.innerHTML = `
                        ${criarCabecalhoSecao('Itens Aprovados', 0, '#6c757d', 'fas fa-check-circle')}
                        <div class="text-center p-4 bg-light rounded">
                            <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Nenhum item aprovado</p>
                        </div>`;
                }
            }
            
            // Renderizar itens rejeitados
            const containerRejeitados = document.getElementById('itensRejeitados');
            if (containerRejeitados) {
                if (rejeitados.length > 0) {
                    containerRejeitados.innerHTML = `
                        ${criarCabecalhoSecao('Itens Rejeitados', rejeitados.length, '#dc3545', 'fas fa-times-circle')}
                        <div class="row g-3">
                            ${rejeitados.map(item => renderizarItem(item)).join('')}
                        </div>`;
                } else {
                    containerRejeitados.innerHTML = `
                        ${criarCabecalhoSecao('Itens Rejeitados', 0, '#6c757d', 'fas fa-times-circle')}
                        <div class="text-center p-4 bg-light rounded">
                            <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Nenhum item rejeitado</p>
                        </div>`;
                }
            }
            
            // Inicializar tooltips do Bootstrap
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Inicializar popovers do Bootstrap
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    trigger: 'hover',
                    html: true
                });
            });
        }

        function renderizarItem(item) {
            // Função auxiliar para formatar data
            const formatarData = (data) => {
                if (!data) return 'N/A';
                try {
                    const date = data.seconds ? new Date(data.seconds * 1000) : new Date(data);
                    return date.toLocaleString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (e) {
                    console.error('Erro ao formatar data:', e, data);
                    return 'Data inválida';
                }
            };
            
            // Formatar datas
            const dataEntradaFormatada = formatarData(item.dataEntrada);
            const dataAprovacaoFormatada = item.dataAprovacao ? formatarData(item.dataAprovacao) : 'N/A';
            const dataRejeicaoFormatada = item.dataRejeicao ? formatarData(item.dataRejeicao) : 'N/A';
            
            // Determinar cores e ícones com base no status
            const statusConfig = {
                'PENDENTE': { 
                    color: '#ffc107', 
                    icon: 'clock',
                    bgColor: '#fff8e1',
                    text: 'Pendente de Análise'
                },
                'APROVADO': { 
                    color: '#28a745', 
                    icon: 'check-circle',
                    bgColor: '#e8f5e9',
                    text: 'Aprovado'
                },
                'REJEITADO': { 
                    color: '#dc3545', 
                    icon: 'times-circle',
                    bgColor: '#ffebee',
                    text: 'Rejeitado'
                }
            };
            
            const status = statusConfig[item.status] || statusConfig['PENDENTE'];
            
            // Criar badge de status
            const statusBadge = `
                <span class="badge" style="background-color: ${status.color}20; color: ${status.color}; border: 1px solid ${status.color}80;">
                    <i class="fas fa-${status.icon} me-1"></i>
                    ${status.text}
                </span>`;
                
            // Criar botões de ação com base no status
            let botoesAcao = '';
            if (item.status === 'PENDENTE') {
                botoesAcao = `
                    <div class="d-flex gap-2 mt-3">
                        <button class="btn btn-sm btn-success flex-grow-1" 
                                onclick="aprovarItem('${item.id}')"
                                data-bs-toggle="tooltip" 
                                title="Aprovar item">
                            <i class="fas fa-check me-1"></i> Aprovar
                        </button>
                        <button class="btn btn-sm btn-outline-danger flex-grow-1" 
                                onclick="rejeitarItem('${item.id}')"
                                data-bs-toggle="tooltip" 
                                title="Rejeitar item">
                            <i class="fas fa-times me-1"></i> Rejeitar
                        </button>
                    </div>`;
            } else if (item.status === 'REJEITADO' && item.motivoRejeicao) {
                botoesAcao = `
                    <div class="alert alert-danger p-2 mt-2 mb-0" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <div>
                                <strong>Motivo da rejeição:</strong>
                                <p class="mb-0">${item.motivoRejeicao}</p>
                            </div>
                        </div>
                    </div>`;
            }
            
            // Criar informações adicionais
            const informacoesAdicionais = [];
            
            if (item.lote) informacoesAdicionais.push(`<strong>Lote:</strong> ${item.lote}`);
            if (item.validade) informacoesAdicionais.push(`<strong>Validade:</strong> ${formatarData(item.validade)}`);
            if (item.quantidade) informacoesAdicionais.push(`<strong>Qtd:</strong> ${item.quantidade} ${item.unidade || 'un'}`);
            if (item.fornecedor) informacoesAdicionais.push(`<strong>Fornecedor:</strong> ${item.fornecedor}`);
            if (item.notaFiscal) informacoesAdicionais.push(`<strong>NF:</strong> ${item.notaFiscal}`);
            if (item.pedidoCompra) informacoesAdicionais.push(`<strong>Pedido:</strong> ${item.pedidoCompra}`);
            
            const infoAdicionalHTML = informacoesAdicionais.length > 0 
                ? `<div class="small text-muted mt-2">${informacoesAdicionais.join(' • ')}</div>` 
                : '';
                
            // Criar cartão do item
            return `
            <div class="col-12">
                <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid ${status.color} !important;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-1 text-truncate" style="max-width: 70%;" 
                                data-bs-toggle="popover" 
                                data-bs-placement="top" 
                                data-bs-content="${item.descricao || 'Sem descrição'}" 
                                data-bs-trigger="hover">
                                ${item.descricao || 'Item sem descrição'}
                            </h6>
                            ${statusBadge}
                        </div>
                        
                        <div class="d-flex flex-wrap gap-2 small text-muted mb-2">
                            <span data-bs-toggle="tooltip" title="Data de entrada">
                                <i class="far fa-calendar-alt me-1"></i>${dataEntradaFormatada}
                            </span>
                            ${item.status === 'APROVADO' ? `
                                <span data-bs-toggle="tooltip" title="Data de aprovação">
                                    <i class="far fa-calendar-check me-1"></i>${dataAprovacaoFormatada}
                                </span>` : ''}
                            ${item.status === 'REJEITADO' ? `
                                <span data-bs-toggle="tooltip" title="Data de rejeição">
                                    <i class="far fa-calendar-times me-1"></i>${dataRejeicaoFormatada}
                                </span>` : ''}
                        </div>
                        
                        ${infoAdicionalHTML}
                        ${botoesAcao}
                    </div>
                    ${item.observacoes ? `
                        <div class="card-footer bg-light">
                            <small class="text-muted">
                                <i class="far fa-comment-dots me-1"></i>
                                <strong>Observações:</strong> ${item.observacoes}
                            </small>
                        </div>` : ''}
                </div>
            </div>`;
        }

        window.aprovarItem = async function(itemId) {
            // Verificar se há um usuário logado
            if (!currentUser || !currentUser.nome) {
                showNotification('Você precisa estar logado para aprovar itens.', 'error');
                return;
            }
            
            // Verificar se o serviço está disponível
            if (typeof MaterialEntryService === 'undefined' || !MaterialEntryService.approveQualityMaterial) {
                showNotification('Serviço de aprovação não disponível. Por favor, recarregue a página.', 'error');
                return;
            }
            
            const item = itensQualidade.find(i => i.id === itemId);
            if (!item) {
                showNotification('Item não encontrado.', 'error');
                return;
            }
            
            // Criar modal de confirmação
            const modalId = 'confirmApproveItemModal';
            let modal = document.getElementById(modalId);
            
            if (!modal) {
                modal = document.createElement('div');
                modal.id = modalId;
                modal.className = 'custom-modal';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Confirmar Aprovação</h5>
                            </div>
                            <div class="modal-body">
                                <p>Deseja aprovar o item abaixo?</p>
                                <div class="alert alert-info">
                                    <strong>${item.codigo} - ${item.descricao}</strong><br>
                                    Quantidade: ${item.quantidade} ${item.unidade || ''}<br>
                                    Lote: ${item.loteFornecedor || 'N/A'}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" id="cancelApproveItem">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </button>
                                <button type="button" class="btn btn-success" id="confirmApproveItemBtn">
                                    <i class="fas fa-check-circle me-2"></i>Confirmar Aprovação
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                // Adicionar eventos aos botões
                document.getElementById('cancelApproveItem').addEventListener('click', function() {
                    modal.style.display = 'none';
                });
                
                document.getElementById('confirmApproveItemBtn').addEventListener('click', async function() {
                    const btn = this;
                    const originalText = btn.innerHTML;
                    btn.disabled = true;
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';
                    
                    try {
                        const resultado = await MaterialEntryService.approveQualityMaterial(itemId, currentUser.nome);
                        
                        // Fechar o modal
                        modal.style.display = 'none';
                        
                        // Mostrar notificação de sucesso
                        showNotification(
                            `✅ Item aprovado com sucesso! ${resultado.message}`,
                            'success',
                            5000
                        );
                        
                        // Recarregar itens
                        await carregarItens();
                        
                    } catch (error) {
                        console.error('Erro na aprovação:', error);
                        showNotification('Erro ao aprovar item: ' + error.message, 'error');
                    } finally {
                        btn.disabled = false;
                        btn.innerHTML = originalText;
                    }
                });
            }
            
            // Mostrar o modal
            modal.style.display = 'flex';
        };

        window.rejeitarItem = function(itemId) {
            // Verificar se há um usuário logado
            if (!currentUser || !currentUser.nome) {
                showNotification('Você precisa estar logado para rejeitar itens.', 'error');
                return;
            }
            
            // Verificar se o serviço está disponível
            if (typeof MaterialEntryService === 'undefined' || !MaterialEntryService.rejectQualityMaterial) {
                showNotification('Serviço de rejeição não disponível. Por favor, recarregue a página.', 'error');
                return;
            }
            
            const item = itensQualidade.find(i => i.id === itemId);
            if (!item) {
                showNotification('Item não encontrado.', 'error');
                return;
            }
            
            // Criar modal de rejeição
            const modalId = 'rejectItemModal';
            let modal = document.getElementById(modalId);
            
            if (!modal) {
                modal = document.createElement('div');
                modal.id = modalId;
                modal.className = 'custom-modal';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>Confirmar Rejeição</h5>
                            </div>
                            <div class="modal-body">
                                <p>Você está prestes a rejeitar o seguinte item:</p>
                                <div class="alert alert-warning">
                                    <strong>${item.codigo} - ${item.descricao}</strong><br>
                                    Quantidade: ${item.quantidade} ${item.unidade || ''}<br>
                                    Lote: ${item.loteFornecedor || 'N/A'}
                                </div>
                                <div class="mb-3">
                                    <label for="motivoRejeicao" class="form-label">
                                        <strong>Motivo da Rejeição <span class="text-danger">*</span></strong>
                                    </label>
                                    <textarea 
                                        class="form-control" 
                                        id="motivoRejeicao" 
                                        rows="3" 
                                        placeholder="Descreva o motivo da rejeição..."
                                        required
                                    ></textarea>
                                    <div class="form-text">Este campo é obrigatório para registrar a rejeição.</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" id="cancelRejectItem">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </button>
                                <button type="button" class="btn btn-danger" id="confirmRejectItemBtn">
                                    <i class="fas fa-ban me-2"></i>Confirmar Rejeição
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                // Adicionar eventos aos botões
                document.getElementById('cancelRejectItem').addEventListener('click', function() {
                    modal.style.display = 'none';
                });
                
                document.getElementById('confirmRejectItemBtn').addEventListener('click', async function() {
                    const motivo = document.getElementById('motivoRejeicao').value.trim();
                    
                    if (!motivo) {
                        showNotification('Por favor, informe o motivo da rejeição.', 'warning');
                        document.getElementById('motivoRejeicao').focus();
                        return;
                    }
                    
                    const btn = this;
                    const originalText = btn.innerHTML;
                    btn.disabled = true;
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';
                    
                    try {
                        await MaterialEntryService.rejectQualityMaterial(itemId, currentUser.nome, motivo);
                        
                        // Fechar o modal
                        modal.style.display = 'none';
                        
                        // Mostrar notificação de sucesso
                        showNotification('❌ Item rejeitado com sucesso!', 'success', 5000);
                        
                        // Recarregar itens
                        await carregarItens();
                        
                    } catch (error) {
                        console.error('Erro na rejeição:', error);
                        showNotification('Erro ao rejeitar item: ' + error.message, 'error');
                    } finally {
                        btn.disabled = false;
                        btn.innerHTML = originalText;
                    }
                });
            } else {
                // Resetar o formulário se o modal já existir
                document.getElementById('motivoRejeicao').value = '';
            }
            
            // Mostrar o modal
            modal.style.display = 'flex';
            document.getElementById('motivoRejeicao').focus();
        };

        window.aprovarTodos = async function() {
            const pendentes = itensQualidade.filter(item => item.status === 'PENDENTE');
            
            if (pendentes.length === 0) {
                showNotification('Não há itens pendentes para aprovar', 'info');
                return;
            }
            
            // Criar modal de confirmação personalizado
            const modalId = 'confirmApproveAllModal';
            let modal = document.getElementById(modalId);
            
            if (!modal) {
                modal = document.createElement('div');
                modal.id = modalId;
                modal.className = 'custom-modal';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-tasks me-2"></i>Confirmar Aprovação em Lote
                                </h5>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Você está prestes a aprovar <strong>${pendentes.length} itens</strong> em lote. 
                                    Esta ação não pode ser desfeita.
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Resumo dos Itens a Aprovar</label>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Código</th>
                                                    <th>Descrição</th>
                                                    <th>Quantidade</th>
                                                    <th>Lote</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${pendentes.slice(0, 5).map(item => `
                                                    <tr>
                                                        <td>${item.codigo || 'N/A'}</td>
                                                        <td>${item.descricao || 'N/A'}</td>
                                                        <td>${item.quantidade} ${item.unidade || ''}</td>
                                                        <td>${item.loteFornecedor || 'N/A'}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                    ${pendentes.length > 5 ? 
                                        `<div class="text-muted text-center">
                                            + ${pendentes.length - 5} itens adicionais...
                                        </div>` : ''
                                    }
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="confirmApproveAll">
                                    <label class="form-check-label" for="confirmApproveAll">
                                        Confirmo que verifiquei os itens selecionados e desejo prosseguir com a aprovação em lote.
                                    </label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary" id="cancelApproveAll">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </button>
                                <button type="button" class="btn btn-success" id="confirmApproveAllBtn" disabled>
                                    <i class="fas fa-check-circle me-2"></i>Confirmar Aprovação (${pendentes.length} itens)
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                // Adicionar eventos aos botões
                document.getElementById('confirmApproveAll').addEventListener('change', function() {
                    document.getElementById('confirmApproveAllBtn').disabled = !this.checked;
                });
                
                document.getElementById('cancelApproveAll').addEventListener('click', function() {
                    modal.style.display = 'none';
                });
                
                document.getElementById('confirmApproveAllBtn').addEventListener('click', async function() {
                    const btn = this;
                    const originalText = btn.innerHTML;
                    btn.disabled = true;
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';
                    
                    // Fechar o modal
                    modal.style.display = 'none';
                    
                    // Mostrar barra de progresso
                    const progressContainer = document.createElement('div');
                    progressContainer.className = 'progress-container';
                    progressContainer.innerHTML = `
                        <div class="progress-container-content">
                            <h5 class="text-center mb-4">
                                <i class="fas fa-sync-alt fa-spin me-2"></i>Processando Aprovação em Lote
                            </h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" 
                                     style="width: 0%" 
                                     aria-valuenow="0" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <div class="progress-details">
                                <div class="progress-text text-center mb-2">
                                    Aprovando item 1 de ${pendentes.length}...
                                </div>
                                <div class="progress-status text-center text-muted small">
                                    <span class="current-item">-</span>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Processando...</span>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(progressContainer);
                    
                    try {
                        // Processar aprovações em sequência
                        let aprovadosComSucesso = 0;
                        const erros = [];
                        
                        for (let i = 0; i < pendentes.length; i++) {
                            const item = pendentes[i];
                            const itemName = `${item.codigo} - ${item.descricao}`.substring(0, 30) + (item.descricao.length > 30 ? '...' : '');
                            
                            // Atualizar barra de progresso
                            const percent = Math.round(((i + 1) / pendentes.length) * 100);
                            const progressBar = progressContainer.querySelector('.progress-bar');
                            const progressText = progressContainer.querySelector('.progress-text');
                            const progressStatus = progressContainer.querySelector('.progress-status .current-item');
                            
                            progressBar.style.width = `${percent}%`;
                            progressBar.setAttribute('aria-valuenow', percent);
                            progressText.textContent = `Aprovando item ${i + 1} de ${pendentes.length} (${percent}%)`;
                            progressStatus.textContent = `Processando: ${itemName}`;
                            
                            try {
                                await MaterialEntryService.approveQualityMaterial(item.id, currentUser.nome);
                                aprovadosComSucesso++;
                            } catch (error) {
                                console.error(`Erro ao aprovar item ${item.id}:`, error);
                                erros.push({
                                    itemId: item.id,
                                    itemName: itemName,
                                    error: error.message
                                });
                                // Continuar com os próximos itens mesmo se um falhar
                            }
                            
                            // Pequeno atraso para não sobrecarregar a UI
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                        
                        // Remover barra de progresso
                        document.body.removeChild(progressContainer);
                        
                        // Mostrar resumo
                        let mensagem = `✅ ${aprovadosComSucesso} de ${pendentes.length} itens aprovados com sucesso!`;
                        let tipo = 'success';
                        
                        if (erros.length > 0) {
                            mensagem += `\n\n❌ Ocorreram erros em ${erros.length} itens.`;
                            if (erros.length <= 3) {
                                mensagem += '\n\nItens com erro:\n' + 
                                    erros.map(e => `- ${e.itemName}: ${e.error}`).join('\n');
                            } else {
                                mensagem += '\n\nAlguns itens não puderam ser aprovados. Verifique o console para mais detalhes.';
                            }
                            tipo = erros.length === pendentes.length ? 'error' : 'warning';
                        }
                        
                        // Mostrar notificação de resumo
                        showNotification(mensagem, tipo, 15000);
                        
                        // Recarregar lista após processamento
                        await carregarItens();
                        
                    } catch (error) {
                        console.error('Erro ao processar aprovações em lote:', error);
                        showNotification('❌ Erro ao processar aprovações: ' + error.message, 'error');
                        
                        // Garantir que a barra de progresso seja removida em caso de erro
                        if (document.body.contains(progressContainer)) {
                            document.body.removeChild(progressContainer);
                        }
                    } finally {
                        btn.disabled = false;
                        btn.innerHTML = originalText;
                    }
                });
            }
            
            // Mostrar o modal
            modal.style.display = 'flex';
            document.getElementById('confirmApproveAll').focus();
        };

        // Estilos para os modais
        const style = document.createElement('style');
        style.textContent = `
            /* Estilos para o modal de rejeição */
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                align-items: center;
                justify-content: center;
            }
            
            .modal-content {
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            
            .modal-actions {
                display: flex;
                justify-content: flex-end;
                margin-top: 20px;
                gap: 10px;
            }
            
            /* Estilos para o modal de confirmação */
            .custom-modal {
                display: none;
                position: fixed;
                z-index: 1060;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                align-items: center;
                justify-content: center;
                padding: 15px;
            }
            
            .modal-dialog {
                width: 100%;
                max-width: 500px;
                margin: 1.75rem auto;
            }
            
            .modal-content {
                position: relative;
                display: flex;
                flex-direction: column;
                width: 100%;
                pointer-events: auto;
                background-color: #fff;
                background-clip: padding-box;
                border: 1px solid rgba(0, 0, 0, 0.2);
                border-radius: 0.3rem;
                outline: 0;
            }
            
            .modal-header {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                padding: 1rem 1rem;
                border-bottom: 1px solid #dee2e6;
                border-top-left-radius: calc(0.3rem - 1px);
                border-top-right-radius: calc(0.3rem - 1px);
            }
            
            .modal-title {
                margin-bottom: 0;
                line-height: 1.5;
            }
            
            .modal-body {
                position: relative;
                flex: 1 1 auto;
                padding: 1rem;
            }
            
            .modal-footer {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: flex-end;
                padding: 0.75rem;
                border-top: 1px solid #dee2e6;
                border-bottom-right-radius: calc(0.3rem - 1px);
                border-bottom-left-radius: calc(0.3rem - 1px);
            }
            
            .btn {
                display: inline-block;
                font-weight: 400;
                text-align: center;
                white-space: nowrap;
                vertical-align: middle;
                user-select: none;
                border: 1px solid transparent;
                padding: 0.375rem 0.75rem;
                font-size: 1rem;
                line-height: 1.5;
                border-radius: 0.25rem;
                transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, 
                            border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            }
            
            .btn-primary {
                color: #fff;
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
            
            .btn-primary:hover {
                background-color: #0b5ed7;
                border-color: #0a58ca;
            }
            
            .btn-secondary {
                color: #fff;
                background-color: #6c757d;
                border-color: #6c757d;
            }
            
            .btn-secondary:hover {
                background-color: #5c636a;
                border-color: #565e64;
            }
            
            /* Estilos para a barra de progresso */
            .progress-container {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 2rem;
                border-radius: 0.5rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                z-index: 2000;
                width: 90%;
                max-width: 500px;
            }
            
            .progress {
                height: 1.5rem;
                background-color: #e9ecef;
                border-radius: 0.25rem;
                overflow: hidden;
            }
            
            .progress-bar {
                display: flex;
                flex-direction: column;
                justify-content: center;
                color: #fff;
                text-align: center;
                white-space: nowrap;
                background-color: #0d6efd;
                transition: width 0.6s ease;
            }
            
            .progress-bar-striped {
                background-image: linear-gradient(
                    45deg,
                    rgba(255, 255, 255, 0.15) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.15) 50%,
                    rgba(255, 255, 255, 0.15) 75%,
                    transparent 75%,
                    transparent
                );
                background-size: 1rem 1rem;
            }
            
            .progress-bar-animated {
                animation: progress-bar-stripes 1s linear infinite;
            }
            
            @keyframes progress-bar-stripes {
                0% { background-position: 1rem 0; }
                100% { background-position: 0 0; }
            }
            
            .progress-text {
                margin-top: 1rem;
                font-size: 0.9rem;
                color: #6c757d;
            }
            
            /* Estilos para o formulário de confirmação */
            .form-check {
                display: block;
                min-height: 1.5rem;
                padding-left: 1.5em;
                margin-bottom: 0.125rem;
            }
            
            .form-check-input {
                width: 1em;
                height: 1em;
                margin-top: 0.25em;
                vertical-align: top;
                background-color: #fff;
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
                border: 1px solid #ced4da;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                margin-left: -1.5em;
            }
            
            .form-check-input:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
            
            .form-check-input:checked[type=checkbox] {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
            }
            
            .form-check-label {
                cursor: pointer;
                user-select: none;
            }
            
            .mt-2 { margin-top: 0.5rem !important; }
            .mt-3 { margin-top: 1rem !important; }
            .mt-4 { margin-top: 1.5rem !important; }
            .me-2 { margin-right: 0.5rem !important; }
            .text-center { text-align: center !important; }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
