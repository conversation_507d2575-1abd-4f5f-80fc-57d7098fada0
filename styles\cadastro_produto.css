/* Estilos padronizados conforme cadastro_grupo.html */
:root {
  --primary-color: #0854a0;
  --primary-hover: #0a4d8c;
  --secondary-color: #f0f3f6;
  --border-color: #d4d4d4;
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #107e3e;
  --success-hover: #0d6e36;
  --danger-color: #bb0000;
  --danger-hover: #a30000;
  --warning-color: #e9730c;
  --header-bg: #354a5f;
}

.container {
  width: 95%;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 200px;
}

.table-responsive {
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

input, select, textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f7f7f7;
  color: var(--text-color);
}

.header {
  background-color: var(--header-bg);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  margin: -20px -20px 20px -20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
}

.form-container {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: #fff;
}

.form-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  color: var(--primary-color);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
}

textarea {
  resize: vertical;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.products-table th,
.products-table td {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.products-table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  position: relative;
}

.products-table th:hover {
  background-color: #e0e0e0;
}

.products-table tr:hover {
  background-color: #f8f9fa;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.edit-btn, .delete-btn {
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.edit-btn {
  background-color: #ffc107;
  color: #000;
}

.delete-btn {
  background-color: var(--danger-color);
  color: white;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.required::after {
  content: "*";
  color: var(--danger-color);
  margin-left: 4px;
}

.info-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.table-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
}

.search-container input {
  width: 300px;
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

.sort-indicator {
  margin-left: 5px;
  font-size: 12px;
}

/* Sistema de abas */
.tab-container {
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  gap: 2px;
  background-color: #f8f9fa;
  padding: 5px;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: 8px 16px;
  cursor: pointer;
  border: none;
  background: none;
  color: #666;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab:hover {
  color: var(--primary-color);
}

.tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
  padding: 20px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Seções do formulário */
.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  color: var(--primary-color);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

/* Campos específicos */
.form-group {
  position: relative;
}

.form-group.codigo, 
.form-group.unidade, 
.form-group.tipo {
  max-width: 125px;
}

.form-group.descricao {
  max-width: none;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
  outline: none;
}

/* Switch toggle */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Paginação */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
}

.pagination button {
  min-width: 100px;
}

/* Notificações */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: var(--border-radius-md);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.notification-success {
  background-color: var(--success-color);
}

.notification-error {
  background-color: var(--danger-color);
}

.notification-warning {
  background-color: var(--warning-color);
  color: #000;
}

.notification-info {
  background-color: var(--primary-color);
}

/* Responsividade */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group.codigo, 
  .form-group.unidade, 
  .form-group.tipo {
    max-width: none;
    width: 100%;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    flex: none;
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }
} 