// Função para visualizar itens vinculados ao fornecedor
window.viewLinkedItems = () => {
  const fornecedorId = document.getElementById("fornecedor").value
  if (!fornecedorId) {
    alert("Selecione um fornecedor primeiro")
    return
  }

  const tableBody = document.getElementById("linkedItemsTableBody")
  if (!tableBody) {
    console.error("Elemento linkedItemsTableBody não encontrado")
    return
  }

  tableBody.innerHTML = ""

  // Verificar se há produtos vinculados a este fornecedor
  const produtosVinculados = window.produtosFornecedores.filter((v) => v.fornecedorId === fornecedorId)

  if (produtosVinculados.length === 0) {
    tableBody.innerHTML =
      '<tr><td colspan="5" style="text-align: center;">Nenhum item vinculado a este fornecedor</td></tr>'
  } else {
    produtosVinculados.forEach((vinculo) => {
      const produto = window.produtos.find((p) => p.id === vinculo.produtoId) // Declare produtos before using it
      if (produto) {
        const row = document.createElement("tr")
        row.innerHTML = `
          <td>
            <input type="checkbox" class="item-select" data-codigo="${produto.codigo}">
          </td>
          <td>${produto.codigo || "N/A"}</td>
          <td>${produto.descricao || "N/A"}</td>
          <td>${produto.unidade || "UN"}</td>
          <td>
            <input type="number" class="item-qty" min="0.001" step="0.001" style="width: 100px;">
          </td>
        `
        tableBody.appendChild(row)
      }
    })
  }

  document.getElementById("linkedItemsModal").style.display = "block"
}

// Função para adicionar itens selecionados à solicitação
window.addSelectedItems = () => {
  const selectedRows = document.querySelectorAll("#linkedItemsTableBody tr")
  let itemsAdded = 0

  selectedRows.forEach((row) => {
    const checkbox = row.querySelector(".item-select")
    if (!checkbox) return

    const quantityInput = row.querySelector(".item-qty")
    if (!quantityInput) return

    const quantidade = Number.parseFloat(quantityInput.value)

    if (checkbox.checked && quantidade > 0) {
      const codigo = checkbox.dataset.codigo
      const newRow = window.addItem() // Declare addItem before using it
      const codigoInput = newRow.querySelector(".item-codigo")
      codigoInput.value = codigo
      window.searchProduct(codigoInput) // Declare searchProduct before using it
      newRow.querySelector(".item-quantidade").value = quantidade
      window.updateConversion(newRow) // Declare updateConversion before using it
      itemsAdded++
    }
  })

  if (itemsAdded > 0) {
    window.closeModal("linkedItemsModal") // Declare closeModal before using it
    window.showNotification(`${itemsAdded} item(s) adicionado(s) com sucesso`, "success") // Declare showNotification before using it
  } else {
    alert("Selecione pelo menos um item e informe a quantidade.")
  }
}

// Função para filtrar itens vinculados
window.filterLinkedItems = (searchText) => {
  const rows = document.querySelectorAll("#linkedItemsTableBody tr")
  const search = searchText.toLowerCase()

  rows.forEach((row) => {
    if (row.cells.length < 3) return

    const descricao = row.cells[2].textContent.toLowerCase()
    row.style.display = descricao.includes(search) ? "" : "none"
  })
}

// Declare global variables or import functions here
window.produtos = [] // Example declaration for produtos
window.addItem = () => {} // Example declaration for addItem
window.searchProduct = () => {} // Example declaration for searchProduct
window.updateConversion = () => {} // Example declaration for updateConversion
window.closeModal = () => {} // Example declaration for closeModal
window.showNotification = () => {} // Example declaration for showNotification
