<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 IA Sistema Autônomo - Wizar</title>
    <style>
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --success-color: #00b894;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #ddd;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            color: var(--dark-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .ai-status {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--success-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #666;
        }

        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .ai-log {
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00ff00;
            padding-left: 10px;
        }

        .log-entry.warning {
            border-left-color: var(--warning-color);
            color: var(--warning-color);
        }

        .log-entry.error {
            border-left-color: var(--danger-color);
            color: var(--danger-color);
        }

        .log-entry.success {
            border-left-color: var(--success-color);
            color: var(--success-color);
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-primary back-button" onclick="window.location.href='index.html'">
        ← Voltar ao Menu
    </button>

    <div class="container">
        <div class="header">
            <h1>🤖 IA Sistema Autônomo</h1>
            <p>Inteligência Artificial Avançada para Automação Completa do Sistema</p>
            <div class="ai-status" id="aiStatus">
                <span id="statusIcon">🟢</span>
                <span id="statusText">IA ATIVA - Monitorando Sistema</span>
            </div>
        </div>

        <div class="controls-panel">
            <h3>🎛️ Controles da IA</h3>
            <div class="controls-grid">
                <div>
                    <label>🤖 IA Principal</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="aiMaster" checked onchange="toggleAI('master')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>📦 Auto Compras</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoCompras" checked onchange="toggleAI('compras')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>🏭 Auto Produção</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoProducao" onchange="toggleAI('producao')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>📊 Auto Relatórios</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoRelatorios" checked onchange="toggleAI('relatorios')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>⚡ Modo Agressivo</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="modoAgressivo" onchange="toggleAI('agressivo')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>🔔 Notificações</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="notificacoes" checked onchange="toggleAI('notificacoes')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>⚙️ Config Automática</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="configAutomatica" checked onchange="toggleAI('configAutomatica')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>🔧 Otimização Sistema</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="otimizacaoSistema" onchange="toggleAI('otimizacaoSistema')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>🔍 Varredura Problemas</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="varreduraProblemas" checked onchange="toggleAI('varreduraProblemas')">
                        <span class="slider"></span>
                    </label>
                </div>
                <div>
                    <label>🛠️ Auto Correção</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoCorrecao" onchange="toggleAI('autoCorrecao')">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: center;">
                <button class="btn btn-success" onclick="iniciarIA()">🚀 Iniciar IA</button>
                <button class="btn btn-warning" onclick="pausarIA()">⏸️ Pausar IA</button>
                <button class="btn btn-danger" onclick="pararIA()">🛑 Parar IA</button>
                <button class="btn btn-primary" onclick="configurarIA()">⚙️ Configurar</button>
                <button class="btn btn-primary" onclick="analisarColecoes()">🔍 Analisar Coleções</button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Métricas da IA -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--primary-color);">🧠</div>
                    <div class="card-title">Métricas da IA</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Tempo Ativo</span>
                    <span class="metric-value" id="tempoAtivo">00:00:00</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Decisões Tomadas</span>
                    <span class="metric-value" id="decisoesTomadas">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Economia Gerada</span>
                    <span class="metric-value" id="economiaGerada">R$ 0,00</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Eficiência</span>
                    <span class="metric-value" id="eficiencia">100%</span>
                </div>
            </div>

            <!-- Automação de Compras -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--success-color);">🛒</div>
                    <div class="card-title">Automação de Compras</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Pedidos Gerados</span>
                    <span class="metric-value" id="pedidosGerados">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Valor Total</span>
                    <span class="metric-value" id="valorPedidos">R$ 0,00</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Fornecedores Ativos</span>
                    <span class="metric-value" id="fornecedoresAtivos">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Taxa de Aprovação</span>
                    <span class="metric-value" id="taxaAprovacao">0%</span>
                </div>
            </div>

            <!-- Gestão de Estoque -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--warning-color);">📦</div>
                    <div class="card-title">Gestão de Estoque</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Produtos Monitorados</span>
                    <span class="metric-value" id="produtosMonitorados">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Alertas Gerados</span>
                    <span class="metric-value" id="alertasGerados">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Reposições Auto</span>
                    <span class="metric-value" id="reposicoesAuto">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Acurácia</span>
                    <span class="metric-value" id="acuraciaEstoque">0%</span>
                </div>
            </div>

            <!-- Análise Preditiva -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--danger-color);">🔮</div>
                    <div class="card-title">Análise Preditiva</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Previsões Geradas</span>
                    <span class="metric-value" id="previsoesGeradas">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Precisão Média</span>
                    <span class="metric-value" id="precisaoMedia">0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tendências Detectadas</span>
                    <span class="metric-value" id="tendenciasDetectadas">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Anomalias</span>
                    <span class="metric-value" id="anomalias">0</span>
                </div>
            </div>

            <!-- Análise de Coleções -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: #9b59b6;">🗃️</div>
                    <div class="card-title">Análise de Coleções</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Última Análise</span>
                    <span class="metric-value" id="ultimaAnaliseColecoes">Nunca</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Problemas Críticos</span>
                    <span class="metric-value" id="problemasCriticos">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Melhorias Sugeridas</span>
                    <span class="metric-value" id="melhoriasSugeridas">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Integridade Dados</span>
                    <span class="metric-value" id="integridadeDados">100%</span>
                </div>
            </div>

            <!-- Base de Conhecimento -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: #34495e;">📚</div>
                    <div class="card-title">Base de Conhecimento</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Coleções Mapeadas</span>
                    <span class="metric-value" id="colecoesMapeadas">15</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Documentos</span>
                    <span class="metric-value" id="totalDocumentos">7.046</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Campos Únicos</span>
                    <span class="metric-value" id="camposUnicos">987</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cobertura Sistema</span>
                    <span class="metric-value" id="coberturaSystem">100%</span>
                </div>
            </div>

            <!-- Gestão de Configurações -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--secondary-color);">⚙️</div>
                    <div class="card-title">Gestão de Configurações</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Configs Otimizadas</span>
                    <span class="metric-value" id="configsOtimizadas">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Parâmetros Ajustados</span>
                    <span class="metric-value" id="parametrosAjustados">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Performance Ganho</span>
                    <span class="metric-value" id="performanceGanho">0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Última Otimização</span>
                    <span class="metric-value" id="ultimaOtimizacao">Nunca</span>
                </div>
            </div>

            <!-- Coordenação do Sistema -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(45deg, var(--primary-color), var(--success-color));">🎯</div>
                    <div class="card-title">Coordenação do Sistema</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Módulos Coordenados</span>
                    <span class="metric-value" id="modulosCoordenados">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Sincronizações</span>
                    <span class="metric-value" id="sincronizacoes">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Conflitos Resolvidos</span>
                    <span class="metric-value" id="conflitosResolvidos">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Integridade Sistema</span>
                    <span class="metric-value" id="integridadeSistema">100%</span>
                </div>
            </div>

            <!-- Informações da Empresa -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(45deg, #2d3436, #636e72);">🏢</div>
                    <div class="card-title">Empresa Monitorada</div>
                </div>
                <div class="metric">
                    <span class="metric-label">Nome</span>
                    <span class="metric-value" id="empresaNome">Carregando...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">CNPJ</span>
                    <span class="metric-value" id="empresaCnpj">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Status</span>
                    <span class="metric-value" id="empresaStatus">Verificando...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Configuração</span>
                    <span class="metric-value" id="empresaConfig">-</span>
                </div>
            </div>
        </div>

        <!-- Log da IA -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon" style="background: var(--dark-color);">📋</div>
                <div class="card-title">Log de Atividades da IA</div>
                <button class="btn btn-primary" onclick="limparLog()" style="margin-left: auto;">🗑️ Limpar</button>
            </div>
            <div class="ai-log" id="aiLog">
                <div class="log-entry">🤖 Sistema IA inicializado com sucesso</div>
                <div class="log-entry">🔍 Iniciando monitoramento do sistema...</div>
                <div class="log-entry">📊 Carregando dados históricos para análise...</div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            getDoc,
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Base de Conhecimento das Coleções do Sistema
        const baseConhecimento = {
            colecoes: {
                armazens: {
                    total: 9,
                    campos: ['ativo', 'codigo', 'descricao', 'endereco', 'nome', 'propriedade', 'tipo'],
                    validacoes: ['codigo único', 'tipo válido', 'status ativo'],
                    relacionamentos: ['estoques', 'transferenciasArmazem', 'movimentacoesEstoque'],
                    problemasPotenciais: ['armazéns inativos com estoque', 'códigos duplicados', 'tipos inválidos']
                },
                cotacoes: {
                    total: 33,
                    campos: ['numero', 'status', 'fornecedores', 'itens', 'respostas', 'aglutinada', 'selecaoFinal'],
                    validacoes: ['fornecedores válidos', 'itens com preços', 'status consistente'],
                    relacionamentos: ['solicitacoesCompra', 'pedidosCompra', 'fornecedores'],
                    problemasPotenciais: ['cotações sem respostas', 'preços zerados', 'fornecedores inativos']
                },
                estoques: {
                    total: 933,
                    campos: ['produtoId', 'armazemId', 'saldo', 'saldoReservado', 'precoUnitario'],
                    validacoes: ['saldos não negativos', 'produtos válidos', 'armazéns válidos'],
                    relacionamentos: ['produtos', 'armazens', 'movimentacoesEstoque'],
                    problemasPotenciais: ['saldos negativos', 'produtos órfãos', 'preços zerados']
                },
                familias: {
                    total: 121,
                    campos: ['codigoFamilia', 'nomeFamilia', 'grupo'],
                    validacoes: ['códigos únicos', 'nomes válidos'],
                    relacionamentos: ['produtos', 'grupos'],
                    problemasPotenciais: ['códigos duplicados', 'famílias sem produtos']
                },
                fornecedores: {
                    total: 775,
                    campos: ['codigo', 'razaoSocial', 'cnpjCpf', 'ativo', 'email', 'telefone1'],
                    validacoes: ['CNPJ válido', 'email válido', 'status ativo'],
                    relacionamentos: ['cotacoes', 'pedidosCompra', 'produtos'],
                    problemasPotenciais: ['CNPJs inválidos', 'emails duplicados', 'fornecedores sem produtos']
                },
                grupos: {
                    total: 38,
                    campos: ['codigoGrupo', 'nomeGrupo', 'exemplosGrupo'],
                    validacoes: ['códigos únicos', 'nomes válidos'],
                    relacionamentos: ['familias', 'produtos'],
                    problemasPotenciais: ['códigos duplicados', 'grupos sem famílias']
                },
                movimentacoesestoque: {
                    total: 2724,
                    campos: ['produtoId', 'armazemId', 'quantidade', 'tipo', 'dataHora', 'usuario'],
                    validacoes: ['quantidades válidas', 'tipos válidos', 'usuários válidos'],
                    relacionamentos: ['produtos', 'armazens', 'usuarios', 'estoques'],
                    problemasPotenciais: ['movimentações sem origem', 'quantidades zeradas', 'datas futuras']
                },
                operacoes: {
                    total: 16,
                    campos: ['numero', 'operacao'],
                    validacoes: ['números únicos', 'operações válidas'],
                    relacionamentos: ['ordensProducao'],
                    problemasPotenciais: ['números duplicados', 'operações sem uso']
                },
                ordensproducao: {
                    total: 709,
                    campos: ['numero', 'produtoId', 'quantidade', 'status', 'materiaisNecessarios'],
                    validacoes: ['produtos válidos', 'quantidades positivas', 'materiais suficientes'],
                    relacionamentos: ['produtos', 'estoques', 'transferenciasArmazem'],
                    problemasPotenciais: ['materiais insuficientes', 'produtos inexistentes', 'OPs órfãs']
                },
                parametros: {
                    total: 3,
                    campos: ['configuracaoSistema', 'controleQualidade', 'reservarEstoque'],
                    validacoes: ['configurações consistentes', 'valores válidos'],
                    relacionamentos: ['todos os módulos'],
                    problemasPotenciais: ['configurações conflitantes', 'parâmetros não definidos']
                },
                pedidoscompra: {
                    total: 23,
                    campos: ['numero', 'fornecedorId', 'status', 'itens', 'valorTotal'],
                    validacoes: ['fornecedores válidos', 'valores positivos', 'status válido'],
                    relacionamentos: ['fornecedores', 'cotacoes', 'solicitacoesCompra'],
                    problemasPotenciais: ['pedidos sem recebimento', 'valores divergentes', 'fornecedores inativos']
                },
                produtos: {
                    total: 1604,
                    campos: ['codigo', 'descricao', 'tipo', 'unidade', 'familia', 'grupo', 'custoMedio'],
                    validacoes: ['códigos únicos', 'tipos válidos', 'unidades válidas'],
                    relacionamentos: ['familias', 'grupos', 'estoques', 'ordensProducao'],
                    problemasPotenciais: ['códigos duplicados', 'produtos sem estoque', 'custos zerados']
                },
                solicitacoescompra: {
                    total: 38,
                    campos: ['numero', 'status', 'itens', 'aprovadoPor', 'valorTotal'],
                    validacoes: ['aprovações válidas', 'itens válidos', 'valores positivos'],
                    relacionamentos: ['cotacoes', 'pedidosCompra', 'usuarios'],
                    problemasPotenciais: ['solicitações sem aprovação', 'itens sem preço', 'valores zerados']
                },
                transferenciasarmazem: {
                    total: 15,
                    campos: ['produtoId', 'armazemOrigemId', 'armazemDestinoId', 'quantidade'],
                    validacoes: ['armazéns diferentes', 'quantidades positivas', 'estoque suficiente'],
                    relacionamentos: ['produtos', 'armazens', 'estoques'],
                    problemasPotenciais: ['transferências para mesmo armazém', 'quantidades negativas', 'estoque insuficiente']
                },
                usuarios: {
                    total: 5,
                    campos: ['nome', 'email', 'nivel', 'ativo', 'departamento'],
                    validacoes: ['emails únicos', 'níveis válidos', 'status ativo'],
                    relacionamentos: ['todas as operações'],
                    problemasPotenciais: ['emails duplicados', 'usuários sem departamento', 'níveis inválidos']
                }
            },
            totalDocumentos: 7046,
            totalCampos: 987,
            totalColecoes: 15,
            integridade: {
                critica: ['produtos', 'estoques', 'fornecedores', 'parametros'],
                importante: ['cotacoes', 'pedidoscompra', 'ordensproducao', 'usuarios'],
                auxiliar: ['familias', 'grupos', 'operacoes', 'armazens']
            }
        };

        // Estado da IA
        let iaState = {
            ativa: true,
            inicioTempo: new Date(),
            decisoesTomadas: 0,
            economiaGerada: 0,
            pedidosGerados: 0,
            valorPedidos: 0,
            alertasGerados: 0,
            reposicoesAuto: 0,
            previsoesGeradas: 0,
            tendenciasDetectadas: 0,
            anomalias: 0,
            configsOtimizadas: 0,
            parametrosAjustados: 0,
            performanceGanho: 0,
            ultimaOtimizacao: null,
            modulosCoordenados: 0,
            sincronizacoes: 0,
            conflitosResolvidos: 0,
            integridadeSistema: 100,
            problemasDetectados: [],
            melhoriasSugeridas: [],
            ultimaAnalise: null,
            modulos: {
                compras: true,
                producao: false,
                relatorios: true,
                agressivo: false,
                notificacoes: true,
                configAutomatica: true,
                otimizacaoSistema: false
            }
        };

        // Dados do sistema
        let sistemaDados = {
            produtos: [],
            estoques: [],
            fornecedores: [],
            pedidos: [],
            movimentacoes: [],
            ordensProducao: [],
            configuracoes: {},
            empresa: {}
        };

        // Configurações padrão do sistema
        let configuracoesOtimas = {
            controleQualidade: {
                ativo: true,
                inspecaoRecebimento: 'criticos',
                tempoMaximoInspecao: 3,
                aprovacaoAutomatica: false
            },
            estoque: {
                pontoReposicaoAutomatico: true,
                margemSeguranca: 0.2,
                estoqueMaximoAutomatico: true,
                alertasAtivos: true
            },
            compras: {
                aprovacaoAutomatica: true,
                limiteValorAutomatico: 10000,
                cotacaoObrigatoria: true,
                numeroMinimoCotacoes: 3
            },
            producao: {
                programacaoAutomatica: false,
                otimizacaoSequencia: true,
                alertasAtraso: true,
                integracaoEstoque: true
            },
            financeiro: {
                controleFluxoCaixa: true,
                alertasVencimento: true,
                aprovacaoAutomaticaPagamentos: false,
                limiteAprovacaoAutomatica: 5000
            },
            sistema: {
                backupAutomatico: true,
                frequenciaBackup: 'diario',
                logDetalhado: true,
                monitoramentoPerformance: true
            }
        };

        // Configurações da IA
        let iaConfig = {
            intervalos: {
                monitoramento: 30000, // 30 segundos
                analise: 300000, // 5 minutos
                decisoes: 600000, // 10 minutos
                relatorios: 3600000 // 1 hora
            },
            limites: {
                estoqueMinimo: 10,
                estoqueMaximo: 1000,
                valorMaximoPedido: 50000,
                margemSeguranca: 0.2
            },
            algoritmos: {
                previsaoDemanda: 'exponentialSmoothing',
                otimizacaoCompras: 'economicOrderQuantity',
                selecaoFornecedor: 'multiCriteria'
            }
        };

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let intervalos = {};

        // Inicialização
        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            inicializarIA();
        };

        async function inicializarIA() {
            addLog('🤖 Inicializando Sistema IA Avançado...', 'success');

            try {
                // Carregar dados do sistema
                await carregarDadosSistema();

                // Iniciar monitoramento
                iniciarMonitoramento();

                // Atualizar interface
                atualizarInterface();

                addLog('✅ IA inicializada com sucesso! Sistema operacional.', 'success');

            } catch (error) {
                addLog(`❌ Erro na inicialização: ${error.message}`, 'error');
            }
        }

        async function carregarDadosSistema() {
            addLog('📊 Carregando dados do sistema...', 'info');

            try {
                const [produtosSnap, estoquesSnap, fornecedoresSnap, pedidosSnap, configSnap, empresaDoc] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "configuracoes")),
                    getDoc(doc(db, "empresa", "config"))
                ]);

                sistemaDados.produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                sistemaDados.estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                sistemaDados.fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                sistemaDados.pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar configurações atuais
                if (!configSnap.empty) {
                    sistemaDados.configuracoes = configSnap.docs[0].data();
                } else {
                    sistemaDados.configuracoes = {};
                }

                // Carregar dados da empresa
                if (empresaDoc.exists()) {
                    sistemaDados.empresa = empresaDoc.data();
                    await atualizarInterfaceEmpresa();
                    addLog(`🏢 Empresa carregada: ${sistemaDados.empresa.nomeFantasia || sistemaDados.empresa.razaoSocial || 'Empresa'}`, 'success');
                } else {
                    sistemaDados.empresa = {};
                    addLog('⚠️ Dados da empresa não encontrados - Configure em Configurações > Dados da Empresa', 'warning');
                }

                addLog(`📦 Dados carregados: ${sistemaDados.produtos.length} produtos, ${sistemaDados.estoques.length} estoques`, 'info');
                addLog(`⚙️ Configurações carregadas: ${Object.keys(sistemaDados.configuracoes).length} parâmetros`, 'info');

                // Verificar se precisa otimizar configurações
                if (iaState.modulos.configAutomatica) {
                    await analisarEOtimizarConfiguracoes();
                }

            } catch (error) {
                throw new Error(`Erro ao carregar dados: ${error.message}`);
            }
        }

        async function atualizarInterfaceEmpresa() {
            const empresa = sistemaDados.empresa;

            if (!empresa || Object.keys(empresa).length === 0) {
                return;
            }

            try {
                // Atualizar título da página
                const nomeEmpresa = empresa.nomeFantasia || empresa.razaoSocial || 'Empresa';
                document.title = `🤖 IA Sistema Autônomo - ${nomeEmpresa}`;

                // Atualizar header da IA
                const headerTitle = document.querySelector('.header h1');
                if (headerTitle) {
                    headerTitle.innerHTML = `🤖 IA Sistema Autônomo<br><small style="font-size: 16px; opacity: 0.8;">${nomeEmpresa}</small>`;
                }

                // Atualizar descrição com dados da empresa
                const headerDescription = document.querySelector('.header p');
                if (headerDescription) {
                    const cnpj = empresa.cnpj ? ` • CNPJ: ${empresa.cnpj}` : '';
                    headerDescription.textContent = `Inteligência Artificial para ${nomeEmpresa}${cnpj}`;
                }

                // Adicionar logo se disponível
                if (empresa.logoUrl) {
                    const logoSize = empresa.tamanhoLogo || 50;
                    const logoImg = document.createElement('img');
                    logoImg.src = empresa.logoUrl;
                    logoImg.style.cssText = `
                        width: ${logoSize}px;
                        height: ${logoSize}px;
                        object-fit: contain;
                        margin-right: 15px;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    `;

                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle && !headerTitle.querySelector('img')) {
                        headerTitle.style.display = 'flex';
                        headerTitle.style.alignItems = 'center';
                        headerTitle.insertBefore(logoImg, headerTitle.firstChild);
                    }
                }

                addLog(`🏢 Interface personalizada para ${nomeEmpresa}`, 'success');

            } catch (error) {
                addLog(`❌ Erro ao personalizar interface: ${error.message}`, 'error');
            }
        }

        async function analisarEOtimizarConfiguracoes() {
            addLog('🔧 Analisando configurações do sistema...', 'info');

            try {
                const problemasDetectados = await detectarProblemasConfiguracao();

                if (problemasDetectados.length > 0) {
                    addLog(`⚠️ ${problemasDetectados.length} problemas de configuração detectados`, 'warning');

                    if (iaState.modulos.otimizacaoSistema) {
                        await aplicarOtimizacoesAutomaticas(problemasDetectados);
                    } else {
                        await gerarRecomendacoesConfiguracao(problemasDetectados);
                    }
                }

                iaState.modulosCoordenados = Object.keys(sistemaDados.configuracoes).length;

            } catch (error) {
                addLog(`❌ Erro na análise de configurações: ${error.message}`, 'error');
            }
        }

        async function detectarProblemasConfiguracao() {
            const problemas = [];
            const configAtual = sistemaDados.configuracoes;

            // Verificar configurações de qualidade
            if (!configAtual.controleQualidade && sistemaDados.estoques.some(e => e.saldo < 0)) {
                problemas.push({
                    tipo: 'QUALIDADE_DESATIVADA',
                    severidade: 'ALTA',
                    descricao: 'Controle de qualidade desativado com estoques negativos detectados',
                    solucao: 'Ativar controle de qualidade para evitar problemas de estoque',
                    configuracao: 'controleQualidade',
                    valorRecomendado: true
                });
            }

            // Verificar pontos de reposição
            const produtosSemPontoReposicao = sistemaDados.produtos.filter(p => !p.estoqueMinimo || p.estoqueMinimo <= 0);
            if (produtosSemPontoReposicao.length > 0) {
                problemas.push({
                    tipo: 'PONTOS_REPOSICAO_FALTANDO',
                    severidade: 'MÉDIA',
                    descricao: `${produtosSemPontoReposicao.length} produtos sem ponto de reposição definido`,
                    solucao: 'Definir pontos de reposição automáticos baseados no histórico',
                    configuracao: 'pontoReposicaoAutomatico',
                    valorRecomendado: true
                });
            }

            // Verificar configurações de compras
            if (!configAtual.cotacaoObrigatoria && sistemaDados.pedidos.some(p => p.valorTotal > 5000)) {
                problemas.push({
                    tipo: 'COTACAO_NAO_OBRIGATORIA',
                    severidade: 'MÉDIA',
                    descricao: 'Cotação não obrigatória para pedidos de alto valor',
                    solucao: 'Tornar cotação obrigatória para pedidos acima de R$ 5.000',
                    configuracao: 'cotacaoObrigatoria',
                    valorRecomendado: true
                });
            }

            // Verificar backup automático
            if (!configAtual.backupAutomatico) {
                problemas.push({
                    tipo: 'BACKUP_DESATIVADO',
                    severidade: 'CRÍTICA',
                    descricao: 'Backup automático desativado - risco de perda de dados',
                    solucao: 'Ativar backup automático diário',
                    configuracao: 'backupAutomatico',
                    valorRecomendado: true
                });
            }

            // Verificar limites de aprovação
            if (!configAtual.limiteValorAutomatico || configAtual.limiteValorAutomatico > 50000) {
                problemas.push({
                    tipo: 'LIMITE_APROVACAO_ALTO',
                    severidade: 'ALTA',
                    descricao: 'Limite de aprovação automática muito alto ou indefinido',
                    solucao: 'Definir limite de aprovação automática em R$ 10.000',
                    configuracao: 'limiteValorAutomatico',
                    valorRecomendado: 10000
                });
            }

            return problemas;
        }

        async function aplicarOtimizacoesAutomaticas(problemas) {
            addLog('🚀 Aplicando otimizações automáticas...', 'success');

            let configuracoesAtualizadas = { ...sistemaDados.configuracoes };
            let parametrosAlterados = 0;

            for (const problema of problemas) {
                try {
                    // Aplicar correção automática
                    configuracoesAtualizadas[problema.configuracao] = problema.valorRecomendado;
                    parametrosAlterados++;

                    addLog(`✅ ${problema.configuracao} otimizado: ${problema.solucao}`, 'success');

                    // Registrar conflito resolvido
                    iaState.conflitosResolvidos++;

                } catch (error) {
                    addLog(`❌ Erro ao otimizar ${problema.configuracao}: ${error.message}`, 'error');
                }
            }

            // Salvar configurações otimizadas
            if (parametrosAlterados > 0) {
                await salvarConfiguracoes(configuracoesAtualizadas);

                iaState.configsOtimizadas++;
                iaState.parametrosAjustados += parametrosAlterados;
                iaState.ultimaOtimizacao = new Date();
                iaState.performanceGanho += parametrosAlterados * 5; // Estimativa de ganho

                addLog(`🎉 ${parametrosAlterados} configurações otimizadas automaticamente!`, 'success');
            }
        }

        async function gerarRecomendacoesConfiguracao(problemas) {
            addLog('💡 Gerando recomendações de configuração...', 'info');

            for (const problema of problemas) {
                const severidadeIcon = problema.severidade === 'CRÍTICA' ? '🚨' :
                                     problema.severidade === 'ALTA' ? '⚠️' : '💡';

                addLog(`${severidadeIcon} RECOMENDAÇÃO: ${problema.solucao}`, 'warning');
            }

            // Salvar recomendações para relatório
            const recomendacoes = {
                timestamp: Timestamp.now(),
                problemas: problemas,
                geradoPor: 'IA_SISTEMA',
                status: 'PENDENTE'
            };

            await addDoc(collection(db, "recomendacoesIA"), recomendacoes);
            addLog('📋 Recomendações salvas para análise posterior', 'info');
        }

        async function salvarConfiguracoes(novasConfiguracoes) {
            try {
                // Buscar documento de configurações existente
                const configSnap = await getDocs(collection(db, "configuracoes"));

                if (!configSnap.empty) {
                    // Atualizar configurações existentes
                    const configDoc = configSnap.docs[0];
                    await updateDoc(doc(db, "configuracoes", configDoc.id), {
                        ...novasConfiguracoes,
                        ultimaAtualizacao: Timestamp.now(),
                        atualizadoPor: 'IA_SISTEMA'
                    });
                } else {
                    // Criar novo documento de configurações
                    await addDoc(collection(db, "configuracoes"), {
                        ...novasConfiguracoes,
                        criadoEm: Timestamp.now(),
                        criadoPor: 'IA_SISTEMA'
                    });
                }

                // Atualizar dados locais
                sistemaDados.configuracoes = novasConfiguracoes;
                iaState.sincronizacoes++;

                addLog('💾 Configurações salvas com sucesso', 'success');

            } catch (error) {
                throw new Error(`Erro ao salvar configurações: ${error.message}`);
            }
        }

        async function coordenarConfiguracoesSistema() {
            try {
                // Verificar integridade das configurações
                const integridadeAtual = await verificarIntegridadeSistema();
                iaState.integridadeSistema = integridadeAtual;

                if (integridadeAtual < 90) {
                    addLog(`⚠️ Integridade do sistema baixa: ${integridadeAtual}%`, 'warning');

                    if (iaState.modulos.otimizacaoSistema) {
                        await executarCorrecaoIntegridade();
                    }
                }

                // Sincronizar configurações entre módulos
                await sincronizarConfiguracoesModulos();

            } catch (error) {
                addLog(`❌ Erro na coordenação: ${error.message}`, 'error');
            }
        }

        // Análise Avançada das Coleções
        async function analisarColecoes() {
            addLog('🔍 Iniciando análise avançada das coleções...', 'info');
            iaState.problemasDetectados = [];
            iaState.melhoriasSugeridas = [];
            iaState.ultimaAnalise = new Date();

            try {
                // Buscar dados reais do Firebase
                const [produtosSnap, estoquesSnap, fornecedoresSnap, cotacoesSnap, pedidosSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "cotacoes")),
                    getDocs(collection(db, "pedidosCompra"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Análise de Produtos
                await analisarProdutos(produtos, estoques);

                // Análise de Estoques
                await analisarEstoques(estoques, produtos);

                // Análise de Fornecedores
                await analisarFornecedores(fornecedores, cotacoes, pedidos);

                // Análise de Cotações
                await analisarCotacoes(cotacoes, fornecedores);

                // Análise de Pedidos
                await analisarPedidos(pedidos, fornecedores);

                // Gerar relatório final
                gerarRelatorioAnalise();

            } catch (error) {
                addLog(`❌ Erro na análise: ${error.message}`, 'error');
            }
        }

        async function analisarProdutos(produtos, estoques) {
            addLog('📦 Analisando produtos...', 'info');

            // Produtos sem código
            const produtosSemCodigo = produtos.filter(p => !p.codigo || p.codigo.trim() === '');
            if (produtosSemCodigo.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'CRÍTICO',
                    modulo: 'PRODUTOS',
                    problema: `${produtosSemCodigo.length} produtos sem código`,
                    impacto: 'Alto - Dificulta identificação e controle',
                    solucao: 'Gerar códigos automáticos ou solicitar preenchimento'
                });
            }

            // Produtos sem estoque
            const produtosSemEstoque = produtos.filter(p =>
                !estoques.some(e => e.produtoId === p.id)
            );
            if (produtosSemEstoque.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'PRODUTOS',
                    problema: `${produtosSemEstoque.length} produtos sem registro de estoque`,
                    impacto: 'Médio - Controle de estoque incompleto',
                    solucao: 'Criar registros de estoque zerado para todos os produtos'
                });
            }

            // Produtos com custo zero
            const produtosCustoZero = produtos.filter(p => !p.custoMedio || p.custoMedio === 0);
            if (produtosCustoZero.length > 0) {
                iaState.melhoriasSugeridas.push({
                    tipo: 'OTIMIZAÇÃO',
                    modulo: 'PRODUTOS',
                    sugestao: `Atualizar custos de ${produtosCustoZero.length} produtos`,
                    beneficio: 'Melhora cálculos financeiros e relatórios de margem',
                    prioridade: 'Média'
                });
            }

            addLog(`✅ Produtos analisados: ${produtos.length} registros`, 'success');
        }

        async function analisarEstoques(estoques, produtos) {
            addLog('📊 Analisando estoques...', 'info');

            // Estoques negativos
            const estoquesNegativos = estoques.filter(e => e.saldo < 0);
            if (estoquesNegativos.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'CRÍTICO',
                    modulo: 'ESTOQUES',
                    problema: `${estoquesNegativos.length} itens com saldo negativo`,
                    impacto: 'Alto - Inconsistência nos dados de estoque',
                    solucao: 'Revisar movimentações e corrigir saldos'
                });
            }

            // Estoques órfãos (sem produto)
            const estoquesOrfaos = estoques.filter(e =>
                !produtos.some(p => p.id === e.produtoId)
            );
            if (estoquesOrfaos.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'ESTOQUES',
                    problema: `${estoquesOrfaos.length} registros de estoque órfãos`,
                    impacto: 'Médio - Dados inconsistentes',
                    solucao: 'Remover registros órfãos ou recriar produtos'
                });
            }

            // Produtos com muito estoque
            const estoqueAlto = estoques.filter(e => e.saldo > 1000);
            if (estoqueAlto.length > 0) {
                iaState.melhoriasSugeridas.push({
                    tipo: 'ANÁLISE',
                    modulo: 'ESTOQUES',
                    sugestao: `Revisar ${estoqueAlto.length} itens com estoque alto`,
                    beneficio: 'Otimização de capital de giro',
                    prioridade: 'Baixa'
                });
            }

            addLog(`✅ Estoques analisados: ${estoques.length} registros`, 'success');
        }

        async function analisarFornecedores(fornecedores, cotacoes, pedidos) {
            addLog('🏢 Analisando fornecedores...', 'info');

            // Fornecedores sem CNPJ
            const fornecedoresSemCNPJ = fornecedores.filter(f => !f.cnpjCpf || f.cnpjCpf.trim() === '');
            if (fornecedoresSemCNPJ.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'FORNECEDORES',
                    problema: `${fornecedoresSemCNPJ.length} fornecedores sem CNPJ`,
                    impacto: 'Médio - Problemas fiscais e de identificação',
                    solucao: 'Solicitar e cadastrar CNPJs faltantes'
                });
            }

            // Fornecedores inativos
            const fornecedoresInativos = fornecedores.filter(f => f.ativo === false);
            if (fornecedoresInativos.length > 0) {
                iaState.melhoriasSugeridas.push({
                    tipo: 'LIMPEZA',
                    modulo: 'FORNECEDORES',
                    sugestao: `Revisar ${fornecedoresInativos.length} fornecedores inativos`,
                    beneficio: 'Limpeza da base de dados',
                    prioridade: 'Baixa'
                });
            }

            // Fornecedores sem atividade recente
            const fornecedoresSemAtividade = fornecedores.filter(f => {
                const temCotacao = cotacoes.some(c => c.fornecedores && c.fornecedores.includes(f.id));
                const temPedido = pedidos.some(p => p.fornecedorId === f.id);
                return !temCotacao && !temPedido;
            });

            if (fornecedoresSemAtividade.length > 0) {
                iaState.melhoriasSugeridas.push({
                    tipo: 'ANÁLISE',
                    modulo: 'FORNECEDORES',
                    sugestao: `${fornecedoresSemAtividade.length} fornecedores sem atividade`,
                    beneficio: 'Identificar fornecedores para reativação ou remoção',
                    prioridade: 'Baixa'
                });
            }

            addLog(`✅ Fornecedores analisados: ${fornecedores.length} registros`, 'success');
        }

        async function verificarIntegridadeSistema() {
            let pontuacao = 100;
            const config = sistemaDados.configuracoes;

            // Verificar configurações críticas
            if (!config.backupAutomatico) pontuacao -= 20;
            if (!config.controleQualidade) pontuacao -= 15;
            if (!config.alertasAtivos) pontuacao -= 10;
            if (!config.monitoramentoPerformance) pontuacao -= 10;

            // Verificar consistência de dados
            const produtosSemEstoque = sistemaDados.produtos.filter(p =>
                !sistemaDados.estoques.some(e => e.produtoId === p.id)
            );
            if (produtosSemEstoque.length > 0) {
                pontuacao -= Math.min(20, produtosSemEstoque.length * 2);
            }

            // Verificar fornecedores sem produtos
            const fornecedoresSemProdutos = sistemaDados.fornecedores.filter(f =>
                !f.produtos || f.produtos.length === 0
            );
            if (fornecedoresSemProdutos.length > 0) {
                pontuacao -= Math.min(15, fornecedoresSemProdutos.length * 3);
            }

            return Math.max(0, pontuacao);
        }

        async function analisarCotacoes(cotacoes, fornecedores) {
            addLog('💰 Analisando cotações...', 'info');

            // Cotações sem respostas
            const cotacoesSemResposta = cotacoes.filter(c =>
                !c.respostas || Object.keys(c.respostas).length === 0
            );
            if (cotacoesSemResposta.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'COTAÇÕES',
                    problema: `${cotacoesSemResposta.length} cotações sem respostas`,
                    impacto: 'Médio - Processo de compra incompleto',
                    solucao: 'Acompanhar fornecedores ou cancelar cotações antigas'
                });
            }

            // Cotações com fornecedores inativos
            const cotacoesComFornecedorInativo = cotacoes.filter(c => {
                if (!c.fornecedores) return false;
                return c.fornecedores.some(fId => {
                    const fornecedor = fornecedores.find(f => f.id === fId);
                    return fornecedor && !fornecedor.ativo;
                });
            });

            if (cotacoesComFornecedorInativo.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'COTAÇÕES',
                    problema: `${cotacoesComFornecedorInativo.length} cotações com fornecedores inativos`,
                    impacto: 'Médio - Cotações inválidas',
                    solucao: 'Remover fornecedores inativos das cotações'
                });
            }

            addLog(`✅ Cotações analisadas: ${cotacoes.length} registros`, 'success');
        }

        async function analisarPedidos(pedidos, fornecedores) {
            addLog('🛒 Analisando pedidos de compra...', 'info');

            // Pedidos pendentes há muito tempo
            const agora = new Date();
            const pedidosPendentes = pedidos.filter(p => {
                if (p.status !== 'PENDENTE' && p.status !== 'ENVIADO') return false;
                const dataCriacao = p.dataCriacao?.toDate ? p.dataCriacao.toDate() : new Date(p.dataCriacao);
                const diasPendente = (agora - dataCriacao) / (1000 * 60 * 60 * 24);
                return diasPendente > 30;
            });

            if (pedidosPendentes.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'ATENÇÃO',
                    modulo: 'PEDIDOS',
                    problema: `${pedidosPendentes.length} pedidos pendentes há mais de 30 dias`,
                    impacto: 'Médio - Atraso no processo de compras',
                    solucao: 'Acompanhar fornecedores ou cancelar pedidos antigos'
                });
            }

            // Pedidos sem valor total
            const pedidosSemValor = pedidos.filter(p => !p.valorTotal || p.valorTotal === 0);
            if (pedidosSemValor.length > 0) {
                iaState.problemasDetectados.push({
                    tipo: 'CRÍTICO',
                    modulo: 'PEDIDOS',
                    problema: `${pedidosSemValor.length} pedidos sem valor total`,
                    impacto: 'Alto - Controle financeiro comprometido',
                    solucao: 'Recalcular valores dos pedidos'
                });
            }

            addLog(`✅ Pedidos analisados: ${pedidos.length} registros`, 'success');
        }

        function gerarRelatorioAnalise() {
            addLog('📋 === RELATÓRIO DE ANÁLISE COMPLETO ===', 'info');

            // Resumo geral
            addLog(`🔍 Análise realizada em: ${iaState.ultimaAnalise.toLocaleString()}`, 'info');
            addLog(`📊 Base de conhecimento: ${baseConhecimento.totalColecoes} coleções, ${baseConhecimento.totalDocumentos} documentos`, 'info');

            // Problemas detectados
            if (iaState.problemasDetectados.length > 0) {
                addLog(`❌ PROBLEMAS DETECTADOS: ${iaState.problemasDetectados.length}`, 'error');

                const criticos = iaState.problemasDetectados.filter(p => p.tipo === 'CRÍTICO');
                const atencao = iaState.problemasDetectados.filter(p => p.tipo === 'ATENÇÃO');

                if (criticos.length > 0) {
                    addLog(`🚨 CRÍTICOS: ${criticos.length}`, 'error');
                    criticos.forEach(p => {
                        addLog(`  • ${p.modulo}: ${p.problema}`, 'error');
                        addLog(`    Solução: ${p.solucao}`, 'info');
                    });
                }

                if (atencao.length > 0) {
                    addLog(`⚠️ ATENÇÃO: ${atencao.length}`, 'warning');
                    atencao.forEach(p => {
                        addLog(`  • ${p.modulo}: ${p.problema}`, 'warning');
                    });
                }
            } else {
                addLog('✅ Nenhum problema crítico detectado!', 'success');
            }

            // Melhorias sugeridas
            if (iaState.melhoriasSugeridas.length > 0) {
                addLog(`💡 MELHORIAS SUGERIDAS: ${iaState.melhoriasSugeridas.length}`, 'info');

                const alta = iaState.melhoriasSugeridas.filter(m => m.prioridade === 'Alta');
                const media = iaState.melhoriasSugeridas.filter(m => m.prioridade === 'Média');
                const baixa = iaState.melhoriasSugeridas.filter(m => m.prioridade === 'Baixa');

                if (alta.length > 0) {
                    addLog(`🔥 PRIORIDADE ALTA: ${alta.length}`, 'warning');
                    alta.forEach(m => addLog(`  • ${m.modulo}: ${m.sugestao}`, 'warning'));
                }

                if (media.length > 0) {
                    addLog(`📈 PRIORIDADE MÉDIA: ${media.length}`, 'info');
                    media.forEach(m => addLog(`  • ${m.modulo}: ${m.sugestao}`, 'info'));
                }

                if (baixa.length > 0) {
                    addLog(`📋 PRIORIDADE BAIXA: ${baixa.length}`, 'info');
                }
            }

            // Atualizar métricas
            iaState.anomalias = iaState.problemasDetectados.filter(p => p.tipo === 'CRÍTICO').length;
            iaState.tendenciasDetectadas = iaState.melhoriasSugeridas.length;
            iaState.decisoesTomadas++;

            addLog('📋 === FIM DO RELATÓRIO ===', 'info');
        }

        async function executarCorrecaoIntegridade() {
            addLog('🔧 Executando correção de integridade...', 'info');

            let correcoesAplicadas = 0;

            // Corrigir produtos sem estoque
            const produtosSemEstoque = sistemaDados.produtos.filter(p =>
                !sistemaDados.estoques.some(e => e.produtoId === p.id)
            );

            for (const produto of produtosSemEstoque) {
                try {
                    // Criar registro de estoque zerado
                    await addDoc(collection(db, "estoques"), {
                        produtoId: produto.id,
                        codigo: produto.codigo,
                        saldo: 0,
                        armazemId: produto.armazemPadraoId || 'PRINCIPAL',
                        ultimaMovimentacao: Timestamp.now(),
                        criadoPor: 'IA_SISTEMA_CORRECAO'
                    });

                    correcoesAplicadas++;
                    addLog(`✅ Estoque criado para produto ${produto.codigo}`, 'success');

                } catch (error) {
                    addLog(`❌ Erro ao criar estoque para ${produto.codigo}: ${error.message}`, 'error');
                }
            }

            // Ativar configurações críticas
            const configCriticas = {
                backupAutomatico: true,
                alertasAtivos: true,
                monitoramentoPerformance: true
            };

            await salvarConfiguracoes({ ...sistemaDados.configuracoes, ...configCriticas });
            correcoesAplicadas += Object.keys(configCriticas).length;

            iaState.conflitosResolvidos += correcoesAplicadas;
            addLog(`🎉 ${correcoesAplicadas} correções de integridade aplicadas`, 'success');
        }

        async function sincronizarConfiguracoesModulos() {
            // Verificar se configurações estão sincronizadas entre módulos
            const modulosDesincronizados = [];

            // Verificar sincronização estoque-compras
            if (sistemaDados.configuracoes.pontoReposicaoAutomatico &&
                !sistemaDados.configuracoes.aprovacaoAutomatica) {
                modulosDesincronizados.push('estoque-compras');
            }

            // Verificar sincronização qualidade-estoque
            if (sistemaDados.configuracoes.controleQualidade &&
                !sistemaDados.configuracoes.integracaoEstoque) {
                modulosDesincronizados.push('qualidade-estoque');
            }

            if (modulosDesincronizados.length > 0) {
                addLog(`🔄 Sincronizando ${modulosDesincronizados.length} módulos...`, 'info');

                // Aplicar sincronização automática
                const configSincronizada = { ...sistemaDados.configuracoes };

                if (modulosDesincronizados.includes('estoque-compras')) {
                    configSincronizada.aprovacaoAutomatica = true;
                    configSincronizada.limiteValorAutomatico = 10000;
                }

                if (modulosDesincronizados.includes('qualidade-estoque')) {
                    configSincronizada.integracaoEstoque = true;
                }

                await salvarConfiguracoes(configSincronizada);
                iaState.sincronizacoes += modulosDesincronizados.length;

                addLog(`✅ Módulos sincronizados com sucesso`, 'success');
            }
        }

        function iniciarMonitoramento() {
            if (!iaState.ativa) return;

            addLog('🔍 Iniciando monitoramento contínuo...', 'info');

            // Monitoramento principal
            intervalos.monitoramento = setInterval(async () => {
                if (iaState.ativa) {
                    await executarMonitoramento();
                }
            }, iaConfig.intervalos.monitoramento);

            // Análise preditiva
            intervalos.analise = setInterval(async () => {
                if (iaState.ativa) {
                    await executarAnalise();
                }
            }, iaConfig.intervalos.analise);

            // Tomada de decisões
            intervalos.decisoes = setInterval(async () => {
                if (iaState.ativa && iaState.modulos.compras) {
                    await executarDecisoes();
                }
            }, iaConfig.intervalos.decisoes);

            // Relatórios automáticos
            intervalos.relatorios = setInterval(async () => {
                if (iaState.ativa && iaState.modulos.relatorios) {
                    await gerarRelatoriosAutomaticos();
                }
            }, iaConfig.intervalos.relatorios);

            // Análise das coleções a cada 30 minutos
            intervalos.analiseColecoes = setInterval(async () => {
                if (iaState.ativa && iaState.modulos.varreduraProblemas) {
                    addLog('🔍 Executando análise automática das coleções...', 'info');
                    await analisarColecoes();
                }
            }, 1800000); // 30 minutos
        }

        async function executarMonitoramento() {
            try {
                // Recarregar dados críticos
                await carregarDadosSistema();

                // Verificar estoques críticos
                const estoquesCriticos = await verificarEstoquesCriticos();

                if (estoquesCriticos.length > 0) {
                    addLog(`⚠️ ${estoquesCriticos.length} produtos com estoque crítico detectados`, 'warning');
                    iaState.alertasGerados += estoquesCriticos.length;

                    if (iaState.modulos.compras) {
                        await processarEstoquesCriticos(estoquesCriticos);
                    }
                }

                // Detectar anomalias
                const anomalias = await detectarAnomalias();
                if (anomalias.length > 0) {
                    iaState.anomalias += anomalias.length;
                    addLog(`🚨 ${anomalias.length} anomalias detectadas no sistema`, 'error');
                }

                // Coordenar configurações se ativado
                if (iaState.modulos.configAutomatica) {
                    await coordenarConfiguracoesSistema();
                }

                atualizarInterface();

            } catch (error) {
                addLog(`❌ Erro no monitoramento: ${error.message}`, 'error');
            }
        }

        async function verificarEstoquesCriticos() {
            const criticos = [];

            for (const produto of sistemaDados.produtos) {
                const estoque = sistemaDados.estoques.find(e => e.produtoId === produto.id);
                const saldoAtual = estoque ? estoque.saldo : 0;
                const estoqueMinimo = produto.estoqueMinimo || iaConfig.limites.estoqueMinimo;

                if (saldoAtual <= estoqueMinimo) {
                    criticos.push({
                        produto,
                        saldoAtual,
                        estoqueMinimo,
                        deficit: estoqueMinimo - saldoAtual,
                        prioridade: saldoAtual === 0 ? 'CRÍTICA' : 'ALTA'
                    });
                }
            }

            return criticos.sort((a, b) => a.saldoAtual - b.saldoAtual);
        }

        async function processarEstoquesCriticos(estoquesCriticos) {
            for (const item of estoquesCriticos) {
                try {
                    // Calcular quantidade ideal para pedido
                    const qtdPedido = calcularQuantidadeOtima(item.produto, item.deficit);

                    // Selecionar melhor fornecedor
                    const fornecedor = await selecionarMelhorFornecedor(item.produto);

                    if (fornecedor && qtdPedido > 0) {
                        // Gerar pedido automático
                        await gerarPedidoAutomatico(item.produto, qtdPedido, fornecedor);

                        iaState.reposicoesAuto++;
                        iaState.decisoesTomadas++;

                        addLog(`🛒 Pedido automático gerado: ${item.produto.codigo} - Qtd: ${qtdPedido} - Fornecedor: ${fornecedor.nome}`, 'success');
                    }

                } catch (error) {
                    addLog(`❌ Erro ao processar ${item.produto.codigo}: ${error.message}`, 'error');
                }
            }
        }

        function calcularQuantidadeOtima(produto, deficit) {
            // Algoritmo EOQ (Economic Order Quantity) simplificado
            const demandaMedia = produto.demandaMedia || 100;
            const custoArmazenagem = produto.custoArmazenagem || 0.1;
            const custoPedido = produto.custoPedido || 50;

            // EOQ = √(2 × Demanda × Custo do Pedido / Custo de Armazenagem)
            const eoq = Math.sqrt((2 * demandaMedia * custoPedido) / custoArmazenagem);

            // Considerar deficit e margem de segurança
            const margemSeguranca = demandaMedia * iaConfig.limites.margemSeguranca;
            const qtdOtima = Math.max(deficit + margemSeguranca, eoq);

            return Math.ceil(qtdOtima);
        }

        async function selecionarMelhorFornecedor(produto) {
            // Filtrar fornecedores que fornecem este produto
            const fornecedoresDisponiveis = sistemaDados.fornecedores.filter(f =>
                f.produtos && f.produtos.some(p => p.produtoId === produto.id)
            );

            if (fornecedoresDisponiveis.length === 0) {
                return null;
            }

            // Algoritmo de seleção multi-critério
            let melhorFornecedor = null;
            let melhorScore = -1;

            for (const fornecedor of fornecedoresDisponiveis) {
                const produtoFornecedor = fornecedor.produtos.find(p => p.produtoId === produto.id);

                // Critérios de avaliação (0-1)
                const precoScore = 1 - (produtoFornecedor.preco / produto.precoMaximo || 1);
                const qualidadeScore = fornecedor.qualidade || 0.8;
                const prazoScore = 1 - (fornecedor.prazoMedio || 7) / 30;
                const confiabilidadeScore = fornecedor.confiabilidade || 0.8;

                // Peso dos critérios
                const score = (precoScore * 0.3) + (qualidadeScore * 0.25) +
                             (prazoScore * 0.25) + (confiabilidadeScore * 0.2);

                if (score > melhorScore) {
                    melhorScore = score;
                    melhorFornecedor = fornecedor;
                }
            }

            return melhorFornecedor;
        }

        async function gerarPedidoAutomatico(produto, quantidade, fornecedor) {
            try {
                const produtoFornecedor = fornecedor.produtos.find(p => p.produtoId === produto.id);
                const valorUnitario = produtoFornecedor.preco;
                const valorTotal = quantidade * valorUnitario;

                // Verificar limite de valor
                if (valorTotal > iaConfig.limites.valorMaximoPedido && !iaState.modulos.agressivo) {
                    addLog(`⚠️ Pedido ${produto.codigo} excede limite (R$ ${valorTotal.toFixed(2)}) - Aguardando aprovação manual`, 'warning');
                    return;
                }

                const pedido = {
                    numeroPedido: `PC-AI-${Date.now()}`,
                    fornecedorId: fornecedor.id,
                    fornecedor: fornecedor.nome,
                    dataPedido: Timestamp.now(),
                    status: 'ENVIADO',
                    valorTotal: valorTotal,
                    observacoes: `Pedido gerado automaticamente pela IA - Estoque crítico detectado`,
                    criadoPor: 'IA_SISTEMA',
                    geradoAutomaticamente: true,
                    itens: [{
                        produtoId: produto.id,
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        quantidade: quantidade,
                        valorUnitario: valorUnitario,
                        valorTotal: valorTotal,
                        unidade: produto.unidade
                    }]
                };

                // Salvar no Firebase
                await addDoc(collection(db, "pedidosCompra"), pedido);

                iaState.pedidosGerados++;
                iaState.valorPedidos += valorTotal;
                iaState.economiaGerada += valorTotal * 0.05; // Estimativa de economia

                addLog(`✅ Pedido ${pedido.numeroPedido} criado com sucesso - Valor: R$ ${valorTotal.toFixed(2)}`, 'success');

            } catch (error) {
                throw new Error(`Erro ao gerar pedido: ${error.message}`);
            }
        }

        async function detectarAnomalias() {
            const anomalias = [];

            // Detectar movimentações suspeitas
            for (const produto of sistemaDados.produtos) {
                const estoque = sistemaDados.estoques.find(e => e.produtoId === produto.id);
                if (!estoque) continue;

                // Anomalia: Estoque negativo
                if (estoque.saldo < 0) {
                    anomalias.push({
                        tipo: 'ESTOQUE_NEGATIVO',
                        produto: produto.codigo,
                        valor: estoque.saldo,
                        severidade: 'ALTA'
                    });
                }

                // Anomalia: Estoque muito alto
                const estoqueMaximo = produto.estoqueMaximo || iaConfig.limites.estoqueMaximo;
                if (estoque.saldo > estoqueMaximo * 2) {
                    anomalias.push({
                        tipo: 'ESTOQUE_EXCESSIVO',
                        produto: produto.codigo,
                        valor: estoque.saldo,
                        severidade: 'MÉDIA'
                    });
                }
            }

            return anomalias;
        }

        async function executarAnalise() {
            try {
                addLog('🔮 Executando análise preditiva...', 'info');

                // Análise de tendências
                const tendencias = await analisarTendencias();
                iaState.tendenciasDetectadas += tendencias.length;

                // Previsão de demanda
                const previsoes = await gerarPrevisoesDemanda();
                iaState.previsoesGeradas += previsoes.length;

                if (tendencias.length > 0) {
                    addLog(`📈 ${tendencias.length} tendências detectadas`, 'info');
                }

                if (previsoes.length > 0) {
                    addLog(`🎯 ${previsoes.length} previsões de demanda geradas`, 'info');
                }

            } catch (error) {
                addLog(`❌ Erro na análise: ${error.message}`, 'error');
            }
        }

        async function analisarTendencias() {
            // Análise simplificada de tendências
            const tendencias = [];

            // Aqui implementaríamos algoritmos mais complexos
            // Por enquanto, análise básica

            return tendencias;
        }

        async function gerarPrevisoesDemanda() {
            // Previsão simplificada usando média móvel
            const previsoes = [];

            // Aqui implementaríamos algoritmos de machine learning
            // Por enquanto, previsão básica

            return previsoes;
        }

        async function executarDecisoes() {
            try {
                addLog('🧠 Executando tomada de decisões...', 'info');

                // Decisões já são tomadas no monitoramento
                // Aqui podemos adicionar decisões mais complexas

            } catch (error) {
                addLog(`❌ Erro nas decisões: ${error.message}`, 'error');
            }
        }

        async function gerarRelatoriosAutomaticos() {
            try {
                addLog('📊 Gerando relatórios automáticos...', 'info');

                // Gerar relatório de performance da IA
                const relatorio = {
                    timestamp: new Date(),
                    metricas: { ...iaState },
                    recomendacoes: await gerarRecomendacoes()
                };

                // Salvar relatório
                await addDoc(collection(db, "relatoriosIA"), relatorio);

                addLog('✅ Relatório automático gerado', 'success');

            } catch (error) {
                addLog(`❌ Erro ao gerar relatório: ${error.message}`, 'error');
            }
        }

        async function gerarRecomendacoes() {
            const recomendacoes = [];

            // Analisar performance e gerar recomendações
            if (iaState.alertasGerados > 10) {
                recomendacoes.push('Considere ajustar os pontos de pedido para reduzir alertas');
            }

            if (iaState.economiaGerada > 1000) {
                recomendacoes.push('IA está gerando economia significativa - considere expandir automação');
            }

            return recomendacoes;
        }

        function atualizarInterface() {
            // Atualizar métricas
            const tempoAtivo = new Date() - iaState.inicioTempo;
            const horas = Math.floor(tempoAtivo / 3600000);
            const minutos = Math.floor((tempoAtivo % 3600000) / 60000);
            const segundos = Math.floor((tempoAtivo % 60000) / 1000);

            document.getElementById('tempoAtivo').textContent =
                `${horas.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}:${segundos.toString().padStart(2, '0')}`;

            document.getElementById('decisoesTomadas').textContent = iaState.decisoesTomadas;
            document.getElementById('economiaGerada').textContent = `R$ ${iaState.economiaGerada.toFixed(2)}`;
            document.getElementById('pedidosGerados').textContent = iaState.pedidosGerados;
            document.getElementById('valorPedidos').textContent = `R$ ${iaState.valorPedidos.toFixed(2)}`;
            document.getElementById('alertasGerados').textContent = iaState.alertasGerados;
            document.getElementById('reposicoesAuto').textContent = iaState.reposicoesAuto;
            document.getElementById('previsoesGeradas').textContent = iaState.previsoesGeradas;
            document.getElementById('tendenciasDetectadas').textContent = iaState.tendenciasDetectadas;
            document.getElementById('anomalias').textContent = iaState.anomalias;

            // Atualizar métricas de configuração
            document.getElementById('configsOtimizadas').textContent = iaState.configsOtimizadas;
            document.getElementById('parametrosAjustados').textContent = iaState.parametrosAjustados;
            document.getElementById('performanceGanho').textContent = `${iaState.performanceGanho.toFixed(1)}%`;
            document.getElementById('ultimaOtimizacao').textContent = iaState.ultimaOtimizacao ?
                iaState.ultimaOtimizacao.toLocaleTimeString() : 'Nunca';

            // Atualizar métricas de coordenação
            document.getElementById('modulosCoordenados').textContent = iaState.modulosCoordenados;
            document.getElementById('sincronizacoes').textContent = iaState.sincronizacoes;
            document.getElementById('conflitosResolvidos').textContent = iaState.conflitosResolvidos;
            document.getElementById('integridadeSistema').textContent = `${iaState.integridadeSistema.toFixed(0)}%`;

            // Atualizar informações da empresa
            const empresa = sistemaDados.empresa;
            if (empresa && Object.keys(empresa).length > 0) {
                const nomeEmpresa = empresa.nomeFantasia || empresa.razaoSocial || 'Empresa';
                document.getElementById('empresaNome').textContent = nomeEmpresa.length > 20 ?
                    nomeEmpresa.substring(0, 20) + '...' : nomeEmpresa;

                document.getElementById('empresaCnpj').textContent = empresa.cnpj || 'Não informado';

                const statusEmpresa = empresa.cnpj && empresa.razaoSocial ? 'Configurada' : 'Incompleta';
                document.getElementById('empresaStatus').textContent = statusEmpresa;

                const modulosAtivos = empresa.modulos ?
                    Object.values(empresa.modulos).filter(Boolean).length : 0;
                document.getElementById('empresaConfig').textContent = `${modulosAtivos} módulos`;
            } else {
                document.getElementById('empresaNome').textContent = 'Não configurada';
                document.getElementById('empresaCnpj').textContent = '-';
                document.getElementById('empresaStatus').textContent = 'Pendente';
                document.getElementById('empresaConfig').textContent = 'Configure';
            }

            // Atualizar campos de análise de coleções
            document.getElementById('ultimaAnaliseColecoes').textContent = iaState.ultimaAnalise ?
                iaState.ultimaAnalise.toLocaleTimeString() : 'Nunca';

            const problemasCriticos = iaState.problemasDetectados.filter(p => p.tipo === 'CRÍTICO').length;
            document.getElementById('problemasCriticos').textContent = problemasCriticos;
            document.getElementById('melhoriasSugeridas').textContent = iaState.melhoriasSugeridas.length;

            // Calcular integridade dos dados baseado nos problemas
            const integridadeDados = Math.max(0, 100 - (problemasCriticos * 10) - (iaState.problemasDetectados.length * 2));
            document.getElementById('integridadeDados').textContent = `${integridadeDados}%`;

            // Atualizar base de conhecimento
            document.getElementById('colecoesMapeadas').textContent = baseConhecimento.totalColecoes;
            document.getElementById('totalDocumentos').textContent = baseConhecimento.totalDocumentos.toLocaleString();
            document.getElementById('camposUnicos').textContent = baseConhecimento.totalCampos;

            // Atualizar outros campos
            document.getElementById('produtosMonitorados').textContent = sistemaDados.produtos.length;
            document.getElementById('fornecedoresAtivos').textContent = sistemaDados.fornecedores.length;

            // Calcular métricas derivadas
            const taxaAprovacao = iaState.pedidosGerados > 0 ?
                ((iaState.pedidosGerados / (iaState.pedidosGerados + iaState.alertasGerados)) * 100).toFixed(1) : '0';
            document.getElementById('taxaAprovacao').textContent = `${taxaAprovacao}%`;

            const acuraciaEstoque = sistemaDados.estoques.length > 0 ?
                (((sistemaDados.estoques.length - iaState.anomalias) / sistemaDados.estoques.length) * 100).toFixed(1) : '0';
            document.getElementById('acuraciaEstoque').textContent = `${acuraciaEstoque}%`;

            const precisaoMedia = iaState.previsoesGeradas > 0 ?
                Math.min(95, 80 + (iaState.decisoesTomadas / 10)).toFixed(1) : '0';
            document.getElementById('precisaoMedia').textContent = `${precisaoMedia}%`;

            const eficiencia = Math.min(100, 90 + (iaState.economiaGerada / 1000)).toFixed(0);
            document.getElementById('eficiencia').textContent = `${eficiencia}%`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('aiLog');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // Limitar número de logs
            const logs = logContainer.children;
            if (logs.length > 100) {
                logContainer.removeChild(logs[0]);
            }
        }

        // Funções de controle da interface
        window.toggleAI = function(modulo) {
            if (modulo === 'master') {
                iaState.ativa = document.getElementById('aiMaster').checked;
                if (iaState.ativa) {
                    iniciarMonitoramento();
                    addLog('🟢 IA Principal ativada', 'success');
                } else {
                    pararMonitoramento();
                    addLog('🔴 IA Principal desativada', 'warning');
                }
            } else {
                const checkbox = document.getElementById(`auto${modulo.charAt(0).toUpperCase() + modulo.slice(1)}`) ||
                               document.getElementById(modulo) ||
                               document.getElementById(`${modulo}Automatica`) ||
                               document.getElementById(`${modulo}Sistema`);

                if (checkbox) {
                    iaState.modulos[modulo] = checkbox.checked;
                    addLog(`⚙️ Módulo ${modulo} ${iaState.modulos[modulo] ? 'ativado' : 'desativado'}`, 'info');

                    // Ações específicas por módulo
                    if (modulo === 'configAutomatica' && iaState.modulos[modulo]) {
                        addLog('🔧 Iniciando análise de configurações...', 'info');
                        setTimeout(() => analisarEOtimizarConfiguracoes(), 2000);
                    }

                    if (modulo === 'otimizacaoSistema' && iaState.modulos[modulo]) {
                        addLog('🚀 Modo otimização ativado - Correções automáticas habilitadas', 'success');
                    }
                }
            }

            atualizarStatusIA();
        };

        window.iniciarIA = function() {
            iaState.ativa = true;
            document.getElementById('aiMaster').checked = true;
            iniciarMonitoramento();
            addLog('🚀 IA iniciada manualmente', 'success');

            // Executar análise inicial das coleções se varredura estiver ativa
            if (iaState.modulos.varreduraProblemas) {
                setTimeout(() => {
                    addLog('🔍 Executando análise inicial das coleções...', 'info');
                    analisarColecoes();
                }, 3000);
            }

            atualizarStatusIA();
        };

        window.pausarIA = function() {
            iaState.ativa = false;
            pararMonitoramento();
            addLog('⏸️ IA pausada', 'warning');
            atualizarStatusIA();
        };

        window.pararIA = function() {
            iaState.ativa = false;
            document.getElementById('aiMaster').checked = false;
            pararMonitoramento();
            addLog('🛑 IA parada', 'error');
            atualizarStatusIA();
        };

        window.configurarIA = function() {
            // Mostrar informações da empresa
            addLog('🏢 === INFORMAÇÕES DA EMPRESA ===', 'info');
            const empresa = sistemaDados.empresa;

            if (empresa && Object.keys(empresa).length > 0) {
                addLog(`📋 Razão Social: ${empresa.razaoSocial || 'Não informado'}`, 'info');
                addLog(`🏪 Nome Fantasia: ${empresa.nomeFantasia || 'Não informado'}`, 'info');
                addLog(`📄 CNPJ: ${empresa.cnpj || 'Não informado'}`, 'info');
                addLog(`📧 Email: ${empresa.email || 'Não informado'}`, 'info');
                addLog(`📞 Telefone: ${empresa.telefone || 'Não informado'}`, 'info');
                addLog(`🌐 Site: ${empresa.site || 'Não informado'}`, 'info');
                addLog(`🖼️ Logo: ${empresa.logoUrl ? 'Configurado' : 'Não configurado'}`, empresa.logoUrl ? 'success' : 'warning');

                // Mostrar módulos ativos
                if (empresa.modulos) {
                    addLog('📦 Módulos da Empresa:', 'info');
                    Object.entries(empresa.modulos).forEach(([modulo, ativo]) => {
                        const status = ativo ? '✅' : '❌';
                        addLog(`  ${status} ${modulo}: ${ativo ? 'Ativo' : 'Inativo'}`, ativo ? 'success' : 'warning');
                    });
                }
            } else {
                addLog('❌ Dados da empresa não configurados', 'warning');
                addLog('💡 Configure em: Configurações > Dados da Empresa', 'info');
            }

            addLog('🏢 === FIM INFORMAÇÕES EMPRESA ===', 'info');

            // Mostrar status atual das configurações
            addLog('⚙️ === STATUS CONFIGURAÇÕES DO SISTEMA ===', 'info');

            const config = sistemaDados.configuracoes;
            const configKeys = Object.keys(config);

            if (configKeys.length === 0) {
                addLog('❌ Nenhuma configuração encontrada', 'warning');
            } else {
                addLog(`📊 ${configKeys.length} configurações ativas:`, 'info');

                // Mostrar configurações críticas
                const configsCriticas = [
                    'controleQualidade', 'backupAutomatico', 'aprovacaoAutomatica',
                    'pontoReposicaoAutomatico', 'alertasAtivos'
                ];

                configsCriticas.forEach(key => {
                    const valor = config[key];
                    const status = valor ? '✅' : '❌';
                    addLog(`  ${status} ${key}: ${valor}`, valor ? 'success' : 'warning');
                });
            }

            addLog(`🎯 Integridade do Sistema: ${iaState.integridadeSistema}%`,
                   iaState.integridadeSistema >= 90 ? 'success' : 'warning');
            addLog('⚙️ === FIM STATUS CONFIGURAÇÕES ===', 'info');
        };

        window.limparLog = function() {
            document.getElementById('aiLog').innerHTML = '';
            addLog('🗑️ Log limpo', 'info');
        };

        function pararMonitoramento() {
            Object.values(intervalos).forEach(interval => {
                if (interval) clearInterval(interval);
            });
            intervalos = {};
        }

        function atualizarStatusIA() {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');

            if (iaState.ativa) {
                statusIcon.textContent = '🟢';
                statusText.textContent = 'IA ATIVA - Monitorando Sistema';
            } else {
                statusIcon.textContent = '🔴';
                statusText.textContent = 'IA INATIVA - Sistema Manual';
            }
        }

        // Atualizar interface a cada segundo
        setInterval(atualizarInterface, 1000);
    </script>
</body>
</html>