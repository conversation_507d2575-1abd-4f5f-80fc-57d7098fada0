<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório Onde é Usado</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 3px solid #2c3e50;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header h1 i {
      color: #3498db;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
    }

    .btn-secondary:hover {
      background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
    }

    .main-content {
      padding: 30px;
    }

    .search-container {
      background: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      margin-bottom: 25px;
      border: 1px solid #e9ecef;
    }

    .search-group {
      margin-bottom: 20px;
    }

    .search-label {
      display: block;
      margin-bottom: 8px;
      color: #2c3e50;
      font-weight: 600;
      font-size: 14px;
    }

    .search-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    .search-input:focus {
      outline: none;
      border-color: #3498db;
      background: white;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .search-results {
      margin-top: 8px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      max-height: 250px;
      overflow-y: auto;
      background-color: white;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .search-result-item {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #e9ecef;
      transition: all 0.2s ease;
    }

    .search-result-item:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      color: #2c3e50;
    }

    .search-result-item:last-child {
      border-bottom: none;
    }

    .summary-box {
      background: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      margin-bottom: 25px;
      border: 1px solid #e9ecef;
    }

    .summary-box h3 {
      color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .summary-box h3 i {
      color: #3498db;
    }

    .usage-info {
      margin: 8px 0;
      color: #34495e;
      font-size: 14px;
      line-height: 1.6;
    }

    .usage-info strong {
      color: #2c3e50;
      font-weight: 600;
    }

    .summary-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .summary-item {
      text-align: center;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;
    }

    .summary-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .summary-value {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .summary-label {
      color: #7f8c8d;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      margin-bottom: 25px;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
    }

    .table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
    }

    .table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
      color: #2c3e50;
    }

    .table tbody tr {
      transition: all 0.2s ease;
    }

    .table tbody tr:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      transform: scale(1.01);
    }

    .table tbody tr:last-child td {
      border-bottom: none;
    }

    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(44, 62, 80, 0.8);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 6px solid rgba(255, 255, 255, 0.3);
      border-top: 6px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-results {
      text-align: center;
      padding: 40px;
      color: #7f8c8d;
      font-size: 16px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .error-message {
      text-align: center;
      padding: 40px;
      color: #e74c3c;
      font-size: 16px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    @media print {
      body {
        background: white !important;
        margin: 0;
        padding: 0;
      }
      .no-print {
        display: none !important;
      }
      .container {
        width: 100%;
        margin: 0;
        padding: 15px;
        box-shadow: none;
        border-radius: 0;
      }
      .header {
        background: #2c3e50 !important;
        -webkit-print-color-adjust: exact;
      }
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        border-radius: 10px;
      }

      .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header h1 {
        font-size: 24px;
      }

      .main-content {
        padding: 20px;
      }

      .search-container {
        padding: 20px;
      }

      .summary-info {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .table th,
      .table td {
        padding: 10px 8px;
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .header h1 {
        font-size: 20px;
      }

      .summary-value {
        font-size: 24px;
      }

      .btn {
        padding: 10px 16px;
        font-size: 12px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-search-plus"></i>Relatório Onde é Usado</h1>
      <button class="btn btn-secondary no-print" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-left"></i>Voltar
      </button>
    </div>

    <div class="main-content">
      <div class="search-container">
        <div class="search-group">
          <label class="search-label">
            <i class="fas fa-search"></i>
            Digite o código ou descrição do produto
          </label>
          <input
            type="text"
            id="searchInput"
            class="search-input"
            placeholder="Ex: 001-001 ou Parafuso M6..."
            autocomplete="off"
          >
          <div id="searchResults" class="search-results" style="display: none;"></div>
        </div>
      </div>

      <div id="reportContent"></div>
    </div>
  </div>

  <div id="loadingOverlay" class="loading-overlay">
    <div class="loading-spinner"></div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, query, where, getDocs, getDoc, doc, limit } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let selectedProductId = null;
    const loadingOverlay = document.getElementById('loadingOverlay');
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout = null;

    // Verificar autenticação
    const userSession = localStorage.getItem('currentUser');
    if (!userSession) {
      window.location.href = 'login.html';
    }

    // Função para mostrar/ocultar loading
    function showLoading(show = true) {
      loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    // Busca otimizada de produtos - CORRIGIDA
    async function searchProducts(searchText) {
      if (!searchText || searchText.length < 2) {
        searchResults.style.display = 'none';
        return;
      }

      try {
        showLoading(true);
        const searchLower = searchText.toLowerCase();
        const produtosRef = collection(db, "produtos");

        // Buscar TODOS os produtos (não apenas MP e SP)
        const snapshot = await getDocs(produtosRef);
        const produtos = [];

        // Filtrar no cliente para busca por código ou descrição
        snapshot.forEach(doc => {
          const data = doc.data();
          if (data.codigo?.toLowerCase().includes(searchLower) ||
              data.descricao?.toLowerCase().includes(searchLower)) {
            produtos.push({ id: doc.id, ...data });
          }
        });

        // Ordenar por relevância (código primeiro, depois descrição)
        produtos.sort((a, b) => {
          const aCodigoMatch = a.codigo?.toLowerCase().includes(searchLower);
          const bCodigoMatch = b.codigo?.toLowerCase().includes(searchLower);

          if (aCodigoMatch && !bCodigoMatch) return -1;
          if (!aCodigoMatch && bCodigoMatch) return 1;

          return a.codigo?.localeCompare(b.codigo) || 0;
        });

        // Limitar a 15 resultados após a filtragem
        displaySearchResults(produtos.slice(0, 15));
      } catch (error) {
        console.error("Erro na busca:", error);
        alert("Erro ao buscar produtos. Por favor, tente novamente.");
      } finally {
        showLoading(false);
      }
    }

    // Exibir resultados da busca
    function displaySearchResults(produtos) {
      searchResults.innerHTML = '';

      if (produtos.length > 0) {
        produtos.forEach(produto => {
          const div = document.createElement('div');
          div.className = 'search-result-item';
          div.textContent = `${produto.codigo} - ${produto.descricao} (${produto.tipo})`;
          div.onclick = () => selectProduct(produto);
          searchResults.appendChild(div);
        });
        searchResults.style.display = 'block';
      } else {
        searchResults.style.display = 'none';
      }
    }

    // Selecionar produto
    function selectProduct(produto) {
      selectedProductId = produto.id;
      searchInput.value = `${produto.codigo} - ${produto.descricao}`;
      searchResults.style.display = 'none';
      generateReport();
    }

    // Gerar relatório otimizado - CORRIGIDO
    async function generateReport() {
      if (!selectedProductId) {
        document.getElementById('reportContent').innerHTML =
          '<div class="no-results"><i class="fas fa-info-circle"></i> Selecione um produto para ver onde é usado.</div>';
        return;
      }

      showLoading();
      try {
        // Buscar o produto selecionado primeiro
        const produtoDoc = await getDoc(doc(db, "produtos", selectedProductId));

        if (!produtoDoc.exists()) {
          throw new Error("Produto não encontrado");
        }

        const produto = { id: produtoDoc.id, ...produtoDoc.data() };

        // Buscar TODAS as estruturas
        const estruturasRef = collection(db, "estruturas");
        const estruturasSnap = await getDocs(estruturasRef);

        console.log(`Buscando uso do produto ${produto.codigo} em ${estruturasSnap.docs.length} estruturas...`);

        // Filtrar estruturas que contêm o componente
        const estruturasComProduto = [];

        estruturasSnap.docs.forEach(estruturaDoc => {
          const estruturaData = estruturaDoc.data();

          // Verificar se o produto está nos componentes
          if (estruturaData.componentes && Array.isArray(estruturaData.componentes)) {
            const componenteEncontrado = estruturaData.componentes.find(comp =>
              comp.componentId === selectedProductId || comp.produtoId === selectedProductId
            );

            if (componenteEncontrado) {
              estruturasComProduto.push({
                id: estruturaDoc.id,
                ...estruturaData,
                componenteInfo: componenteEncontrado
              });
            }
          }
        });

        console.log(`Produto encontrado em ${estruturasComProduto.length} estruturas`);

        // Renderizar relatório
        await renderReport(produto, estruturasComProduto);
      } catch (error) {
        console.error("Erro ao gerar relatório:", error);
        document.getElementById('reportContent').innerHTML =
          '<div class="error-message"><i class="fas fa-exclamation-triangle"></i> Erro ao gerar relatório. Tente novamente.</div>';
      } finally {
        showLoading(false);
      }
    }

    // Renderizar relatório - CORRIGIDO
    async function renderReport(produto, estruturas) {
      const reportContent = document.getElementById('reportContent');

      let html = `
        <div class="summary-box">
          <h3><i class="fas fa-info-circle"></i>Resumo do Produto</h3>
          <div class="usage-info">
            <strong>Código:</strong> ${produto.codigo || 'N/A'}<br>
            <strong>Descrição:</strong> ${produto.descricao || 'N/A'}<br>
            <strong>Tipo:</strong> ${produto.tipo || 'N/A'}<br>
            <strong>Unidade:</strong> ${produto.unidade || 'N/A'}<br>
            <strong>Família:</strong> ${produto.familia || 'N/A'}
          </div>
          <div class="summary-info">
            <div class="summary-item">
              <div class="summary-value">${estruturas.length}</div>
              <div class="summary-label">Total de Utilizações</div>
            </div>
          </div>
        </div>
      `;

      if (estruturas.length === 0) {
        html += '<div class="no-results"><i class="fas fa-info-circle"></i> Este produto não é utilizado em nenhuma estrutura cadastrada no sistema.</div>';
      } else {
        html += `
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th><i class="fas fa-barcode"></i> Código</th>
                  <th><i class="fas fa-tag"></i> Descrição</th>
                  <th><i class="fas fa-layer-group"></i> Tipo</th>
                  <th><i class="fas fa-calculator"></i> Quantidade</th>
                  <th><i class="fas fa-ruler"></i> Unidade</th>
                </tr>
              </thead>
              <tbody>
        `;

        // Buscar produtos pais de forma mais eficiente
        const produtosIds = [...new Set(estruturas.map(e => e.produtoPaiId).filter(id => id))];

        if (produtosIds.length > 0) {
          // Buscar produtos em lotes se necessário (Firestore tem limite de 10 itens no 'in')
          const produtosMap = {};

          for (let i = 0; i < produtosIds.length; i += 10) {
            const batch = produtosIds.slice(i, i + 10);
            const produtosSnap = await getDocs(
              query(collection(db, "produtos"), where("__name__", "in", batch))
            );

            produtosSnap.forEach(doc => {
              produtosMap[doc.id] = { id: doc.id, ...doc.data() };
            });
          }

          estruturas.forEach(estrutura => {
            const produtoPai = produtosMap[estrutura.produtoPaiId];
            const componente = estrutura.componenteInfo;

            if (produtoPai && componente) {
              html += `
                <tr>
                  <td><strong>${produtoPai.codigo || 'N/A'}</strong></td>
                  <td>${produtoPai.descricao || 'N/A'}</td>
                  <td><span class="badge">${produtoPai.tipo || 'N/A'}</span></td>
                  <td style="text-align: right; font-weight: 600;">${(componente.quantidade || 0).toFixed(3)}</td>
                  <td>${componente.unidade || produto.unidade || 'UN'}</td>
                </tr>
              `;
            }
          });
        }

        html += `
              </tbody>
            </table>
          </div>
        `;
      }

      // Adicionar CSS para badge
      html += `
        <style>
          .badge {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        </style>
      `;

      reportContent.innerHTML = html;
    }

    // Event listeners
    searchInput.addEventListener('input', (e) => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => searchProducts(e.target.value), 300);
    });

    document.addEventListener('click', (e) => {
      if (!searchResults.contains(e.target) && e.target !== searchInput) {
        searchResults.style.display = 'none';
      }
    });
  </script>
</body>
</html>