// ===================================================================
// SERVIÇO DE EMPENHOS - WIZHAR ERP
// ===================================================================
// Gerencia o ciclo completo de empenhos de materiais:
// 1. Transferência Reserva → Empenho (quando OP inicia)
// 2. Baixa automática no apontamento
// 3. Liberação de empenhos restantes
// ===================================================================

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDocs, 
    getDoc, 
    updateDoc, 
    addDoc,
    query, 
    where, 
    runTransaction,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class EmpenhoService {
    
    /**
     * 🔄 TRANSFERIR RESERVAS PARA EMPENHOS
     * Chamado quando OP muda status para "Em Produção"
     */
    static async transferirReservasParaEmpenhos(ordemProducaoId) {
        console.log(`🔄 Transferindo reservas para empenhos - OP: ${ordemProducaoId}`);
        
        return runTransaction(db, async (transaction) => {
            // 1. Buscar OP
            const opRef = doc(db, "ordensProducao", ordemProducaoId);
            const opDoc = await transaction.get(opRef);
            
            if (!opDoc.exists()) {
                throw new Error('Ordem de produção não encontrada');
            }
            
            const op = opDoc.data();
            const materiaisNecessarios = op.materiaisNecessarios || [];
            
            let transferencias = 0;
            let erros = [];
            
            // 2. Para cada material com reserva
            for (const material of materiaisNecessarios) {
                if (!material.quantidadeReservada || material.quantidadeReservada <= 0) {
                    continue;
                }
                
                try {
                    // 3. Buscar estoque
                    const estoqueQuery = query(
                        collection(db, "estoques"),
                        where("produtoId", "==", material.produtoId),
                        where("armazemId", "==", op.armazemProducaoId)
                    );
                    
                    const estoqueSnapshot = await getDocs(estoqueQuery);
                    if (estoqueSnapshot.empty) {
                        erros.push(`Estoque não encontrado para produto ${material.produtoId}`);
                        continue;
                    }
                    
                    const estoqueDoc = estoqueSnapshot.docs[0];
                    const estoque = estoqueDoc.data();
                    
                    // 4. Transferir Reserva → Empenho
                    const quantidadeTransferir = material.quantidadeReservada;
                    const novoSaldoReservado = Math.max(0, (estoque.saldoReservado || 0) - quantidadeTransferir);
                    const novoSaldoEmpenhado = (estoque.saldoEmpenhado || 0) + quantidadeTransferir;
                    
                    // 5. Atualizar estoque
                    transaction.update(doc(db, "estoques", estoqueDoc.id), {
                        saldoReservado: novoSaldoReservado,
                        saldoEmpenhado: novoSaldoEmpenhado,
                        ultimaMovimentacao: Timestamp.now()
                    });
                    
                    // 6. Registrar empenho
                    const empenhoRef = doc(collection(db, "empenhos"));
                    transaction.set(empenhoRef, {
                        ordemProducaoId,
                        produtoId: material.produtoId,
                        armazemId: op.armazemProducaoId,
                        quantidadeEmpenhada: quantidadeTransferir,
                        quantidadeConsumida: 0,
                        status: 'ATIVO',
                        dataEmpenho: Timestamp.now(),
                        origemReserva: true
                    });
                    
                    transferencias++;
                    
                } catch (error) {
                    erros.push(`Erro no material ${material.produtoId}: ${error.message}`);
                }
            }
            
            // 7. Atualizar status da OP
            transaction.update(opRef, {
                status: 'Em Produção',
                dataInicioProducao: Timestamp.now(),
                empenhosAtivos: transferencias
            });
            
            return {
                transferencias,
                erros,
                ordemProducaoId
            };
        });
    }
    
    /**
     * ⚡ CONSUMIR MATERIAL EMPENHADO
     * Chamado durante apontamento de produção
     */
    static async consumirMaterialEmpenhado(ordemProducaoId, consumos) {
        console.log(`⚡ Consumindo materiais empenhados - OP: ${ordemProducaoId}`);
        
        return runTransaction(db, async (transaction) => {
            let consumosRealizados = 0;
            let erros = [];
            
            for (const consumo of consumos) {
                try {
                    // 1. Buscar empenho ativo
                    const empenhoQuery = query(
                        collection(db, "empenhos"),
                        where("ordemProducaoId", "==", ordemProducaoId),
                        where("produtoId", "==", consumo.produtoId),
                        where("status", "==", "ATIVO")
                    );
                    
                    const empenhoSnapshot = await getDocs(empenhoQuery);
                    if (empenhoSnapshot.empty) {
                        erros.push(`Empenho não encontrado para produto ${consumo.produtoId}`);
                        continue;
                    }
                    
                    const empenhoDoc = empenhoSnapshot.docs[0];
                    const empenho = empenhoDoc.data();
                    
                    // 2. Validar quantidade
                    const quantidadeDisponivel = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                    const quantidadeConsumir = Math.min(consumo.quantidade, quantidadeDisponivel);
                    
                    if (quantidadeConsumir <= 0) {
                        erros.push(`Sem quantidade empenhada disponível para ${consumo.produtoId}`);
                        continue;
                    }
                    
                    // 3. Atualizar empenho
                    const novaQuantidadeConsumida = empenho.quantidadeConsumida + quantidadeConsumir;
                    const novoStatus = novaQuantidadeConsumida >= empenho.quantidadeEmpenhada ? 'CONSUMIDO' : 'ATIVO';
                    
                    transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                        quantidadeConsumida: novaQuantidadeConsumida,
                        status: novoStatus,
                        ultimoConsumo: Timestamp.now()
                    });
                    
                    // 4. Atualizar estoque (reduzir saldo empenhado e saldo total)
                    const estoqueQuery = query(
                        collection(db, "estoques"),
                        where("produtoId", "==", consumo.produtoId),
                        where("armazemId", "==", empenho.armazemId)
                    );
                    
                    const estoqueSnapshot = await getDocs(estoqueQuery);
                    if (!estoqueSnapshot.empty) {
                        const estoqueDoc = estoqueSnapshot.docs[0];
                        const estoque = estoqueDoc.data();
                        
                        transaction.update(doc(db, "estoques", estoqueDoc.id), {
                            saldo: estoque.saldo - quantidadeConsumir,
                            saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeConsumir),
                            ultimaMovimentacao: Timestamp.now()
                        });
                    }
                    
                    // 5. Registrar movimentação de consumo
                    const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                    transaction.set(movimentacaoRef, {
                        produtoId: consumo.produtoId,
                        armazemId: empenho.armazemId,
                        tipo: 'SAIDA',
                        quantidade: quantidadeConsumir,
                        tipoDocumento: 'CONSUMO_PRODUCAO',
                        numeroDocumento: ordemProducaoId,
                        observacoes: `Consumo OP ${ordemProducaoId} - Empenho`,
                        dataHora: Timestamp.now(),
                        empenhoId: empenhoDoc.id
                    });
                    
                    consumosRealizados++;
                    
                } catch (error) {
                    erros.push(`Erro no consumo ${consumo.produtoId}: ${error.message}`);
                }
            }
            
            return {
                consumosRealizados,
                erros
            };
        });
    }
    
    /**
     * 🔓 LIBERAR EMPENHOS RESTANTES
     * Chamado quando OP é finalizada ou cancelada
     */
    static async liberarEmpenhosRestantes(ordemProducaoId, motivo = 'OP_FINALIZADA') {
        console.log(`🔓 Liberando empenhos restantes - OP: ${ordemProducaoId}`);
        
        return runTransaction(db, async (transaction) => {
            // 1. Buscar empenhos ativos
            const empenhosQuery = query(
                collection(db, "empenhos"),
                where("ordemProducaoId", "==", ordemProducaoId),
                where("status", "==", "ATIVO")
            );
            
            const empenhosSnapshot = await getDocs(empenhosQuery);
            let liberacoes = 0;
            
            for (const empenhoDoc of empenhosSnapshot.docs) {
                const empenho = empenhoDoc.data();
                const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                
                if (quantidadeRestante > 0) {
                    // 2. Liberar empenho
                    transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                        status: 'LIBERADO',
                        quantidadeLiberada: quantidadeRestante,
                        dataLiberacao: Timestamp.now(),
                        motivoLiberacao: motivo
                    });
                    
                    // 3. Atualizar estoque (reduzir saldo empenhado)
                    const estoqueQuery = query(
                        collection(db, "estoques"),
                        where("produtoId", "==", empenho.produtoId),
                        where("armazemId", "==", empenho.armazemId)
                    );
                    
                    const estoqueSnapshot = await getDocs(estoqueQuery);
                    if (!estoqueSnapshot.empty) {
                        const estoqueDoc = estoqueSnapshot.docs[0];
                        const estoque = estoqueDoc.data();
                        
                        transaction.update(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeRestante),
                            ultimaMovimentacao: Timestamp.now()
                        });
                    }
                    
                    liberacoes++;
                }
            }
            
            return {
                liberacoes,
                ordemProducaoId
            };
        });
    }
    
    /**
     * 📊 CONSULTAR STATUS DOS EMPENHOS
     */
    static async consultarEmpenhosOP(ordemProducaoId) {
        const empenhosQuery = query(
            collection(db, "empenhos"),
            where("ordemProducaoId", "==", ordemProducaoId)
        );
        
        const empenhosSnapshot = await getDocs(empenhosQuery);
        return empenhosSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        }));
    }
    
    /**
     * 🔧 INICIALIZAR CAMPO saldoEmpenhado NOS ESTOQUES EXISTENTES
     */
    static async inicializarCampoEmpenho() {
        console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');
        
        const estoquesSnapshot = await getDocs(collection(db, "estoques"));
        let atualizados = 0;
        
        for (const estoqueDoc of estoquesSnapshot.docs) {
            const estoque = estoqueDoc.data();
            
            if (estoque.saldoEmpenhado === undefined) {
                await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                    saldoEmpenhado: 0
                });
                atualizados++;
            }
        }
        
        console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
        return atualizados;
    }
}

// Exportar para uso global
window.EmpenhoService = EmpenhoService;
