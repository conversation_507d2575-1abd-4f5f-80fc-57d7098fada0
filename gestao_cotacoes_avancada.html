
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão Avançada de Cotações - Sistema ERP</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      margin: 0;
    }

    .analysis-grid {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 20px;
      padding: 20px;
    }

    .main-panel {
      background: white;
    }

    .side-panel {
      background: var(--secondary-color);
      padding: 20px;
      border-radius: 8px;
    }

    .quotation-card {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 20px;
      overflow: hidden;
    }

    .quotation-header {
      background: var(--primary-color);
      color: white;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .quotation-content {
      padding: 20px;
    }

    .supplier-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .supplier-card {
      background: white;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      padding: 15px;
      transition: all 0.3s;
    }

    .supplier-card:hover {
      border-color: var(--primary-color);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .supplier-card.winner {
      border-color: var(--success-color);
      background: #f8fff9;
    }

    .supplier-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .supplier-name {
      font-weight: 600;
      color: var(--primary-color);
    }

    .response-status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }

    .status-responded {
      background: var(--success-color);
      color: white;
    }

    .status-pending {
      background: var(--warning-color);
      color: white;
    }

    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .comparison-table th,
    .comparison-table td {
      padding: 8px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .comparison-table th {
      background: var(--secondary-color);
      font-weight: 600;
    }

    .best-price {
      background: #e8f5e9;
      font-weight: bold;
      color: var(--success-color);
    }

    .criteria-panel {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .criteria-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .weight-slider {
      width: 100px;
    }

    .score-display {
      text-align: center;
      padding: 20px;
      background: var(--primary-color);
      color: white;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .score-number {
      font-size: 36px;
      font-weight: bold;
    }

    .score-label {
      font-size: 14px;
      opacity: 0.9;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-warning {
      background: var(--warning-color);
      color: white;
    }

    .btn-danger {
      background: var(--danger-color);
      color: white;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    .tabs {
      display: flex;
      background: var(--secondary-color);
      border-radius: 8px 8px 0 0;
    }

    .tab {
      padding: 12px 20px;
      cursor: pointer;
      background: transparent;
      border: none;
      color: var(--text-color);
      font-weight: 500;
    }

    .tab.active {
      background: var(--primary-color);
      color: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
    }

    .tab-content.active {
      display: block;
    }

    .negotiation-history {
      max-height: 300px;
      overflow-y: auto;
    }

    .negotiation-item {
      background: white;
      border-left: 3px solid var(--primary-color);
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 0 4px 4px 0;
    }

    .negotiation-date {
      font-size: 12px;
      color: var(--text-color);
      opacity: 0.7;
    }

    .alert {
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .alert-info {
      background: #d1ecf1;
      border-left: 4px solid #17a2b8;
      color: #0c5460;
    }

    .alert-warning {
      background: #fff3cd;
      border-left: 4px solid #ffc107;
      color: #856404;
    }

    .metric-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .metric-card {
      background: white;
      padding: 15px;
      border-radius: 8px;
      text-align: center;
      border-left: 4px solid var(--primary-color);
    }

    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .metric-label {
      font-size: 12px;
      color: var(--text-color);
      margin-top: 5px;
    }

    .decision-matrix {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }

    .matrix-table {
      width: 100%;
      border-collapse: collapse;
    }

    .matrix-table th,
    .matrix-table td {
      padding: 10px;
      border: 1px solid var(--border-color);
      text-align: center;
    }

    .matrix-table th {
      background: var(--secondary-color);
      font-weight: 600;
    }

    .winner-cell {
      background: var(--success-color);
      color: white;
      font-weight: bold;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
    }

    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 20px;
      width: 90%;
      max-width: 1000px;
      border-radius: 8px;
      position: relative;
      max-height: 90vh;
      overflow-y: auto;
    }

    .close {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 24px;
      cursor: pointer;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }

    .timeline {
      position: relative;
      padding-left: 30px;
    }

    .timeline::before {
      content: '';
      position: absolute;
      left: 15px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--border-color);
    }

    .timeline-item {
      position: relative;
      margin-bottom: 20px;
    }

    .timeline-item::before {
      content: '';
      position: absolute;
      left: -19px;
      top: 5px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: var(--primary-color);
    }

    .timeline-content {
      background: white;
      padding: 15px;
      border-radius: 8px;
      border-left: 3px solid var(--primary-color);
    }

    .timeline-date {
      font-size: 12px;
      color: var(--text-color);
      opacity: 0.7;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Gestão Avançada de Cotações</h1>
      <div>
        <button class="btn btn-primary" onclick="openAnalysisMode()">Modo Análise</button>
        <button class="btn btn-warning" onclick="exportReport()">Exportar Relatório</button>
        <button class="btn btn-secondary" onclick="window.location.href='cotacoes.html'">Voltar</button>
      </div>
    </div>

    <div class="analysis-grid">
      <div class="main-panel">
        <div class="tabs">
          <button class="tab active" onclick="showTab('comparison')">Comparação</button>
          <button class="tab" onclick="showTab('negotiation')">Negociação</button>
          <button class="tab" onclick="showTab('decision')">Decisão</button>
          <button class="tab" onclick="showTab('timeline')">Timeline</button>
        </div>

        <!-- Aba Comparação -->
        <div id="comparison" class="tab-content active">
          <div class="alert alert-info">
            <strong>Análise Comparativa:</strong> Cotação SC2025010001 - Equipamentos de TI
          </div>

          <div class="metric-grid">
            <div class="metric-card">
              <div class="metric-value">3</div>
              <div class="metric-label">Fornecedores</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">2</div>
              <div class="metric-label">Responderam</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">R$ 2.500</div>
              <div class="metric-label">Menor Preço</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">15%</div>
              <div class="metric-label">Economia</div>
            </div>
          </div>

          <div class="supplier-grid">
            <div class="supplier-card winner">
              <div class="supplier-header">
                <div class="supplier-name">TechSupply Ltda</div>
                <div class="response-status status-responded">Respondida</div>
              </div>
              <div class="comparison-table">
                <table style="width: 100%;">
                  <tr>
                    <td><strong>Preço Total:</strong></td>
                    <td class="best-price">R$ 2.500,00</td>
                  </tr>
                  <tr>
                    <td><strong>Prazo:</strong></td>
                    <td>7 dias</td>
                  </tr>
                  <tr>
                    <td><strong>Garantia:</strong></td>
                    <td>12 meses</td>
                  </tr>
                  <tr>
                    <td><strong>Pagamento:</strong></td>
                    <td>30 dias</td>
                  </tr>
                  <tr>
                    <td><strong>Score:</strong></td>
                    <td><strong>9.2</strong></td>
                  </tr>
                </table>
              </div>
              <div style="margin-top: 15px;">
                <button class="btn btn-success btn-sm">Selecionar</button>
                <button class="btn btn-warning btn-sm">Negociar</button>
                <button class="btn btn-primary btn-sm">Detalhes</button>
              </div>
            </div>

            <div class="supplier-card">
              <div class="supplier-header">
                <div class="supplier-name">InfoTech Solutions</div>
                <div class="response-status status-responded">Respondida</div>
              </div>
              <div class="comparison-table">
                <table style="width: 100%;">
                  <tr>
                    <td><strong>Preço Total:</strong></td>
                    <td>R$ 2.800,00</td>
                  </tr>
                  <tr>
                    <td><strong>Prazo:</strong></td>
                    <td class="best-price">5 dias</td>
                  </tr>
                  <tr>
                    <td><strong>Garantia:</strong></td>
                    <td class="best-price">24 meses</td>
                  </tr>
                  <tr>
                    <td><strong>Pagamento:</strong></td>
                    <td class="best-price">45 dias</td>
                  </tr>
                  <tr>
                    <td><strong>Score:</strong></td>
                    <td><strong>8.7</strong></td>
                  </tr>
                </table>
              </div>
              <div style="margin-top: 15px;">
                <button class="btn btn-warning btn-sm">Negociar</button>
                <button class="btn btn-primary btn-sm">Detalhes</button>
              </div>
            </div>

            <div class="supplier-card">
              <div class="supplier-header">
                <div class="supplier-name">Digital Store</div>
                <div class="response-status status-pending">Aguardando</div>
              </div>
              <div style="text-align: center; padding: 20px; color: var(--text-color); opacity: 0.7;">
                Aguardando resposta do fornecedor<br>
                <small>Prazo: 16/01/2025</small>
              </div>
              <div style="margin-top: 15px;">
                <button class="btn btn-warning btn-sm">Reenviar</button>
                <button class="btn btn-danger btn-sm">Excluir</button>
              </div>
            </div>
          </div>

          <div class="decision-matrix">
            <h3>Matriz de Decisão</h3>
            <table class="matrix-table">
              <thead>
                <tr>
                  <th>Critério</th>
                  <th>Peso</th>
                  <th>TechSupply</th>
                  <th>InfoTech</th>
                  <th>Vencedor</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Preço</td>
                  <td>40%</td>
                  <td class="winner-cell">10</td>
                  <td>8</td>
                  <td>TechSupply</td>
                </tr>
                <tr>
                  <td>Prazo</td>
                  <td>25%</td>
                  <td>8</td>
                  <td class="winner-cell">10</td>
                  <td>InfoTech</td>
                </tr>
                <tr>
                  <td>Qualidade</td>
                  <td>20%</td>
                  <td>9</td>
                  <td class="winner-cell">9</td>
                  <td>Empate</td>
                </tr>
                <tr>
                  <td>Garantia</td>
                  <td>15%</td>
                  <td>8</td>
                  <td class="winner-cell">10</td>
                  <td>InfoTech</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Aba Negociação -->
        <div id="negotiation" class="tab-content">
          <div class="alert alert-warning">
            <strong>Negociação Ativa:</strong> Aguardando contraprosta do TechSupply Ltda
          </div>

          <div class="negotiation-history">
            <div class="negotiation-item">
              <div class="negotiation-date">15/01/2025 14:30</div>
              <strong>Proposta Inicial - TechSupply</strong><br>
              Valor: R$ 2.500,00 | Prazo: 7 dias | Pagamento: 30 dias
            </div>
            <div class="negotiation-item">
              <div class="negotiation-date">15/01/2025 16:15</div>
              <strong>Solicitação de Melhoria</strong><br>
              Solicitado: Redução de 5% no preço e prazo de pagamento para 45 dias
            </div>
            <div class="negotiation-item">
              <div class="negotiation-date">16/01/2025 09:00</div>
              <strong>Contraprosta - TechSupply</strong><br>
              Valor: R$ 2.400,00 | Prazo: 8 dias | Pagamento: 35 dias
            </div>
          </div>

          <div style="margin-top: 20px;">
            <button class="btn btn-success">Aceitar Contraprosta</button>
            <button class="btn btn-warning">Nova Solicitação</button>
            <button class="btn btn-danger">Encerrar Negociação</button>
          </div>
        </div>

        <!-- Aba Decisão -->
        <div id="decision" class="tab-content">
          <div class="quotation-card">
            <div class="quotation-header">
              <span>Resumo da Decisão</span>
              <span>Cotação: SC2025010001</span>
            </div>
            <div class="quotation-content">
              <div class="form-grid">
                <div class="form-group">
                  <label>Fornecedor Vencedor:</label>
                  <select class="form-control" id="fornecedor-vencedor">
                    <option value="techsupply">TechSupply Ltda</option>
                    <option value="infotech">InfoTech Solutions</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Valor Final:</label>
                  <input type="text" class="form-control" value="R$ 2.400,00" readonly>
                </div>
                <div class="form-group">
                  <label>Economia Obtida:</label>
                  <input type="text" class="form-control" value="R$ 400,00 (14.3%)" readonly>
                </div>
                <div class="form-group">
                  <label>Data de Entrega:</label>
                  <input type="date" class="form-control" value="2025-01-24">
                </div>
              </div>
              
              <div class="form-group">
                <label>Justificativa da Decisão:</label>
                <textarea class="form-control" rows="4" placeholder="Descreva os motivos da escolha do fornecedor..."></textarea>
              </div>

              <div style="margin-top: 20px;">
                <button class="btn btn-success">Finalizar Cotação</button>
                <button class="btn btn-primary" onclick="generatePurchaseOrder()">Gerar Pedido de Compra</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Aba Timeline -->
        <div id="timeline" class="tab-content">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-content">
                <div class="timeline-date">13/01/2025 10:00</div>
                <strong>Solicitação Criada</strong><br>
                Solicitação SC2025010001 criada pelo departamento de TI
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-content">
                <div class="timeline-date">13/01/2025 14:30</div>
                <strong>Cotação Iniciada</strong><br>
                Enviado para 3 fornecedores: TechSupply, InfoTech, Digital Store
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-content">
                <div class="timeline-date">14/01/2025 16:20</div>
                <strong>Primeira Resposta</strong><br>
                TechSupply enviou proposta: R$ 2.500,00
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-content">
                <div class="timeline-date">15/01/2025 11:45</div>
                <strong>Segunda Resposta</strong><br>
                InfoTech enviou proposta: R$ 2.800,00
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-content">
                <div class="timeline-date">15/01/2025 16:15</div>
                <strong>Negociação Iniciada</strong><br>
                Solicitada melhoria de preço para TechSupply
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="side-panel">
        <div class="criteria-panel">
          <h3>Critérios de Avaliação</h3>
          <div class="criteria-item">
            <span>Preço</span>
            <input type="range" class="weight-slider" min="0" max="50" value="40">
            <span>40%</span>
          </div>
          <div class="criteria-item">
            <span>Prazo</span>
            <input type="range" class="weight-slider" min="0" max="50" value="25">
            <span>25%</span>
          </div>
          <div class="criteria-item">
            <span>Qualidade</span>
            <input type="range" class="weight-slider" min="0" max="50" value="20">
            <span>20%</span>
          </div>
          <div class="criteria-item">
            <span>Garantia</span>
            <input type="range" class="weight-slider" min="0" max="50" value="15">
            <span>15%</span>
          </div>
          <button class="btn btn-primary" style="width: 100%; margin-top: 15px;">Recalcular</button>
        </div>

        <div class="score-display">
          <div class="score-number">9.2</div>
          <div class="score-label">Score TechSupply</div>
        </div>

        <div class="score-display" style="background: var(--warning-color);">
          <div class="score-number">8.7</div>
          <div class="score-label">Score InfoTech</div>
        </div>

        <div style="background: white; padding: 15px; border-radius: 8px; margin-top: 20px;">
          <h4>Ações Rápidas</h4>
          <button class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">Enviar Mensagem</button>
          <button class="btn btn-warning" style="width: 100%; margin-bottom: 10px;">Solicitar Desconto</button>
          <button class="btn btn-success" style="width: 100%;">Aprovar Melhor</button>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      doc, 
      updateDoc, 
      addDoc,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let currentUser = null;
    let cotacaoAtual = null;

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }
      
      currentUser = JSON.parse(userSession);
      await loadCotacaoData();
    };

    async function loadCotacaoData() {
      // Carregar dados da cotação selecionada
      const urlParams = new URLSearchParams(window.location.search);
      const cotacaoId = urlParams.get('id');
      
      if (cotacaoId) {
        // Implementar carregamento específico da cotação
      } else {
        // Usar dados de exemplo se não houver ID
        cotacaoAtual = {
          id: 'exemplo',
          numero: 'SC2025010001',
          fornecedores: ['techsupply', 'infotech', 'digitalstore'],
          respostas: {
            'techsupply': { preco: 2500, prazo: 7, garantia: 12 },
            'infotech': { preco: 2800, prazo: 5, garantia: 24 }
          }
        };
      }
    }

    window.showTab = function(tabName) {
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
      document.getElementById(tabName).classList.add('active');
    };

    window.openAnalysisMode = function() {
      alert('Modo de análise avançada ativado!');
    };

    window.exportReport = function() {
      // Implementar exportação de relatório
      const reportData = {
        cotacao: cotacaoAtual?.numero || 'SC2025010001',
        fornecedores: 3,
        economia: 'R$ 400,00',
        vencedor: 'TechSupply Ltda'
      };
      
      console.log('Exportando relatório:', reportData);
      alert('Relatório exportado com sucesso!');
    };

    window.generatePurchaseOrder = async function() {
      if (confirm('Gerar pedido de compra com o fornecedor selecionado?')) {
        try {
          const pedido = {
            cotacaoId: cotacaoAtual?.id,
            fornecedorId: 'techsupply',
            valorTotal: 2400,
            status: 'PENDENTE',
            dataCriacao: Timestamp.now(),
            criadoPor: currentUser.nome
          };

          // await addDoc(collection(db, "pedidosCompra"), pedido);
          alert('Pedido de compra gerado com sucesso!');
          window.location.href = 'pedidos_compra.html';
        } catch (error) {
          console.error("Erro ao gerar pedido:", error);
          alert("Erro ao gerar pedido de compra.");
        }
      }
    };

    // Event listeners para sliders de peso
    document.querySelectorAll('.weight-slider').forEach(slider => {
      slider.addEventListener('input', function() {
        const value = this.value;
        this.nextElementSibling.textContent = value + '%';
        // Implementar recálculo automático dos scores
      });
    });
  </script>
</body>
</html>
