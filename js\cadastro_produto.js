document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    const searchInput = document.getElementById('searchInput');
    const productsTableBody = document.getElementById('productsTableBody');
    const productForm = document.getElementById('productForm');
    const btnNovo = document.getElementById('btnNovo');
    const btnCancelar = document.getElementById('btnCancelar');
    const btnFiltrar = document.getElementById('btnFiltrar');
    const btnExportar = document.getElementById('btnExportar');
    const btnAnterior = document.getElementById('btnAnterior');
    const btnProximo = document.getElementById('btnProximo');
    const paginationInfo = document.getElementById('paginationInfo');

    // Estado da aplicação
    let currentPage = 1;
    let itemsPerPage = 10;
    let totalItems = 0;
    let products = [];

    // Funções de utilidade
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
            <span>${message}</span>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    function formatDate(date) {
        return new Date(date).toLocaleDateString('pt-BR');
    }

    // Sistema de abas
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const target = tab.dataset.tab;
            
            // Atualiza as abas
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Atualiza os conteúdos
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === target) {
                    content.classList.add('active');
                }
            });
        });
    });

    // Funções de manipulação de produtos
    async function loadProducts(page = 1) {
        try {
            // Simulação de carregamento de dados
            // Em produção, isso seria uma chamada à API
            const response = await fetch(`/api/produtos?page=${page}&limit=${itemsPerPage}`);
            const data = await response.json();
            
            products = data.items;
            totalItems = data.total;
            
            renderProducts();
            updatePagination();
        } catch (error) {
            console.error('Erro ao carregar produtos:', error);
            showNotification('Erro ao carregar produtos', 'error');
        }
    }

    function renderProducts() {
        productsTableBody.innerHTML = products.map(product => `
            <tr>
                <td>${product.codigo}</td>
                <td>${product.descricao}</td>
                <td>${product.unidade}</td>
                <td>${product.tipo}</td>
                <td>
                    <span class="status-badge status-${product.ativo ? 'ativo' : 'inativo'}">
                        ${product.ativo ? 'Ativo' : 'Inativo'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-secondary btn-sm" onclick="editProduct(${product.id})">
                        ✏️
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                        🗑️
                    </button>
                </td>
            </tr>
        `).join('');
    }

    function updatePagination() {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        paginationInfo.textContent = `Página ${currentPage} de ${totalPages}`;
        
        btnAnterior.disabled = currentPage === 1;
        btnProximo.disabled = currentPage === totalPages;
    }

    // Event Listeners
    btnNovo.addEventListener('click', () => {
        tabs[1].click(); // Muda para a aba de cadastro
        productForm.reset();
    });

    btnCancelar.addEventListener('click', () => {
        if (confirm('Deseja cancelar o cadastro?')) {
            tabs[0].click(); // Volta para a aba de lista
            productForm.reset();
        }
    });

    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const formData = new FormData(productForm);
            const productData = Object.fromEntries(formData.entries());
            
            // Simulação de envio para API
            // Em produção, isso seria uma chamada à API
            const response = await fetch('/api/produtos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });
            
            if (response.ok) {
                showNotification('Produto salvo com sucesso!', 'success');
                tabs[0].click(); // Volta para a aba de lista
                loadProducts(currentPage);
            } else {
                throw new Error('Erro ao salvar produto');
            }
        } catch (error) {
            console.error('Erro ao salvar produto:', error);
            showNotification('Erro ao salvar produto', 'error');
        }
    });

    searchInput.addEventListener('input', debounce(async (e) => {
        const searchTerm = e.target.value.trim();
        if (searchTerm.length >= 3) {
            currentPage = 1;
            await loadProducts(currentPage);
        }
    }, 300));

    btnFiltrar.addEventListener('click', () => {
        // Implementar lógica de filtros avançados
        showNotification('Funcionalidade em desenvolvimento', 'info');
    });

    btnExportar.addEventListener('click', () => {
        // Implementar lógica de exportação
        showNotification('Funcionalidade em desenvolvimento', 'info');
    });

    btnAnterior.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadProducts(currentPage);
        }
    });

    btnProximo.addEventListener('click', () => {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadProducts(currentPage);
        }
    });

    // Função de debounce para otimizar buscas
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Carrega os produtos iniciais
    loadProducts();
}); 