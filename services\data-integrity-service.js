// ===================================================================
// SERVIÇO DE INTEGRIDADE DE DADOS - WiZAR ERP
// ===================================================================
// Garante a integridade dos dados no fluxo de compras
// Previne problemas de sincronização entre módulos
// ===================================================================

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc,
    getDocs,
    updateDoc, 
    addDoc,
    query,
    where,
    Timestamp,
    runTransaction,
    writeBatch
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class DataIntegrityService {
    
    // ===================================================================
    // VALIDAÇÕES DE INTEGRIDADE
    // ===================================================================
    
    /**
     * Valida a integridade de uma solicitação antes de salvar
     */
    static async validateSolicitacao(solicitacaoData) {
        const errors = [];
        
        // Validar campos obrigatórios
        if (!solicitacaoData.numero) {
            errors.push('Número da solicitação é obrigatório');
        }
        
        if (!solicitacaoData.solicitante) {
            errors.push('Solicitante é obrigatório');
        }
        
        if (!solicitacaoData.itens || solicitacaoData.itens.length === 0) {
            errors.push('Pelo menos um item é obrigatório');
        }
        
        // Validar itens
        if (solicitacaoData.itens) {
            solicitacaoData.itens.forEach((item, index) => {
                if (!item.produtoId && !item.codigo) {
                    errors.push(`Item ${index + 1}: Produto ou código é obrigatório`);
                }
                
                if (!item.quantidade || item.quantidade <= 0) {
                    errors.push(`Item ${index + 1}: Quantidade deve ser maior que zero`);
                }
            });
        }
        
        // Verificar duplicação de número
        if (solicitacaoData.numero) {
            const existingQuery = query(
                collection(db, "solicitacoesCompra"),
                where("numero", "==", solicitacaoData.numero)
            );
            const existingDocs = await getDocs(existingQuery);
            
            if (existingDocs.size > 0) {
                errors.push(`Número ${solicitacaoData.numero} já existe`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Valida a integridade de uma cotação antes de salvar
     */
    static async validateCotacao(cotacaoData) {
        const errors = [];
        
        // Validar campos obrigatórios
        if (!cotacaoData.numero) {
            errors.push('Número da cotação é obrigatório');
        }
        
        // Validar vínculo com solicitação
        if (cotacaoData.solicitacaoId) {
            const solicitacaoRef = doc(db, "solicitacoesCompra", cotacaoData.solicitacaoId);
            const solicitacaoDoc = await getDoc(solicitacaoRef);
            
            if (!solicitacaoDoc.exists()) {
                errors.push('Solicitação de origem não encontrada');
            } else {
                const solicitacao = solicitacaoDoc.data();
                if (solicitacao.status !== 'APROVADA' && solicitacao.status !== 'EM_COTACAO') {
                    errors.push('Solicitação deve estar aprovada para gerar cotação');
                }
            }
        }
        
        // Validar itens
        if (!cotacaoData.itens || cotacaoData.itens.length === 0) {
            errors.push('Pelo menos um item é obrigatório');
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Valida a integridade de um pedido antes de salvar
     */
    static async validatePedido(pedidoData) {
        const errors = [];
        
        // Validar campos obrigatórios
        if (!pedidoData.numero) {
            errors.push('Número do pedido é obrigatório');
        }
        
        if (!pedidoData.fornecedorId) {
            errors.push('Fornecedor é obrigatório');
        }
        
        // Validar vínculo com cotação
        if (pedidoData.cotacaoId) {
            const cotacaoRef = doc(db, "cotacoes", pedidoData.cotacaoId);
            const cotacaoDoc = await getDoc(cotacaoRef);
            
            if (!cotacaoDoc.exists()) {
                errors.push('Cotação de origem não encontrada');
            } else {
                const cotacao = cotacaoDoc.data();
                if (cotacao.status !== 'RESPONDIDA' && cotacao.status !== 'ANALISADA') {
                    errors.push('Cotação deve estar respondida para gerar pedido');
                }
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    // ===================================================================
    // SINCRONIZAÇÃO AUTOMÁTICA
    // ===================================================================
    
    /**
     * Cria cotação automaticamente para solicitação aprovada
     */
    static async createCotacaoFromSolicitacao(solicitacaoId) {
        try {
            return await runTransaction(db, async (transaction) => {
                // Buscar solicitação
                const solicitacaoRef = doc(db, "solicitacoesCompra", solicitacaoId);
                const solicitacaoDoc = await transaction.get(solicitacaoRef);
                
                if (!solicitacaoDoc.exists()) {
                    throw new Error('Solicitação não encontrada');
                }
                
                const solicitacao = solicitacaoDoc.data();
                
                // Verificar se já existe cotação
                const cotacoesQuery = query(
                    collection(db, "cotacoes"),
                    where("solicitacaoId", "==", solicitacaoId)
                );
                const cotacoesExistentes = await getDocs(cotacoesQuery);
                
                if (cotacoesExistentes.size > 0) {
                    throw new Error('Já existe cotação para esta solicitação');
                }
                
                // Gerar número da cotação
                const numeroCotacao = await this.generateCotacaoNumber();
                
                // Criar cotação
                const cotacaoRef = doc(collection(db, "cotacoes"));
                const cotacaoData = {
                    numero: numeroCotacao,
                    solicitacaoId: solicitacaoId,
                    itens: solicitacao.itens.map(item => ({
                        produtoId: item.produtoId,
                        codigo: item.codigo,
                        descricao: item.descricao,
                        quantidade: item.quantidadeCompra || item.quantidade,
                        unidade: item.unidadeCompra || item.unidade
                    })),
                    status: 'ABERTA',
                    dataCriacao: Timestamp.now(),
                    criadoPor: 'Sistema Automático',
                    observacoes: `Cotação criada automaticamente da solicitação ${solicitacao.numero}`
                };
                
                transaction.set(cotacaoRef, cotacaoData);
                
                // Atualizar status da solicitação
                transaction.update(solicitacaoRef, {
                    status: 'EM_COTACAO',
                    cotacaoId: cotacaoRef.id,
                    statusAtualizadoEm: Timestamp.now()
                });
                
                return {
                    success: true,
                    cotacaoId: cotacaoRef.id,
                    numeroCotacao
                };
            });
            
        } catch (error) {
            console.error('Erro ao criar cotação automática:', error);
            throw error;
        }
    }
    
    /**
     * Atualiza status automaticamente baseado no fluxo
     */
    static async updateStatusAutomatico(documentType, documentId, newStatus) {
        try {
            const batch = writeBatch(db);
            
            if (documentType === 'solicitacao') {
                const solicitacaoRef = doc(db, "solicitacoesCompra", documentId);
                
                batch.update(solicitacaoRef, {
                    status: newStatus,
                    statusAtualizadoEm: Timestamp.now(),
                    statusAtualizadoPor: 'Sistema Automático'
                });
                
                // Se aprovada, criar cotação automaticamente
                if (newStatus === 'APROVADA') {
                    await this.createCotacaoFromSolicitacao(documentId);
                }
            }
            
            await batch.commit();
            
            return { success: true };
            
        } catch (error) {
            console.error('Erro ao atualizar status:', error);
            throw error;
        }
    }
    
    // ===================================================================
    // CORREÇÃO DE INCONSISTÊNCIAS
    // ===================================================================
    
    /**
     * Corrige vínculos quebrados no fluxo de compras
     */
    static async repairBrokenLinks() {
        try {
            const results = {
                solicitacoesCorrigidas: 0,
                cotacoesCorrigidas: 0,
                pedidosCorrigidos: 0,
                errors: []
            };
            
            // Carregar todos os dados
            const [solicitacoesSnap, cotacoesSnap, pedidosSnap] = await Promise.all([
                getDocs(collection(db, "solicitacoesCompra")),
                getDocs(collection(db, "cotacoes")),
                getDocs(collection(db, "pedidosCompra"))
            ]);
            
            const solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            const cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            
            // Corrigir cotações órfãs
            for (const cotacao of cotacoes) {
                if (cotacao.solicitacaoId && !solicitacoes.find(s => s.id === cotacao.solicitacaoId)) {
                    // Tentar encontrar solicitação correspondente
                    const solicitacaoCorrespondente = solicitacoes.find(sol => {
                        return sol.numero && cotacao.numero && 
                               cotacao.numero.includes(sol.numero.replace('SC', ''));
                    });
                    
                    if (solicitacaoCorrespondente) {
                        await updateDoc(doc(db, "cotacoes", cotacao.id), {
                            solicitacaoId: solicitacaoCorrespondente.id,
                            corrigidoEm: Timestamp.now()
                        });
                        results.cotacoesCorrigidas++;
                    }
                }
            }
            
            // Corrigir pedidos órfãos
            for (const pedido of pedidos) {
                if (pedido.cotacaoId && !cotacoes.find(c => c.id === pedido.cotacaoId)) {
                    // Tentar encontrar cotação correspondente
                    const cotacaoCorrespondente = cotacoes.find(cot => {
                        return cot.numero && pedido.numero &&
                               pedido.numero.includes(cot.numero.replace('CT', ''));
                    });
                    
                    if (cotacaoCorrespondente) {
                        await updateDoc(doc(db, "pedidosCompra", pedido.id), {
                            cotacaoId: cotacaoCorrespondente.id,
                            corrigidoEm: Timestamp.now()
                        });
                        results.pedidosCorrigidos++;
                    }
                }
            }
            
            return results;
            
        } catch (error) {
            console.error('Erro ao corrigir vínculos:', error);
            throw error;
        }
    }
    
    // ===================================================================
    // UTILITÁRIOS
    // ===================================================================
    
    /**
     * Gera número sequencial para cotação (usando serviço centralizado)
     */
    static async generateCotacaoNumber() {
        try {
            // Importar dinamicamente o serviço de numeração
            const { NumberGeneratorService } = await import('../services/number-generator-service.js');
            return await NumberGeneratorService.generateCotacaoNumber();
        } catch (error) {
            console.warn('Erro ao usar serviço centralizado, usando fallback:', error);
            // Fallback para método antigo
            const cotacoesSnap = await getDocs(collection(db, "cotacoes"));
            const nextNumber = cotacoesSnap.size + 1;
            return `CT${nextNumber.toString().padStart(6, '0')}`;
        }
    }
    
    /**
     * Verifica integridade geral do sistema
     */
    static async checkSystemIntegrity() {
        try {
            const issues = [];
            
            // Verificar solicitações órfãs
            const solicitacoesQuery = query(
                collection(db, "solicitacoesCompra"),
                where("status", "==", "APROVADA")
            );
            const solicitacoesAprovadas = await getDocs(solicitacoesQuery);
            
            for (const solDoc of solicitacoesAprovadas.docs) {
                const cotacoesQuery = query(
                    collection(db, "cotacoes"),
                    where("solicitacaoId", "==", solDoc.id)
                );
                const cotacoes = await getDocs(cotacoesQuery);
                
                if (cotacoes.size === 0) {
                    issues.push({
                        type: 'SOLICITACAO_SEM_COTACAO',
                        id: solDoc.id,
                        data: solDoc.data()
                    });
                }
            }
            
            return {
                healthy: issues.length === 0,
                issues
            };
            
        } catch (error) {
            console.error('Erro ao verificar integridade:', error);
            throw error;
        }
    }
    
    /**
     * Monitora mudanças e mantém sincronização
     */
    static async monitorAndSync() {
        // Esta função seria chamada periodicamente para manter a sincronização
        console.log('🔄 Monitoramento de integridade ativo');
        
        try {
            const integrity = await this.checkSystemIntegrity();
            
            if (!integrity.healthy) {
                console.warn('⚠️ Problemas de integridade detectados:', integrity.issues.length);
                
                // Auto-correção para problemas simples
                for (const issue of integrity.issues) {
                    if (issue.type === 'SOLICITACAO_SEM_COTACAO') {
                        await this.createCotacaoFromSolicitacao(issue.id);
                        console.log('✅ Cotação criada automaticamente para:', issue.id);
                    }
                }
            }
            
            return integrity;
            
        } catch (error) {
            console.error('Erro no monitoramento:', error);
        }
    }
}

// ===================================================================
// INICIALIZAÇÃO DO SERVIÇO
// ===================================================================

// Executar verificação inicial
DataIntegrityService.checkSystemIntegrity().then(result => {
    if (result.healthy) {
        console.log('✅ Sistema de integridade: OK');
    } else {
        console.warn('⚠️ Problemas de integridade detectados:', result.issues.length);
    }
});

// Monitoramento periódico (a cada 5 minutos)
setInterval(() => {
    DataIntegrityService.monitorAndSync();
}, 5 * 60 * 1000);

console.log('🛡️ Serviço de Integridade de Dados carregado');
