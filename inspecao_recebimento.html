<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Inspeção de Recebimento - Controle de Qualidade</title>
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --success-color: #27ae60;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --info-color: #17a2b8;
      --light-bg: #ecf0f1;
      --border-color: #bdc3c7;
      --text-color: #2c3e50;
      --shadow: 0 4px 15px rgba(0,0,0,0.1);
      --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --gradient-header: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
      background: var(--gradient-primary);
      min-height: 100vh;
      padding: 20px;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: var(--shadow);
      overflow: hidden;
    }

    .header {
      background: var(--gradient-header);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header h1::before {
      content: "🔍";
      font-size: 32px;
    }

    .main-content {
      padding: 30px;
    }

    .alert {
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
      border-left: 4px solid var(--warning-color);
    }

    .alert-warning {
      background: #fff3cd;
      color: #856404;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: var(--info-color);
    }

    .development-notice {
      text-align: center;
      padding: 40px 20px;
      background: var(--light-bg);
      border-radius: 12px;
      margin-bottom: 30px;
    }

    .development-notice h2 {
      font-size: 24px;
      margin-bottom: 15px;
      color: var(--warning-color);
    }

    .development-notice p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 10px;
    }

    .features-preview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }

    .feature-card {
      background: white;
      border: 2px solid var(--border-color);
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow);
      border-color: var(--secondary-color);
    }

    .feature-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .feature-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--primary-color);
    }

    .feature-description {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .back-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30px;
    }

    .timeline {
      margin-top: 30px;
      padding: 20px;
      background: var(--light-bg);
      border-radius: 10px;
    }

    .timeline h3 {
      margin-bottom: 20px;
      color: var(--primary-color);
    }

    .timeline-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      background: white;
      border-radius: 8px;
      border-left: 4px solid var(--info-color);
    }

    .timeline-icon {
      font-size: 20px;
      margin-right: 15px;
      width: 30px;
      text-align: center;
    }

    .timeline-content h4 {
      margin-bottom: 5px;
      color: var(--primary-color);
    }

    .timeline-content p {
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Inspeção de Recebimento</h1>
      <div class="header-actions">
        <button class="btn btn-secondary" onclick="window.history.back()">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
        <a href="index.html" class="btn btn-primary">
          <i class="fas fa-home"></i> Início
        </a>
      </div>
    </div>

    <div class="main-content">
      <div class="alert alert-warning">
        <h3>🚧 Módulo em Desenvolvimento</h3>
        <p><strong>Esta tela está em desenvolvimento e será implementada nas próximas atualizações do sistema.</strong></p>
        <p>Por enquanto, os materiais que requerem inspeção serão direcionados para o recebimento padrão com marcação especial.</p>
      </div>

      <div class="alert alert-info">
        <h3>📋 Informações sobre o Módulo de Qualidade</h3>
        <p>O módulo de Inspeção de Recebimento faz parte do <strong>Sistema de Controle de Qualidade</strong> e será ativado quando as configurações do sistema estiverem habilitadas:</p>
        <ul style="margin: 10px 0 0 20px;">
          <li><strong>Módulo de Qualidade:</strong> ATIVO</li>
          <li><strong>Inspeção de Recebimento:</strong> OBRIGATÓRIA</li>
        </ul>
      </div>

      <div class="development-notice">
        <h2>🔬 Controle de Qualidade Avançado</h2>
        <p>Esta funcionalidade permitirá o controle completo de qualidade no recebimento de materiais,</p>
        <p>garantindo que apenas produtos aprovados entrem no estoque da empresa.</p>
      </div>

      <div class="features-preview">
        <div class="feature-card">
          <div class="feature-icon">📋</div>
          <div class="feature-title">Planos de Inspeção</div>
          <div class="feature-description">
            Definição de critérios específicos de inspeção por produto ou categoria,
            com parâmetros personalizáveis de qualidade.
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🔍</div>
          <div class="feature-title">Controle de Conformidade</div>
          <div class="feature-description">
            Verificação sistemática de especificações técnicas, dimensões,
            qualidade visual e documentação obrigatória.
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <div class="feature-title">Relatórios de Qualidade</div>
          <div class="feature-description">
            Geração automática de certificados de qualidade e relatórios
            de não conformidade para rastreabilidade completa.
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-icon">⚡</div>
          <div class="feature-title">Fluxo Automatizado</div>
          <div class="feature-description">
            Aprovação ou rejeição automática baseada em critérios pré-definidos,
            com notificações para responsáveis.
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📦</div>
          <div class="feature-title">Quarentena Inteligente</div>
          <div class="feature-description">
            Isolamento automático de materiais pendentes de inspeção
            com controle de localização e rastreamento.
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🏷️</div>
          <div class="feature-title">Etiquetagem</div>
          <div class="feature-description">
            Sistema de identificação com códigos QR para rastreamento
            completo do processo de inspeção.
          </div>
        </div>
      </div>

      <div class="timeline">
        <h3>🗓️ Cronograma de Desenvolvimento</h3>
        
        <div class="timeline-item">
          <div class="timeline-icon">✅</div>
          <div class="timeline-content">
            <h4>Fase 1 - Estrutura Base</h4>
            <p>Configurações do sistema e integração com recebimento - <strong>Concluído</strong></p>
          </div>
        </div>

        <div class="timeline-item" style="border-left-color: var(--warning-color);">
          <div class="timeline-icon">🔄</div>
          <div class="timeline-content">
            <h4>Fase 2 - Interface de Inspeção</h4>
            <p>Desenvolvimento da tela de inspeção e planos de qualidade - <strong>Em desenvolvimento</strong></p>
          </div>
        </div>

        <div class="timeline-item" style="border-left-color: var(--border-color);">
          <div class="timeline-icon">⏳</div>
          <div class="timeline-content">
            <h4>Fase 3 - Relatórios e Certificados</h4>
            <p>Sistema de relatórios e certificação de qualidade - <strong>Planejado</strong></p>
          </div>
        </div>

        <div class="timeline-item" style="border-left-color: var(--border-color);">
          <div class="timeline-icon">🚀</div>
          <div class="timeline-content">
            <h4>Fase 4 - Automação Avançada</h4>
            <p>Integração com equipamentos e IoT para inspeção automatizada - <strong>Futuro</strong></p>
          </div>
        </div>
      </div>

      <div class="back-controls">
        <button class="btn btn-secondary" onclick="window.close()">
          ❌ Fechar Aba
        </button>
        <div>
          <a href="config_parametros.html" class="btn btn-primary" target="_blank">
            ⚙️ Configurar Parâmetros
          </a>
          <a href="recebimento_materiais_melhorado.html" class="btn btn-primary">
            📦 Recebimento Padrão
          </a>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Verificar se há dados de pedido no localStorage
    document.addEventListener('DOMContentLoaded', function() {
      const pedidoData = localStorage.getItem('selectedOrderForReceiving');
      if (pedidoData) {
        const pedido = JSON.parse(pedidoData);
        console.log('Pedido selecionado para inspeção:', pedido);
        
        // Mostrar informações do pedido
        if (pedido.numero) {
          const alert = document.createElement('div');
          alert.className = 'alert alert-info';
          alert.innerHTML = `
            <h3>📦 Pedido Selecionado para Inspeção</h3>
            <p><strong>Número:</strong> ${pedido.numero}</p>
            <p><strong>Valor:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
            <p><strong>Itens:</strong> ${pedido.itens?.length || 0} item(ns)</p>
            <p><strong>Requer Inspeção:</strong> ${pedido.requiresInspection ? '✅ SIM' : '❌ NÃO'}</p>
          `;
          
          document.querySelector('.main-content').insertBefore(alert, document.querySelector('.development-notice'));
        }
      }
    });

    // Função para redirecionar para recebimento padrão
    function redirectToStandardReceiving() {
      window.location.href = 'recebimento_materiais_melhorado.html';
    }
  </script>
</body>
</html> 