<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>r <PERSON> - Sistema MRP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 800px;
            margin-top: 50px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #0d6efd;
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
        }
        .card-body {
            padding: 30px;
        }
        .form-select {
            margin-bottom: 20px;
        }
        .btn-danger {
            width: 100%;
            padding: 12px;
            font-size: 1.1em;
        }
        .alert {
            margin-top: 20px;
            display: none;
        }
        .table-description {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-trash-alt me-2"></i>Zerar Tabelas</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Atenção!</strong> Esta operação irá excluir permanentemente todos os dados da tabela selecionada.
                </div>

                <div class="mb-4">
                    <label for="tabela" class="form-label">Selecione a tabela para zerar:</label>
                    <select class="form-select" id="tabela">
                        <option value="">Selecione uma tabela...</option>
                        <optgroup label="Produção">
                            <option value="ordensProducao">Ordens de Produção</option>
                            <option value="operacoes">Operações</option>
                            <option value="recursos">Recursos</option>
                        </optgroup>
                        <optgroup label="Produtos e Materiais">
                            <option value="produtos">Produtos</option>
                            <option value="estruturas">Estruturas de Produtos</option>
                            <option value="revisoes_estrutura">Revisões de Estrutura</option>
                            <option value="familias">Famílias</option>
                            <option value="grupos">Grupos</option>
                        </optgroup>
                        <optgroup label="Compras e Fornecedores">
                            <option value="solicitacoesCompra">Solicitações de Compra</option>
                            <option value="cotacoes">Cotações</option>
                            <option value="fornecedores">Fornecedores</option>
                            <option value="tabelaPrecos">Tabela de Preços</option>
                            <option value="aprovacoesPrecos">Aprovações de Preços</option>
                            <option value="analiseCredito">Análise de Crédito</option>
                        </optgroup>
                        <optgroup label="Estoque e Armazéns">
                            <option value="estoques">Estoques</option>
                            <option value="armazens">Armazéns</option>
                            <option value="movimentacoesEstoque">Movimentações de Estoque</option>
                            <option value="transferenciasArmazem">Transferências entre Armazéns</option>
                            <option value="reservas">Reservas</option>
                            <option value="apontamentos">Apontamentos</option>
                            <option value="movimentacao_armazem">Movimentação de Armazém</option>
                        </optgroup>
                        <optgroup label="Financeiro">
                            <option value="cfops">CFOPs</option>
                            <option value="contadores">Contadores</option>
                        </optgroup>
                        <optgroup label="Organizacional">
                            <option value="centrosCusto">Centros de Custo</option>
                            <option value="setores">Setores</option>
                            <option value="empresa">Empresa</option>
                        </optgroup>
                        <optgroup label="Sistema">
                            <option value="usuarios">Usuários</option>
                            <option value="permissoes">Permissões</option>
                            <option value="parametros">Parâmetros</option>
                            <option value="notificacoes">Notificações</option>
                            <option value="backups">Backups</option>
                        </optgroup>
                    </select>
                </div>

                <div id="tabelaInfo" class="mb-4" style="display: none;">
                    <h5>Informações da Tabela:</h5>
                    <div id="tabelaDescription" class="table-description"></div>
                </div>

                <div class="mb-4" id="dataApontamentoDiv" style="display:none;">
                    <label for="dataApontamento" class="form-label">Data dos Apontamentos a Apagar:</label>
                    <input type="date" class="form-control" id="dataApontamento">
                    <div class="form-text">Se selecionar uma data, apenas os apontamentos desse dia serão apagados. Se não selecionar, todos serão apagados.</div>
                </div>

                <button class="btn btn-danger" id="btnZerar" disabled>
                    <i class="fas fa-trash-alt me-2"></i>Zerar Tabela
                </button>

                <div class="alert alert-success mt-3" id="successAlert" style="display: none;">
                    <i class="fas fa-check-circle me-2"></i>
                    Tabela zerada com sucesso!
                </div>

                <div class="alert alert-danger mt-3" id="errorAlert" style="display: none;">
                    <i class="fas fa-times-circle me-2"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" title="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja zerar a tabela <strong id="tabelaNome"></strong>?</p>
                    <p class="text-danger">Esta ação não pode ser desfeita!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="confirmZerar">Confirmar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module">
        import { db } from './firebase-config.js';
        const { Timestamp, getDocs, query, where, collection, deleteDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        // Descrições das tabelas
        const tabelaDescriptions = {
            ordensProducao: "Contém todas as ordens de produção do sistema, incluindo status, quantidades e datas.",
            operacoes: "Contém as operações de produção disponíveis no sistema.",
            recursos: "Contém os recursos de produção (máquinas, equipamentos, etc).",
            produtos: "Contém todos os produtos cadastrados no sistema.",
            estruturas: "Contém as estruturas de produtos (BOM) cadastradas no sistema.",
            revisoes_estrutura: "Contém o histórico de revisões das estruturas de produtos.",
            familias: "Contém as famílias de produtos cadastradas no sistema.",
            grupos: "Contém os grupos de produtos cadastrados no sistema.",
            solicitacoesCompra: "Contém todas as solicitações de compra geradas pelo sistema.",
            cotacoes: "Contém todas as cotações de preços do sistema.",
            fornecedores: "Contém todos os fornecedores cadastrados no sistema.",
            tabelaPrecos: "Contém as tabelas de preços dos fornecedores.",
            aprovacoesPrecos: "Contém o histórico de aprovações de preços.",
            analiseCredito: "Contém as análises de crédito dos fornecedores.",
            estoques: "Contém os registros de estoque de todos os materiais.",
            armazens: "Contém os armazéns cadastrados no sistema.",
            movimentacoesEstoque: "Contém o histórico de movimentações de estoque.",
            cfops: "Contém os códigos fiscais de operações e prestações.",
            contadores: "Contém os contadores cadastrados no sistema.",
            centrosCusto: "Contém os centros de custo cadastrados no sistema.",
            setores: "Contém os setores da empresa cadastrados no sistema.",
            empresa: "Contém as informações da empresa.",
            usuarios: "Contém os usuários cadastrados no sistema.",
            permissoes: "Contém as permissões de acesso do sistema.",
            parametros: "Contém os parâmetros de configuração do sistema.",
            notificacoes: "Contém o histórico de notificações do sistema.",
            backups: "Contém o histórico de backups realizados."
        };

        // Elementos do DOM
        const tabelaSelect = document.getElementById('tabela');
        const btnZerar = document.getElementById('btnZerar');
        const tabelaInfo = document.getElementById('tabelaInfo');
        const tabelaDescription = document.getElementById('tabelaDescription');
        const successAlert = document.getElementById('successAlert');
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));
        const tabelaNome = document.getElementById('tabelaNome');
        const confirmZerar = document.getElementById('confirmZerar');

        // Event Listeners
        tabelaSelect.addEventListener('change', () => {
            const selectedTabela = tabelaSelect.value;
            btnZerar.disabled = !selectedTabela;

            if (selectedTabela) {
                tabelaInfo.style.display = 'block';
                tabelaDescription.textContent = tabelaDescriptions[selectedTabela];
            } else {
                tabelaInfo.style.display = 'none';
            }
            // Mostrar campo de data só para apontamentos
            document.getElementById('dataApontamentoDiv').style.display = selectedTabela === 'apontamentos' ? 'block' : 'none';
        });

        btnZerar.addEventListener('click', () => {
            const selectedTabela = tabelaSelect.value;
            tabelaNome.textContent = selectedTabela;
            confirmModal.show();
        });

        confirmZerar.addEventListener('click', async () => {
            const selectedTabela = tabelaSelect.value;
            try {
                if(selectedTabela === 'apontamentos') {
                    const dataStr = document.getElementById('dataApontamento').value;
                    if(dataStr) {
                        // Apagar apenas apontamentos do dia selecionado
                        const dataIni = new Date(dataStr);
                        dataIni.setHours(0,0,0,0);
                        const dataFim = new Date(dataIni);
                        dataFim.setDate(dataFim.getDate() + 1);
                        // Firestore Timestamp
                        const q = query(collection(db, 'apontamentos'), where('dataHora', '>=', Timestamp.fromDate(dataIni)), where('dataHora', '<', Timestamp.fromDate(dataFim)));
                        const snap = await getDocs(q);
                        const deletePromises = snap.docs.map(doc => deleteDoc(doc.ref));
                        await Promise.all(deletePromises);
                    } else {
                        // Apagar todos os apontamentos
                        const snap = await getDocs(collection(db, 'apontamentos'));
                        const deletePromises = snap.docs.map(doc => deleteDoc(doc.ref));
                        await Promise.all(deletePromises);
                    }
                } else {
                    // Outras tabelas: comportamento padrão
                    const snap = await getDocs(collection(db, selectedTabela));
                    const deletePromises = snap.docs.map(doc => deleteDoc(doc.ref));
                    await Promise.all(deletePromises);
                }
                // Mensagem de sucesso e reset UI
                confirmModal.hide();
                successAlert.style.display = 'block';
                errorAlert.style.display = 'none';
                tabelaSelect.value = '';
                btnZerar.disabled = true;
                tabelaInfo.style.display = 'none';
                document.getElementById('dataApontamento').value = '';
                document.getElementById('dataApontamentoDiv').style.display = 'none';
                setTimeout(() => { successAlert.style.display = 'none'; }, 3000);
            } catch (error) {
                console.error('Erro ao zerar tabela:', error);
                errorMessage.textContent = `Erro ao zerar tabela: ${error.message}`;
                errorAlert.style.display = 'block';
                successAlert.style.display = 'none';
            }
        });
    </script>
</body>
</html> 