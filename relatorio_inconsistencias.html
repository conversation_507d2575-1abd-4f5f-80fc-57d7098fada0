<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚠️ Relatório de Inconsistências</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .report-container {
            max-width: 1600px;
            margin: 20px auto;
            padding: 20px;
        }
        .report-header {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 6px solid #e67e22;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .analysis-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
        }
        .analysis-card.critical {
            border-left-color: #e74c3c;
            animation: pulse-red 2s infinite;
        }
        .analysis-card.warning {
            border-left-color: #f39c12;
        }
        .analysis-card.success {
            border-left-color: #27ae60;
        }
        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
            50% { box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3); }
        }
        .analysis-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .analysis-icon {
            font-size: 2.5em;
            margin-right: 15px;
        }
        .analysis-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }
        .analysis-count {
            font-size: 3em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .analysis-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 15px;
        }
        .analysis-status {
            text-align: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .status-ok {
            background: #d4edda;
            color: #155724;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        .status-critical {
            background: #f8d7da;
            color: #721c24;
        }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
        }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .details-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .inconsistency-item {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .inconsistency-item.critical {
            background: #f8d7da;
            border-color: #dc3545;
        }
        .inconsistency-item.resolved {
            background: #d4edda;
            border-color: #28a745;
            opacity: 0.7;
        }
        .inconsistency-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .inconsistency-title {
            font-weight: bold;
            color: #2c3e50;
        }
        .inconsistency-severity {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .severity-low {
            background: #d1ecf1;
            color: #0c5460;
        }
        .severity-medium {
            background: #fff3cd;
            color: #856404;
        }
        .severity-high {
            background: #f8d7da;
            color: #721c24;
        }
        .inconsistency-description {
            color: #6c757d;
            margin-bottom: 10px;
            line-height: 1.5;
        }
        .inconsistency-details {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
        }
        .inconsistency-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #27ae60; font-weight: bold; }
        .error { color: #e74c3c; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .info { color: #3498db; font-weight: bold; }
        .summary-section {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e67e22 0%, #d35400 100%);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>⚠️ Relatório de Inconsistências</h1>
            <p>Análise completa de inconsistências e problemas no sistema</p>
            <p><strong>Status da Análise:</strong> <span id="statusAnalise">Aguardando execução</span></p>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Controles de Análise</h3>
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="executarAnaliseCompleta()" class="btn btn-primary">🔍 Análise Completa</button>
                <button onclick="analiseCritica()" class="btn btn-danger">🚨 Apenas Críticas</button>
                <button onclick="analiseRapida()" class="btn btn-warning">⚡ Análise Rápida</button>
                <button onclick="exportarRelatorio()" class="btn btn-success">📊 Exportar Relatório</button>
                <button onclick="limparResultados()" class="btn btn-secondary">🗑️ Limpar</button>
            </div>
            
            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill">0%</div>
            </div>
        </div>

        <!-- Grid de Análises -->
        <div class="analysis-grid" id="analysisGrid">
            <div class="analysis-card" onclick="analisarProdutosSemReferencia()">
                <div class="analysis-header">
                    <div class="analysis-icon">📦</div>
                    <div class="analysis-title">Produtos Órfãos</div>
                </div>
                <div class="analysis-count" id="count-produtos-orfaos">-</div>
                <div class="analysis-description">Produtos sem referências válidas</div>
                <div class="analysis-status status-ok" id="status-produtos-orfaos">Não analisado</div>
            </div>

            <div class="analysis-card" onclick="analisarSaldosInconsistentes()">
                <div class="analysis-header">
                    <div class="analysis-icon">📊</div>
                    <div class="analysis-title">Saldos Inconsistentes</div>
                </div>
                <div class="analysis-count" id="count-saldos-inconsistentes">-</div>
                <div class="analysis-description">Saldos negativos ou inválidos</div>
                <div class="analysis-status status-ok" id="status-saldos-inconsistentes">Não analisado</div>
            </div>

            <div class="analysis-card" onclick="analisarMovimentacoesSemProduto()">
                <div class="analysis-header">
                    <div class="analysis-icon">🔄</div>
                    <div class="analysis-title">Movimentações Órfãs</div>
                </div>
                <div class="analysis-count" id="count-movimentacoes-orfas">-</div>
                <div class="analysis-description">Movimentações sem produto válido</div>
                <div class="analysis-status status-ok" id="status-movimentacoes-orfas">Não analisado</div>
            </div>

            <div class="analysis-card" onclick="analisarCamposUndefined()">
                <div class="analysis-header">
                    <div class="analysis-icon">❓</div>
                    <div class="analysis-title">Campos Undefined</div>
                </div>
                <div class="analysis-count" id="count-campos-undefined">-</div>
                <div class="analysis-description">Campos com valores undefined/null</div>
                <div class="analysis-status status-ok" id="status-campos-undefined">Não analisado</div>
            </div>

            <div class="analysis-card" onclick="analisarDuplicatas()">
                <div class="analysis-header">
                    <div class="analysis-icon">👥</div>
                    <div class="analysis-title">Registros Duplicados</div>
                </div>
                <div class="analysis-count" id="count-duplicatas">-</div>
                <div class="analysis-description">Registros duplicados no sistema</div>
                <div class="analysis-status status-ok" id="status-duplicatas">Não analisado</div>
            </div>

            <div class="analysis-card" onclick="analisarDatasInvalidas()">
                <div class="analysis-header">
                    <div class="analysis-icon">📅</div>
                    <div class="analysis-title">Datas Inválidas</div>
                </div>
                <div class="analysis-count" id="count-datas-invalidas">-</div>
                <div class="analysis-description">Datas futuras ou inválidas</div>
                <div class="analysis-status status-ok" id="status-datas-invalidas">Não analisado</div>
            </div>
        </div>

        <!-- Resumo Geral -->
        <div class="summary-section" id="summarySection" style="display: none;">
            <h3>📋 Resumo da Análise</h3>
            <div id="summaryContent"></div>
        </div>

        <!-- Log de Atividades -->
        <div class="log-area" id="logArea"></div>

        <!-- Detalhes das Inconsistências -->
        <div class="details-section" id="detailsSection" style="display: none;">
            <h3>📋 Detalhes das Inconsistências Encontradas</h3>
            <div id="inconsistenciesContent"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let analysisResults = {};
        let inconsistencies = [];

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            progressFill.style.width = percentage + '%';
            progressFill.textContent = text || `${percentage}%`;
        }

        function updateAnalysisCard(type, count, status) {
            const countElement = document.getElementById(`count-${type}`);
            const statusElement = document.getElementById(`status-${type}`);
            const cardElement = countElement.closest('.analysis-card');
            
            countElement.textContent = count;
            statusElement.textContent = status;
            
            // Aplicar classes baseadas no resultado
            cardElement.className = 'analysis-card';
            statusElement.className = 'analysis-status';
            
            if (count === 0) {
                cardElement.classList.add('success');
                statusElement.classList.add('status-ok');
                statusElement.textContent = 'OK';
            } else if (count < 5) {
                cardElement.classList.add('warning');
                statusElement.classList.add('status-warning');
                statusElement.textContent = 'Atenção';
            } else {
                cardElement.classList.add('critical');
                statusElement.classList.add('status-critical');
                statusElement.textContent = 'Crítico';
            }
            
            analysisResults[type] = { count, status };
        }

        function addInconsistency(type, title, description, severity, details, actions = []) {
            inconsistencies.push({
                type,
                title,
                description,
                severity,
                details,
                actions,
                timestamp: new Date(),
                resolved: false
            });
            updateInconsistenciesDisplay();
        }

        function updateInconsistenciesDisplay() {
            const section = document.getElementById('detailsSection');
            const content = document.getElementById('inconsistenciesContent');
            
            if (inconsistencies.length === 0) {
                section.style.display = 'none';
                return;
            }
            
            section.style.display = 'block';
            
            let html = '';
            inconsistencies.forEach((inc, index) => {
                const severityClass = `severity-${inc.severity}`;
                const itemClass = inc.resolved ? 'inconsistency-item resolved' : 
                                inc.severity === 'high' ? 'inconsistency-item critical' : 'inconsistency-item';
                
                html += `
                    <div class="${itemClass}">
                        <div class="inconsistency-header">
                            <div class="inconsistency-title">${inc.title}</div>
                            <div class="inconsistency-severity ${severityClass}">${inc.severity}</div>
                        </div>
                        <div class="inconsistency-description">${inc.description}</div>
                        ${inc.details ? `<div class="inconsistency-details">${inc.details}</div>` : ''}
                        <div class="inconsistency-actions">
                            ${inc.actions.map(action => `<button class="btn btn-small btn-warning" onclick="${action.function}">${action.label}</button>`).join('')}
                            <button class="btn btn-small btn-success" onclick="marcarResolvido(${index})">✅ Marcar como Resolvido</button>
                        </div>
                        <small style="color: #6c757d;">Detectado em: ${inc.timestamp.toLocaleString()}</small>
                    </div>
                `;
            });
            
            content.innerHTML = html;
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de relatório de inconsistências carregado', 'info');
        };

        window.executarAnaliseCompleta = async function() {
            log('🔍 Iniciando análise completa de inconsistências...', 'info');
            document.getElementById('statusAnalise').textContent = 'Executando análise completa...';
            
            inconsistencies = [];
            const analyses = [
                'produtos-orfaos',
                'saldos-inconsistentes', 
                'movimentacoes-orfas',
                'campos-undefined',
                'duplicatas',
                'datas-invalidas'
            ];
            
            for (let i = 0; i < analyses.length; i++) {
                const analysis = analyses[i];
                const progress = Math.round(((i + 1) / analyses.length) * 100);
                
                updateProgress(progress, `Analisando ${analysis}...`);
                
                try {
                    switch (analysis) {
                        case 'produtos-orfaos':
                            await analisarProdutosSemReferencia();
                            break;
                        case 'saldos-inconsistentes':
                            await analisarSaldosInconsistentes();
                            break;
                        case 'movimentacoes-orfas':
                            await analisarMovimentacoesSemProduto();
                            break;
                        case 'campos-undefined':
                            await analisarCamposUndefined();
                            break;
                        case 'duplicatas':
                            await analisarDuplicatas();
                            break;
                        case 'datas-invalidas':
                            await analisarDatasInvalidas();
                            break;
                    }
                    
                    // Pequeno delay para visualização
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    log(`❌ Erro na análise ${analysis}: ${error.message}`, 'error');
                    updateAnalysisCard(analysis, 'ERRO', 'Erro na análise');
                }
            }
            
            updateProgress(100, 'Análise concluída');
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
            }, 2000);
            
            // Mostrar resumo
            mostrarResumo();
            
            log('✅ Análise completa finalizada', 'success');
            document.getElementById('statusAnalise').textContent = 'Análise concluída';
        };

        // Função para análise crítica apenas
        window.analiseCritica = async function() {
            log('🚨 Iniciando análise crítica...', 'warning');
            document.getElementById('statusAnalise').textContent = 'Executando análise crítica...';

            inconsistencies = [];
            const analyses = ['saldos-inconsistentes', 'movimentacoes-orfas', 'duplicatas'];

            for (let i = 0; i < analyses.length; i++) {
                const analysis = analyses[i];
                const progress = Math.round(((i + 1) / analyses.length) * 100);

                updateProgress(progress, `Analisando ${analysis}...`);

                try {
                    switch (analysis) {
                        case 'saldos-inconsistentes':
                            await analisarSaldosInconsistentes();
                            break;
                        case 'movimentacoes-orfas':
                            await analisarMovimentacoesSemProduto();
                            break;
                        case 'duplicatas':
                            await analisarDuplicatas();
                            break;
                    }

                    await new Promise(resolve => setTimeout(resolve, 300));

                } catch (error) {
                    log(`❌ Erro na análise ${analysis}: ${error.message}`, 'error');
                    updateAnalysisCard(analysis, 'ERRO', 'Erro na análise');
                }
            }

            updateProgress(100, 'Análise crítica concluída');
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
            }, 2000);

            mostrarResumo();
            log('✅ Análise crítica finalizada', 'success');
            document.getElementById('statusAnalise').textContent = 'Análise crítica concluída';
        };

        // Função para análise rápida
        window.analiseRapida = async function() {
            log('⚡ Iniciando análise rápida...', 'info');
            document.getElementById('statusAnalise').textContent = 'Executando análise rápida...';

            inconsistencies = [];
            const analyses = ['produtos-orfaos', 'campos-undefined'];

            for (let i = 0; i < analyses.length; i++) {
                const analysis = analyses[i];
                const progress = Math.round(((i + 1) / analyses.length) * 100);

                updateProgress(progress, `Analisando ${analysis}...`);

                try {
                    switch (analysis) {
                        case 'produtos-orfaos':
                            await analisarProdutosSemReferencia();
                            break;
                        case 'campos-undefined':
                            await analisarCamposUndefined();
                            break;
                    }

                    await new Promise(resolve => setTimeout(resolve, 200));

                } catch (error) {
                    log(`❌ Erro na análise ${analysis}: ${error.message}`, 'error');
                    updateAnalysisCard(analysis, 'ERRO', 'Erro na análise');
                }
            }

            updateProgress(100, 'Análise rápida concluída');
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
            }, 2000);

            mostrarResumo();
            log('✅ Análise rápida finalizada', 'success');
            document.getElementById('statusAnalise').textContent = 'Análise rápida concluída';
        };

        // Função para analisar produtos sem referência
        window.analisarProdutosSemReferencia = async function() {
            log('📦 Analisando produtos órfãos...', 'info');

            try {
                const [produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let produtosOrfaos = 0;

                produtos.forEach(produto => {
                    if (produto.tipo === 'PA' || produto.tipo === 'SP') {
                        const temEstrutura = estruturas.some(e => e.produtoPaiId === produto.id);
                        if (!temEstrutura) {
                            produtosOrfaos++;
                            addInconsistency(
                                'produtos-orfaos',
                                `Produto ${produto.codigo} sem estrutura`,
                                `O produto ${produto.codigo} - ${produto.descricao} do tipo ${produto.tipo} não possui estrutura cadastrada.`,
                                'medium',
                                `ID: ${produto.id}\nTipo: ${produto.tipo}\nCódigo: ${produto.codigo}`,
                                [
                                    { label: '🔧 Criar Estrutura', function: `criarEstrutura('${produto.id}')` }
                                ]
                            );
                        }
                    }
                });

                updateAnalysisCard('produtos-orfaos', produtosOrfaos, produtosOrfaos === 0 ? 'OK' : 'Problemas encontrados');
                log(`📦 Encontrados ${produtosOrfaos} produtos órfãos`, produtosOrfaos === 0 ? 'success' : 'warning');

            } catch (error) {
                log(`❌ Erro ao analisar produtos órfãos: ${error.message}`, 'error');
                updateAnalysisCard('produtos-orfaos', 'ERRO', 'Erro na análise');
            }
        };

        // Função para analisar saldos inconsistentes
        window.analisarSaldosInconsistentes = async function() {
            log('📊 Analisando saldos inconsistentes...', 'info');

            try {
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let saldosInconsistentes = 0;

                estoques.forEach(estoque => {
                    if (estoque.saldo < 0) {
                        saldosInconsistentes++;
                        addInconsistency(
                            'saldos-inconsistentes',
                            `Saldo negativo detectado`,
                            `O produto ${estoque.produtoId} possui saldo negativo: ${estoque.saldo}`,
                            'high',
                            `ID Estoque: ${estoque.id}\nProduto ID: ${estoque.produtoId}\nSaldo: ${estoque.saldo}`,
                            [
                                { label: '🔧 Corrigir Saldo', function: `corrigirSaldo('${estoque.id}')` }
                            ]
                        );
                    }

                    if (isNaN(estoque.saldo) || estoque.saldo === undefined || estoque.saldo === null) {
                        saldosInconsistentes++;
                        addInconsistency(
                            'saldos-inconsistentes',
                            `Saldo inválido detectado`,
                            `O produto ${estoque.produtoId} possui saldo inválido: ${estoque.saldo}`,
                            'high',
                            `ID Estoque: ${estoque.id}\nProduto ID: ${estoque.produtoId}\nSaldo: ${estoque.saldo}`,
                            [
                                { label: '🔧 Corrigir Saldo', function: `corrigirSaldo('${estoque.id}')` }
                            ]
                        );
                    }
                });

                updateAnalysisCard('saldos-inconsistentes', saldosInconsistentes, saldosInconsistentes === 0 ? 'OK' : 'Problemas encontrados');
                log(`📊 Encontrados ${saldosInconsistentes} saldos inconsistentes`, saldosInconsistentes === 0 ? 'success' : 'error');

            } catch (error) {
                log(`❌ Erro ao analisar saldos: ${error.message}`, 'error');
                updateAnalysisCard('saldos-inconsistentes', 'ERRO', 'Erro na análise');
            }
        };

        // Função para analisar movimentações sem produto
        window.analisarMovimentacoesSemProduto = async function() {
            log('🔄 Analisando movimentações órfãs...', 'info');

            try {
                const [movimentacoesSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "movimentacoes")),
                    getDocs(collection(db, "produtos"))
                ]);

                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtoIds = new Set(produtos.map(p => p.id));

                let movimentacoesOrfas = 0;

                movimentacoes.forEach(mov => {
                    if (!produtoIds.has(mov.produtoId)) {
                        movimentacoesOrfas++;
                        addInconsistency(
                            'movimentacoes-orfas',
                            `Movimentação órfã detectada`,
                            `A movimentação ${mov.id} referencia um produto inexistente: ${mov.produtoId}`,
                            'high',
                            `ID Movimentação: ${mov.id}\nProduto ID: ${mov.produtoId}\nTipo: ${mov.tipo}\nQuantidade: ${mov.quantidade}`,
                            [
                                { label: '🗑️ Remover Movimentação', function: `removerMovimentacao('${mov.id}')` }
                            ]
                        );
                    }
                });

                updateAnalysisCard('movimentacoes-orfas', movimentacoesOrfas, movimentacoesOrfas === 0 ? 'OK' : 'Problemas encontrados');
                log(`🔄 Encontradas ${movimentacoesOrfas} movimentações órfãs`, movimentacoesOrfas === 0 ? 'success' : 'error');

            } catch (error) {
                log(`❌ Erro ao analisar movimentações: ${error.message}`, 'error');
                updateAnalysisCard('movimentacoes-orfas', 'ERRO', 'Erro na análise');
            }
        };

        // Função para analisar campos undefined
        window.analisarCamposUndefined = async function() {
            log('❓ Analisando campos undefined...', 'info');

            try {
                const [produtosSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let camposUndefined = 0;

                // Verificar produtos
                produtos.forEach(produto => {
                    const camposObrigatorios = ['codigo', 'descricao', 'tipo', 'unidade'];
                    camposObrigatorios.forEach(campo => {
                        if (produto[campo] === undefined || produto[campo] === null || produto[campo] === '') {
                            camposUndefined++;
                            addInconsistency(
                                'campos-undefined',
                                `Campo obrigatório vazio em produto`,
                                `O produto ${produto.id} possui o campo '${campo}' vazio ou undefined`,
                                'medium',
                                `ID Produto: ${produto.id}\nCampo: ${campo}\nValor: ${produto[campo]}`,
                                [
                                    { label: '🔧 Corrigir Campo', function: `corrigirCampo('produtos', '${produto.id}', '${campo}')` }
                                ]
                            );
                        }
                    });
                });

                // Verificar estoques
                estoques.forEach(estoque => {
                    if (estoque.produtoId === undefined || estoque.produtoId === null) {
                        camposUndefined++;
                        addInconsistency(
                            'campos-undefined',
                            `Estoque sem produto definido`,
                            `O estoque ${estoque.id} não possui produtoId definido`,
                            'high',
                            `ID Estoque: ${estoque.id}\nProduto ID: ${estoque.produtoId}`,
                            [
                                { label: '🗑️ Remover Estoque', function: `removerEstoque('${estoque.id}')` }
                            ]
                        );
                    }
                });

                updateAnalysisCard('campos-undefined', camposUndefined, camposUndefined === 0 ? 'OK' : 'Problemas encontrados');
                log(`❓ Encontrados ${camposUndefined} campos undefined`, camposUndefined === 0 ? 'success' : 'warning');

            } catch (error) {
                log(`❌ Erro ao analisar campos undefined: ${error.message}`, 'error');
                updateAnalysisCard('campos-undefined', 'ERRO', 'Erro na análise');
            }
        };

        // Função para analisar duplicatas
        window.analisarDuplicatas = async function() {
            log('👥 Analisando registros duplicados...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let duplicatas = 0;
                const codigosVistos = new Map();

                produtos.forEach(produto => {
                    if (produto.codigo) {
                        if (codigosVistos.has(produto.codigo)) {
                            duplicatas++;
                            const produtoOriginal = codigosVistos.get(produto.codigo);
                            addInconsistency(
                                'duplicatas',
                                `Código duplicado detectado`,
                                `O código '${produto.codigo}' está sendo usado por múltiplos produtos`,
                                'high',
                                `Produto 1: ${produtoOriginal.id} - ${produtoOriginal.descricao}\nProduto 2: ${produto.id} - ${produto.descricao}`,
                                [
                                    { label: '🔧 Corrigir Duplicata', function: `corrigirDuplicata('${produto.id}')` }
                                ]
                            );
                        } else {
                            codigosVistos.set(produto.codigo, produto);
                        }
                    }
                });

                updateAnalysisCard('duplicatas', duplicatas, duplicatas === 0 ? 'OK' : 'Problemas encontrados');
                log(`👥 Encontradas ${duplicatas} duplicatas`, duplicatas === 0 ? 'success' : 'error');

            } catch (error) {
                log(`❌ Erro ao analisar duplicatas: ${error.message}`, 'error');
                updateAnalysisCard('duplicatas', 'ERRO', 'Erro na análise');
            }
        };

        // Função para analisar datas inválidas
        window.analisarDatasInvalidas = async function() {
            log('📅 Analisando datas inválidas...', 'info');

            try {
                const [movimentacoesSnap, ordensSnap] = await Promise.all([
                    getDocs(collection(db, "movimentacoes")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let datasInvalidas = 0;
                const agora = new Date();

                // Verificar movimentações
                movimentacoes.forEach(mov => {
                    if (mov.data && mov.data.seconds) {
                        const dataMovimentacao = new Date(mov.data.seconds * 1000);
                        if (dataMovimentacao > agora) {
                            datasInvalidas++;
                            addInconsistency(
                                'datas-invalidas',
                                `Data futura em movimentação`,
                                `A movimentação ${mov.id} possui data futura: ${dataMovimentacao.toLocaleDateString()}`,
                                'medium',
                                `ID Movimentação: ${mov.id}\nData: ${dataMovimentacao.toLocaleString()}\nTipo: ${mov.tipo}`,
                                [
                                    { label: '🔧 Corrigir Data', function: `corrigirData('movimentacoes', '${mov.id}')` }
                                ]
                            );
                        }
                    }
                });

                // Verificar ordens de produção
                ordens.forEach(ordem => {
                    if (ordem.dataCriacao && ordem.dataCriacao.seconds) {
                        const dataCriacao = new Date(ordem.dataCriacao.seconds * 1000);
                        if (dataCriacao > agora) {
                            datasInvalidas++;
                            addInconsistency(
                                'datas-invalidas',
                                `Data de criação futura em OP`,
                                `A ordem ${ordem.numero} possui data de criação futura: ${dataCriacao.toLocaleDateString()}`,
                                'medium',
                                `ID Ordem: ${ordem.id}\nNúmero: ${ordem.numero}\nData: ${dataCriacao.toLocaleString()}`,
                                [
                                    { label: '🔧 Corrigir Data', function: `corrigirData('ordensProducao', '${ordem.id}')` }
                                ]
                            );
                        }
                    }
                });

                updateAnalysisCard('datas-invalidas', datasInvalidas, datasInvalidas === 0 ? 'OK' : 'Problemas encontrados');
                log(`📅 Encontradas ${datasInvalidas} datas inválidas`, datasInvalidas === 0 ? 'success' : 'warning');

            } catch (error) {
                log(`❌ Erro ao analisar datas: ${error.message}`, 'error');
                updateAnalysisCard('datas-invalidas', 'ERRO', 'Erro na análise');
            }
        };

        // Função para mostrar resumo
        function mostrarResumo() {
            const section = document.getElementById('summarySection');
            const content = document.getElementById('summaryContent');

            const totalInconsistencias = inconsistencies.length;
            const criticas = inconsistencies.filter(i => i.severity === 'high').length;
            const avisos = inconsistencies.filter(i => i.severity === 'medium').length;
            const leves = inconsistencies.filter(i => i.severity === 'low').length;

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="text-align: center;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #e67e22;">${totalInconsistencias}</div>
                        <div>Total de Inconsistências</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #e74c3c;">${criticas}</div>
                        <div>Críticas</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #f39c12;">${avisos}</div>
                        <div>Avisos</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #3498db;">${leves}</div>
                        <div>Leves</div>
                    </div>
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    ${totalInconsistencias === 0 ?
                        '<div style="color: #27ae60; font-weight: bold; font-size: 1.2em;">🎉 Nenhuma inconsistência encontrada!</div>' :
                        '<div style="color: #e67e22; font-weight: bold;">⚠️ Inconsistências detectadas - Recomenda-se correção</div>'
                    }
                </div>
            `;

            section.style.display = 'block';
        }

        // Função para exportar relatório
        window.exportarRelatorio = function() {
            if (inconsistencies.length === 0) {
                alert('Nenhuma inconsistência para exportar. Execute uma análise primeiro.');
                return;
            }

            let csv = 'Tipo,Título,Descrição,Severidade,Detalhes,Data\n';
            inconsistencies.forEach(inc => {
                const linha = [
                    inc.type,
                    inc.title.replace(/"/g, '""'),
                    inc.description.replace(/"/g, '""'),
                    inc.severity,
                    inc.details ? inc.details.replace(/"/g, '""') : '',
                    inc.timestamp.toLocaleString()
                ].map(field => `"${field}"`).join(',');
                csv += linha + '\n';
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `relatorio_inconsistencias_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            log('📊 Relatório exportado com sucesso', 'success');
        };

        // Função para limpar resultados
        window.limparResultados = function() {
            inconsistencies = [];
            analysisResults = {};

            // Resetar cards
            const cards = document.querySelectorAll('.analysis-card');
            cards.forEach(card => {
                card.className = 'analysis-card';
                const type = card.onclick.toString().match(/analisar(\w+)/)?.[1]?.toLowerCase() || '';
                if (type) {
                    const countElement = document.getElementById(`count-${type.replace(/([A-Z])/g, '-$1').toLowerCase()}`);
                    const statusElement = document.getElementById(`status-${type.replace(/([A-Z])/g, '-$1').toLowerCase()}`);
                    if (countElement) countElement.textContent = '-';
                    if (statusElement) {
                        statusElement.textContent = 'Não analisado';
                        statusElement.className = 'analysis-status status-ok';
                    }
                }
            });

            // Ocultar seções
            document.getElementById('summarySection').style.display = 'none';
            document.getElementById('detailsSection').style.display = 'none';
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('statusAnalise').textContent = 'Aguardando execução';

            // Limpar log
            document.getElementById('logArea').innerHTML = '';

            log('🗑️ Resultados limpos', 'info');
        };

        // Função para marcar como resolvido
        window.marcarResolvido = function(index) {
            if (inconsistencies[index]) {
                inconsistencies[index].resolved = true;
                updateInconsistenciesDisplay();
                log(`✅ Inconsistência marcada como resolvida: ${inconsistencies[index].title}`, 'success');
            }
        };

        // Funções de ação (placeholders)
        window.criarEstrutura = function(produtoId) {
            alert(`Função para criar estrutura do produto ${produtoId} - Implementar integração`);
        };

        window.corrigirSaldo = function(estoqueId) {
            alert(`Função para corrigir saldo do estoque ${estoqueId} - Implementar integração`);
        };

        window.removerMovimentacao = function(movimentacaoId) {
            alert(`Função para remover movimentação ${movimentacaoId} - Implementar integração`);
        };

        window.corrigirCampo = function(colecao, documentoId, campo) {
            alert(`Função para corrigir campo ${campo} do documento ${documentoId} na coleção ${colecao} - Implementar integração`);
        };

        window.removerEstoque = function(estoqueId) {
            alert(`Função para remover estoque ${estoqueId} - Implementar integração`);
        };

        window.corrigirDuplicata = function(produtoId) {
            alert(`Função para corrigir duplicata do produto ${produtoId} - Implementar integração`);
        };

        window.corrigirData = function(colecao, documentoId) {
            alert(`Função para corrigir data do documento ${documentoId} na coleção ${colecao} - Implementar integração`);
        };

    </script>
</body>
</html>
