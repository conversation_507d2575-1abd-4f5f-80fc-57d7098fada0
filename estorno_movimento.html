<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Estorno de Movimentações</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    h2 {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .search-bar {
      margin-bottom: 20px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .movements-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .movements-table th,
    .movements-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .movements-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    .movements-table tr:hover {
      background-color: #f8f9fa;
    }

    .movement-type {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }

    .type-entrada {
      background-color: var(--success-color);
      color: white;
    }

    .type-saida {
      background-color: var(--danger-color);
      color: white;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
    }

    .estorno-btn {
      background-color: var(--warning-color);
      color: #000;
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      cursor: pointer;
    }

    .estorno-btn:hover {
      background-color: #d9620a;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      width: 80%;
      max-width: 600px;
      border-radius: 8px;
      position: relative;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-body {
      padding: 15px 5px;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .form-group {
      margin-bottom: 15px;
    }

    textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
      resize: vertical;
    }

    textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .op-card {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .op-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dee2e6;
    }

    .op-title {
      font-size: 16px;
      font-weight: 600;
      color: #495057;
    }

    .op-movements {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 10px;
      margin-bottom: 15px;
    }

    .op-movement {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 10px;
      font-size: 13px;
    }

    .op-movement.entrada {
      border-left: 4px solid #28a745;
    }

    .op-movement.saida {
      border-left: 4px solid #dc3545;
    }

    .sequence-badge {
      background: #007bff;
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 600;
      margin-right: 5px;
    }

    .btn-estorno-op {
      background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-estorno-op:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(229, 90, 43, 0.3);
    }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1>Estorno de Movimentações</h1>
    <div>
      <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
    </div>
  </div>

  <div class="search-bar">
    <div class="form-row">
      <div class="form-col">
        <input type="text" id="searchInput" placeholder="Buscar por código, descrição ou documento..." oninput="filterMovements()">
      </div>
      <div class="form-col">
        <select id="typeFilter" onchange="filterMovements()">
          <option value="">Todos os tipos</option>
          <option value="ENTRADA">Entradas</option>
          <option value="SAIDA">Saídas</option>
        </select>
      </div>
    </div>
  </div>

  <div>
    <h2>Movimentações Registradas</h2>
    
    <!-- Seção de OPs para Estorno Completo -->
    <div id="opSection" style="margin-bottom: 20px;"></div>
    
    <table class="movements-table">
      <thead>
        <tr>
          <th>Data/Hora</th>
          <th>Produto</th>
          <th>Tipo</th>
          <th>Quantidade</th>
          <th>Documento</th>
          <th>Observações</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="movementsTableBody"></tbody>
    </table>
  </div>
</div>

<!-- Modal de Confirmação de Estorno -->
<div id="estornoModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h2>Confirmar Estorno</h2>
      <span class="close-button" onclick="closeModal()">×</span>
    </div>

    <div class="modal-body">
      <form id="estornoForm" onsubmit="handleEstorno(event)">
        <input type="hidden" id="movementId">
        <div class="form-group">
          <label>Detalhes da Movimentação:</label>
          <div id="movementDetails" style="padding: 8px; background-color: var(--secondary-color); border-radius: 4px;"></div>
        </div>
        <div class="form-group">
          <label for="estornoReason">Motivo do Estorno:</label>
          <textarea id="estornoReason" rows="3" placeholder="Digite o motivo do estorno..." required></textarea>
        </div>
        <button type="submit" class="btn-danger">Confirmar Estorno</button>
      </form>
    </div>
  </div>
</div>

<script type="module">
  import { db } from './firebase-config.js';
  import { 
    collection,
    getDoc,
    addDoc, 
    onSnapshot,
    doc,
    updateDoc,
    setDoc,
    Timestamp,
    deleteDoc
  } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

  let produtos = [];
  let estoques = [];
  let movimentacoes = [];
  let armazens = [];

  window.onload = async function() {
    await setupRealTimeListeners();
  };

  async function setupRealTimeListeners() {
    try {
      const promises = [
        new Promise(resolve => onSnapshot(collection(db, "produtos"), 
          snap => { 
            try {
              produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(p => p.id); 
              resolve(); 
            } catch (error) {
              console.error("Erro ao processar produtos:", error);
              resolve();
            }
          },
          error => {
            console.error("Erro no listener de produtos:", error);
            resolve();
          }
        )),
        new Promise(resolve => onSnapshot(collection(db, "estoques"), 
          snap => { 
            try {
              estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(e => e.id); 
              resolve(); 
            } catch (error) {
              console.error("Erro ao processar estoques:", error);
              resolve();
            }
          },
          error => {
            console.error("Erro no listener de estoques:", error);
            resolve();
          }
        )),
        new Promise(resolve => onSnapshot(collection(db, "movimentacoesEstoque"), 
          snap => { 
            try {
              movimentacoes = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(m => m.id); 
              displayMovements(); 
              resolve(); 
            } catch (error) {
              console.error("Erro ao processar movimentações:", error);
              resolve();
            }
          },
          error => {
            console.error("Erro no listener de movimentações:", error);
            resolve();
          }
        )),
        new Promise(resolve => onSnapshot(collection(db, "armazens"), 
          snap => { 
            try {
              armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(a => a.id); 
              resolve(); 
            } catch (error) {
              console.error("Erro ao processar armazéns:", error);
              resolve();
            }
          },
          error => {
            console.error("Erro no listener de armazéns:", error);
            resolve();
          }
        ))
      ];
      await Promise.all(promises);
      console.log("Listeners configurados com sucesso");
    } catch (error) {
      console.error("Erro ao configurar listeners:", error);
      alert("Erro ao carregar dados do sistema. Por favor, recarregue a página.");
    }
  }

  function displayMovements(filteredMovements = movimentacoes) {
    const tableBody = document.getElementById('movementsTableBody');
    const opSection = document.getElementById('opSection');
    if (!tableBody || !opSection) return;
    
    tableBody.innerHTML = '';
    opSection.innerHTML = '';

    // Validações básicas e filtrar movimentações que têm dados essenciais
    const validMovements = (filteredMovements || []).filter(mov => 
      mov && 
      mov.id && 
      mov.produtoId && 
      mov.dataHora && 
      (mov.dataHora.seconds || mov.dataHora.toDate) &&
      mov.tipo &&
      typeof mov.quantidade !== 'undefined'
    );

    // Agrupar movimentações por OP
    displayOPGroups(validMovements);

    const sortedMovements = validMovements.sort((a, b) => {
      const timeA = a.dataHora.seconds || (a.dataHora.toDate ? a.dataHora.toDate().getTime() / 1000 : 0);
      const timeB = b.dataHora.seconds || (b.dataHora.toDate ? b.dataHora.toDate().getTime() / 1000 : 0);
      return timeB - timeA;
    });

    if (sortedMovements.length === 0) {
      const emptyRow = document.createElement('tr');
      emptyRow.innerHTML = `
        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
          <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 5px;">Nenhuma movimentação encontrada</div>
          <div style="font-size: 14px;">Verifique os filtros ou aguarde o carregamento dos dados</div>
        </td>`;
      tableBody.appendChild(emptyRow);
      return;
    }

    sortedMovements.forEach(mov => {
      const produto = produtos.find(p => p.id === mov.produtoId);
      if (!produto) {
        console.warn('Produto não encontrado para movimentação:', mov.id);
        return;
      }

      const armazem = armazens.find(a => a.id === (mov.armazemOrigemId || mov.armazemDestinoId));
      const armazemText = armazem ? ` (${armazem.codigo})` : '';

      // Formatação segura da data
      let dataFormatada = 'Data inválida';
      try {
        if (mov.dataHora.seconds) {
          dataFormatada = new Date(mov.dataHora.seconds * 1000).toLocaleString('pt-BR');
        } else if (mov.dataHora.toDate) {
          dataFormatada = mov.dataHora.toDate().toLocaleString('pt-BR');
        } else if (mov.dataHora instanceof Date) {
          dataFormatada = mov.dataHora.toLocaleString('pt-BR');
        }
      } catch (error) {
        console.warn('Erro ao formatar data da movimentação:', error, mov);
      }

      // Verificar se é movimentação de OP
      const isOPMovement = mov.observacoes && (
        mov.observacoes.includes('OP ') || 
        mov.observacoes.includes('Ordem de Produção') ||
        mov.observacoes.includes('Transferência para OP') ||
        mov.observacoes.includes('Consumo para OP') ||
        mov.observacoes.includes('Produção OP')
      );

      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${dataFormatada}</td>
        <td>${produto.codigo || 'N/A'} - ${produto.descricao || 'N/A'}${armazemText}${isOPMovement ? ' <span style="color: #007bff; font-size: 11px;">🏭 OP</span>' : ''}</td>
        <td><span class="movement-type type-${mov.tipo.toLowerCase()}">${mov.tipo}</span></td>
        <td>${mov.quantidade ? mov.quantidade.toFixed(3) : '0.000'} ${mov.unidade || ''}</td>
        <td>${mov.tipoDocumento || 'N/A'} ${mov.numeroDocumento || ''}</td>
        <td>${mov.observacoes || ''}</td>
        <td class="action-buttons">
          ${mov.estornada ?
            `<span style="color: #666; font-style: italic;">Estornada</span>
             ${(mov.tipo === 'ENTRADA' && mov.observacoes && (mov.observacoes.includes('Produção OP') || mov.observacoes.includes('PRODUCAO OP'))) ?
               `<button class="btn" style="background: #ffc107; color: #000; font-size: 11px; padding: 3px 6px; margin-left: 5px;" onclick="corrigirOPManualmente('${mov.id}')">Corrigir OP</button>` :
               ''}` :
            `<button class="estorno-btn" onclick="openEstornoModal('${mov.id}')">Estornar</button>`}
          <button class="btn-danger" onclick="excluirMovimentacao('${mov.id}')">Excluir</button>
        </td>`;
      tableBody.appendChild(row);
    });
  }

  function displayOPGroups(validMovements) {
    const opSection = document.getElementById('opSection');
    
    // Agrupar por OP
    const opGroups = {};
    validMovements.forEach(mov => {
      if (mov.estornada) return;
      
      const isOPMovement = mov.observacoes && (
        mov.observacoes.includes('OP ') || 
        mov.observacoes.includes('Ordem de Produção') ||
        mov.observacoes.includes('Transferência para OP') ||
        mov.observacoes.includes('Consumo para OP') ||
        mov.observacoes.includes('Produção OP')
      );
      
      if (isOPMovement) {
        // Extrair número da OP
        const opMatch = mov.observacoes.match(/OP(\d+)/);
        if (opMatch) {
          const opNumber = opMatch[1];
          if (!opGroups[opNumber]) {
            opGroups[opNumber] = [];
          }
          opGroups[opNumber].push(mov);
        }
      }
    });

    // Exibir cards das OPs
    Object.keys(opGroups).forEach(opNumber => {
      const movements = opGroups[opNumber];
      if (movements.length <= 1) return; // Só mostrar OPs com múltiplas movimentações
      
      // Ordenar movimentações: primeiro ENTRADA (produção), depois SAÍDA (consumos)
      const sortedMovements = movements.sort((a, b) => {
        if (a.tipo === 'ENTRADA' && b.tipo === 'SAIDA') return 1;
        if (a.tipo === 'SAIDA' && b.tipo === 'ENTRADA') return -1;
        return 0;
      });

      const opCard = document.createElement('div');
      opCard.className = 'op-card';
      
      let movementsHtml = '';
      sortedMovements.forEach((mov, index) => {
        const produto = produtos.find(p => p.id === mov.produtoId);
        const sequenceNumber = mov.tipo === 'ENTRADA' ? sortedMovements.filter(m => m.tipo === 'ENTRADA').indexOf(mov) + 1 : sortedMovements.filter(m => m.tipo === 'SAIDA').indexOf(mov) + 1 + sortedMovements.filter(m => m.tipo === 'ENTRADA').length;
        
        movementsHtml += `
          <div class="op-movement ${mov.tipo.toLowerCase()}">
            <span class="sequence-badge">${sequenceNumber}º</span>
            <strong>${produto?.codigo || 'N/A'}</strong><br>
            ${produto?.descricao || 'N/A'}<br>
            <strong>${mov.tipo}:</strong> ${mov.quantidade?.toFixed(3) || '0.000'} ${mov.unidade || ''}
          </div>
        `;
      });

      opCard.innerHTML = `
        <div class="op-header">
          <div class="op-title">🏭 OP${opNumber} - ${movements.length} movimentações</div>
          <button class="btn-estorno-op" onclick="estornarOPCompleta('${opNumber}')">
            Estornar OP Completa
          </button>
        </div>
        <div class="op-movements">
          ${movementsHtml}
        </div>
        <div style="font-size: 12px; color: #6c757d; font-style: italic;">
          ⚠️ Sequência recomendada: 1º Estornar ENTRADAS (produtos), 2º Estornar SAÍDAS (consumos)
        </div>
      `;
      
      opSection.appendChild(opCard);
    });
    
    if (Object.keys(opGroups).length > 0) {
      const titleDiv = document.createElement('div');
      titleDiv.innerHTML = '<h3 style="margin-bottom: 15px;">🎯 Estorno Rápido por OP</h3>';
      opSection.insertBefore(titleDiv, opSection.firstChild);
    }
  }

  window.filterMovements = function() {
    const searchText = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;

    const filteredMovements = movimentacoes.filter(mov => {
      // Validações básicas
      if (!mov || !mov.produtoId) return false;
      
      const produto = produtos.find(p => p.id === mov.produtoId);
      const searchMatch = (produto && (
          (produto.codigo || '').toLowerCase().includes(searchText) || 
          (produto.descricao || '').toLowerCase().includes(searchText)
        )) ||
        (mov.numeroDocumento || '').toLowerCase().includes(searchText) ||
        (mov.observacoes || '').toLowerCase().includes(searchText);
      const typeMatch = !typeFilter || mov.tipo === typeFilter;
      return searchMatch && typeMatch && !mov.estornada;
    });

    displayMovements(filteredMovements);
  };

  window.openEstornoModal = function(movementId) {
    const mov = movimentacoes.find(m => m.id === movementId);
    if (!mov || mov.estornada) return;

    const produto = produtos.find(p => p.id === mov.produtoId);
    
    // Determinar qual armazém será usado no estorno
    const isOPMovement = mov.observacoes && (
      mov.observacoes.includes('OP ') ||
      mov.observacoes.includes('Ordem de Produção') ||
      mov.observacoes.includes('Transferência para OP') ||
      mov.observacoes.includes('Consumo para OP') ||
      mov.observacoes.includes('Produção OP')
    );

    let armazemId;
    // Lógica corrigida para determinar o armazém de estorno
    if (mov.tipo === 'ENTRADA') {
      // Para ENTRADAS, o estorno deve ser no mesmo armazém onde entrou
      armazemId = mov.armazemId || mov.armazemDestinoId;
    } else if (mov.tipo === 'SAIDA') {
      // Para SAÍDAS, o estorno deve retornar ao armazém de onde saiu
      armazemId = mov.armazemId || mov.armazemOrigemId;
    }

    // Se ainda não identificou o armazém, tentar outras opções
    if (!armazemId) {
      armazemId = mov.armazemOrigemId || mov.armazemDestinoId || mov.armazemId;
    }
    
    const armazem = armazens.find(a => a.id === armazemId);
    
    // Formatação segura da data
    let dataFormatada = 'Data inválida';
    try {
      if (mov.dataHora && mov.dataHora.seconds) {
        dataFormatada = new Date(mov.dataHora.seconds * 1000).toLocaleString('pt-BR');
      } else if (mov.dataHora && mov.dataHora.toDate) {
        dataFormatada = mov.dataHora.toDate().toLocaleString('pt-BR');
      } else if (mov.dataHora instanceof Date) {
        dataFormatada = mov.dataHora.toLocaleString('pt-BR');
      }
    } catch (error) {
      console.warn('Erro ao formatar data no modal:', error);
    }

    document.getElementById('movementId').value = movementId;
    document.getElementById('movementDetails').innerHTML = `
      <strong>Produto:</strong> ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}<br>
      <strong>Tipo:</strong> ${mov.tipo || 'N/A'}<br>
      <strong>Quantidade:</strong> ${mov.quantidade ? mov.quantidade.toFixed(3) : '0.000'} ${mov.unidade || ''}<br>
      <strong>Armazém para estorno:</strong> ${armazem?.codigo || armazem?.nome || 'Não identificado'}${isOPMovement ? ' (Origem da OP)' : ''}<br>
      <strong>Documento:</strong> ${mov.tipoDocumento || 'N/A'} ${mov.numeroDocumento || ''}<br>
      <strong>Data:</strong> ${dataFormatada}${isOPMovement ? '<br><strong>⚠️ Movimentação de OP:</strong> O estorno será feito no armazém de origem' : ''}`;
    document.getElementById('estornoModal').style.display = 'block';
  };

  window.closeModal = function() {
    document.getElementById('estornoModal').style.display = 'none';
    document.getElementById('estornoForm').reset();
  };

  window.handleEstorno = async function(event) {
    event.preventDefault();

    const movementId = document.getElementById('movementId').value;
    const estornoReason = document.getElementById('estornoReason').value;
    
    if (!movementId || !estornoReason.trim()) {
      alert('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    const mov = movimentacoes.find(m => m.id === movementId);
    if (!mov || mov.estornada) {
      alert('Movimentação não encontrada ou já estornada.');
      return;
    }

    // Validações adicionais de segurança
    if (!mov.produtoId || !mov.tipo || typeof mov.quantidade === 'undefined') {
      alert('Dados da movimentação estão incompletos. Não é possível realizar o estorno.');
      return;
    }

    // Determinar o armazém para estorno baseado na lógica correta
    let armazemId;

    // Identificar se é movimentação relacionada a OP
    const isOPMovement = mov.observacoes && (
      mov.observacoes.includes('OP ') ||
      mov.observacoes.includes('Ordem de Produção') ||
      mov.observacoes.includes('Transferência para OP') ||
      mov.observacoes.includes('Consumo para OP') ||
      mov.observacoes.includes('Produção OP')
    );

    // Lógica corrigida para determinar o armazém de estorno
    if (mov.tipo === 'ENTRADA') {
      // Para ENTRADAS, o estorno deve ser no mesmo armazém onde entrou
      armazemId = mov.armazemId || mov.armazemDestinoId;
    } else if (mov.tipo === 'SAIDA') {
      // Para SAÍDAS, o estorno deve retornar ao armazém de onde saiu
      armazemId = mov.armazemId || mov.armazemOrigemId;
    }

    // Se ainda não identificou o armazém, tentar outras opções
    if (!armazemId) {
      armazemId = mov.armazemOrigemId || mov.armazemDestinoId || mov.armazemId;
    }

    const armazemOrigem = armazens.find(a => a.id === armazemId);
    console.log(`Estorno ${mov.tipo}: Armazém identificado:`, armazemOrigem?.codigo || armazemId,
                `(${isOPMovement ? 'OP' : 'Normal'})`);

    if (!armazemId) {
      console.error('Dados da movimentação para debug:', {
        id: mov.id,
        tipo: mov.tipo,
        armazemId: mov.armazemId,
        armazemOrigemId: mov.armazemOrigemId,
        armazemDestinoId: mov.armazemDestinoId,
        observacoes: mov.observacoes
      });
      alert(`Não foi possível identificar o armazém da movimentação para estorno.\n\nDetalhes:\n- Tipo: ${mov.tipo}\n- Armazém ID: ${mov.armazemId || 'N/A'}\n- Origem ID: ${mov.armazemOrigemId || 'N/A'}\n- Destino ID: ${mov.armazemDestinoId || 'N/A'}`);
      return;
    }
    const estoque = estoques.find(e => e.produtoId === mov.produtoId && e.armazemId === armazemId);

    try {
      const novoSaldo = mov.tipo === 'ENTRADA'
        ? (estoque?.saldo || 0) - mov.quantidade
        : (estoque?.saldo || 0) + mov.quantidade;

      // Verifica configuração de estoque negativo
      const docRef = doc(db, "parametros", "sistema");
      const docSnap = await getDoc(docRef);
      const parametros = docSnap.exists() ? docSnap.data() : {};
      const permitirEstoqueNegativo = parametros.permitirEstoqueNegativo || false;
      const metodoValorizacao = parametros.metodoValorizacao || 'MEDIO';

      if (!permitirEstoqueNegativo && novoSaldo < 0) {
        alert('Estorno não permitido: saldo ficaria negativo no armazém!');
        return;
      }

      // Validação do método de valorização
      if (mov.tipo === 'SAIDA') {
        const movimentacoesValorizacao = movimentacoes
          .filter(m => m.produtoId === mov.produtoId && m.tipo === 'ENTRADA' && !m.estornada && m.dataHora)
          .sort((a, b) => {
            // Obter timestamps seguros
            const timeA = a.dataHora.seconds || (a.dataHora.toDate ? a.dataHora.toDate().getTime() / 1000 : 0);
            const timeB = b.dataHora.seconds || (b.dataHora.toDate ? b.dataHora.toDate().getTime() / 1000 : 0);
            
            if (metodoValorizacao === 'FIFO') return timeA - timeB;
            if (metodoValorizacao === 'LIFO') return timeB - timeA;
            return 0;
          });

        const movimentacaoOriginal = movimentacoesValorizacao[0];
        if (!movimentacaoOriginal) {
          alert('Não foi possível identificar a movimentação original para estorno baseado no método ' + metodoValorizacao);
          return;
        }

        if (metodoValorizacao !== 'MEDIO' && mov.valorUnitario !== movimentacaoOriginal.valorUnitario) {
          alert(`Estorno não permitido: valor unitário incompatível com o método ${metodoValorizacao}`);
          return;
        }
      }

      const estornoMovimentacao = {
        produtoId: mov.produtoId,
        tipo: mov.tipo === 'ENTRADA' ? 'SAIDA' : 'ENTRADA',
        quantidade: mov.quantidade,
        unidade: mov.unidade,
        quantidadeOriginal: mov.quantidadeOriginal || mov.quantidade,
        unidadeOriginal: mov.unidadeOriginal || mov.unidade,
        valorUnitario: mov.valorUnitario || 0,
        tipoDocumento: 'ESTORNO',
        numeroDocumento: `EST-${movementId}`,
        observacoes: `Estorno da movimentação ${mov.tipoDocumento} ${mov.numeroDocumento}${isOPMovement ? ' (OP)' : ''}. Motivo: ${estornoReason}`,
        dataHora: Timestamp.now(),
        estornoDe: movementId,
        [mov.tipo === 'ENTRADA' ? 'armazemOrigemId' : 'armazemDestinoId']: armazemId
      };

      if (estoque) {
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });
      } else if (mov.tipo === 'SAIDA') {
        await addDoc(collection(db, "estoques"), {
          produtoId: mov.produtoId,
          armazemId: armazemId,
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });
      }

      // Tratamento especial para estornos relacionados a OP
      if (mov.observacoes && mov.observacoes.includes('OP')) {
        const opMatch = mov.observacoes.match(/OP(\d+)/);
        if (opMatch) {
          const opNumero = 'OP' + opMatch[1];

          // Se for estorno de ENTRADA de transferência para OP, reduzir saldoReservado na OP
          if (mov.tipo === 'ENTRADA' && mov.observacoes.includes('Transferência para OP')) {
            const opSnap = await getDoc(doc(db, "ordensProducao", opNumero));
            if (opSnap.exists()) {
              const opData = opSnap.data();
              const materiaisNecessarios = opData.materiaisNecessarios || [];
              const updatedMateriais = materiaisNecessarios.map(m => {
                if (m.produtoId === mov.produtoId) {
                  return { ...m, saldoReservado: Math.max(0, (m.saldoReservado || 0) - mov.quantidade) };
                }
                return m;
              });
              await updateDoc(doc(db, "ordensProducao", opNumero), {
                materiaisNecessarios: updatedMateriais
              });
            }
          }

          // Se for estorno de ENTRADA de produção (produto acabado), reduzir quantidadeProduzida na OP
          if (mov.tipo === 'ENTRADA' && (mov.observacoes.includes('Produção OP') || mov.observacoes.includes('PRODUCAO OP'))) {
            const opSnap = await getDoc(doc(db, "ordensProducao", opNumero));
            if (opSnap.exists()) {
              const opData = opSnap.data();
              const novaQuantidadeProduzida = Math.max(0, (opData.quantidadeProduzida || 0) - mov.quantidade);

              // Recalcular status da OP
              let novoStatus = opData.status;
              if (novaQuantidadeProduzida === 0) {
                novoStatus = 'Pendente';
              } else if (novaQuantidadeProduzida < opData.quantidade && opData.status === 'Concluída') {
                novoStatus = 'Em Produção';
              }

              await updateDoc(doc(db, "ordensProducao", opNumero), {
                quantidadeProduzida: novaQuantidadeProduzida,
                status: novoStatus,
                ultimaAtualizacao: Timestamp.now()
              });

              console.log(`OP ${opNumero}: Quantidade produzida reduzida de ${opData.quantidadeProduzida || 0} para ${novaQuantidadeProduzida}. Status: ${novoStatus}`);
            }
          }
        }
      }

      await addDoc(collection(db, "movimentacoesEstoque"), estornoMovimentacao);
      await updateDoc(doc(db, "movimentacoesEstoque", movementId), {
        estornada: true,
        estornoData: Timestamp.now(),
        estornoMotivo: estornoReason
      });

      const successMessage = isOPMovement 
        ? `Movimentação de OP estornada com sucesso! (Armazém de origem: ${armazens.find(a => a.id === armazemId)?.codigo || armazemId})` 
        : 'Movimentação estornada com sucesso!';
      alert(successMessage);
      closeModal();
    } catch (error) {
      console.error("Erro ao estornar:", error);
      alert("Erro ao estornar: " + error.message);
    }
  };

  window.estornarOPCompleta = async function(opNumber) {
    const movimentacoesOP = movimentacoes.filter(mov => 
      !mov.estornada && 
      mov.observacoes && 
      mov.observacoes.includes(`OP${opNumber}`)
    );

    if (movimentacoesOP.length === 0) {
      alert('Nenhuma movimentação encontrada para esta OP!');
      return;
    }

    const motivo = prompt('Digite o motivo para estorno completo da OP' + opNumber + ':');
    if (!motivo || !motivo.trim()) {
      alert('Motivo é obrigatório para o estorno!');
      return;
    }

    // Confirmar estorno
    const totalMovs = movimentacoesOP.length;
    const produtosOP = movimentacoesOP.map(m => {
      const prod = produtos.find(p => p.id === m.produtoId);
      return prod?.codigo || 'Produto não encontrado';
    });
    
    if (!confirm(`Confirma estorno COMPLETO da OP${opNumber}?\n\n${totalMovs} movimentações serão estornadas:\n${produtosOP.join(', ')}\n\nEsta ação não pode ser desfeita!`)) {
      return;
    }

    try {
      // Ordenar: primeiro ENTRADAS, depois SAÍDAS (ordem correta de estorno)
      const movimentacoesOrdenadas = movimentacoesOP.sort((a, b) => {
        if (a.tipo === 'ENTRADA' && b.tipo === 'SAIDA') return -1;
        if (a.tipo === 'SAIDA' && b.tipo === 'ENTRADA') return 1;
        return 0;
      });

      let sucessos = 0;
      let erros = [];

      for (const mov of movimentacoesOrdenadas) {
        try {
          await estornarMovimentacao(mov.id, `${motivo} (Estorno OP${opNumber})`);
          sucessos++;
        } catch (error) {
          console.error(`Erro ao estornar ${mov.id}:`, error);
          erros.push(`${produtos.find(p => p.id === mov.produtoId)?.codigo || mov.produtoId}: ${error.message}`);
        }
      }

      if (erros.length === 0) {
        alert(`✅ OP${opNumber} estornada com sucesso!\n${sucessos} movimentações processadas.`);
      } else {
        alert(`⚠️ Estorno parcial da OP${opNumber}:\n✅ ${sucessos} sucessos\n❌ ${erros.length} erros:\n\n${erros.join('\n')}`);
      }

    } catch (error) {
      console.error('Erro no estorno da OP:', error);
      alert('Erro no estorno da OP: ' + error.message);
    }
  };

  async function estornarMovimentacao(movementId, estornoReason) {
    const mov = movimentacoes.find(m => m.id === movementId);
    if (!mov || mov.estornada) {
      throw new Error('Movimentação não encontrada ou já estornada');
    }

    // Validações (mesma lógica da função handleEstorno)
    if (!mov.produtoId || !mov.tipo || typeof mov.quantidade === 'undefined') {
      throw new Error('Dados da movimentação estão incompletos');
    }

    // Determinar armazém (mesma lógica corrigida)
    let armazemId;
    const isOPMovement = mov.observacoes && (
      mov.observacoes.includes('OP ') ||
      mov.observacoes.includes('Ordem de Produção') ||
      mov.observacoes.includes('Transferência para OP') ||
      mov.observacoes.includes('Consumo para OP') ||
      mov.observacoes.includes('Produção OP')
    );

    // Lógica corrigida para determinar o armazém de estorno
    if (mov.tipo === 'ENTRADA') {
      // Para ENTRADAS, o estorno deve ser no mesmo armazém onde entrou
      armazemId = mov.armazemId || mov.armazemDestinoId;
    } else if (mov.tipo === 'SAIDA') {
      // Para SAÍDAS, o estorno deve retornar ao armazém de onde saiu
      armazemId = mov.armazemId || mov.armazemOrigemId;
    }

    // Se ainda não identificou o armazém, tentar outras opções
    if (!armazemId) {
      armazemId = mov.armazemOrigemId || mov.armazemDestinoId || mov.armazemId;
    }

    if (!armazemId) {
      throw new Error('Não foi possível identificar o armazém');
    }

    const estoque = estoques.find(e => e.produtoId === mov.produtoId && e.armazemId === armazemId);

    const novoSaldo = mov.tipo === 'ENTRADA'
      ? (estoque?.saldo || 0) - mov.quantidade
      : (estoque?.saldo || 0) + mov.quantidade;

    // Verificar estoque negativo
    const docRef = doc(db, "parametros", "sistema");
    const docSnap = await getDoc(docRef);
    const parametros = docSnap.exists() ? docSnap.data() : {};
    const permitirEstoqueNegativo = parametros.permitirEstoqueNegativo || false;

    if (!permitirEstoqueNegativo && novoSaldo < 0) {
      throw new Error('Saldo ficaria negativo');
    }

    // Criar movimentação de estorno
    const estornoMovimentacao = {
      produtoId: mov.produtoId,
      tipo: mov.tipo === 'ENTRADA' ? 'SAIDA' : 'ENTRADA',
      quantidade: mov.quantidade,
      unidade: mov.unidade,
      quantidadeOriginal: mov.quantidadeOriginal || mov.quantidade,
      unidadeOriginal: mov.unidadeOriginal || mov.unidade,
      valorUnitario: mov.valorUnitario || 0,
      tipoDocumento: 'ESTORNO',
      numeroDocumento: `EST-${movementId}`,
      observacoes: `Estorno da movimentação ${mov.tipoDocumento} ${mov.numeroDocumento}${isOPMovement ? ' (OP)' : ''}. Motivo: ${estornoReason}`,
      dataHora: Timestamp.now(),
      estornoDe: movementId,
      [mov.tipo === 'ENTRADA' ? 'armazemOrigemId' : 'armazemDestinoId']: armazemId
    };

    // Atualizar estoque
    if (estoque) {
      await updateDoc(doc(db, "estoques", estoque.id), {
        saldo: novoSaldo,
        ultimaMovimentacao: Timestamp.now()
      });
    } else if (mov.tipo === 'SAIDA') {
      await addDoc(collection(db, "estoques"), {
        produtoId: mov.produtoId,
        armazemId: armazemId,
        saldo: novoSaldo,
        ultimaMovimentacao: Timestamp.now()
      });
    }

    // Tratamento especial para estornos relacionados a OP (mesma lógica da função principal)
    if (mov.observacoes && mov.observacoes.includes('OP')) {
      const opMatch = mov.observacoes.match(/OP(\d+)/);
      if (opMatch) {
        const opNumero = 'OP' + opMatch[1];

        // Se for estorno de ENTRADA de transferência para OP, reduzir saldoReservado na OP
        if (mov.tipo === 'ENTRADA' && mov.observacoes.includes('Transferência para OP')) {
          const opSnap = await getDoc(doc(db, "ordensProducao", opNumero));
          if (opSnap.exists()) {
            const opData = opSnap.data();
            const materiaisNecessarios = opData.materiaisNecessarios || [];
            const updatedMateriais = materiaisNecessarios.map(m => {
              if (m.produtoId === mov.produtoId) {
                return { ...m, saldoReservado: Math.max(0, (m.saldoReservado || 0) - mov.quantidade) };
              }
              return m;
            });
            await updateDoc(doc(db, "ordensProducao", opNumero), {
              materiaisNecessarios: updatedMateriais
            });
          }
        }

        // Se for estorno de ENTRADA de produção (produto acabado), reduzir quantidadeProduzida na OP
        if (mov.tipo === 'ENTRADA' && (mov.observacoes.includes('Produção OP') || mov.observacoes.includes('PRODUCAO OP'))) {
          const opSnap = await getDoc(doc(db, "ordensProducao", opNumero));
          if (opSnap.exists()) {
            const opData = opSnap.data();
            const novaQuantidadeProduzida = Math.max(0, (opData.quantidadeProduzida || 0) - mov.quantidade);

            // Recalcular status da OP
            let novoStatus = opData.status;
            if (novaQuantidadeProduzida === 0) {
              novoStatus = 'Pendente';
            } else if (novaQuantidadeProduzida < opData.quantidade && opData.status === 'Concluída') {
              novoStatus = 'Em Produção';
            }

            await updateDoc(doc(db, "ordensProducao", opNumero), {
              quantidadeProduzida: novaQuantidadeProduzida,
              status: novoStatus,
              ultimaAtualizacao: Timestamp.now()
            });

            console.log(`OP ${opNumero}: Quantidade produzida reduzida de ${opData.quantidadeProduzida || 0} para ${novaQuantidadeProduzida}. Status: ${novoStatus}`);
          }
        }
      }
    }

    // Adicionar estorno e marcar original como estornada
    await addDoc(collection(db, "movimentacoesEstoque"), estornoMovimentacao);
    await updateDoc(doc(db, "movimentacoesEstoque", movementId), {
      estornada: true,
      estornoData: Timestamp.now(),
      estornoMotivo: estornoReason
    });
  }

  // Função para corrigir OP manualmente quando movimentação já foi estornada mas OP não foi atualizada
  window.corrigirOPManualmente = async function(movId) {
    const mov = movimentacoes.find(m => m.id === movId);
    if (!mov) {
      alert('Movimentação não encontrada!');
      return;
    }

    // Verificar se é movimentação de produção estornada
    if (!mov.estornada || mov.tipo !== 'ENTRADA' ||
        !mov.observacoes || !mov.observacoes.includes('OP')) {
      alert('Esta função é apenas para movimentações de produção já estornadas!');
      return;
    }

    const opMatch = mov.observacoes.match(/OP(\d+)/);
    if (!opMatch) {
      alert('Não foi possível identificar o número da OP!');
      return;
    }

    const opNumero = 'OP' + opMatch[1];

    if (!confirm(`Deseja corrigir manualmente a OP ${opNumero}?\n\nEsta ação irá:\n- Reduzir a quantidade produzida em ${mov.quantidade}\n- Recalcular o status da OP\n\nContinuar?`)) {
      return;
    }

    try {
      const opSnap = await getDoc(doc(db, "ordensProducao", opNumero));
      if (!opSnap.exists()) {
        alert('OP não encontrada no banco de dados!');
        return;
      }

      const opData = opSnap.data();
      const novaQuantidadeProduzida = Math.max(0, (opData.quantidadeProduzida || 0) - mov.quantidade);

      // Recalcular status da OP
      let novoStatus = opData.status;
      if (novaQuantidadeProduzida === 0) {
        novoStatus = 'Pendente';
      } else if (novaQuantidadeProduzida < opData.quantidade && opData.status === 'Concluída') {
        novoStatus = 'Em Produção';
      }

      await updateDoc(doc(db, "ordensProducao", opNumero), {
        quantidadeProduzida: novaQuantidadeProduzida,
        status: novoStatus,
        ultimaAtualizacao: Timestamp.now()
      });

      alert(`✅ OP ${opNumero} corrigida com sucesso!\n\nQuantidade produzida: ${opData.quantidadeProduzida || 0} → ${novaQuantidadeProduzida}\nStatus: ${opData.status} → ${novoStatus}`);

    } catch (error) {
      console.error('Erro ao corrigir OP:', error);
      alert('Erro ao corrigir OP: ' + error.message);
    }
  };

  // Função para zerar completamente a quantidade produzida de uma OP
  window.zerarQuantidadeOP = async function() {
    const opNumero = prompt('Digite o número da OP para zerar a quantidade produzida (ex: OP25050216):');
    if (!opNumero || !opNumero.trim()) {
      return;
    }

    const opFormatada = opNumero.toUpperCase().startsWith('OP') ? opNumero.toUpperCase() : 'OP' + opNumero;

    if (!confirm(`⚠️ ATENÇÃO: Deseja ZERAR completamente a quantidade produzida da ${opFormatada}?\n\nEsta ação irá:\n- Definir quantidade produzida = 0\n- Alterar status para 'Pendente'\n- Não pode ser desfeita!\n\nContinuar?`)) {
      return;
    }

    try {
      const opSnap = await getDoc(doc(db, "ordensProducao", opFormatada));
      if (!opSnap.exists()) {
        alert(`❌ OP ${opFormatada} não encontrada no banco de dados!`);
        return;
      }

      const opData = opSnap.data();

      await updateDoc(doc(db, "ordensProducao", opFormatada), {
        quantidadeProduzida: 0,
        status: 'Pendente',
        ultimaAtualizacao: Timestamp.now()
      });

      alert(`✅ ${opFormatada} zerada com sucesso!\n\nQuantidade produzida: ${opData.quantidadeProduzida || 0} → 0\nStatus: ${opData.status} → Pendente\n\nVerifique no apontamentos.html`);

    } catch (error) {
      console.error('Erro ao zerar OP:', error);
      alert('Erro ao zerar OP: ' + error.message);
    }
  };

  window.excluirMovimentacao = async function(id) {
    if (!confirm('Tem certeza que deseja excluir esta movimentação?')) return;
    try {
      await deleteDoc(doc(db, 'movimentacoesEstoque', id));
    } catch (e) {
      alert('Erro ao excluir: ' + e.message);
    }
  };

  window.onclick = function(event) {
    const modal = document.getElementById('estornoModal');
    if (event.target === modal) closeModal();
  };
</script>
</body>
</html>