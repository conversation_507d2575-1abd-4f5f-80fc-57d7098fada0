<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCP - Planejamento e Controle de Produção</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script type="module" src="./firebase-config.js"></script>
    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        
        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.Timestamp = Timestamp;
    </script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --primary-hover: #34495e;
            --secondary-color: #3498db;
            --secondary-hover: #2980b9;
            --success-color: #27ae60;
            --success-hover: #229954;
            --warning-color: #f39c12;
            --warning-hover: #e67e22;
            --danger-color: #e74c3c;
            --danger-hover: #c0392b;
            --info-color: #17a2b8;
            --info-hover: #138496;
            --light-bg: #ecf0f1;
            --border-color: #bdc3c7;
            --text-color: #2c3e50;
            --text-secondary: #7f8c8d;
            --shadow: 0 4px 15px rgba(0,0,0,0.1);
            --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-header: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .header {
            background: var(--gradient-header);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1::before {
            content: "🏭";
            font-size: 32px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        /* Botões modernos */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 2px solid var(--border-color);
        }

        .main-content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 3px solid var(--light-bg);
            margin-bottom: 30px;
            overflow-x: auto;
            gap: 5px;
        }

        .tab {
            padding: 15px 25px;
            border: none;
            background: var(--light-bg);
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab.active {
            background: var(--gradient-header);
            color: white;
            transform: translateY(-2px);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Cards de resumo modernos */
        .summary-section {
            margin: 25px 0;
            padding: 25px;
            background: var(--light-bg);
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .summary-grid, .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-item, .stat-card {
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: var(--shadow);
            border-left: 4px solid var(--secondary-color);
            transition: all 0.3s ease;
            text-align: center;
        }

        .summary-item:hover, .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-hover);
        }

        .summary-item h3, .stat-label {
            margin: 0 0 10px 0;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .summary-value, .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .stat-icon {
            font-size: 20px;
            margin-bottom: 10px;
        }

        /* Filtros modernos */
        .filters {
            background: var(--light-bg);
            padding: 25px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: var(--shadow);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 600;
            font-size: 14px;
        }

        .filter-group input, .filter-group select {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        /* Tabela moderna */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin: 25px 0;
        }

        .data-table, .orders-table, .table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th, .orders-table th, .table th {
            background: var(--gradient-header);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table td, .orders-table td, .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .data-table tbody tr, .orders-table tbody tr, .table tbody tr {
            transition: all 0.3s ease;
        }

        .data-table tbody tr:hover, .orders-table tbody tr:hover, .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
        }

        /* Alertas modernos */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            border: 1px solid;
        }

        .alert-info {
            background: #e3f2fd;
            color: #1976d2;
            border-color: #bbdefb;
        }

        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border-color: #c8e6c9;
        }

        .alert-warning {
            background: #fff3e0;
            color: #f57c00;
            border-color: #ffcc02;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Status badges modernos */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
            display: inline-block;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-aberto {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-recebido {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-cancelado {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-andamento, .status-em_producao {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-concluida {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        /* Classes de prioridade */
        .priority-high, .priority-alta {
            color: var(--danger-color);
            font-weight: bold;
        }

        .priority-medium, .priority-media {
            color: var(--warning-color);
            font-weight: bold;
        }

        .priority-low, .priority-baixa {
            color: var(--success-color);
            font-weight: bold;
        }

        /* Classes de texto auxiliares */
        .text-success {
            color: var(--success-color) !important;
        }

        .text-warning {
            color: var(--warning-color) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        .text-muted {
            color: var(--text-secondary) !important;
        }

        /* Loading e animações */
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 20px;
            }

            .main-content {
                padding: 20px;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .table-container {
                overflow-x: auto;
            }

            .stats-grid, .summary-grid {
                grid-template-columns: 1fr;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-industry"></i>
                PCP - Planejamento e Controle de Produção
            </h1>
            <div class="header-actions">
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="tabs">
                <button class="tab active" onclick="switchTab('disponibilidade')">
                    <i class="fas fa-boxes"></i>
                    Disponibilidade de Materiais
                </button>
                <button class="tab" onclick="switchTab('planejamento')">
                    <i class="fas fa-calendar-alt"></i>
                    Planejamento de OPs
                </button>
                <button class="tab" onclick="switchTab('viabilidade')">
                    <i class="fas fa-rocket"></i>
                    Viabilidade de OPs
                </button>
                <button class="tab" onclick="switchTab('simulacao')">
                    <i class="fas fa-brain"></i>
                    Simulação Inteligente
                </button>
                <button class="tab" onclick="switchTab('analise')">
                    <i class="fas fa-chart-line"></i>
                    Análise de Viabilidade
                </button>
                <button class="tab" onclick="switchTab('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard PCP
                </button>
            </div>

            <!-- Tab: Disponibilidade de Materiais -->
            <div id="disponibilidadeTab" class="tab-content active">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Disponibilidade de Materiais:</strong> Analise quais produtos em estoque podem ser utilizados nas OPs pendentes e identifique necessidades de compra.
                    <br><br>
                    <i class="fas fa-warehouse"></i>
                    <strong>Filtro por Armazém:</strong> Selecione um armazém específico para analisar apenas o estoque daquele local, ou deixe em "Todos os Armazéns" para ver o saldo consolidado.
                </div>

                <!-- Status de Carregamento de Dados -->
                <div id="dataLoadingStatus" class="alert alert-warning" style="margin-bottom: 20px;">
                    <i class="fas fa-spinner fa-spin"></i>
                    <strong>Status do Sistema:</strong> <span id="loadingStatusText">Carregando dados...</span>
                    <div id="dataCounters" style="margin-top: 10px; font-size: 14px;">
                        <span id="produtosCount">📦 Produtos: 0</span> |
                        <span id="estoquesCount">📊 Estoques: 0</span> |
                        <span id="armazensCount">🏭 Armazéns: 0</span> |
                        <span id="opsCount">📋 OPs: 0</span> |
                        <span id="estruturasCount">🔧 Estruturas: 0</span>
                    </div>
                </div>

                <div class="alert alert-success" id="partialProductionInfo" style="display: none; margin-bottom: 20px;">
                    <i class="fas fa-percentage"></i>
                    <strong>Modo Produção Parcial Ativo:</strong> Visualizando percentual de produção possível com estoque atual.
                    Use os botões <i class="fas fa-percentage"></i> para sugestões específicas de cada material.
                </div>

                <div class="filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Filtrar por OP:</label>
                            <select id="opFilter" class="form-control" onchange="filterByOP()">
                                <option value="">Todas as OPs</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Filtrar por Armazém:</label>
                            <select id="warehouseFilter" class="form-control" onchange="loadMaterialAvailability()">
                                <option value="">Todos os Armazéns</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Filtrar por Produto:</label>
                            <input type="text" id="productFilter" class="form-control" placeholder="Código ou descrição..." oninput="filterAvailableProducts()">
                        </div>
                        <div class="form-group">
                            <label>Simular Produção:</label>
                            <div style="display: flex; gap: 5px;">
                                <input type="text" id="productCodeSimulation" class="form-control" placeholder="Código do produto..." style="flex: 2;">
                                <input type="number" id="quantitySimulation" class="form-control" placeholder="Qtd" min="1" value="1" style="flex: 1;">
                                <button class="btn btn-success" onclick="simulateProduction()" title="Simular produção">
                                    <i class="fas fa-calculator"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Status do Material:</label>
                            <select id="materialStatus" class="form-control" onchange="filterAvailableProducts()">
                                <option value="all">Todos</option>
                                <option value="available">Disponível</option>
                                <option value="insufficient">Insuficiente</option>
                                <option value="missing">Em Falta</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Análise de Produção:</label>
                            <select id="productionAnalysis" class="form-control" onchange="toggleProductionAnalysis()">
                                <option value="standard">Padrão</option>
                                <option value="partial">Produção Parcial</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="loadMaterialAvailability()">
                                <i class="fas fa-sync"></i>
                                Atualizar Análise
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-info" onclick="showDataDetails()" title="Ver detalhes dos dados carregados">
                                <i class="fas fa-info-circle"></i>
                                Verificar Dados
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="analyzePartialProductionForAllOPs()" title="Analisar produção parcial para todas as OPs">
                                <i class="fas fa-percentage"></i>
                                Análise Parcial
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-warning" onclick="debugOPsLoading()" title="Diagnosticar problema de carregamento de OPs">
                                <i class="fas fa-bug"></i>
                                Debug OPs
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-secondary" onclick="createExampleOPs()" title="Criar OPs de exemplo para teste">
                                <i class="fas fa-plus"></i>
                                Criar OPs Teste
                            </button>
                        </div>
                    </div>
                </div>

                <div class="stats-grid" id="materialStats" style="display: none;">
                    <div class="stat-card" style="border-left: 4px solid #3498db;">
                        <div class="stat-number" id="totalOPs" style="color: #3498db;">0</div>
                        <div class="stat-label">OPs Pendentes</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #27ae60;">
                        <div class="stat-number" id="materialsAvailable" style="color: #27ae60;">0</div>
                        <div class="stat-label">Materiais Disponíveis</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #f39c12;">
                        <div class="stat-number" id="materialsInsufficient" style="color: #f39c12;">0</div>
                        <div class="stat-label">Materiais Insuficientes</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #e74c3c;">
                        <div class="stat-number" id="materialsMissing" style="color: #e74c3c;">0</div>
                        <div class="stat-label">Materiais em Falta</div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Armazém</th>
                                <th>Saldo Atual</th>
                                <th>Necessário</th>
                                <th>Status</th>
                                <th id="partialProductionHeader" style="display: none;">% Produção Possível</th>
                                <th>OPs Relacionadas</th>
                                <th>Prioridade</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="materialAvailabilityTableBody">
                            <tr>
                                <td colspan="10" class="loading">
                                    <i class="fas fa-info-circle"></i>
                                    Clique em "Atualizar Análise" para carregar a disponibilidade de materiais
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Tab: Viabilidade de OPs -->
            <div id="viabilidadeTab" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-rocket"></i>
                    <strong>Viabilidade de OPs PENDENTES:</strong> Analise quais Ordens de Produção com status "Pendente" podem ser iniciadas com base na disponibilidade atual de materiais.
                    <br><br>
                    <i class="fas fa-percentage"></i>
                    <strong>Produção Parcial:</strong> Identifique OPs pendentes que podem ser iniciadas parcialmente, otimizando o uso dos recursos disponíveis.
                    <br><br>
                    <i class="fas fa-info-circle"></i>
                    <strong>Foco Inteligente:</strong> Apenas OPs "Pendente" são analisadas, pois são as que precisam de decisão para iniciar. OPs em produção, concluídas ou canceladas não precisam desta análise.
                </div>

                <div class="filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="statusInfo">Status Analisado:</label>
                            <div style="padding: 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; color: #0066cc;">
                                <i class="fas fa-filter"></i> <strong>Apenas OPs PENDENTES</strong>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="viabilityFilter">Filtro de Viabilidade:</label>
                            <select id="viabilityFilter" onchange="filterOPViability()">
                                <option value="all">Todas as OPs Pendentes</option>
                                <option value="viable">Viáveis (100%)</option>
                                <option value="partial">Parciais (1-99%)</option>
                                <option value="blocked">Bloqueadas (0%)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="priorityFilter">Filtro de Prioridade:</label>
                            <select id="priorityFilter" onchange="filterOPViability()">
                                <option value="all">Todas as Prioridades</option>
                                <option value="alta">Alta Prioridade</option>
                                <option value="media">Média Prioridade</option>
                                <option value="baixa">Baixa Prioridade</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="analyzeOPViability()">
                                <i class="fas fa-search"></i>
                                Analisar Viabilidade
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="generateViabilityReport()">
                                <i class="fas fa-file-alt"></i>
                                Relatório Detalhado
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-info" onclick="generateSequencingReport()">
                                <i class="fas fa-sort-numeric-down"></i>
                                Sequenciamento BOM
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Estatísticas de Viabilidade -->
                <div id="viabilityStats" class="stats-grid" style="display: none;">
                    <div class="stat-card viable">
                        <div class="stat-icon">🟢</div>
                        <div class="stat-content">
                            <div class="stat-number" id="viableOPs">0</div>
                            <div class="stat-label">OPs Viáveis</div>
                        </div>
                    </div>
                    <div class="stat-card partial">
                        <div class="stat-icon">🟡</div>
                        <div class="stat-content">
                            <div class="stat-number" id="partialOPs">0</div>
                            <div class="stat-label">OPs Parciais</div>
                        </div>
                    </div>
                    <div class="stat-card blocked">
                        <div class="stat-icon">🔴</div>
                        <div class="stat-content">
                            <div class="stat-number" id="blockedOPs">0</div>
                            <div class="stat-label">OPs Bloqueadas</div>
                        </div>
                    </div>
                    <div class="stat-card priority">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <div class="stat-number" id="priorityOPs">0</div>
                            <div class="stat-label">Alta Prioridade</div>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>OP</th>
                                <th>Produto</th>
                                <th>Nível BOM</th>
                                <th>Dependências</th>
                                <th>Quantidade</th>
                                <th>Status</th>
                                <th>Viabilidade</th>
                                <th>% Possível</th>
                                <th>Materiais Faltantes</th>
                                <th>Prioridade</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="viabilityTableBody">
                            <tr>
                                <td colspan="9" class="loading">
                                    <i class="fas fa-info-circle"></i>
                                    Clique em "Analisar Viabilidade" para verificar quais OPs podem ser iniciadas
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Tab: Simulação Inteligente -->
            <div id="simulacaoTab" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-brain"></i>
                    <strong>Simulação Inteligente:</strong> Digite o código de um produto PA ou SP para fazer explosão de BOM e analisar viabilidade de produção.
                    <br><br>
                    <i class="fas fa-cogs"></i>
                    <strong>Análise Avançada:</strong> Veja saldos disponíveis, materiais reservados, percentual de produção possível e sugestões inteligentes.
                </div>

                <div class="filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="intelligentProductCode">Código do Produto (PA/SP):</label>
                            <input type="text" id="intelligentProductCode" placeholder="Digite o código do produto..."
                                   style="text-transform: uppercase;" onkeyup="this.value = this.value.toUpperCase()">
                        </div>
                        <div class="form-group">
                            <label for="intelligentQuantity">Quantidade a Produzir:</label>
                            <input type="number" id="intelligentQuantity" value="1" min="1" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="intelligentWarehouse">Armazém:</label>
                            <select id="intelligentWarehouse">
                                <option value="">Todos os Armazéns</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="runIntelligentSimulation()">
                                <i class="fas fa-brain"></i>
                                Simular Produção
                            </button>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="reservationMode">Modo de Reserva:</label>
                            <select id="reservationMode">
                                <option value="consider">Considerar Reservas</option>
                                <option value="ignore">Ignorar Reservas</option>
                                <option value="only">Apenas Disponível</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="suggestOptimalProduction()">
                                <i class="fas fa-lightbulb"></i>
                                Sugestão Inteligente
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-warning" onclick="reserveMaterials()">
                                <i class="fas fa-lock"></i>
                                Reservar Materiais
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Resultado da Simulação -->
                <div id="simulationResult" style="display: none;">
                    <div class="alert alert-success">
                        <h4 id="simulationTitle">Resultado da Simulação</h4>
                        <div id="simulationSummary"></div>
                    </div>

                    <!-- Estatísticas da Simulação -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📦</div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalMaterials">0</div>
                                <div class="stat-label">Total de Materiais</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">✅</div>
                            <div class="stat-content">
                                <div class="stat-number" id="availableMaterials">0</div>
                                <div class="stat-label">Disponíveis</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🔒</div>
                            <div class="stat-content">
                                <div class="stat-number" id="reservedMaterials">0</div>
                                <div class="stat-label">Reservados</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📊</div>
                            <div class="stat-content">
                                <div class="stat-number" id="productionPercentage">0%</div>
                                <div class="stat-label">Produção Possível</div>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Material</th>
                                    <th>Descrição</th>
                                    <th>Necessário</th>
                                    <th>Saldo Total</th>
                                    <th>Reservado</th>
                                    <th>Disponível</th>
                                    <th>Status</th>
                                    <th>% Atende</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="simulationTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Tab: Planejamento de OPs -->
            <div id="planejamentoTab" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-calendar-alt"></i>
                    <strong>Planejamento de OPs:</strong> Organize e priorize as Ordens de Produção baseado na disponibilidade de materiais e prazos.
                </div>

                <div class="filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Ordenar por:</label>
                            <select id="sortBy" class="form-control" onchange="sortOPs()">
                                <option value="priority">Prioridade</option>
                                <option value="date">Data de Entrega</option>
                                <option value="availability">Disponibilidade de Material</option>
                                <option value="value">Valor da OP</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Status:</label>
                            <select id="statusFilter" class="form-control" onchange="filterOPs()">
                                <option value="all">Todos</option>
                                <option value="PENDENTE">Pendente</option>
                                <option value="EM_ANDAMENTO">Em Andamento</option>
                                <option value="PAUSADA">Pausada</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Viabilidade:</label>
                            <select id="viabilityFilter" class="form-control" onchange="filterOPs()">
                                <option value="all">Todas</option>
                                <option value="viable">Viáveis</option>
                                <option value="partial">Parcialmente Viáveis</option>
                                <option value="blocked">Bloqueadas</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="loadOPPlanning()">
                                <i class="fas fa-sync"></i>
                                Atualizar Planejamento
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>OP</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Data Entrega</th>
                                <th>Status</th>
                                <th>Viabilidade</th>
                                <th>Materiais Faltantes</th>
                                <th>Prioridade</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="opPlanningTableBody">
                            <tr>
                                <td colspan="9" class="loading">
                                    <i class="fas fa-info-circle"></i>
                                    Clique em "Atualizar Planejamento" para carregar as OPs
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Tab: Análise de Viabilidade -->
            <div id="analiseTab" class="tab-content">
                <div class="alert alert-warning">
                    <i class="fas fa-chart-line"></i>
                    <strong>Análise de Viabilidade:</strong> Análise detalhada da viabilidade de produção baseada em materiais, recursos e prazos.
                </div>

                <div class="stats-grid" id="viabilityStats" style="display: none;">
                    <div class="stat-card" style="border-left: 4px solid #27ae60;">
                        <div class="stat-number" id="viableOPs" style="color: #27ae60;">0</div>
                        <div class="stat-label">OPs Viáveis</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #f39c12;">
                        <div class="stat-number" id="partialOPs" style="color: #f39c12;">0</div>
                        <div class="stat-label">Parcialmente Viáveis</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #e74c3c;">
                        <div class="stat-number" id="blockedOPs" style="color: #e74c3c;">0</div>
                        <div class="stat-label">OPs Bloqueadas</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #3498db;">
                        <div class="stat-number" id="totalValue" style="color: #3498db;">R$ 0</div>
                        <div class="stat-label">Valor Total</div>
                    </div>
                </div>

                <div class="filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Período de Análise:</label>
                            <select id="analysisPeriod" class="form-control" onchange="updateViabilityAnalysis()">
                                <option value="7">Próximos 7 dias</option>
                                <option value="15">Próximos 15 dias</option>
                                <option value="30" selected>Próximos 30 dias</option>
                                <option value="60">Próximos 60 dias</option>
                                <option value="all">Todas</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Tipo de Análise:</label>
                            <select id="analysisType" class="form-control" onchange="updateViabilityAnalysis()">
                                <option value="materials">Por Materiais</option>
                                <option value="resources">Por Recursos</option>
                                <option value="timeline">Por Cronograma</option>
                                <option value="complete">Análise Completa</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-info" onclick="runViabilityAnalysis()">
                                <i class="fas fa-analytics"></i>
                                Executar Análise
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="exportAnalysisReport()">
                                <i class="fas fa-file-excel"></i>
                                Exportar Relatório
                            </button>
                        </div>
                    </div>
                </div>

                <div id="analysisResults" style="display: none;">
                    <h4 style="margin-bottom: 20px; color: #2c3e50;">
                        <i class="fas fa-chart-pie"></i>
                        Resultados da Análise
                    </h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>OP</th>
                                    <th>Produto</th>
                                    <th>Viabilidade</th>
                                    <th>Bloqueios</th>
                                    <th>Recomendações</th>
                                    <th>Impacto</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="viabilityAnalysisTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Tab: Dashboard PCP -->
            <div id="dashboardTab" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-tachometer-alt"></i>
                    <strong>Dashboard PCP:</strong> Visão geral do status da produção, indicadores de performance e alertas críticos.
                </div>

                <div class="stats-grid" id="dashboardStats">
                    <div class="stat-card" style="border-left: 4px solid #3498db;">
                        <div class="stat-number" id="totalOPsDash" style="color: #3498db;">0</div>
                        <div class="stat-label">Total de OPs</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #27ae60;">
                        <div class="stat-number" id="onTimeOPs" style="color: #27ae60;">0</div>
                        <div class="stat-label">No Prazo</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #f39c12;">
                        <div class="stat-number" id="delayedOPs" style="color: #f39c12;">0</div>
                        <div class="stat-label">Atrasadas</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #e74c3c;">
                        <div class="stat-number" id="criticalOPs" style="color: #e74c3c;">0</div>
                        <div class="stat-label">Críticas</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #9b59b6;">
                        <div class="stat-number" id="productionEfficiency" style="color: #9b59b6;">0%</div>
                        <div class="stat-label">Eficiência</div>
                    </div>
                    <div class="stat-card" style="border-left: 4px solid #17a2b8;">
                        <div class="stat-number" id="materialUtilization" style="color: #17a2b8;">0%</div>
                        <div class="stat-label">Utilização de Material</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
                    <div class="table-container">
                        <h4 style="padding: 15px; margin: 0; background: #34495e; color: white;">
                            <i class="fas fa-exclamation-triangle"></i>
                            Alertas Críticos
                        </h4>
                        <div id="criticalAlerts" style="padding: 20px; max-height: 300px; overflow-y: auto;">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                Carregando alertas...
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <h4 style="padding: 15px; margin: 0; background: #34495e; color: white;">
                            <i class="fas fa-clock"></i>
                            Próximas Entregas
                        </h4>
                        <div id="upcomingDeliveries" style="padding: 20px; max-height: 300px; overflow-y: auto;">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                Carregando entregas...
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="loadDashboard()">
                        <i class="fas fa-sync"></i>
                        Atualizar Dashboard
                    </button>
                    <button class="btn btn-success" onclick="exportDashboardReport()">
                        <i class="fas fa-file-pdf"></i>
                        Exportar Relatório
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variáveis globais
        let produtos = [];
        let estoques = [];
        let armazens = [];
        let ordensProducao = [];
        let estruturas = [];
        let materialAvailabilityData = [];
        let opPlanningData = [];

        // Inicialização
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await loadInitialData();
                await loadWarehouseFilter();
                await loadOPFilter();
                await loadDashboard();
            } catch (error) {
                console.error('Erro na inicialização:', error);
                showError('Erro ao carregar sistema: ' + error.message);
            }
        });

        // Função para carregar filtro de OPs
        async function loadOPFilter() {
            try {
                const opFilter = document.getElementById('opFilter');
                if (!opFilter) {
                    console.warn('⚠️ Elemento opFilter não encontrado');
                    return;
                }

                // Limpar opções existentes
                opFilter.innerHTML = '';

                // Adicionar opção padrão
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Todas as OPs';
                opFilter.appendChild(defaultOption);

                console.log('🔄 Carregando filtro de OPs...');
                console.log('📋 OPs disponíveis:', ordensProducao.length);

                if (ordensProducao.length === 0) {
                    console.warn('⚠️ Nenhuma OP encontrada para o filtro');
                    const option = document.createElement('option');
                    option.value = 'no-ops';
                    option.textContent = 'Nenhuma OP encontrada';
                    option.disabled = true;
                    option.style.color = '#dc3545';
                    opFilter.appendChild(option);
                    return;
                }

                ordensProducao.forEach((op, index) => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    const option = document.createElement('option');
                    option.value = op.id;
                    option.textContent = `OP ${op.numero || op.id} - ${produto?.codigo || 'Produto não encontrado'} (${op.status || 'SEM_STATUS'})`;
                    opFilter.appendChild(option);

                    if (index < 3) { // Log das primeiras 3 OPs
                        console.log(`📋 OP ${index + 1}: ${op.numero || op.id} - Produto: ${produto?.codigo || 'NÃO_ENCONTRADO'} - Status: ${op.status || 'SEM_STATUS'}`);
                    }
                });

                console.log('✅ Filtro de OPs carregado:', ordensProducao.length, 'OPs');
            } catch (error) {
                console.error('❌ Erro ao carregar filtro de OPs:', error);
            }
        }

        // Função para carregar filtro de armazéns
        async function loadWarehouseFilter() {
            try {
                const warehouseFilter = document.getElementById('warehouseFilter');
                if (!warehouseFilter) return;

                warehouseFilter.innerHTML = '<option value="">Todos os Armazéns</option>';

                armazens.forEach(armazem => {
                    const option = document.createElement('option');
                    option.value = armazem.id;
                    option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                    warehouseFilter.appendChild(option);
                });

                console.log('✅ Filtro de armazéns carregado:', armazens.length, 'armazéns');
            } catch (error) {
                console.error('❌ Erro ao carregar filtro de armazéns:', error);
            }
        }

        // Função para carregar dados iniciais
        async function loadInitialData() {
            try {
                updateLoadingStatus('🔄 Conectando ao Firebase...', 'warning');

                // Carregar dados um por vez para melhor diagnóstico
                console.log('🔄 Carregando produtos...');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ Produtos carregados: ${produtos.length}`);

                console.log('🔄 Carregando estoques...');
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ Estoques carregados: ${estoques.length}`);

                console.log('🔄 Carregando armazéns...');
                const armazensSnap = await getDocs(collection(db, "armazens"));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ Armazéns carregados: ${armazens.length}`);

                console.log('🔄 Carregando estruturas...');
                const estruturasSnap = await getDocs(collection(db, "estruturas"));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ Estruturas carregadas: ${estruturas.length}`);

                updateLoadingStatus('📊 Carregando ordens de produção...', 'warning');

                // Carregar todas as OPs (igual ao relatorio_necessidades_compras.html)
                console.log('🔄 Carregando todas as ordens de produção...');
                try {
                    const opsSnapAll = await getDocs(collection(db, "ordensProducao"));
                    ordensProducao = opsSnapAll.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`✅ Ordens de Produção carregadas: ${ordensProducao.length}`, ordensProducao.map(op => ({id: op.id, numero: op.numero, status: op.status})));

                    // Log de todas as OPs para debug
                    if (ordensProducao.length > 0) {
                        console.log('📋 Primeiras 5 OPs encontradas:');
                        ordensProducao.slice(0, 5).forEach((op, index) => {
                            console.log(`   ${index + 1}. ID: ${op.id}, Status: ${op.status || 'SEM_STATUS'}, Número: ${op.numero || 'SEM_NUMERO'}, Materiais: ${op.materiaisNecessarios?.length || 0}`);
                        });
                    }

                } catch (opsError) {
                    console.error('❌ Erro ao carregar OPs:', opsError);

                    console.log('🔄 Criando OPs de exemplo...');
                    ordensProducao = [
                        {
                            id: 'op_exemplo_001',
                            numero: 'EX001',
                            produtoId: produtos[0]?.id || 'produto_exemplo',
                            quantidade: 100,
                            status: 'Pendente',
                            dataInicio: new Date(),
                            dataFim: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                            tipo: 'EXEMPLO',
                            materiaisNecessarios: []
                        },
                        {
                            id: 'op_exemplo_002',
                            numero: 'EX002',
                            produtoId: produtos[1]?.id || 'produto_exemplo_2',
                            quantidade: 50,
                            status: 'Em Produção',
                            dataInicio: new Date(),
                            dataFim: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
                            tipo: 'EXEMPLO',
                            materiaisNecessarios: []
                        }
                    ];
                    console.log(`✅ OPs de exemplo criadas: ${ordensProducao.length}`);
                }

                // Atualizar contadores
                updateDataCounters();

                // Log detalhado final
                console.log('✅ DADOS CARREGADOS COM SUCESSO:');
                console.log(`📦 Produtos: ${produtos.length}`);
                console.log(`📊 Estoques: ${estoques.length}`);
                console.log(`🏭 Armazéns: ${armazens.length}`);
                console.log(`📋 OPs: ${ordensProducao.length}`);
                console.log(`🔧 Estruturas: ${estruturas.length}`);

                // Verificar se há dados críticos
                if (produtos.length === 0) {
                    console.warn('⚠️ ATENÇÃO: Nenhum produto encontrado!');
                }
                if (estoques.length === 0) {
                    console.warn('⚠️ ATENÇÃO: Nenhum estoque encontrado!');
                }
                if (ordensProducao.length === 0) {
                    console.warn('⚠️ ATENÇÃO: Nenhuma OP encontrada! Use a simulação para testar.');
                }
                if (estruturas.length === 0) {
                    console.warn('⚠️ ATENÇÃO: Nenhuma estrutura (BOM) encontrada!');
                }

                updateLoadingStatus('✅ Sistema pronto para uso!', 'success');

            } catch (error) {
                console.error('❌ ERRO AO CARREGAR DADOS:', error);
                updateLoadingStatus('❌ Erro ao carregar dados: ' + error.message, 'danger');
                throw error;
            }
        }

        // Função para atualizar status de carregamento
        function updateLoadingStatus(message, type) {
            const statusDiv = document.getElementById('dataLoadingStatus');
            const statusText = document.getElementById('loadingStatusText');

            if (statusDiv && statusText) {
                statusText.textContent = message;

                // Remover classes anteriores
                statusDiv.className = 'alert';

                // Adicionar nova classe
                switch(type) {
                    case 'success':
                        statusDiv.classList.add('alert-success');
                        break;
                    case 'danger':
                        statusDiv.classList.add('alert-danger');
                        break;
                    case 'warning':
                    default:
                        statusDiv.classList.add('alert-warning');
                        break;
                }
            }
        }

        // Função para atualizar contadores de dados
        function updateDataCounters() {
            const counters = {
                produtosCount: produtos.length,
                estoquesCount: estoques.length,
                armazensCount: armazens.length,
                opsCount: ordensProducao.length,
                estruturasCount: estruturas.length
            };

            Object.entries(counters).forEach(([id, count]) => {
                const element = document.getElementById(id);
                if (element) {
                    const icon = element.textContent.split(' ')[0]; // Manter o ícone
                    const label = element.textContent.split(':')[0].substring(2); // Manter o label
                    element.textContent = `${icon} ${label}: ${count}`;

                    // Colorir baseado na quantidade
                    if (count === 0) {
                        element.style.color = '#dc3545'; // Vermelho
                        element.style.fontWeight = 'bold';
                    } else if (count < 5) {
                        element.style.color = '#ffc107'; // Amarelo
                    } else {
                        element.style.color = '#28a745'; // Verde
                    }
                }
            });
        }

        // Função para alternar abas
        window.switchTab = function(tabName) {
            // Remover classe active de todas as abas
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Adicionar classe active na aba selecionada
            document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');

            // Carregar dados específicos da aba
            switch (tabName) {
                case 'analise':
                    if (materialAvailabilityData.length === 0) {
                        console.log('Carregando dados da análise...');
                    }
                    break;
                case 'viabilidade':
                    // Carregar filtro de armazéns para viabilidade
                    loadWarehouseFilter('intelligentWarehouse');
                    break;
                case 'simulacao':
                    // Carregar filtro de armazéns para simulação
                    loadWarehouseFilter('intelligentWarehouse');
                    break;
                case 'planejamento':
                    loadOPPlanning();
                    break;
                case 'dashboard':
                    loadDashboard();
                    break;
            }
        };

        // Função para carregar filtro de armazéns em campos específicos
        function loadWarehouseFilter(selectId) {
            const select = document.getElementById(selectId);
            if (!select) return;

            select.innerHTML = '<option value="">Todos os Armazéns</option>';

            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                select.appendChild(option);
            });
        }

        // Funções utilitárias
        function formatNumber(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 3,
                maximumFractionDigits: 3
            }).format(value);
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        }

        function formatDate(timestamp) {
            if (!timestamp) return '';
            try {
                let date;
                if (typeof timestamp.toDate === 'function') {
                    date = timestamp.toDate();
                } else if (typeof timestamp.seconds === 'number') {
                    date = new Date(timestamp.seconds * 1000);
                } else if (timestamp instanceof Date) {
                    date = timestamp;
                } else {
                    date = new Date(timestamp);
                }
                return date.toLocaleDateString('pt-BR');
            } catch (e) {
                return '';
            }
        }

        function showLoading(message) {
            console.log('🔄 ' + message);
        }

        function showError(message) {
            console.error('❌ ' + message);
            alert(message);
        }

        // ===== FUNÇÕES DA ABA DISPONIBILIDADE DE MATERIAIS =====

        window.loadMaterialAvailability = async function() {
            try {
                showLoadingTable('materialAvailabilityTableBody', 'Analisando disponibilidade de materiais...');

                materialAvailabilityData = [];

                // Obter filtro de armazém selecionado
                const selectedWarehouseId = document.getElementById('warehouseFilter').value;
                console.log('🏭 Analisando armazém:', selectedWarehouseId || 'Todos os armazéns');

                console.log('🔄 Iniciando análise de materiais com explosão de BOM...');
                console.log('📋 OPs para análise:', ordensProducao.length);
                console.log('🔧 Estruturas disponíveis:', estruturas.length);

                // Mapa para armazenar necessidades agregadas por produto
                const necessidadesMap = new Map();

                // Filtrar APENAS ordens PENDENTES (que precisam de análise para iniciar)
                const ordensValidas = ordensProducao.filter(op => {
                    const status = (op.status || '').toLowerCase();
                    return status === 'pendente';
                });

                console.log(`📊 OPs PENDENTES para análise: ${ordensValidas.length} de ${ordensProducao.length}`);
                console.log(`Total de ordens PENDENTES encontradas: ${ordensValidas.length}. Detalhes:`, ordensValidas.map(op => ({numero: op.numero, status: op.status})));

                if (ordensValidas.length === 0) {
                    console.warn('⚠️ Nenhuma OP PENDENTE encontrada para análise de viabilidade.');
                    console.log('💡 OPs em outros status não precisam de análise de viabilidade:');
                    console.log('   - "Em Produção": já iniciadas');
                    console.log('   - "Concluída": já finalizadas');
                    console.log('   - "Cancelada": não serão produzidas');
                    console.log('   - Apenas OPs "Pendente" precisam de análise para decidir se podem iniciar');
                }

                // Para cada OP válida, processar materiais necessários (igual ao relatorio_necessidades_compras.html)
                for (const op of ordensValidas) {
                    console.log(`\n🔍 Processando OP ${op.numero || op.id}...`);

                    const produto = produtos.find(p => p.id === op.produtoId);
                    if (!produto) {
                        console.warn(`❌ Produto não encontrado para OP ${op.numero || op.id}`);
                        continue;
                    }

                    console.log(`📦 Produto: ${produto.codigo} - ${produto.descricao} (Tipo: ${produto.tipo})`);
                    console.log(`📊 Quantidade da OP: ${op.quantidade || 0}`);

                    // Verificar se tem materiais necessários (igual ao relatorio_necessidades_compras.html)
                    if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                        console.log(`OP ${op.numero || op.id} pulada: sem materiais necessários.`);
                        continue;
                    }

                    console.log(`OP ${op.numero || op.id}: ${op.materiaisNecessarios.length} materiais necessários encontrados.`, op.materiaisNecessarios);

                    // Processar cada material da ordem (igual ao relatorio_necessidades_compras.html)
                    for (const material of op.materiaisNecessarios) {
                        const materialProduto = produtos.find(p => p.id === material.produtoId);
                        if (!materialProduto) {
                            console.warn(`Produto não encontrado para material com produtoId ${material.produtoId} na OP ${op.numero || op.id}. Pulando material.`);
                            continue;
                        }

                        console.log(`Processando material ${materialProduto.codigo} (${materialProduto.descricao}) para OP ${op.numero || op.id}. Tipo: ${materialProduto.tipo}`);

                        // Ignorar produtos do tipo PA ou SP (igual ao relatorio_necessidades_compras.html)
                        if (materialProduto.tipo === 'PA' || materialProduto.tipo === 'SP') {
                            console.log(`Produto ${materialProduto.codigo} pulado (tipo: ${materialProduto.tipo}).`);
                            continue;
                        }

                        // Usar diretamente a quantidade do material necessário (igual ao relatorio_necessidades_compras.html)
                        const quantidadeNecessaria = Number(material.quantidade) || 0;

                        // Log detalhado do cálculo
                        console.log(`Cálculo inicial para ${materialProduto.codigo} na OP ${op.numero || op.id}: Quantidade necessária do material: ${material.quantidade}, Convertido para número: ${quantidadeNecessaria}`);

                        // Se a quantidade calculada for 0 ou NaN, pular
                        if (!quantidadeNecessaria || isNaN(quantidadeNecessaria)) {
                            console.warn(`Quantidade necessária inválida (${quantidadeNecessaria}) para ${materialProduto.codigo} na OP ${op.numero || op.id}. Pulando material.`);
                            continue;
                        }

                        console.log(`Quantidade válida (${quantidadeNecessaria}) para ${materialProduto.codigo} na OP ${op.numero || op.id}.`);

                        // Criar ou atualizar a necessidade no mapa
                        if (!necessidadesMap.has(materialProduto.id)) {
                            necessidadesMap.set(materialProduto.id, {
                                produto: materialProduto,
                                quantidadeNecessaria: 0,
                                opsRelacionadas: [],
                                armazem: null // Será definido depois
                            });
                        }

                        const necessidade = necessidadesMap.get(materialProduto.id);
                        necessidade.quantidadeNecessaria += quantidadeNecessaria;

                        // Adicionar OP relacionada se ainda não existe
                        const opExistente = necessidade.opsRelacionadas.find(opRel => opRel.opId === op.id);
                        if (!opExistente) {
                            necessidade.opsRelacionadas.push({
                                opId: op.id,
                                opNumero: op.numero || op.id,
                                quantidade: quantidadeNecessaria,
                                status: op.status
                            });
                        } else {
                            opExistente.quantidade += quantidadeNecessaria;
                        }

                        console.log(`Necessidade agregada para ${materialProduto.codigo}: ${necessidade.quantidadeNecessaria}. OPs: ${necessidade.opsRelacionadas.map(op => op.opNumero).join(', ')}`);
                    }
                }

                console.log('\n📊 Processando necessidades calculadas...');

                // Converter o mapa em array e calcular saldos/status
                for (const [produtoId, necessidade] of necessidadesMap) {
                    const produto = necessidade.produto;
                    const quantidadeNecessaria = necessidade.quantidadeNecessaria;

                    console.log(`📋 Processando material: ${produto.codigo} - Necessário: ${quantidadeNecessaria}`);

                    // Buscar saldo em estoque (considerando filtro de armazém)
                    let saldoAtual = 0;
                    let armazemInfo = null;

                    if (selectedWarehouseId) {
                        // Filtrar por armazém específico
                        const estoqueArmazem = estoques.find(e =>
                            e.produtoId === produtoId && e.armazemId === selectedWarehouseId
                        );
                        saldoAtual = estoqueArmazem ? (estoqueArmazem.saldo || 0) : 0;
                        armazemInfo = armazens.find(a => a.id === selectedWarehouseId);
                    } else {
                        // Somar todos os armazéns
                        const estoquesTotal = estoques.filter(e => e.produtoId === produtoId);
                        saldoAtual = estoquesTotal.reduce((total, e) => total + (e.saldo || 0), 0);
                        armazemInfo = { nome: 'Todos os Armazéns', codigo: 'TODOS' };
                    }

                    console.log(`   💰 Saldo disponível: ${saldoAtual}`);

                    // Calcular status e percentual de produção
                    let status = 'missing';
                    let percentualProducao = 0;

                    if (saldoAtual >= quantidadeNecessaria) {
                        status = 'available';
                        percentualProducao = 100;
                    } else if (saldoAtual > 0) {
                        status = 'insufficient';
                        percentualProducao = Math.floor((saldoAtual / quantidadeNecessaria) * 100);
                    } else {
                        status = 'missing';
                        percentualProducao = 0;
                    }

                    console.log(`   📊 Status: ${status} - Percentual: ${percentualProducao}%`);

                    // Calcular prioridade baseada na primeira OP relacionada
                    const primeiraOP = ordensProducao.find(op => op.id === necessidade.opsRelacionadas[0]?.opId);
                    const prioridade = primeiraOP ? calculatePriority(primeiraOP, quantidadeNecessaria, saldoAtual) : 'baixa';

                    // Adicionar aos dados de disponibilidade
                    materialAvailabilityData.push({
                        produto: produto,
                        saldoAtual: saldoAtual,
                        quantidadeNecessaria: quantidadeNecessaria,
                        status: status,
                        percentualProducao: percentualProducao,
                        armazem: armazemInfo,
                        opsRelacionadas: necessidade.opsRelacionadas,
                        prioridade: prioridade
                    });
                }

                console.log('\n✅ ANÁLISE DE MATERIAIS CONCLUÍDA');
                console.log(`📊 Total de materiais únicos: ${materialAvailabilityData.length}`);
                console.log(`🟢 Disponíveis: ${materialAvailabilityData.filter(m => m.status === 'available').length}`);
                console.log(`🟡 Insuficientes: ${materialAvailabilityData.filter(m => m.status === 'insufficient').length}`);
                console.log(`🔴 Em falta: ${materialAvailabilityData.filter(m => m.status === 'missing').length}`);

                // Ordenar por prioridade e código
                materialAvailabilityData.sort((a, b) => {
                    const prioridadeOrder = { 'alta': 3, 'media': 2, 'baixa': 1 };
                    const prioA = prioridadeOrder[a.prioridade] || 1;
                    const prioB = prioridadeOrder[b.prioridade] || 1;

                    if (prioA !== prioB) {
                        return prioB - prioA; // Prioridade alta primeiro
                    }

                    return a.produto.codigo.localeCompare(b.produto.codigo);
                });

                displayMaterialAvailability();
                updateMaterialStats();
                updateOPFilter();

            } catch (error) {
                console.error('❌ Erro ao carregar disponibilidade:', error);
                showErrorTable('materialAvailabilityTableBody', 'Erro ao carregar dados: ' + error.message);
            }
        };

        function displayMaterialAvailability(filteredData = null) {
            const tbody = document.getElementById('materialAvailabilityTableBody');
            const data = filteredData || materialAvailabilityData;

            if (data.length === 0) {
                const isPartialMode = document.getElementById('productionAnalysis').value === 'partial';
                const colspan = isPartialMode ? "10" : "9";

                tbody.innerHTML = `
                    <tr>
                        <td colspan="${colspan}" style="text-align: center; padding: 40px;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #f39c12;"></i>
                            <div>Nenhum material encontrado para análise</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            data.forEach(item => {
                const statusBadge = getStatusBadge(item.status);
                const priorityClass = getPriorityClass(item.prioridade);
                const opsText = item.opsRelacionadas.map(op => `OP ${op.opNumero}`).join(', ');

                const row = document.createElement('tr');
                const armazemText = item.armazem ? `${item.armazem.codigo} - ${item.armazem.nome}` : 'N/A';
                const isPartialMode = document.getElementById('productionAnalysis').value === 'partial';

                // Calcular produção parcial
                let partialProductionCell = '';
                if (isPartialMode) {
                    const percentage = item.percentualProducao || 0;
                    let percentageClass = '';
                    let percentageIcon = '';

                    if (percentage >= 100) {
                        percentageClass = 'text-success';
                        percentageIcon = '✅';
                    } else if (percentage >= 50) {
                        percentageClass = 'text-warning';
                        percentageIcon = '⚠️';
                    } else if (percentage > 0) {
                        percentageClass = 'text-danger';
                        percentageIcon = '🔴';
                    } else {
                        percentageClass = 'text-muted';
                        percentageIcon = '❌';
                    }

                    const quantidadePossivel = Math.floor((item.saldoAtual / item.quantidadeNecessaria) * item.opsRelacionadas[0]?.quantidade || 0);

                    partialProductionCell = `
                        <td style="text-align: center;" class="${percentageClass}">
                            <strong>${percentageIcon} ${percentage}%</strong>
                            <br><small>Qtd possível: ${quantidadePossivel}</small>
                        </td>
                    `;
                }

                row.innerHTML = `
                    <td><strong>${item.produto.codigo}</strong></td>
                    <td>${item.produto.descricao}</td>
                    <td>
                        <strong>${armazemText}</strong>
                        ${item.armazem && item.armazem.codigo !== 'TODOS' ? '<br><small style="color: #666;">Armazém específico</small>' : '<br><small style="color: #666;">Consolidado</small>'}
                    </td>
                    <td style="text-align: right;">${formatNumber(item.saldoAtual)} ${item.produto.unidade || 'UN'}</td>
                    <td style="text-align: right;">${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}</td>
                    <td>${statusBadge}</td>
                    ${partialProductionCell}
                    <td>
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${opsText}">
                            ${opsText}
                        </div>
                        <small style="color: #666;">${item.opsRelacionadas.length} OP(s)</small>
                    </td>
                    <td class="${priorityClass}">${getPriorityText(item.prioridade)}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="viewMaterialDetails('${item.produto.id}')" title="Ver detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${item.status !== 'available' ? `
                        <button class="btn btn-sm btn-warning" onclick="createPurchaseRequest('${item.produto.id}')" title="Solicitar compra">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        ` : ''}
                        ${isPartialMode && item.percentualProducao > 0 && item.percentualProducao < 100 ? `
                        <button class="btn btn-sm btn-success" onclick="suggestPartialProduction('${item.produto.id}')" title="Sugerir produção parcial">
                            <i class="fas fa-percentage"></i>
                        </button>
                        ` : ''}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        function updateMaterialStats() {
            const total = ordensProducao.length;
            const available = materialAvailabilityData.filter(item => item.status === 'available').length;
            const insufficient = materialAvailabilityData.filter(item => item.status === 'insufficient').length;
            const missing = materialAvailabilityData.filter(item => item.status === 'missing').length;

            document.getElementById('totalOPs').textContent = total;
            document.getElementById('materialsAvailable').textContent = available;
            document.getElementById('materialsInsufficient').textContent = insufficient;
            document.getElementById('materialsMissing').textContent = missing;
        }

        function updateOPFilter() {
            const select = document.getElementById('opFilter');
            select.innerHTML = '<option value="">Todas as OPs</option>';

            ordensProducao.forEach(op => {
                const option = document.createElement('option');
                option.value = op.id;
                option.textContent = `OP ${op.numero || op.id} - ${op.status}`;
                select.appendChild(option);
            });
        }

        window.filterByOP = function() {
            const opId = document.getElementById('opFilter').value;

            console.log('🔍 Filtrando por OP:', opId || 'Todas as OPs');

            if (!opId) {
                console.log('📊 Mostrando todos os materiais');
                displayMaterialAvailability();
                return;
            }

            // Encontrar a OP selecionada
            const selectedOP = ordensProducao.find(op => op.id === opId);
            if (selectedOP) {
                const produto = produtos.find(p => p.id === selectedOP.produtoId);
                console.log(`📋 OP Selecionada: ${selectedOP.numero || selectedOP.id} - Produto: ${produto?.codigo || 'NÃO_ENCONTRADO'} - Qtd: ${selectedOP.quantidade || 0}`);
            }

            const filteredData = materialAvailabilityData.filter(item =>
                item.opsRelacionadas.some(op => op.opId === opId)
            );

            console.log(`📊 Materiais filtrados: ${filteredData.length} de ${materialAvailabilityData.length}`);

            if (filteredData.length === 0) {
                console.warn('⚠️ Nenhum material encontrado para esta OP. Verifique se a análise foi executada.');
            }

            displayMaterialAvailability(filteredData);
        };

        window.filterAvailableProducts = function() {
            const productFilter = document.getElementById('productFilter').value.toLowerCase();
            const statusFilter = document.getElementById('materialStatus').value;

            let filteredData = [...materialAvailabilityData];

            if (productFilter) {
                filteredData = filteredData.filter(item =>
                    item.produto.codigo.toLowerCase().includes(productFilter) ||
                    item.produto.descricao.toLowerCase().includes(productFilter)
                );
            }

            if (statusFilter !== 'all') {
                filteredData = filteredData.filter(item => item.status === statusFilter);
            }

            displayMaterialAvailability(filteredData);
        };

        window.viewMaterialDetails = function(produtoId) {
            const item = materialAvailabilityData.find(i => i.produto.id === produtoId);
            if (!item) return;

            let detailsText = `DETALHES DO MATERIAL: ${item.produto.codigo} - ${item.produto.descricao}\n\n`;
            detailsText += `Saldo Atual: ${formatNumber(item.saldoAtual)} ${item.produto.unidade}\n`;
            detailsText += `Quantidade Necessária: ${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade}\n`;
            detailsText += `Status: ${getStatusText(item.status)}\n`;
            detailsText += `Prioridade: ${getPriorityText(item.prioridade)}\n\n`;
            detailsText += `OPs que utilizam este material:\n`;

            item.opsRelacionadas.forEach(op => {
                detailsText += `\n• OP ${op.opNumero} (${op.status})\n`;
                detailsText += `  Quantidade: ${formatNumber(op.quantidade)} ${item.produto.unidade}\n`;
            });

            alert(detailsText);
        };

        window.createPurchaseRequest = function(produtoId) {
            const item = materialAvailabilityData.find(i => i.produto.id === produtoId);
            if (!item) return;

            const quantidadeFaltante = Math.max(0, item.quantidadeNecessaria - item.saldoAtual);

            if (confirm(`Criar solicitação de compra para:\n\nProduto: ${item.produto.codigo} - ${item.produto.descricao}\nQuantidade: ${formatNumber(quantidadeFaltante)} ${item.produto.unidade}\n\nConfirmar?`)) {
                // Aqui você pode implementar a criação da solicitação de compra
                alert('Funcionalidade de solicitação de compra será implementada em breve!');
            }
        };

        // Funções auxiliares
        function calculatePriority(op, quantidadeNecessaria, saldoAtual) {
            let priority = 1;

            // Prioridade baseada no status da OP
            if (op.status === 'EM_ANDAMENTO') priority += 3;
            else if (op.status === 'PENDENTE') priority += 2;

            // Prioridade baseada na disponibilidade
            if (saldoAtual === 0) priority += 3;
            else if (saldoAtual < quantidadeNecessaria) priority += 2;

            // Prioridade baseada na data de entrega (se existir)
            if (op.dataEntrega) {
                const hoje = new Date();
                const entrega = new Date(op.dataEntrega.seconds * 1000);
                const diasRestantes = Math.ceil((entrega - hoje) / (1000 * 60 * 60 * 24));

                if (diasRestantes <= 7) priority += 3;
                else if (diasRestantes <= 15) priority += 2;
                else if (diasRestantes <= 30) priority += 1;
            }

            return priority;
        }

        function getStatusBadge(status) {
            switch (status) {
                case 'available':
                    return '<span class="status-badge" style="background: #d4edda; color: #155724;">✅ Disponível</span>';
                case 'insufficient':
                    return '<span class="status-badge" style="background: #fff3cd; color: #856404;">⚠️ Insuficiente</span>';
                case 'missing':
                    return '<span class="status-badge" style="background: #f8d7da; color: #721c24;">❌ Em Falta</span>';
                default:
                    return '<span class="status-badge" style="background: #e2e3e5; color: #383d41;">❓ Indefinido</span>';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'available': return 'Disponível';
                case 'insufficient': return 'Insuficiente';
                case 'missing': return 'Em Falta';
                default: return 'Indefinido';
            }
        }

        function getPriorityClass(priority) {
            if (priority >= 7) return 'priority-high';
            if (priority >= 4) return 'priority-medium';
            return 'priority-low';
        }

        function getPriorityText(priority) {
            if (priority >= 7) return 'ALTA';
            if (priority >= 4) return 'MÉDIA';
            return 'BAIXA';
        }

        function showLoadingTable(tableBodyId, message) {
            const tbody = document.getElementById(tableBodyId);
            if (tbody) {
                const colSpan = tbody.closest('table').querySelectorAll('th').length;
                tbody.innerHTML = `<tr><td colspan="${colSpan}" style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> ${message}</td></tr>`;
            }
        }

        function showErrorTable(tableBodyId, message) {
            const tbody = document.getElementById(tableBodyId);
            if (tbody) {
                const colSpan = tbody.closest('table').querySelectorAll('th').length;
                tbody.innerHTML = `<tr><td colspan="${colSpan}" style="text-align: center; padding: 20px; color: red;"><i class="fas fa-exclamation-triangle"></i> ${message}</td></tr>`;
            }
        }

        // ===== ANÁLISE DE VIABILIDADE DE OPs COM PRIORIZAÇÃO POR NÍVEL BOM =====

        let opViabilityData = [];
        let bomLevelsCache = new Map(); // Cache para níveis BOM calculados

        // ===== CÁLCULO DE NÍVEIS BOM =====

        function calculateBOMLevel(produtoId, visited = new Set(), depth = 0) {
            // Evitar ciclos infinitos
            if (depth > 20 || visited.has(produtoId)) {
                console.warn(`🔄 Ciclo ou profundidade excessiva detectada para produto ${produtoId}`);
                return 0;
            }

            // Verificar cache
            if (bomLevelsCache.has(produtoId)) {
                return bomLevelsCache.get(produtoId);
            }

            const produto = produtos.find(p => p.id === produtoId);
            if (!produto) {
                bomLevelsCache.set(produtoId, 0);
                return 0;
            }

            // MP (Matéria Prima) = nível 0 (base da pirâmide)
            if (produto.tipo === 'MP') {
                bomLevelsCache.set(produtoId, 0);
                return 0;
            }

            // Buscar estrutura do produto
            const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
            if (!estrutura || !estrutura.componentes || estrutura.componentes.length === 0) {
                // Produto sem estrutura = nível 0
                bomLevelsCache.set(produtoId, 0);
                return 0;
            }

            visited.add(produtoId);

            // Calcular nível baseado no maior nível dos componentes + 1
            let maxComponentLevel = -1;

            for (const componente of estrutura.componentes) {
                const componentLevel = calculateBOMLevel(componente.componentId, new Set(visited), depth + 1);
                if (componentLevel > maxComponentLevel) {
                    maxComponentLevel = componentLevel;
                }
            }

            const productLevel = maxComponentLevel + 1;
            bomLevelsCache.set(produtoId, productLevel);

            console.log(`📊 Produto ${produto.codigo} (${produto.tipo}) = Nível BOM ${productLevel}`);
            return productLevel;
        }

        function calculateAllBOMLevels() {
            console.log('🔄 Calculando níveis BOM para todos os produtos...');
            bomLevelsCache.clear();

            const levelStats = {};

            produtos.forEach(produto => {
                const level = calculateBOMLevel(produto.id);
                if (!levelStats[level]) {
                    levelStats[level] = [];
                }
                levelStats[level].push(produto.codigo);
            });

            console.log('📊 DISTRIBUIÇÃO DE NÍVEIS BOM:');
            Object.entries(levelStats).sort((a, b) => parseInt(b[0]) - parseInt(a[0])).forEach(([level, produtos]) => {
                console.log(`   Nível ${level}: ${produtos.length} produto(s) - ${produtos.slice(0, 5).join(', ')}${produtos.length > 5 ? '...' : ''}`);
            });

            return levelStats;
        }

        function getBOMDependencies(produtoId) {
            const dependencies = [];
            const produto = produtos.find(p => p.id === produtoId);

            if (!produto) return dependencies;

            // Buscar onde este produto é usado como componente
            estruturas.forEach(estrutura => {
                if (estrutura.componentes) {
                    estrutura.componentes.forEach(componente => {
                        if (componente.componentId === produtoId) {
                            const produtoPai = produtos.find(p => p.id === estrutura.produtoPaiId);
                            if (produtoPai) {
                                dependencies.push({
                                    produtoId: produtoPai.id,
                                    codigo: produtoPai.codigo,
                                    descricao: produtoPai.descricao,
                                    nivel: calculateBOMLevel(produtoPai.id),
                                    quantidade: componente.quantidade
                                });
                            }
                        }
                    });
                }
            });

            // Ordenar por nível (maior primeiro)
            dependencies.sort((a, b) => b.nivel - a.nivel);

            return dependencies;
        }

        window.analyzeOPViability = async function() {
            try {
                showLoadingTable('viabilityTableBody', 'Analisando viabilidade das OPs...');

                opViabilityData = [];

                console.log('🚀 Iniciando análise de viabilidade de OPs...');

                // Filtrar APENAS OPs PENDENTES (que precisam de decisão de viabilidade)
                const ordensValidas = ordensProducao.filter(op => {
                    const status = (op.status || '').toLowerCase();
                    return status === 'pendente';
                });

                console.log(`📊 Analisando ${ordensValidas.length} OPs PENDENTES`);
                console.log('🎯 Foco em OPs PENDENTES: são as que precisam de decisão para iniciar produção');

                if (ordensValidas.length === 0) {
                    alert('📋 ANÁLISE DE VIABILIDADE\n\n❌ Nenhuma OP PENDENTE encontrada.\n\n💡 Apenas OPs com status "Pendente" precisam de análise de viabilidade.\n\nOPs em outros status:\n• "Em Produção" = já iniciadas\n• "Concluída" = já finalizadas\n• "Cancelada" = não serão produzidas\n\nVerifique se existem OPs pendentes no sistema.');
                    document.getElementById('viabilityTableBody').innerHTML = `
                        <tr>
                            <td colspan="11" style="text-align: center; padding: 40px;">
                                <i class="fas fa-info-circle" style="font-size: 24px; color: #17a2b8;"></i>
                                <div><strong>Nenhuma OP PENDENTE encontrada</strong></div>
                                <div style="margin-top: 10px; color: #666;">
                                    Apenas OPs com status "Pendente" são analisadas para viabilidade.<br>
                                    OPs em produção, concluídas ou canceladas não precisam desta análise.
                                </div>
                            </td>
                        </tr>
                    `;
                    return;
                }

                // Calcular níveis BOM antes da análise
                console.log('🔄 Calculando níveis BOM...');
                calculateAllBOMLevels();

                for (const op of ordensValidas) {
                    console.log(`\n🔍 Analisando OP ${op.numero || op.id}...`);

                    const produto = produtos.find(p => p.id === op.produtoId);
                    if (!produto) {
                        console.warn(`❌ Produto não encontrado para OP ${op.numero || op.id}`);
                        continue;
                    }

                    // Verificar se tem materiais necessários
                    if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                        console.log(`⚠️ OP ${op.numero || op.id} sem materiais necessários`);

                        // OP sem materiais = 100% viável
                        opViabilityData.push({
                            op: op,
                            produto: produto,
                            viabilidade: 'viable',
                            percentualPossivel: 100,
                            materiaisFaltantes: 0,
                            totalMateriais: 0,
                            materiaisDisponiveis: 0,
                            prioridade: calculateOPPriority(op),
                            detalhes: 'OP sem materiais necessários - Pode ser iniciada'
                        });
                        continue;
                    }

                    console.log(`📋 OP ${op.numero || op.id}: ${op.materiaisNecessarios.length} materiais`);

                    let materiaisDisponiveis = 0;
                    let materiaisFaltantes = 0;
                    let menorPercentual = 100;
                    let detalhesProblemas = [];

                    // Analisar cada material
                    for (const material of op.materiaisNecessarios) {
                        const materialProduto = produtos.find(p => p.id === material.produtoId);
                        if (!materialProduto) continue;

                        const quantidadeNecessaria = Number(material.quantidade) || 0;

                        // Calcular saldo disponível
                        const estoquesTotal = estoques.filter(e => e.produtoId === material.produtoId);
                        const saldoTotal = estoquesTotal.reduce((total, e) => total + (e.saldo || 0), 0);
                        const saldoReservado = estoquesTotal.reduce((total, e) => total + (e.saldoReservado || 0), 0);
                        const saldoDisponivel = saldoTotal - saldoReservado;

                        if (saldoDisponivel >= quantidadeNecessaria) {
                            materiaisDisponiveis++;
                        } else {
                            materiaisFaltantes++;
                            const percentualMaterial = saldoDisponivel > 0 ?
                                Math.floor((saldoDisponivel / quantidadeNecessaria) * 100) : 0;

                            if (percentualMaterial < menorPercentual) {
                                menorPercentual = percentualMaterial;
                            }

                            const falta = quantidadeNecessaria - saldoDisponivel;
                            detalhesProblemas.push(`${materialProduto.codigo}: falta ${falta.toFixed(2)} ${materialProduto.unidade || 'UN'}`);
                        }
                    }

                    // Determinar viabilidade
                    let viabilidade = 'blocked';
                    let percentualPossivel = 0;

                    if (materiaisFaltantes === 0) {
                        viabilidade = 'viable';
                        percentualPossivel = 100;
                    } else if (menorPercentual > 0) {
                        viabilidade = 'partial';
                        percentualPossivel = menorPercentual;
                    }

                    const bomLevel = calculateBOMLevel(produto.id);
                    const dependencies = getBOMDependencies(produto.id);
                    const prioridade = calculateOPPriorityWithBOM(op, materiaisFaltantes, percentualPossivel, bomLevel, dependencies);

                    opViabilityData.push({
                        op: op,
                        produto: produto,
                        viabilidade: viabilidade,
                        percentualPossivel: percentualPossivel,
                        materiaisFaltantes: materiaisFaltantes,
                        totalMateriais: op.materiaisNecessarios.length,
                        materiaisDisponiveis: materiaisDisponiveis,
                        prioridade: prioridade,
                        bomLevel: bomLevel,
                        dependencies: dependencies,
                        detalhes: detalhesProblemas.join('; ') || 'Todos os materiais disponíveis'
                    });

                    console.log(`✅ OP ${op.numero || op.id}: ${viabilidade} (${percentualPossivel}%)`);
                }

                // Ordenar por NÍVEL BOM (maior primeiro), depois prioridade, depois percentual
                opViabilityData.sort((a, b) => {
                    // 1º critério: Nível BOM (maior primeiro - produtos de nível alto são dependências)
                    if (a.bomLevel !== b.bomLevel) {
                        return b.bomLevel - a.bomLevel;
                    }

                    // 2º critério: Prioridade
                    const prioridadeOrder = { 'alta': 3, 'media': 2, 'baixa': 1 };
                    const prioA = prioridadeOrder[a.prioridade] || 1;
                    const prioB = prioridadeOrder[b.prioridade] || 1;

                    if (prioA !== prioB) {
                        return prioB - prioA;
                    }

                    // 3º critério: Percentual possível (maior primeiro)
                    if (a.percentualPossivel !== b.percentualPossivel) {
                        return b.percentualPossivel - a.percentualPossivel;
                    }

                    // 4º critério: Número de dependências (mais dependências = maior prioridade)
                    return b.dependencies.length - a.dependencies.length;
                });

                console.log(`✅ Análise concluída: ${opViabilityData.length} OPs analisadas`);

                displayOPViability();
                updateViabilityStats();
                document.getElementById('viabilityStats').style.display = 'grid';

            } catch (error) {
                console.error('❌ Erro na análise de viabilidade:', error);
                showErrorTable('viabilityTableBody', 'Erro ao analisar viabilidade: ' + error.message);
            }
        };

        function displayOPViability(filteredData = null) {
            const tbody = document.getElementById('viabilityTableBody');
            const data = filteredData || opViabilityData;

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" style="text-align: center; padding: 40px;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #f39c12;"></i>
                            <div>Nenhuma OP encontrada para análise</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            data.forEach(item => {
                const viabilityBadge = getViabilityBadge(item.viabilidade, item.percentualPossivel);
                const priorityClass = getPriorityClass(item.prioridade);

                const row = document.createElement('tr');
                row.className = `viability-${item.viabilidade}`;

                const bomLevelBadge = getBOMLevelBadge(item.bomLevel);
                const dependenciesText = item.dependencies.length > 0 ?
                    `${item.dependencies.length} produto(s)` : 'Nenhuma';
                const dependenciesTitle = item.dependencies.length > 0 ?
                    `Usado em: ${item.dependencies.map(d => d.codigo).join(', ')}` : 'Produto não é componente de outros';

                row.innerHTML = `
                    <td><strong>${item.op.numero || item.op.id}</strong></td>
                    <td>
                        <strong>${item.produto.codigo}</strong> (${item.produto.tipo})<br>
                        <small>${item.produto.descricao}</small>
                    </td>
                    <td style="text-align: center;">${bomLevelBadge}</td>
                    <td style="text-align: center;" title="${dependenciesTitle}">
                        <span style="color: ${item.dependencies.length > 0 ? '#dc3545' : '#6c757d'};">
                            ${dependenciesText}
                        </span>
                        ${item.dependencies.length > 0 ? '<br><small>🔗 Crítico</small>' : '<br><small>📦 Independente</small>'}
                    </td>
                    <td style="text-align: right;">${formatNumber(item.op.quantidade || 0)} ${item.produto.unidade || 'UN'}</td>
                    <td>
                        <span class="status-badge status-${item.op.status?.toLowerCase() || 'indefinido'}">
                            ${item.op.status || 'INDEFINIDO'}
                        </span>
                    </td>
                    <td>${viabilityBadge}</td>
                    <td style="text-align: center;">
                        <strong style="color: ${item.percentualPossivel >= 100 ? '#28a745' :
                                                item.percentualPossivel >= 50 ? '#ffc107' : '#dc3545'};">
                            ${item.percentualPossivel}%
                        </strong>
                    </td>
                    <td>
                        <span style="color: ${item.materiaisFaltantes === 0 ? '#28a745' : '#dc3545'};">
                            ${item.materiaisFaltantes} de ${item.totalMateriais}
                        </span>
                        <br><small title="${item.detalhes}">${item.detalhes.substring(0, 50)}${item.detalhes.length > 50 ? '...' : ''}</small>
                    </td>
                    <td class="${priorityClass}">${getPriorityText(item.prioridade)}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="viewOPDetails('${item.op.id}')" title="Ver detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${item.viabilidade === 'viable' ? `
                        <button class="btn btn-sm btn-success" onclick="startOP('${item.op.id}')" title="Iniciar OP">
                            <i class="fas fa-play"></i>
                        </button>
                        ` : ''}
                        ${item.viabilidade === 'partial' ? `
                        <button class="btn btn-sm btn-warning" onclick="suggestPartialOP('${item.op.id}')" title="Produção parcial">
                            <i class="fas fa-percentage"></i>
                        </button>
                        ` : ''}
                        ${item.materiaisFaltantes > 0 ? `
                        <button class="btn btn-sm btn-danger" onclick="requestMissingMaterials('${item.op.id}')" title="Solicitar materiais">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        ` : ''}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        function updateViabilityStats() {
            const viable = opViabilityData.filter(item => item.viabilidade === 'viable').length;
            const partial = opViabilityData.filter(item => item.viabilidade === 'partial').length;
            const blocked = opViabilityData.filter(item => item.viabilidade === 'blocked').length;
            const priority = opViabilityData.filter(item => item.prioridade === 'alta').length;

            document.getElementById('viableOPs').textContent = viable;
            document.getElementById('partialOPs').textContent = partial;
            document.getElementById('blockedOPs').textContent = blocked;
            document.getElementById('priorityOPs').textContent = priority;
        }

        window.filterOPViability = function() {
            const viabilityFilter = document.getElementById('viabilityFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;

            let filteredData = [...opViabilityData];

            if (viabilityFilter !== 'all') {
                filteredData = filteredData.filter(item => item.viabilidade === viabilityFilter);
            }

            if (priorityFilter !== 'all') {
                filteredData = filteredData.filter(item => item.prioridade === priorityFilter);
            }

            displayOPViability(filteredData);
        };

        function getViabilityBadge(viabilidade, percentual) {
            switch (viabilidade) {
                case 'viable':
                    return '<span class="status-badge" style="background: #d4edda; color: #155724;">🟢 Viável</span>';
                case 'partial':
                    return `<span class="status-badge" style="background: #fff3cd; color: #856404;">🟡 Parcial (${percentual}%)</span>`;
                case 'blocked':
                    return '<span class="status-badge" style="background: #f8d7da; color: #721c24;">🔴 Bloqueada</span>';
                default:
                    return '<span class="status-badge" style="background: #e2e3e5; color: #383d41;">❓ Indefinido</span>';
            }
        }

        function getBOMLevelBadge(level) {
            let color, icon, text, priority;

            if (level >= 4) {
                color = '#721c24'; // Vermelho escuro
                icon = '🔴';
                text = `Nível ${level}`;
                priority = 'CRÍTICO';
            } else if (level >= 3) {
                color = '#856404'; // Amarelo escuro
                icon = '🟠';
                text = `Nível ${level}`;
                priority = 'ALTO';
            } else if (level >= 2) {
                color = '#0c5460'; // Azul escuro
                icon = '🔵';
                text = `Nível ${level}`;
                priority = 'MÉDIO';
            } else if (level >= 1) {
                color = '#155724'; // Verde escuro
                icon = '🟢';
                text = `Nível ${level}`;
                priority = 'BAIXO';
            } else {
                color = '#6c757d'; // Cinza
                icon = '⚪';
                text = `Nível ${level}`;
                priority = 'MP';
            }

            return `
                <span class="status-badge" style="background: ${color}20; color: ${color}; font-weight: bold;">
                    ${icon} ${text}
                </span>
                <br><small style="color: ${color};">${priority}</small>
            `;
        }

        function calculateOPPriorityWithBOM(op, materiaisFaltantes = 0, percentualPossivel = 100, bomLevel = 0, dependencies = []) {
            let priority = 1;

            // 🎯 PRIORIDADE MÁXIMA: Nível BOM (produtos de nível alto são críticos)
            if (bomLevel >= 4) priority += 5;      // Nível 4+ = Produtos muito complexos
            else if (bomLevel >= 3) priority += 4; // Nível 3 = Produtos complexos
            else if (bomLevel >= 2) priority += 3; // Nível 2 = Produtos intermediários
            else if (bomLevel >= 1) priority += 2; // Nível 1 = Produtos simples
            // Nível 0 (MP) = +0

            // Prioridade baseada no número de dependências (quantos produtos dependem deste)
            if (dependencies.length >= 5) priority += 3;
            else if (dependencies.length >= 3) priority += 2;
            else if (dependencies.length >= 1) priority += 1;

            // Prioridade baseada no status da OP
            const status = (op.status || '').toLowerCase();
            if (status === 'em produção') priority += 3;
            else if (status === 'firme') priority += 2;
            else if (status === 'pendente') priority += 1;

            // Prioridade baseada na viabilidade
            if (materiaisFaltantes === 0) priority += 3;
            else if (percentualPossivel >= 50) priority += 2;
            else if (percentualPossivel > 0) priority += 1;

            // Prioridade baseada na data de entrega
            if (op.dataEntrega) {
                const hoje = new Date();
                const entrega = new Date(op.dataEntrega.seconds ? op.dataEntrega.seconds * 1000 : op.dataEntrega);
                const diasRestantes = Math.ceil((entrega - hoje) / (1000 * 60 * 60 * 24));

                if (diasRestantes <= 3) priority += 3;
                else if (diasRestantes <= 7) priority += 2;
                else if (diasRestantes <= 15) priority += 1;
            }

            // Classificação final (ajustada para considerar nível BOM)
            if (priority >= 12) return 'alta';     // Produtos complexos com alta prioridade
            if (priority >= 8) return 'media';     // Produtos intermediários
            return 'baixa';                        // Produtos simples ou baixa prioridade
        }

        // Manter função original para compatibilidade
        function calculateOPPriority(op, materiaisFaltantes = 0, percentualPossivel = 100) {
            return calculateOPPriorityWithBOM(op, materiaisFaltantes, percentualPossivel, 0, []);
        }

        // ===== SIMULAÇÃO INTELIGENTE =====

        let intelligentSimulationData = [];

        window.runIntelligentSimulation = async function() {
            try {
                const productCode = document.getElementById('intelligentProductCode').value.trim();
                const quantity = parseFloat(document.getElementById('intelligentQuantity').value) || 1;
                const warehouseId = document.getElementById('intelligentWarehouse').value;
                const reservationMode = document.getElementById('reservationMode').value;

                if (!productCode) {
                    alert('Por favor, digite o código do produto para simular a produção.');
                    return;
                }

                // Buscar produto por código
                const produto = produtos.find(p =>
                    p.codigo && p.codigo.toLowerCase() === productCode.toLowerCase()
                );

                if (!produto) {
                    alert(`Produto com código "${productCode}" não encontrado.\n\nVerifique se o código está correto.`);
                    return;
                }

                if (produto.tipo !== 'PA' && produto.tipo !== 'SP') {
                    alert(`Produto "${productCode}" não é do tipo PA (Produto Acabado) ou SP (Semi Produto).\n\nTipo atual: ${produto.tipo}\n\nApenas produtos PA ou SP podem ter estrutura (BOM) explodida.`);
                    return;
                }

                console.log('🧠 Iniciando simulação inteligente:', produto.codigo, '-', produto.descricao, 'Qtd:', quantity);

                showLoadingTable('simulationTableBody', 'Fazendo explosão de BOM e analisando materiais...');

                // Fazer explosão de BOM
                const necessidadesMP = await explodirEstruturaPCP(produto.id, quantity, estruturas);

                if (necessidadesMP.length === 0) {
                    alert(`SIMULAÇÃO INTELIGENTE\n\nProduto: ${produto.codigo} - ${produto.descricao}\nQuantidade: ${quantity}\n\n❌ Nenhum material necessário encontrado.\n\nVerifique se o produto possui estrutura (BOM) cadastrada.`);
                    document.getElementById('simulationResult').style.display = 'none';
                    return;
                }

                console.log(`🧮 Explosão de BOM concluída: ${necessidadesMP.length} materiais encontrados`);

                intelligentSimulationData = [];
                let totalMaterials = 0;
                let availableMaterials = 0;
                let reservedMaterials = 0;
                let menorPercentual = 100;

                // Analisar cada material
                for (const necessidadeMP of necessidadesMP) {
                    const materialProduto = necessidadeMP.produto;
                    const quantidadeNecessaria = necessidadeMP.quantidade;

                    console.log(`📋 Analisando material: ${materialProduto.codigo} - Necessário: ${quantidadeNecessaria}`);

                    // Calcular saldos por armazém
                    let saldoTotal = 0;
                    let saldoReservado = 0;
                    let saldoDisponivel = 0;

                    if (warehouseId) {
                        // Armazém específico
                        const estoqueArmazem = estoques.find(e =>
                            e.produtoId === materialProduto.id && e.armazemId === warehouseId
                        );
                        saldoTotal = estoqueArmazem ? (estoqueArmazem.saldo || 0) : 0;
                        saldoReservado = estoqueArmazem ? (estoqueArmazem.saldoReservado || 0) : 0;
                    } else {
                        // Todos os armazéns
                        const estoquesTotal = estoques.filter(e => e.produtoId === materialProduto.id);
                        saldoTotal = estoquesTotal.reduce((total, e) => total + (e.saldo || 0), 0);
                        saldoReservado = estoquesTotal.reduce((total, e) => total + (e.saldoReservado || 0), 0);
                    }

                    // Calcular disponível baseado no modo de reserva
                    switch (reservationMode) {
                        case 'consider':
                            saldoDisponivel = saldoTotal - saldoReservado;
                            break;
                        case 'ignore':
                            saldoDisponivel = saldoTotal;
                            break;
                        case 'only':
                            saldoDisponivel = saldoTotal - saldoReservado;
                            break;
                    }

                    // Calcular status e percentual
                    let status = 'missing';
                    let percentualAtende = 0;

                    if (saldoDisponivel >= quantidadeNecessaria) {
                        status = 'available';
                        percentualAtende = 100;
                        availableMaterials++;
                    } else if (saldoDisponivel > 0) {
                        status = 'insufficient';
                        percentualAtende = Math.floor((saldoDisponivel / quantidadeNecessaria) * 100);
                    } else {
                        status = 'missing';
                        percentualAtende = 0;
                    }

                    if (percentualAtende < menorPercentual) {
                        menorPercentual = percentualAtende;
                    }

                    if (saldoReservado > 0) {
                        reservedMaterials++;
                    }

                    totalMaterials++;

                    intelligentSimulationData.push({
                        produto: materialProduto,
                        quantidadeNecessaria: quantidadeNecessaria,
                        saldoTotal: saldoTotal,
                        saldoReservado: saldoReservado,
                        saldoDisponivel: saldoDisponivel,
                        status: status,
                        percentualAtende: percentualAtende,
                        falta: Math.max(0, quantidadeNecessaria - saldoDisponivel)
                    });

                    console.log(`   📊 ${materialProduto.codigo}: ${status} (${percentualAtende}%)`);
                }

                // Atualizar interface
                document.getElementById('simulationTitle').textContent =
                    `Simulação: ${produto.codigo} - ${produto.descricao} (${quantity} ${produto.unidade || 'UN'})`;

                const canProduce = menorPercentual >= 100;
                const canPartial = menorPercentual > 0 && menorPercentual < 100;

                let summaryText = '';
                if (canProduce) {
                    summaryText = `🎉 <strong>PRODUÇÃO VIÁVEL!</strong> Todos os materiais estão disponíveis para produzir ${quantity} unidades.`;
                } else if (canPartial) {
                    const quantidadePossivel = Math.floor((menorPercentual / 100) * quantity);
                    summaryText = `⚠️ <strong>PRODUÇÃO PARCIAL POSSÍVEL!</strong> É possível produzir ${quantidadePossivel} unidades (${menorPercentual}% da quantidade solicitada).`;
                } else {
                    summaryText = `❌ <strong>PRODUÇÃO BLOQUEADA!</strong> Materiais insuficientes para iniciar a produção.`;
                }

                document.getElementById('simulationSummary').innerHTML = summaryText;

                // Atualizar estatísticas
                document.getElementById('totalMaterials').textContent = totalMaterials;
                document.getElementById('availableMaterials').textContent = availableMaterials;
                document.getElementById('reservedMaterials').textContent = reservedMaterials;
                document.getElementById('productionPercentage').textContent = menorPercentual + '%';

                displayIntelligentSimulation();
                document.getElementById('simulationResult').style.display = 'block';

                console.log(`✅ Simulação concluída: ${menorPercentual}% de viabilidade`);

            } catch (error) {
                console.error('❌ Erro na simulação inteligente:', error);
                alert('Erro na simulação: ' + error.message);
                document.getElementById('simulationResult').style.display = 'none';
            }
        };

        function displayIntelligentSimulation() {
            const tbody = document.getElementById('simulationTableBody');

            if (intelligentSimulationData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px;">
                            <i class="fas fa-info-circle" style="font-size: 24px; color: #17a2b8;"></i>
                            <div>Execute uma simulação para ver os resultados</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            intelligentSimulationData.forEach(item => {
                const statusBadge = getStatusBadge(item.status);
                const percentualClass = item.percentualAtende >= 100 ? 'text-success' :
                                       item.percentualAtende >= 50 ? 'text-warning' : 'text-danger';

                const row = document.createElement('tr');
                row.className = `material-${item.status}`;

                row.innerHTML = `
                    <td><strong>${item.produto.codigo}</strong></td>
                    <td>${item.produto.descricao}</td>
                    <td style="text-align: right;">${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}</td>
                    <td style="text-align: right;">${formatNumber(item.saldoTotal)} ${item.produto.unidade || 'UN'}</td>
                    <td style="text-align: right;">
                        <span style="color: ${item.saldoReservado > 0 ? '#dc3545' : '#6c757d'};">
                            ${formatNumber(item.saldoReservado)} ${item.produto.unidade || 'UN'}
                        </span>
                        ${item.saldoReservado > 0 ? '<br><small>🔒 Reservado</small>' : ''}
                    </td>
                    <td style="text-align: right;">
                        <strong style="color: ${item.saldoDisponivel >= item.quantidadeNecessaria ? '#28a745' : '#dc3545'};">
                            ${formatNumber(item.saldoDisponivel)} ${item.produto.unidade || 'UN'}
                        </strong>
                    </td>
                    <td>${statusBadge}</td>
                    <td style="text-align: center;">
                        <strong class="${percentualClass}">${item.percentualAtende}%</strong>
                        ${item.falta > 0 ? `<br><small style="color: #dc3545;">Falta: ${formatNumber(item.falta)}</small>` : ''}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="viewMaterialStock('${item.produto.id}')" title="Ver estoque detalhado">
                            <i class="fas fa-warehouse"></i>
                        </button>
                        ${item.status !== 'available' ? `
                        <button class="btn btn-sm btn-warning" onclick="createPurchaseRequest('${item.produto.id}')" title="Solicitar compra">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        ` : ''}
                        ${item.saldoReservado > 0 ? `
                        <button class="btn btn-sm btn-secondary" onclick="viewReservations('${item.produto.id}')" title="Ver reservas">
                            <i class="fas fa-lock"></i>
                        </button>
                        ` : ''}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        window.suggestOptimalProduction = function() {
            if (intelligentSimulationData.length === 0) {
                alert('Execute primeiro uma simulação para obter sugestões.');
                return;
            }

            const productCode = document.getElementById('intelligentProductCode').value.trim();
            const quantity = parseFloat(document.getElementById('intelligentQuantity').value) || 1;

            // Calcular quantidade ótima
            let menorPercentual = 100;
            let materiaisProblematicos = [];

            intelligentSimulationData.forEach(item => {
                if (item.percentualAtende < menorPercentual) {
                    menorPercentual = item.percentualAtende;
                }
                if (item.status !== 'available') {
                    materiaisProblematicos.push({
                        codigo: item.produto.codigo,
                        falta: item.falta,
                        percentual: item.percentualAtende
                    });
                }
            });

            const quantidadeOtima = Math.floor((menorPercentual / 100) * quantity);

            let suggestion = `SUGESTÃO INTELIGENTE DE PRODUÇÃO\n`;
            suggestion += `${'='.repeat(50)}\n\n`;
            suggestion += `📦 Produto: ${productCode}\n`;
            suggestion += `📊 Quantidade Solicitada: ${quantity}\n`;
            suggestion += `🎯 Quantidade Ótima: ${quantidadeOtima} (${menorPercentual}%)\n\n`;

            if (menorPercentual >= 100) {
                suggestion += `🎉 RECOMENDAÇÃO: PRODUÇÃO COMPLETA!\n`;
                suggestion += `✅ Todos os materiais estão disponíveis.\n`;
                suggestion += `🚀 Pode iniciar a produção imediatamente.\n\n`;
                suggestion += `💡 Ações sugeridas:\n`;
                suggestion += `   1. Reservar materiais para esta produção\n`;
                suggestion += `   2. Criar ordem de produção\n`;
                suggestion += `   3. Programar início da produção`;
            } else if (menorPercentual >= 75) {
                suggestion += `🟢 RECOMENDAÇÃO: PRODUÇÃO PARCIAL VIÁVEL!\n`;
                suggestion += `✅ ${menorPercentual}% dos materiais disponíveis.\n`;
                suggestion += `📈 Produzir ${quantidadeOtima} unidades agora.\n\n`;
                suggestion += `💡 Ações sugeridas:\n`;
                suggestion += `   1. Iniciar produção parcial de ${quantidadeOtima} unidades\n`;
                suggestion += `   2. Solicitar compra dos materiais faltantes:\n`;
                materiaisProblematicos.forEach(mat => {
                    suggestion += `      • ${mat.codigo}: ${formatNumber(mat.falta)} unidades\n`;
                });
                suggestion += `   3. Programar segunda fase da produção`;
            } else if (menorPercentual >= 25) {
                suggestion += `🟡 RECOMENDAÇÃO: PRODUÇÃO PARCIAL LIMITADA\n`;
                suggestion += `⚠️ Apenas ${menorPercentual}% dos materiais disponíveis.\n`;
                suggestion += `📉 Possível produzir ${quantidadeOtima} unidades.\n\n`;
                suggestion += `💡 Ações sugeridas:\n`;
                suggestion += `   1. Avaliar se vale a pena produção parcial\n`;
                suggestion += `   2. Priorizar compra dos materiais críticos:\n`;
                materiaisProblematicos.forEach(mat => {
                    suggestion += `      • ${mat.codigo}: ${formatNumber(mat.falta)} unidades (${mat.percentual}%)\n`;
                });
                suggestion += `   3. Considerar adiar produção até ter mais material`;
            } else {
                suggestion += `🔴 RECOMENDAÇÃO: AGUARDAR MATERIAIS\n`;
                suggestion += `❌ Materiais insuficientes (${menorPercentual}%).\n`;
                suggestion += `🛒 Necessário comprar materiais antes de produzir.\n\n`;
                suggestion += `💡 Ações sugeridas:\n`;
                suggestion += `   1. Solicitar compra urgente dos materiais:\n`;
                materiaisProblematicos.forEach(mat => {
                    suggestion += `      • ${mat.codigo}: ${formatNumber(mat.falta)} unidades\n`;
                });
                suggestion += `   2. Verificar fornecedores e prazos de entrega\n`;
                suggestion += `   3. Considerar produtos alternativos`;
            }

            alert(suggestion);
        };

        window.reserveMaterials = function() {
            if (intelligentSimulationData.length === 0) {
                alert('Execute primeiro uma simulação para reservar materiais.');
                return;
            }

            const availableMaterials = intelligentSimulationData.filter(item => item.status === 'available');

            if (availableMaterials.length === 0) {
                alert('Nenhum material disponível para reserva.');
                return;
            }

            const productCode = document.getElementById('intelligentProductCode').value.trim();

            let reservationText = `RESERVA DE MATERIAIS\n`;
            reservationText += `${'='.repeat(30)}\n\n`;
            reservationText += `📦 Produto: ${productCode}\n`;
            reservationText += `📋 Materiais a reservar:\n\n`;

            availableMaterials.forEach(item => {
                reservationText += `• ${item.produto.codigo}: ${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}\n`;
            });

            reservationText += `\n🔒 Confirma a reserva destes ${availableMaterials.length} materiais?`;

            if (confirm(reservationText)) {
                alert('Funcionalidade de reserva será implementada em breve!\n\nOs materiais seriam reservados no sistema.');
            }
        };

        // ===== RELATÓRIO DE SEQUENCIAMENTO INTELIGENTE =====

        window.generateSequencingReport = function() {
            if (opViabilityData.length === 0) {
                alert('Execute primeiro a análise de viabilidade para gerar o sequenciamento.');
                return;
            }

            // Agrupar por nível BOM
            const levelGroups = {};
            opViabilityData.forEach(item => {
                const level = item.bomLevel;
                if (!levelGroups[level]) {
                    levelGroups[level] = [];
                }
                levelGroups[level].push(item);
            });

            let report = `SEQUENCIAMENTO INTELIGENTE DE PRODUÇÃO\n`;
            report += `${'='.repeat(60)}\n\n`;
            report += `🎯 ANÁLISE DE OPs PENDENTES\n`;
            report += `📋 Foco apenas em OPs com status "Pendente" que precisam de decisão\n`;
            report += `🚀 OPs em produção, concluídas ou canceladas não são consideradas\n\n`;
            report += `🎯 PRIORIZAÇÃO POR NÍVEL BOM\n`;
            report += `📊 Produtos de nível mais alto são dependências críticas\n`;
            report += `🔄 Devem ser produzidos PRIMEIRO para não bloquear outros\n\n`;

            // Estatísticas gerais
            const totalOPs = opViabilityData.length;
            const viableOPs = opViabilityData.filter(item => item.viabilidade === 'viable').length;
            const partialOPs = opViabilityData.filter(item => item.viabilidade === 'partial').length;
            const blockedOPs = opViabilityData.filter(item => item.viabilidade === 'blocked').length;

            report += `📈 RESUMO GERAL:\n`;
            report += `   Total de OPs: ${totalOPs}\n`;
            report += `   🟢 Viáveis: ${viableOPs} (${Math.round(viableOPs/totalOPs*100)}%)\n`;
            report += `   🟡 Parciais: ${partialOPs} (${Math.round(partialOPs/totalOPs*100)}%)\n`;
            report += `   🔴 Bloqueadas: ${blockedOPs} (${Math.round(blockedOPs/totalOPs*100)}%)\n\n`;

            // Sequenciamento por nível
            const sortedLevels = Object.keys(levelGroups).sort((a, b) => parseInt(b) - parseInt(a));

            report += `🔢 SEQUENCIAMENTO POR NÍVEL BOM:\n`;
            report += `${'='.repeat(40)}\n\n`;

            let sequenceOrder = 1;

            sortedLevels.forEach(level => {
                const items = levelGroups[level];
                const levelInt = parseInt(level);

                let levelIcon, levelPriority;
                if (levelInt >= 4) {
                    levelIcon = '🔴';
                    levelPriority = 'CRÍTICO';
                } else if (levelInt >= 3) {
                    levelIcon = '🟠';
                    levelPriority = 'ALTO';
                } else if (levelInt >= 2) {
                    levelIcon = '🔵';
                    levelPriority = 'MÉDIO';
                } else if (levelInt >= 1) {
                    levelIcon = '🟢';
                    levelPriority = 'BAIXO';
                } else {
                    levelIcon = '⚪';
                    levelPriority = 'MP';
                }

                report += `${levelIcon} NÍVEL ${level} - PRIORIDADE ${levelPriority}\n`;
                report += `   📦 ${items.length} OP(s) neste nível\n`;

                if (levelInt > 0) {
                    const totalDependencies = items.reduce((sum, item) => sum + item.dependencies.length, 0);
                    report += `   🔗 ${totalDependencies} dependência(s) total\n`;
                    report += `   ⚠️ PRODUZIR PRIMEIRO - outros produtos dependem destes\n`;
                } else {
                    report += `   📋 Matéria-prima ou produtos independentes\n`;
                }

                report += `\n   SEQUÊNCIA RECOMENDADA:\n`;

                // Ordenar itens do nível por viabilidade e prioridade
                const sortedItems = items.sort((a, b) => {
                    // Viáveis primeiro
                    if (a.viabilidade === 'viable' && b.viabilidade !== 'viable') return -1;
                    if (b.viabilidade === 'viable' && a.viabilidade !== 'viable') return 1;

                    // Depois parciais
                    if (a.viabilidade === 'partial' && b.viabilidade === 'blocked') return -1;
                    if (b.viabilidade === 'partial' && a.viabilidade === 'blocked') return 1;

                    // Por percentual possível
                    return b.percentualPossivel - a.percentualPossivel;
                });

                sortedItems.forEach(item => {
                    const viabilityIcon = item.viabilidade === 'viable' ? '✅' :
                                         item.viabilidade === 'partial' ? '⚠️' : '❌';

                    report += `   ${sequenceOrder}. ${viabilityIcon} OP ${item.op.numero || item.op.id} - ${item.produto.codigo}\n`;
                    report += `      📊 ${item.percentualPossivel}% viável`;

                    if (item.dependencies.length > 0) {
                        report += ` | 🔗 ${item.dependencies.length} dependência(s)`;
                    }

                    if (item.materiaisFaltantes > 0) {
                        report += ` | ❌ ${item.materiaisFaltantes} material(is) em falta`;
                    }

                    report += `\n`;

                    if (item.dependencies.length > 0) {
                        report += `      🎯 Usado em: ${item.dependencies.slice(0, 3).map(d => d.codigo).join(', ')}`;
                        if (item.dependencies.length > 3) {
                            report += ` e mais ${item.dependencies.length - 3}`;
                        }
                        report += `\n`;
                    }

                    sequenceOrder++;
                });

                report += `\n`;
            });

            // Recomendações estratégicas
            report += `💡 RECOMENDAÇÕES ESTRATÉGICAS:\n`;
            report += `${'='.repeat(40)}\n\n`;

            const highLevelOPs = opViabilityData.filter(item => item.bomLevel >= 3);
            const criticalDependencies = opViabilityData.filter(item => item.dependencies.length >= 3);
            const blockedHighLevel = opViabilityData.filter(item => item.bomLevel >= 2 && item.viabilidade === 'blocked');

            if (highLevelOPs.length > 0) {
                report += `🔴 ATENÇÃO: ${highLevelOPs.length} OP(s) de nível alto (≥3)\n`;
                report += `   Priorize estas OPs para não bloquear a produção\n\n`;
            }

            if (criticalDependencies.length > 0) {
                report += `🔗 CRÍTICO: ${criticalDependencies.length} OP(s) com muitas dependências\n`;
                report += `   Estes produtos são usados em vários outros\n\n`;
            }

            if (blockedHighLevel.length > 0) {
                report += `❌ URGENTE: ${blockedHighLevel.length} OP(s) de nível alto bloqueadas\n`;
                report += `   Solicite materiais urgentemente para estas OPs\n\n`;
            }

            report += `🎯 ESTRATÉGIA RECOMENDADA:\n`;
            report += `1. Inicie OPs de nível mais alto que estão viáveis\n`;
            report += `2. Solicite materiais para OPs de nível alto bloqueadas\n`;
            report += `3. Execute OPs parciais de nível alto se necessário\n`;
            report += `4. Proceda com OPs de níveis menores\n`;
            report += `5. Monitore dependências para evitar gargalos\n\n`;

            report += `${'='.repeat(60)}\n`;
            report += `📊 Relatório gerado em: ${new Date().toLocaleString('pt-BR')}`;

            alert(report);
        };

        // ===== FUNÇÕES PLACEHOLDER PARA OUTRAS ABAS =====

        window.loadOPPlanning = function() {
            showLoadingTable('opPlanningTableBody', 'Carregando planejamento de OPs...');
            setTimeout(() => {
                document.getElementById('opPlanningTableBody').innerHTML = `
                    <tr><td colspan="9" style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-tools" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>Funcionalidade em desenvolvimento</div>
                    </td></tr>
                `;
            }, 1000);
        };

        window.runViabilityAnalysis = function() {
            alert('Análise de viabilidade será implementada em breve!');
        };

        window.loadDashboard = function() {
            // Atualizar estatísticas básicas
            document.getElementById('totalOPsDash').textContent = ordensProducao.length;
            document.getElementById('onTimeOPs').textContent = ordensProducao.filter(op => op.status === 'PENDENTE').length;
            document.getElementById('delayedOPs').textContent = ordensProducao.filter(op => op.status === 'EM_ANDAMENTO').length;
            document.getElementById('criticalOPs').textContent = ordensProducao.filter(op => op.status === 'PAUSADA').length;
            document.getElementById('productionEfficiency').textContent = '85%';
            document.getElementById('materialUtilization').textContent = '72%';

            // Alertas críticos
            document.getElementById('criticalAlerts').innerHTML = `
                <div style="padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 10px; background: #f8d7da;">
                    <strong>Material em Falta:</strong> 15 produtos críticos
                </div>
                <div style="padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 10px; background: #fff3cd;">
                    <strong>OPs Atrasadas:</strong> 3 ordens com atraso
                </div>
                <div style="padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 10px; background: #d1ecf1;">
                    <strong>Recursos Limitados:</strong> Centro de usinagem em manutenção
                </div>
            `;

            // Próximas entregas
            document.getElementById('upcomingDeliveries').innerHTML = `
                <div style="padding: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                    <strong>OP 12345</strong> - Entrega: ${new Date(Date.now() + 2*24*60*60*1000).toLocaleDateString('pt-BR')}
                </div>
                <div style="padding: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                    <strong>OP 12346</strong> - Entrega: ${new Date(Date.now() + 5*24*60*60*1000).toLocaleDateString('pt-BR')}
                </div>
                <div style="padding: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                    <strong>OP 12347</strong> - Entrega: ${new Date(Date.now() + 7*24*60*60*1000).toLocaleDateString('pt-BR')}
                </div>
            `;
        };

        // ===== NOVA FUNCIONALIDADE: SIMULAÇÃO DE PRODUÇÃO =====

        window.simulateProduction = function() {
            const productCode = document.getElementById('productCodeSimulation').value.trim();
            const quantity = parseInt(document.getElementById('quantitySimulation').value) || 1;

            if (!productCode) {
                alert('Por favor, digite o código do produto para simular a produção.');
                return;
            }

            // Buscar produto por código
            const produto = produtos.find(p =>
                p.codigo && p.codigo.toLowerCase() === productCode.toLowerCase()
            );

            if (!produto) {
                alert(`Produto com código "${productCode}" não encontrado.\n\nVerifique se o código está correto.`);
                return;
            }

            console.log('🎯 Simulando produção:', produto.codigo, '-', produto.descricao, 'Qtd:', quantity);

            // Simular uma OP temporária
            const simulatedOP = {
                id: 'simulation_' + Date.now(),
                numero: 'SIM-' + productCode,
                produtoId: produto.id,
                quantidade: quantity,
                status: 'SIMULACAO',
                dataInicio: new Date(),
                tipo: 'SIMULACAO'
            };

            // Adicionar OP simulada temporariamente
            const originalOPs = [...ordensProducao];
            ordensProducao.push(simulatedOP);

            // Executar análise
            loadMaterialAvailability().then(() => {
                // Filtrar apenas resultados da simulação
                const simulationResults = materialAvailabilityData.filter(item =>
                    item.opsRelacionadas.some(op => op.opId === simulatedOP.id)
                );

                // Mostrar resultados da simulação
                displaySimulationResults(produto, quantity, simulationResults);

                // Restaurar OPs originais
                ordensProducao = originalOPs;
            });
        };

        function displaySimulationResults(produto, quantity, results) {
            if (results.length === 0) {
                alert(`SIMULAÇÃO DE PRODUÇÃO\n\nProduto: ${produto.codigo} - ${produto.descricao}\nQuantidade: ${quantity}\n\n❌ Nenhum material necessário encontrado.\n\nVerifique se o produto possui estrutura (BOM) cadastrada.`);
                return;
            }

            let resultText = `SIMULAÇÃO DE PRODUÇÃO\n\n`;
            resultText += `Produto: ${produto.codigo} - ${produto.descricao}\n`;
            resultText += `Quantidade a Produzir: ${formatNumber(quantity)} ${produto.unidade || 'UN'}\n\n`;
            resultText += `MATERIAIS NECESSÁRIOS:\n`;
            resultText += `${'='.repeat(50)}\n\n`;

            let canProduce = true;
            let totalMaterials = results.length;
            let availableMaterials = 0;
            let insufficientMaterials = 0;
            let missingMaterials = 0;

            results.forEach(item => {
                const statusIcon = item.status === 'available' ? '✅' :
                                 item.status === 'insufficient' ? '⚠️' : '❌';
                const statusText = item.status === 'available' ? 'DISPONÍVEL' :
                                 item.status === 'insufficient' ? 'INSUFICIENTE' : 'EM FALTA';

                resultText += `${statusIcon} ${item.produto.codigo} - ${item.produto.descricao}\n`;
                resultText += `   Necessário: ${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}\n`;
                resultText += `   Disponível: ${formatNumber(item.saldoAtual)} ${item.produto.unidade || 'UN'}\n`;
                resultText += `   Status: ${statusText}\n`;

                if (item.status === 'available') {
                    availableMaterials++;
                } else if (item.status === 'insufficient') {
                    insufficientMaterials++;
                    canProduce = false;
                    const falta = item.quantidadeNecessaria - item.saldoAtual;
                    resultText += `   ⚠️ FALTA: ${formatNumber(falta)} ${item.produto.unidade || 'UN'}\n`;
                } else {
                    missingMaterials++;
                    canProduce = false;
                    resultText += `   ❌ COMPRAR: ${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}\n`;
                }

                resultText += `\n`;
            });

            resultText += `${'='.repeat(50)}\n`;
            resultText += `RESUMO DA ANÁLISE:\n\n`;
            resultText += `📊 Total de Materiais: ${totalMaterials}\n`;
            resultText += `✅ Disponíveis: ${availableMaterials}\n`;
            resultText += `⚠️ Insuficientes: ${insufficientMaterials}\n`;
            resultText += `❌ Em Falta: ${missingMaterials}\n\n`;

            if (canProduce) {
                resultText += `🎉 RESULTADO: PRODUÇÃO VIÁVEL!\n`;
                resultText += `✅ Todos os materiais estão disponíveis.\n`;
                resultText += `🚀 A produção pode ser iniciada imediatamente.`;
            } else {
                resultText += `⚠️ RESULTADO: PRODUÇÃO PARCIAL OU BLOQUEADA!\n`;
                if (insufficientMaterials > 0) {
                    resultText += `📦 ${insufficientMaterials} material(is) com estoque insuficiente.\n`;
                }
                if (missingMaterials > 0) {
                    resultText += `🛒 ${missingMaterials} material(is) precisam ser comprados.\n`;
                }
                resultText += `💡 Recomendação: Solicitar compra dos materiais em falta.`;
            }

            alert(resultText);

            // Destacar resultados na tabela
            highlightSimulationResults(results);
        }

        function highlightSimulationResults(results) {
            // Limpar filtros para mostrar todos os resultados
            document.getElementById('productFilter').value = '';
            document.getElementById('materialStatus').value = 'all';

            // Filtrar e exibir apenas os resultados da simulação
            displayMaterialAvailability(results);

            // Adicionar destaque visual
            setTimeout(() => {
                const rows = document.querySelectorAll('#materialAvailabilityTableBody tr');
                rows.forEach(row => {
                    row.style.backgroundColor = '#fff3cd';
                    row.style.border = '2px solid #ffc107';
                });

                // Remover destaque após 5 segundos
                setTimeout(() => {
                    rows.forEach(row => {
                        row.style.backgroundColor = '';
                        row.style.border = '';
                    });
                }, 5000);
            }, 100);
        }

        // ===== FUNÇÃO DE EXPLOSÃO DE BOM (baseada no relatorio_necessidades_compras.html) =====

        async function explodirEstruturaPCP(produtoId, quantidade, estruturas, nivel = 0, visited = new Set()) {
            if (nivel > 10) {
                console.warn('🔄 Estrutura muito profunda, possível ciclo detectado');
                return [];
            }

            if (visited.has(produtoId)) {
                console.warn('🔄 Ciclo detectado na estrutura:', produtoId);
                return [];
            }

            visited.add(produtoId);

            const produto = produtos.find(p => p.id === produtoId);
            if (!produto) {
                console.warn('❌ Produto não encontrado:', produtoId);
                return [];
            }

            console.log(`${'  '.repeat(nivel)}🔍 Explodindo ${produto.codigo} (${produto.tipo}) - Qtd: ${quantidade}`);

            // Se é MP (Matéria Prima), retorna a necessidade direta
            if (produto.tipo === 'MP') {
                console.log(`${'  '.repeat(nivel)}✅ MP encontrada: ${produto.codigo} - ${quantidade}`);
                return [{
                    produtoId: produtoId,
                    produto: produto,
                    quantidade: quantidade
                }];
            }

            // Se é PA (Produto Acabado) ou SP (Semi Produto), busca a estrutura e explode
            const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
            if (!estrutura || !estrutura.componentes) {
                console.warn(`${'  '.repeat(nivel)}⚠️ Estrutura não encontrada para produto: ${produto.codigo}`);
                return [];
            }

            console.log(`${'  '.repeat(nivel)}📋 Estrutura encontrada com ${estrutura.componentes.length} componentes`);

            const necessidadesMP = [];

            for (const componente of estrutura.componentes) {
                const quantidadeComponente = quantidade * (componente.quantidade || 0);
                const componenteProduto = produtos.find(p => p.id === componente.componentId);

                if (!componenteProduto) {
                    console.warn(`${'  '.repeat(nivel)}❌ Componente não encontrado:`, componente.componentId);
                    continue;
                }

                console.log(`${'  '.repeat(nivel)}  🔗 ${produto.codigo} -> ${componenteProduto.codigo} (${componenteProduto.tipo}) - Qtd: ${quantidadeComponente}`);

                if (componenteProduto.tipo === 'MP') {
                    // É MP, adiciona diretamente
                    necessidadesMP.push({
                        produtoId: componente.componentId,
                        produto: componenteProduto,
                        quantidade: quantidadeComponente
                    });
                    console.log(`${'  '.repeat(nivel)}  ✅ MP adicionada: ${componenteProduto.codigo} - ${quantidadeComponente}`);
                } else if (componenteProduto.tipo === 'SP' || componenteProduto.tipo === 'PA') {
                    // É SP/PA, explode recursivamente
                    console.log(`${'  '.repeat(nivel)}  🔄 Explodindo recursivamente: ${componenteProduto.codigo}`);
                    const subNecessidades = await explodirEstruturaPCP(
                        componente.componentId,
                        quantidadeComponente,
                        estruturas,
                        nivel + 1,
                        new Set(visited)
                    );
                    necessidadesMP.push(...subNecessidades);
                }
            }

            console.log(`${'  '.repeat(nivel)}📊 Total de MPs encontradas para ${produto.codigo}: ${necessidadesMP.length}`);
            return necessidadesMP;
        }

        // ===== FUNÇÕES DE PRODUÇÃO PARCIAL =====

        window.toggleProductionAnalysis = function() {
            const analysisMode = document.getElementById('productionAnalysis').value;
            const partialHeader = document.getElementById('partialProductionHeader');
            const partialInfo = document.getElementById('partialProductionInfo');

            if (analysisMode === 'partial') {
                partialHeader.style.display = 'table-cell';
                partialInfo.style.display = 'block';
                console.log('🔄 Modo de análise: Produção Parcial ativado');
            } else {
                partialHeader.style.display = 'none';
                partialInfo.style.display = 'none';
                console.log('🔄 Modo de análise: Padrão ativado');
            }

            // Reexibir dados com nova configuração
            if (materialAvailabilityData.length > 0) {
                displayMaterialAvailability();
            }
        };

        window.suggestPartialProduction = function(productId) {
            const item = materialAvailabilityData.find(m => m.produto.id === productId);
            if (!item) {
                alert('Produto não encontrado na análise.');
                return;
            }

            const percentage = item.percentualProducao || 0;
            const quantidadePossivel = Math.floor((item.saldoAtual / item.quantidadeNecessaria) * item.opsRelacionadas[0]?.quantidade || 0);

            let suggestion = `SUGESTÃO DE PRODUÇÃO PARCIAL\n`;
            suggestion += `${'='.repeat(40)}\n\n`;
            suggestion += `📦 Produto: ${item.produto.codigo} - ${item.produto.descricao}\n`;
            suggestion += `📊 Saldo Disponível: ${formatNumber(item.saldoAtual)} ${item.produto.unidade || 'UN'}\n`;
            suggestion += `📋 Necessário Total: ${formatNumber(item.quantidadeNecessaria)} ${item.produto.unidade || 'UN'}\n\n`;

            suggestion += `🎯 ANÁLISE DE PRODUÇÃO PARCIAL:\n`;
            suggestion += `✅ Percentual Possível: ${percentage}%\n`;
            suggestion += `📈 Quantidade Possível: ${quantidadePossivel} unidades\n\n`;

            if (percentage >= 75) {
                suggestion += `🟢 RECOMENDAÇÃO: PRODUÇÃO VIÁVEL!\n`;
                suggestion += `✅ Com ${percentage}% do material disponível, é possível produzir ${quantidadePossivel} unidades.\n`;
                suggestion += `💡 Sugestão: Iniciar produção parcial e solicitar compra do restante.`;
            } else if (percentage >= 50) {
                suggestion += `🟡 RECOMENDAÇÃO: PRODUÇÃO PARCIAL MODERADA\n`;
                suggestion += `⚠️ Com ${percentage}% do material, é possível produzir ${quantidadePossivel} unidades.\n`;
                suggestion += `💡 Sugestão: Avaliar se vale a pena iniciar produção parcial.`;
            } else if (percentage > 0) {
                suggestion += `🔴 RECOMENDAÇÃO: PRODUÇÃO PARCIAL LIMITADA\n`;
                suggestion += `❌ Apenas ${percentage}% do material disponível (${quantidadePossivel} unidades).\n`;
                suggestion += `💡 Sugestão: Aguardar mais material ou considerar outras prioridades.`;
            }

            suggestion += `\n\n📋 OPs Relacionadas:\n`;
            item.opsRelacionadas.forEach(op => {
                suggestion += `   • OP ${op.opNumero} - Qtd: ${op.quantidade} - Status: ${op.status}\n`;
            });

            alert(suggestion);
        };

        window.analyzePartialProductionForAllOPs = function() {
            if (materialAvailabilityData.length === 0) {
                alert('Execute primeiro a análise de materiais.');
                return;
            }

            const partialItems = materialAvailabilityData.filter(item =>
                item.percentualProducao > 0 && item.percentualProducao < 100
            );

            if (partialItems.length === 0) {
                alert('Nenhum material com possibilidade de produção parcial encontrado.');
                return;
            }

            let report = `RELATÓRIO DE PRODUÇÃO PARCIAL\n`;
            report += `${'='.repeat(50)}\n\n`;
            report += `📊 Total de Materiais Analisados: ${materialAvailabilityData.length}\n`;
            report += `🔄 Materiais com Produção Parcial Possível: ${partialItems.length}\n\n`;

            partialItems.forEach((item, index) => {
                const quantidadePossivel = Math.floor((item.saldoAtual / item.quantidadeNecessaria) * item.opsRelacionadas[0]?.quantidade || 0);

                report += `${index + 1}. ${item.produto.codigo} - ${item.produto.descricao}\n`;
                report += `   📈 ${item.percentualProducao}% possível (${quantidadePossivel} unidades)\n`;
                report += `   📊 Disponível: ${formatNumber(item.saldoAtual)} / Necessário: ${formatNumber(item.quantidadeNecessaria)}\n`;

                if (item.percentualProducao >= 75) {
                    report += `   🟢 ALTA viabilidade de produção parcial\n`;
                } else if (item.percentualProducao >= 50) {
                    report += `   🟡 MÉDIA viabilidade de produção parcial\n`;
                } else {
                    report += `   🔴 BAIXA viabilidade de produção parcial\n`;
                }
                report += `\n`;
            });

            alert(report);
        };

        // ===== FUNÇÃO PARA DEBUG DE OPs =====

        window.debugOPsLoading = async function() {
            console.log('🔍 INICIANDO DEBUG DE OPs...');

            let debugReport = `DEBUG DE ORDENS DE PRODUÇÃO\n`;
            debugReport += `${'='.repeat(50)}\n\n`;

            try {
                // Verificar conexão com Firebase
                debugReport += `🔗 TESTE DE CONEXÃO:\n`;
                debugReport += `✅ Firebase conectado: ${db ? 'SIM' : 'NÃO'}\n\n`;

                // Tentar carregar todas as OPs
                debugReport += `📋 CARREGAMENTO DE OPs:\n`;
                console.log('🔄 Tentando carregar todas as OPs...');

                const allOpsSnapshot = await getDocs(collection(db, "ordensProducao"));
                const allOPs = allOpsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                debugReport += `📊 Total de OPs no banco: ${allOPs.length}\n`;
                console.log(`📊 Total de OPs encontradas: ${allOPs.length}`);

                if (allOPs.length === 0) {
                    debugReport += `❌ PROBLEMA: Nenhuma OP encontrada no banco de dados!\n`;
                    debugReport += `💡 SOLUÇÕES:\n`;
                    debugReport += `   1. Verificar se existem OPs cadastradas no sistema\n`;
                    debugReport += `   2. Verificar permissões de acesso à coleção 'ordensProducao'\n`;
                    debugReport += `   3. Usar a simulação de produção para testar o sistema\n\n`;
                } else {
                    debugReport += `\n📋 ANÁLISE DAS OPs ENCONTRADAS:\n`;

                    // Analisar status das OPs
                    const statusCount = {};
                    allOPs.forEach(op => {
                        const status = op.status || 'SEM_STATUS';
                        statusCount[status] = (statusCount[status] || 0) + 1;
                    });

                    debugReport += `📊 Distribuição por Status:\n`;
                    Object.entries(statusCount).forEach(([status, count]) => {
                        debugReport += `   • ${status}: ${count} OP(s)\n`;
                    });

                    // Filtrar OPs relevantes
                    const relevantOPs = allOPs.filter(op =>
                        op.status && ['PENDENTE', 'EM_ANDAMENTO', 'PAUSADA'].includes(op.status.toUpperCase())
                    );

                    debugReport += `\n🎯 OPs Relevantes (PENDENTE/EM_ANDAMENTO/PAUSADA): ${relevantOPs.length}\n`;

                    if (relevantOPs.length === 0) {
                        debugReport += `⚠️ PROBLEMA: Nenhuma OP com status relevante!\n`;
                        debugReport += `💡 SOLUÇÕES:\n`;
                        debugReport += `   1. Verificar se há OPs com status PENDENTE, EM_ANDAMENTO ou PAUSADA\n`;
                        debugReport += `   2. Criar novas OPs com esses status\n`;
                        debugReport += `   3. Usar a simulação para testar sem OPs reais\n\n`;
                    } else {
                        debugReport += `\n📋 PRIMEIRAS 5 OPs RELEVANTES:\n`;
                        relevantOPs.slice(0, 5).forEach((op, index) => {
                            const produto = produtos.find(p => p.id === op.produtoId);
                            debugReport += `   ${index + 1}. OP ${op.numero || op.id}\n`;
                            debugReport += `      • Status: ${op.status}\n`;
                            debugReport += `      • Produto: ${produto?.codigo || 'NÃO_ENCONTRADO'}\n`;
                            debugReport += `      • Quantidade: ${op.quantidade || 'NÃO_DEFINIDA'}\n`;
                            debugReport += `      • Data: ${op.dataInicio ? new Date(op.dataInicio.seconds * 1000).toLocaleDateString() : 'NÃO_DEFINIDA'}\n\n`;
                        });
                    }

                    // Verificar produtos relacionados
                    debugReport += `🔗 VERIFICAÇÃO DE PRODUTOS:\n`;
                    const opsWithoutProduct = allOPs.filter(op => {
                        const produto = produtos.find(p => p.id === op.produtoId);
                        return !produto;
                    });

                    if (opsWithoutProduct.length > 0) {
                        debugReport += `⚠️ ${opsWithoutProduct.length} OP(s) com produto não encontrado\n`;
                    } else {
                        debugReport += `✅ Todos os produtos das OPs foram encontrados\n`;
                    }
                }

                // Atualizar variável global com dados de debug
                if (allOPs.length > 0) {
                    const relevantOPs = allOPs.filter(op =>
                        op.status && ['PENDENTE', 'EM_ANDAMENTO', 'PAUSADA'].includes(op.status.toUpperCase())
                    );

                    if (relevantOPs.length > 0) {
                        ordensProducao = relevantOPs;
                        updateDataCounters();
                        await loadOPFilter();
                        debugReport += `\n✅ OPs carregadas com sucesso! Recarregue a análise.\n`;
                    }
                }

            } catch (error) {
                debugReport += `❌ ERRO DURANTE DEBUG: ${error.message}\n`;
                debugReport += `🔍 Detalhes do erro: ${error.stack}\n`;
                console.error('❌ Erro no debug:', error);
            }

            debugReport += `\n${'='.repeat(50)}\n`;
            debugReport += `🔍 Debug concluído. Verifique o console para mais detalhes.`;

            alert(debugReport);
        };

        // ===== FUNÇÃO PARA CRIAR OPs DE EXEMPLO =====

        window.createExampleOPs = function() {
            if (produtos.length === 0) {
                alert('❌ Não é possível criar OPs de exemplo sem produtos cadastrados.\n\nCadastre alguns produtos primeiro.');
                return;
            }

            const confirm = window.confirm(
                `CRIAR OPs DE EXEMPLO\n\n` +
                `Isso criará algumas ordens de produção de exemplo para testar o sistema.\n\n` +
                `📋 Serão criadas 3 OPs com diferentes status\n` +
                `📦 Usando produtos existentes: ${produtos.length} disponíveis\n\n` +
                `Deseja continuar?`
            );

            if (!confirm) return;

            try {
                // Criar OPs de exemplo
                const exampleOPs = [
                    {
                        id: 'op_exemplo_001_' + Date.now(),
                        numero: 'EX001',
                        produtoId: produtos[0]?.id,
                        quantidade: 100,
                        status: 'PENDENTE',
                        dataInicio: new Date(),
                        dataFim: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                        tipo: 'EXEMPLO',
                        observacoes: 'OP criada automaticamente para teste do sistema PCP'
                    },
                    {
                        id: 'op_exemplo_002_' + Date.now(),
                        numero: 'EX002',
                        produtoId: produtos[Math.min(1, produtos.length - 1)]?.id,
                        quantidade: 50,
                        status: 'EM_ANDAMENTO',
                        dataInicio: new Date(),
                        dataFim: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
                        tipo: 'EXEMPLO',
                        observacoes: 'OP criada automaticamente para teste do sistema PCP'
                    },
                    {
                        id: 'op_exemplo_003_' + Date.now(),
                        numero: 'EX003',
                        produtoId: produtos[Math.min(2, produtos.length - 1)]?.id,
                        quantidade: 75,
                        status: 'PAUSADA',
                        dataInicio: new Date(),
                        dataFim: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
                        tipo: 'EXEMPLO',
                        observacoes: 'OP criada automaticamente para teste do sistema PCP'
                    }
                ];

                // Adicionar às OPs existentes
                ordensProducao = [...ordensProducao, ...exampleOPs];

                // Atualizar interface
                updateDataCounters();
                loadOPFilter();

                console.log('✅ OPs de exemplo criadas:', exampleOPs.length);

                let successMessage = `✅ OPs DE EXEMPLO CRIADAS COM SUCESSO!\n\n`;
                successMessage += `📋 ${exampleOPs.length} OPs criadas:\n\n`;

                exampleOPs.forEach((op, index) => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    successMessage += `${index + 1}. OP ${op.numero}\n`;
                    successMessage += `   • Produto: ${produto?.codigo || 'N/A'}\n`;
                    successMessage += `   • Quantidade: ${op.quantidade}\n`;
                    successMessage += `   • Status: ${op.status}\n\n`;
                });

                successMessage += `🎯 Agora você pode:\n`;
                successMessage += `✅ Executar "Atualizar Análise"\n`;
                successMessage += `✅ Testar filtros por OP\n`;
                successMessage += `✅ Usar análise de produção parcial\n`;
                successMessage += `✅ Simular produção de outros produtos\n\n`;
                successMessage += `💡 Estas são OPs temporárias para teste. Para OPs reais, use o sistema de produção.`;

                alert(successMessage);

            } catch (error) {
                console.error('❌ Erro ao criar OPs de exemplo:', error);
                alert('❌ Erro ao criar OPs de exemplo: ' + error.message);
            }
        };

        // ===== FUNÇÃO PARA VERIFICAR DADOS CARREGADOS =====

        window.showDataDetails = function() {
            let details = `VERIFICAÇÃO DE DADOS DO SISTEMA\n`;
            details += `${'='.repeat(50)}\n\n`;

            // Status geral
            details += `📊 RESUMO GERAL:\n`;
            details += `📦 Produtos: ${produtos.length}\n`;
            details += `📊 Estoques: ${estoques.length}\n`;
            details += `🏭 Armazéns: ${armazens.length}\n`;
            details += `📋 OPs: ${ordensProducao.length}\n`;
            details += `🔧 Estruturas: ${estruturas.length}\n\n`;

            // Detalhes dos produtos
            if (produtos.length > 0) {
                details += `📦 PRODUTOS (primeiros 5):\n`;
                produtos.slice(0, 5).forEach(p => {
                    details += `   • ${p.codigo || 'SEM_CODIGO'} - ${p.descricao || 'SEM_DESCRIÇÃO'}\n`;
                });
                if (produtos.length > 5) {
                    details += `   ... e mais ${produtos.length - 5} produtos\n`;
                }
                details += `\n`;
            } else {
                details += `❌ PRODUTOS: Nenhum produto encontrado!\n\n`;
            }

            // Detalhes dos estoques
            if (estoques.length > 0) {
                details += `📊 ESTOQUES (primeiros 5):\n`;
                estoques.slice(0, 5).forEach(e => {
                    const produto = produtos.find(p => p.id === e.produtoId);
                    const armazem = armazens.find(a => a.id === e.armazemId);
                    details += `   • ${produto?.codigo || 'PRODUTO_NÃO_ENCONTRADO'} - Saldo: ${e.saldo || 0} - Armazém: ${armazem?.codigo || 'ARMAZÉM_NÃO_ENCONTRADO'}\n`;
                });
                if (estoques.length > 5) {
                    details += `   ... e mais ${estoques.length - 5} estoques\n`;
                }
                details += `\n`;
            } else {
                details += `❌ ESTOQUES: Nenhum estoque encontrado!\n\n`;
            }

            // Detalhes dos armazéns
            if (armazens.length > 0) {
                details += `🏭 ARMAZÉNS:\n`;
                armazens.forEach(a => {
                    details += `   • ${a.codigo || 'SEM_CODIGO'} - ${a.nome || 'SEM_NOME'}\n`;
                });
                details += `\n`;
            } else {
                details += `❌ ARMAZÉNS: Nenhum armazém encontrado!\n\n`;
            }

            // Detalhes das OPs
            if (ordensProducao.length > 0) {
                details += `📋 ORDENS DE PRODUÇÃO:\n`;
                ordensProducao.forEach(op => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    details += `   • OP ${op.numero || op.id} - ${produto?.codigo || 'PRODUTO_NÃO_ENCONTRADO'} - Qtd: ${op.quantidade || 0} - Status: ${op.status || 'SEM_STATUS'}\n`;
                });
                details += `\n`;
            } else {
                details += `❌ OPs: Nenhuma ordem de produção pendente encontrada!\n\n`;
            }

            // Detalhes das estruturas
            if (estruturas.length > 0) {
                details += `🔧 ESTRUTURAS (BOM) - Primeiros 5:\n`;
                const estruturasGrouped = {};
                estruturas.forEach(e => {
                    const produtoPai = produtos.find(p => p.id === e.produtoPaiId);
                    const key = produtoPai?.codigo || 'PRODUTO_NÃO_ENCONTRADO';
                    if (!estruturasGrouped[key]) {
                        estruturasGrouped[key] = 0;
                    }
                    estruturasGrouped[key]++;
                });

                Object.entries(estruturasGrouped).slice(0, 5).forEach(([codigo, count]) => {
                    details += `   • ${codigo} - ${count} componente(s)\n`;
                });

                if (Object.keys(estruturasGrouped).length > 5) {
                    details += `   ... e mais ${Object.keys(estruturasGrouped).length - 5} produtos com estrutura\n`;
                }
                details += `\n`;
            } else {
                details += `❌ ESTRUTURAS: Nenhuma estrutura (BOM) encontrada!\n\n`;
            }

            // Diagnóstico
            details += `${'='.repeat(50)}\n`;
            details += `🔍 DIAGNÓSTICO:\n\n`;

            if (produtos.length === 0) {
                details += `❌ SEM PRODUTOS: Não é possível fazer análise sem produtos cadastrados.\n`;
            }
            if (estoques.length === 0) {
                details += `❌ SEM ESTOQUES: Não é possível verificar disponibilidade sem estoques.\n`;
            }
            if (ordensProducao.length === 0) {
                details += `⚠️ SEM OPs: Nenhuma OP pendente. Use a simulação para analisar produtos.\n`;
            }
            if (estruturas.length === 0) {
                details += `❌ SEM ESTRUTURAS: Não é possível fazer explosão de BOM sem estruturas.\n`;
            }
            if (armazens.length === 0) {
                details += `❌ SEM ARMAZÉNS: Não é possível filtrar por localização.\n`;
            }

            if (produtos.length > 0 && estoques.length > 0 && estruturas.length > 0) {
                details += `✅ SISTEMA OPERACIONAL: Dados suficientes para análise completa.\n`;
            }

            alert(details);
        };

        // ===== FUNÇÕES COMPLEMENTARES DE VIABILIDADE =====

        window.viewOPDetails = function(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                alert('OP não encontrada.');
                return;
            }

            const produto = produtos.find(p => p.id === op.produtoId);
            const viabilityItem = opViabilityData.find(item => item.op.id === opId);

            let details = `DETALHES DA ORDEM DE PRODUÇÃO\n`;
            details += `${'='.repeat(50)}\n\n`;
            details += `📋 OP: ${op.numero || op.id}\n`;
            details += `📦 Produto: ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}\n`;
            details += `📊 Quantidade: ${op.quantidade || 0} ${produto?.unidade || 'UN'}\n`;
            details += `📅 Status: ${op.status || 'N/A'}\n\n`;

            if (viabilityItem) {
                details += `🎯 ANÁLISE DE VIABILIDADE:\n`;
                details += `✅ Viabilidade: ${viabilityItem.viabilidade}\n`;
                details += `📈 Percentual Possível: ${viabilityItem.percentualPossivel}%\n`;
                details += `🔴 Materiais Faltantes: ${viabilityItem.materiaisFaltantes} de ${viabilityItem.totalMateriais}\n`;
                details += `⚡ Prioridade: ${viabilityItem.prioridade}\n`;
                details += `🏗️ Nível BOM: ${viabilityItem.bomLevel}\n`;
                details += `🔗 Dependências: ${viabilityItem.dependencies.length}\n\n`;

                if (viabilityItem.dependencies.length > 0) {
                    details += `📋 PRODUTOS QUE DEPENDEM DESTA OP:\n`;
                    viabilityItem.dependencies.forEach(dep => {
                        details += `   • ${dep.codigo} (Nível ${dep.nivel})\n`;
                    });
                    details += `\n`;
                }

                details += `💡 DETALHES: ${viabilityItem.detalhes}\n`;
            }

            if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
                details += `\n📦 MATERIAIS NECESSÁRIOS:\n`;
                op.materiaisNecessarios.forEach(material => {
                    const materialProduto = produtos.find(p => p.id === material.produtoId);
                    details += `   • ${materialProduto?.codigo || 'N/A'}: ${material.quantidade} ${materialProduto?.unidade || 'UN'}\n`;
                });
            }

            alert(details);
        };

        window.startOP = function(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                alert('OP não encontrada.');
                return;
            }

            const confirm = window.confirm(
                `INICIAR ORDEM DE PRODUÇÃO\n\n` +
                `OP: ${op.numero || op.id}\n` +
                `Produto: ${produtos.find(p => p.id === op.produtoId)?.codigo || 'N/A'}\n` +
                `Quantidade: ${op.quantidade || 0}\n\n` +
                `Confirma o início desta OP?`
            );

            if (confirm) {
                alert('Funcionalidade de iniciar OP será implementada em breve!\n\nA OP seria marcada como "Em Produção" no sistema.');
            }
        };

        window.suggestPartialOP = function(opId) {
            const viabilityItem = opViabilityData.find(item => item.op.id === opId);
            if (!viabilityItem) {
                alert('Dados de viabilidade não encontrados para esta OP.');
                return;
            }

            const quantidadePossivel = Math.floor((viabilityItem.percentualPossivel / 100) * viabilityItem.op.quantidade);

            let suggestion = `SUGESTÃO DE PRODUÇÃO PARCIAL\n`;
            suggestion += `${'='.repeat(40)}\n\n`;
            suggestion += `📋 OP: ${viabilityItem.op.numero || viabilityItem.op.id}\n`;
            suggestion += `📦 Produto: ${viabilityItem.produto.codigo}\n`;
            suggestion += `📊 Quantidade Total: ${viabilityItem.op.quantidade}\n`;
            suggestion += `📈 Quantidade Possível: ${quantidadePossivel} (${viabilityItem.percentualPossivel}%)\n\n`;

            if (viabilityItem.percentualPossivel >= 75) {
                suggestion += `🟢 RECOMENDAÇÃO: PRODUÇÃO PARCIAL VIÁVEL!\n`;
                suggestion += `✅ Inicie produção de ${quantidadePossivel} unidades\n`;
                suggestion += `🛒 Solicite materiais para completar o restante\n`;
            } else if (viabilityItem.percentualPossivel >= 50) {
                suggestion += `🟡 RECOMENDAÇÃO: AVALIAR PRODUÇÃO PARCIAL\n`;
                suggestion += `⚠️ Produção de ${quantidadePossivel} unidades possível\n`;
                suggestion += `💭 Considere se vale a pena iniciar com esta quantidade\n`;
            } else {
                suggestion += `🔴 RECOMENDAÇÃO: AGUARDAR MAIS MATERIAIS\n`;
                suggestion += `❌ Apenas ${quantidadePossivel} unidades possíveis\n`;
                suggestion += `🛒 Priorize compra de materiais antes de iniciar\n`;
            }

            suggestion += `\n💡 Materiais faltantes: ${viabilityItem.materiaisFaltantes} de ${viabilityItem.totalMateriais}`;

            alert(suggestion);
        };

        window.requestMissingMaterials = function(opId) {
            const viabilityItem = opViabilityData.find(item => item.op.id === opId);
            if (!viabilityItem) {
                alert('Dados de viabilidade não encontrados para esta OP.');
                return;
            }

            alert(`SOLICITAÇÃO DE MATERIAIS\n\nFuncionalidade será implementada em breve!\n\nSeria criada uma solicitação de compra para os ${viabilityItem.materiaisFaltantes} materiais em falta da OP ${viabilityItem.op.numero || viabilityItem.op.id}.`);
        };

        window.generateViabilityReport = function() {
            if (opViabilityData.length === 0) {
                alert('Execute primeiro a análise de viabilidade para gerar o relatório.');
                return;
            }

            let report = `RELATÓRIO DETALHADO DE VIABILIDADE\n`;
            report += `${'='.repeat(50)}\n\n`;
            report += `📅 Data: ${new Date().toLocaleString('pt-BR')}\n`;
            report += `📊 Total de OPs Analisadas: ${opViabilityData.length}\n\n`;

            // Estatísticas
            const viable = opViabilityData.filter(item => item.viabilidade === 'viable').length;
            const partial = opViabilityData.filter(item => item.viabilidade === 'partial').length;
            const blocked = opViabilityData.filter(item => item.viabilidade === 'blocked').length;

            report += `📈 RESUMO EXECUTIVO:\n`;
            report += `🟢 OPs Viáveis: ${viable} (${Math.round(viable/opViabilityData.length*100)}%)\n`;
            report += `🟡 OPs Parciais: ${partial} (${Math.round(partial/opViabilityData.length*100)}%)\n`;
            report += `🔴 OPs Bloqueadas: ${blocked} (${Math.round(blocked/opViabilityData.length*100)}%)\n\n`;

            // Detalhes por OP
            report += `📋 DETALHES POR OP:\n`;
            report += `${'='.repeat(30)}\n\n`;

            opViabilityData.forEach((item, index) => {
                const viabilityIcon = item.viabilidade === 'viable' ? '✅' :
                                     item.viabilidade === 'partial' ? '⚠️' : '❌';

                report += `${index + 1}. ${viabilityIcon} OP ${item.op.numero || item.op.id}\n`;
                report += `   📦 ${item.produto.codigo} - ${item.produto.descricao}\n`;
                report += `   📊 ${item.percentualPossivel}% viável | Nível BOM: ${item.bomLevel}\n`;
                report += `   🔴 ${item.materiaisFaltantes} materiais faltantes de ${item.totalMateriais}\n`;
                report += `   ⚡ Prioridade: ${item.prioridade}\n`;
                if (item.dependencies.length > 0) {
                    report += `   🔗 ${item.dependencies.length} dependência(s)\n`;
                }
                report += `\n`;
            });

            alert(report);
        };

        // Funções placeholder para outras funcionalidades
        window.sortOPs = function() { alert('Funcionalidade em desenvolvimento!'); };
        window.filterOPs = function() { alert('Funcionalidade em desenvolvimento!'); };
        window.updateViabilityAnalysis = function() { alert('Funcionalidade em desenvolvimento!'); };
        window.exportAnalysisReport = function() { alert('Funcionalidade em desenvolvimento!'); };
        window.exportDashboardReport = function() { alert('Funcionalidade em desenvolvimento!'); };
    </script>
</body>
</html>
