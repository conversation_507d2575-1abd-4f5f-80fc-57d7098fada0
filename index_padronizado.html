<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <script>
        if (!localStorage.getItem('currentUser')) {
            window.location.href = 'login.html';
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiZAR ERP - Sistema de Gestão Empresarial</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles/sistema-padronizado.css">
    <style>
        /* Estilos específicos para o dashboard */
        body {
            background: var(--bg-secondary);
            padding: 0;
        }

        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100%;
            width: 280px;
            background: linear-gradient(180deg, var(--secondary-color) 0%, #1a242e 100%);
            padding: var(--spacing-lg);
            transition: all var(--transition-normal);
            overflow-y: auto;
            z-index: var(--z-fixed);
            box-shadow: var(--shadow-md);
        }

        .sidebar .logo {
            display: block;
            width: 100%;
            max-width: 240px;
            height: auto;
            margin: 0 auto var(--spacing-lg) auto;
        }

        .user-info {
            color: var(--text-white);
            margin-bottom: var(--spacing-xl);
            text-align: center;
            padding: var(--spacing-md);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-md);
        }

        .user-info .username {
            font-weight: var(--font-weight-bold);
            color: var(--primary-color);
            font-size: var(--font-size-lg);
        }

        .logout-btn {
            background: none;
            border: none;
            color: var(--danger-color);
            cursor: pointer;
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-sm);
            transition: color var(--transition-fast);
        }

        .logout-btn:hover {
            color: var(--danger-hover);
        }

        .nav-list {
            list-style: none;
        }

        .nav-list li {
            margin-bottom: var(--spacing-xs);
        }

        .section-title {
            color: #9191a5;
            font-size: var(--font-size-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: var(--font-weight-semibold);
        }

        .menu-button {
            display: flex;
            align-items: center;
            padding: 12px var(--spacing-md);
            color: var(--text-white);
            background: rgba(255, 255, 255, 0.08);
            border-radius: var(--border-radius-md);
            text-decoration: none;
            font-size: var(--font-size-sm);
            transition: all var(--transition-normal);
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: left;
            margin-bottom: 2px;
            position: relative;
            overflow: hidden;
        }

        .menu-button::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: transform var(--transition-normal);
        }

        .menu-button i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: var(--font-size-sm);
        }

        .menu-button:hover {
            background: rgba(255, 255, 255, 0.15);
            color: var(--text-white);
            transform: translateX(3px);
            box-shadow: var(--shadow-sm);
        }

        .menu-button:hover::before {
            transform: scaleY(1);
        }

        .menu-button.highlight-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--text-white);
            font-weight: var(--font-weight-semibold);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-md);
        }

        .menu-button.highlight-btn:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
            transform: translateX(5px);
            box-shadow: var(--shadow-lg);
        }

        .menu-button.highlight-btn::after {
            content: "NOVO";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--warning-color), var(--warning-hover));
            color: #000;
            padding: 2px 6px;
            border-radius: var(--border-radius-md);
            font-size: 9px;
            font-weight: var(--font-weight-bold);
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-sm);
        }

        .main-content {
            margin-left: 280px;
            flex: 1;
            background: var(--bg-secondary);
        }

        .main-header {
            background: var(--bg-primary);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-section h1 {
            margin: 0;
            font-size: var(--font-size-xxl);
            color: var(--primary-color);
            font-weight: var(--font-weight-semibold);
        }

        .welcome-section p {
            margin: var(--spacing-xs) 0 0 0;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .system-info {
            text-align: right;
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
        }

        .system-info .status {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--success-light);
            color: var(--success-color);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-xl);
            font-size: 11px;
            font-weight: var(--font-weight-semibold);
            margin-top: var(--spacing-xs);
        }

        .dashboard-content {
            padding: var(--spacing-xl);
        }

        .center-image {
            display: block;
            max-width: 100%;
            height: auto;
            margin: var(--spacing-lg) auto 0 auto;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
        }

        .version-info {
            text-align: center;
            color: var(--text-secondary);
            font-size: var(--font-size-xs);
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
        }

        .menu-toggle {
            display: none;
            position: fixed;
            top: var(--spacing-lg);
            left: var(--spacing-lg);
            z-index: calc(var(--z-fixed) + 1);
            background: var(--primary-color);
            color: var(--text-white);
            border: none;
            padding: 12px var(--spacing-md);
            font-size: var(--font-size-lg);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-md);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .menu-toggle:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .sidebar {
                left: -280px;
                box-shadow: none;
            }

            .sidebar.active {
                left: 0;
                box-shadow: var(--shadow-xl);
            }

            .main-content {
                margin-left: 0;
            }

            .main-header {
                padding: var(--spacing-md) var(--spacing-lg);
            }

            .header-content {
                flex-direction: column;
                gap: var(--spacing-md);
                text-align: center;
            }

            .welcome-section h1 {
                font-size: var(--font-size-xl);
            }

            .menu-toggle {
                display: block;
            }

            .dashboard-content {
                padding: var(--spacing-lg) var(--spacing-md);
            }
        }

        @media (max-width: 480px) {
            .dashboard-content {
                padding: var(--spacing-md) var(--spacing-sm);
            }

            .main-header {
                padding: 12px var(--spacing-md);
            }

            .welcome-section h1 {
                font-size: var(--font-size-lg);
            }

            .center-image {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Logo -->
            <svg id="companyLogo" width="240" height="100" viewBox="0 0 400 140" xmlns="http://www.w3.org/2000/svg" class="logo">
                <defs>
                    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                    </linearGradient>
                    <radialGradient id="glowCircle" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0" />
                    </radialGradient>
                </defs>
                <text x="30" y="90" font-family="'Arial Black', 'Helvetica', sans-serif" font-size="64" font-weight="900" fill="url(#textGradient)" letter-spacing="6">WiZAR</text>
                <circle cx="55" cy="65" r="25" fill="url(#glowCircle)" opacity="0.6" />
                <polygon points="70,40 72,45 77,45 73,49 75,54 70,51 65,54 67,49 63,45 68,45" fill="#fbbf24" />
                <text x="30" y="115" font-family="Arial, sans-serif" font-size="16" fill="#9ca3af" letter-spacing="2">ERP SYSTEM</text>
            </svg>

            <!-- Informações do usuário -->
            <div class="user-info">
                <div class="username" id="currentUserDisplay">Usuário</div>
                <div style="font-size: 12px; margin-top: 5px;">Sistema Online</div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Sair
                </button>
            </div>

            <!-- Menu de navegação -->
            <ul class="nav-list">
                <!-- Seção Principal -->
                <li class="section-title">PRINCIPAL</li>
                <li>
                    <a href="dashboard_fluxo_compras.html" class="menu-button highlight-btn">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard Compras
                    </a>
                </li>
                <li>
                    <a href="solicitacao_compras.html" class="menu-button">
                        <i class="fas fa-shopping-cart"></i>
                        Solicitações de Compra
                    </a>
                </li>
                <li>
                    <a href="cotacoes/index.html" class="menu-button">
                        <i class="fas fa-file-invoice"></i>
                        Cotações
                    </a>
                </li>
                <li>
                    <a href="pedidos_compra.html" class="menu-button">
                        <i class="fas fa-file-contract"></i>
                        Pedidos de Compra
                    </a>
                </li>

                <!-- Seção Cadastros -->
                <li class="section-title">CADASTROS</li>
                <li>
                    <a href="cadastro_produto.html" class="menu-button">
                        <i class="fas fa-box"></i>
                        Produtos
                    </a>
                </li>
                <li>
                    <a href="cadastro_fornecedores.html" class="menu-button">
                        <i class="fas fa-truck"></i>
                        Fornecedores
                    </a>
                </li>
                <li>
                    <a href="cadastro_usuarios.html" class="menu-button">
                        <i class="fas fa-users"></i>
                        Usuários
                    </a>
                </li>

                <!-- Seção Estoque -->
                <li class="section-title">ESTOQUE</li>
                <li>
                    <a href="estoques.html" class="menu-button">
                        <i class="fas fa-warehouse"></i>
                        Consulta Estoque
                    </a>
                </li>
                <li>
                    <a href="entrada_material.html" class="menu-button">
                        <i class="fas fa-arrow-down"></i>
                        Entrada Material
                    </a>
                </li>
                <li>
                    <a href="recebimento_materiais.html" class="menu-button">
                        <i class="fas fa-clipboard-check"></i>
                        Recebimento
                    </a>
                </li>

                <!-- Seção Produção -->
                <li class="section-title">PRODUÇÃO</li>
                <li>
                    <a href="ordens_producao.html" class="menu-button">
                        <i class="fas fa-cogs"></i>
                        Ordens de Produção
                    </a>
                </li>
                <li>
                    <a href="apontamentos.html" class="menu-button">
                        <i class="fas fa-clock"></i>
                        Apontamentos
                    </a>
                </li>

                <!-- Seção Qualidade -->
                <li class="section-title">QUALIDADE</li>
                <li>
                    <a href="aprovacao_qualidade.html" class="menu-button">
                        <i class="fas fa-check-circle"></i>
                        Aprovação Qualidade
                    </a>
                </li>
                <li>
                    <a href="controle_qualidade.html" class="menu-button">
                        <i class="fas fa-search"></i>
                        Controle Qualidade
                    </a>
                </li>

                <!-- Seção Relatórios -->
                <li class="section-title">RELATÓRIOS</li>
                <li>
                    <a href="relatorio_estoque_simples.html" class="menu-button">
                        <i class="fas fa-chart-bar"></i>
                        Relatório Estoque
                    </a>
                </li>
                <li>
                    <a href="relatorio_compras.html" class="menu-button">
                        <i class="fas fa-chart-line"></i>
                        Relatório Compras
                    </a>
                </li>

                <!-- Seção Configurações -->
                <li class="section-title">CONFIGURAÇÕES</li>
                <li>
                    <a href="config_parametros.html" class="menu-button">
                        <i class="fas fa-cog"></i>
                        Parâmetros
                    </a>
                </li>
                <li>
                    <a href="backup_sistema.html" class="menu-button">
                        <i class="fas fa-database"></i>
                        Backup Sistema
                    </a>
                </li>
            </ul>
        </div>

        <!-- Conteúdo Principal -->
        <div class="main-content">
            <!-- Header -->
            <div class="main-header">
                <div class="header-content">
                    <div class="welcome-section">
                        <h1><i class="fas fa-home"></i> WiZAR ERP</h1>
                        <p>Sistema de Gestão Empresarial Integrado</p>
                    </div>
                    <div class="system-info">
                        <div>Versão 2.0.1</div>
                        <div class="status">
                            <i class="fas fa-circle"></i>
                            Sistema Online
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conteúdo do Dashboard -->
            <div class="dashboard-content">
                <!-- Cards de estatísticas -->
                <div class="stats-grid">
                    <div class="stat-card" style="border-left-color: var(--primary-color);">
                        <div class="stat-number" id="totalSolicitacoes">0</div>
                        <div class="stat-label">Solicitações Ativas</div>
                    </div>
                    <div class="stat-card" style="border-left-color: var(--warning-color);">
                        <div class="stat-number" id="totalCotacoes">0</div>
                        <div class="stat-label">Cotações Pendentes</div>
                    </div>
                    <div class="stat-card" style="border-left-color: var(--success-color);">
                        <div class="stat-number" id="totalPedidos">0</div>
                        <div class="stat-label">Pedidos do Mês</div>
                    </div>
                    <div class="stat-card" style="border-left-color: var(--info-color);">
                        <div class="stat-number" id="totalEstoque">0</div>
                        <div class="stat-label">Itens em Estoque</div>
                    </div>
                </div>

                <!-- Imagem central -->
                <img src="attached_assets/image_1748384604843.png" alt="Dashboard WiZAR" class="center-image" id="centerImage">

                <!-- Informações da versão -->
                <div class="version-info">
                    <p><strong>WiZAR ERP</strong> - Sistema de Gestão Empresarial</p>
                    <p>Versão 2.0.1 | Desenvolvido com tecnologia moderna</p>
                    <p>© 2024 - Todos os direitos reservados</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Botão toggle para mobile -->
    <button class="menu-toggle" id="menuToggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Scripts -->
    <script>
        // Carregar informações do usuário
        document.addEventListener('DOMContentLoaded', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                document.getElementById('currentUserDisplay').textContent = currentUser;
            }

            // Carregar estatísticas do dashboard
            loadDashboardStats();
        });

        // Toggle sidebar para mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // Logout
        function logout() {
            if (confirm('Deseja realmente sair do sistema?')) {
                localStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
        }

        // Carregar estatísticas do dashboard
        async function loadDashboardStats() {
            try {
                // Simular carregamento de dados
                document.getElementById('totalSolicitacoes').textContent = '25';
                document.getElementById('totalCotacoes').textContent = '8';
                document.getElementById('totalPedidos').textContent = '142';
                document.getElementById('totalEstoque').textContent = '1,247';
            } catch (error) {
                console.error('Erro ao carregar estatísticas:', error);
            }
        }

        // Fechar sidebar ao clicar fora (mobile)
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuToggle = document.getElementById('menuToggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !menuToggle.contains(event.target) &&
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Remover classe active ao redimensionar para desktop
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('active');
            }
        });
    </script>
</body>
</html>
