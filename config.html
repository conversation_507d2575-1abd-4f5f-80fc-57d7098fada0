/* Variáve<PERSON> globais */
:root {
  --primary-color: #0854a0;
  --primary-hover: #0a4d8c;
  --secondary-color: #f0f3f6;
  --border-color: #d4d4d4;
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #107e3e;
  --header-bg: #354a5f;
  --danger-color: #dc3545;
}

/* Reset e estilos base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f7f7f7;
  color: var(--text-color);
  line-height: 1.6;
}

/* Layout principal */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 50px auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  background-color: var(--header-bg);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  margin: -20px -20px 20px -20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  font-size: 24px;
  font-weight: 500;
}

/* Formulários e seções */
.form-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid var(--border-color);
}

.form-title {
  font-size: 18px;
  color: var(--primary-color);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.form-section {
  position: relative;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid var(--primary-color);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
  display: flex;
  align-items: center;
}

/* Grid e layout de formulário */
.form-row {
  display: flex;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 15px;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.form-field {
  margin-bottom: 15px;
}

.field-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

/* Elementos de formulário */
label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-secondary);
  font-weight: 500;
}

input[type="text"],
input[type="number"],
select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

/* Switches */
.switch-container {
  display: flex;
  align-items: center;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  margin-right: 10px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--success-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--success-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.switch-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Botões */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Abas */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Ajuda e tooltips */
.help-container {
  position: relative;
  display: inline-block;
  margin-left: 8px;
  vertical-align: middle;
}

.help-button {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  border: none;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
  transition: background-color 0.2s;
}

.help-button:hover {
  background-color: #2980b9;
}

.help-tooltip {
  display: none;
  position: absolute;
  background-color: #333;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 13px;
  line-height: 1.4;
  width: 300px;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
}

.help-tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  left: -10px;
  transform: translateY(-50%);
  border-width: 5px 10px 5px 0;
  border-style: solid;
  border-color: transparent #333 transparent transparent;
}

.help-container:hover .help-tooltip {
  display: block;
}

/* Parâmetros modificáveis */
.parametro-modificavel {
  border: 2px solid var(--success-color) !important;
  padding: 15px !important;
}

.legenda {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.legenda-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legenda-cor {
  width: 20px;
  height: 20px;
  border: 2px solid var(--success-color);
}

/* Feedback visual */
.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 5px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast.show {
  transform: translateY(0);
  opacity: 1;
}

.toast.success {
  background-color: var(--success-color);
}

.toast.error {
  background-color: var(--danger-color);
}

.toast.warning {
  background-color: #ffc107;
  color: #212529;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}