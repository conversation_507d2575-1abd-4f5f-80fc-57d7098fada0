<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importação de Tabelas</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .form-col {
            flex: 1;
            min-width: 200px;
        }

        .table-responsive {
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        input, select, textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #e0e0e0;
            color: var(--text-color);
        }

        .header, .page-header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1, .page-header h1 {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
        }

        .form-container {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: #fff;
        }

        .form-title, .section-header {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        textarea {
            resize: vertical;
        }

        button, .btn-primary, .btn-secondary, .btn-success, .btn-danger {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        .btn-success:hover {
            background-color: var(--success-hover);
        }
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        .btn-danger:hover {
            background-color: var(--danger-hover);
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .preview-table th,
        .preview-table td {
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            text-align: left;
        }
        .preview-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            position: relative;
        }
        .preview-table th:hover {
            background-color: #e0e0e0;
        }
        .preview-table tr:hover {
            background-color: #f8f9fa;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .required-field {
            color: var(--danger-color);
            margin-left: 4px;
        }
        .info-text, .help-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }
        .form-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: #fff;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: opacity 0.3s ease;
        }
        .notification-success {
            background-color: var(--success-color);
        }
        .notification-error {
            background-color: var(--danger-color);
        }
        .notification-warning {
            background-color: var(--warning-color);
            color: #000;
        }
        .notification-info {
            background-color: var(--primary-color);
        }
        .button-group-right {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .fields-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px 30px;
            margin-bottom: 10px;
        }
        @media (max-width: 900px) {
            .fields-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>Importação de Tabelas</h1>
            <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>

        <div class="form-section" style="background:#f8f9fa; border:1px solid #d4d4d4; border-radius:6px; margin-bottom:24px;">
            <div class="section-header" style="font-size:17px;">Manual de Importação e Cuidados</div>
            <ul style="margin-bottom:10px;">
                <li>Prepare sua planilha no Excel ou Google Sheets, com a primeira linha contendo os nomes dos campos exatamente como aparecem na tela.</li>
                <li>Não deixe campos obrigatórios em branco.</li>
                <li>Evite espaços, acentos e caracteres especiais em códigos, IDs e campos de referência. Use apenas letras, números e underline (_). Descrições podem conter acentos e espaços.</li>
                <li>Use ponto (.) para separar casas decimais (ex: 1.5).</li>
                <li>Não inclua fórmulas, apenas valores.</li>
                <li>Se não houver valor para um campo, deixe-o em branco (não use "null", "-", etc).</li>
                <li>Revise se os códigos/IDs referenciados (ex: grupo, família, produtoPaiId, componentId, operacaoId, recursoId) já existem no sistema antes de importar.</li>
                <li>Evite duplicidade de códigos/IDs. O sistema pode recusar ou sobrescrever registros duplicados.</li>
                <li>Após colar ou importar, confira o preview antes de processar a importação.</li>
                <li>Em caso de erro, revise a mensagem exibida e corrija a planilha antes de tentar novamente.</li>
                <li><b>Clientes e Fornecedores:</b> O sistema valida automaticamente o CPF/CNPJ antes de importar. Registros com CPF ou CNPJ inválido serão destacados em vermelho e não serão salvos.</li>
            </ul>
            <div style="font-size:13px; color:#666;">Dica: Para grandes volumes, importe primeiro cadastros básicos (grupos, famílias, produtos, recursos, operações) e só depois cadastros dependentes (estruturas, ordens, etc).</div>
        </div>

        <div class="form-section">
            <div class="section-header">Selecione a Tabela e Campos</div>
            <div class="form-group">
                <label for="tabelaSelect">Escolha a tabela para importar:</label>
                <select id="tabelaSelect">
                    <option value="">Selecione...</option>
                    <option value="produtos">Produtos</option>
                    <option value="clientes">Clientes</option>
                    <option value="fornecedores">Fornecedores</option>
                    <option value="grupos">Grupos</option>
                    <option value="familias">Famílias</option>
                    <option value="estruturas">Estruturas</option>
                    <option value="centrosCusto">Centros de Custo</option>
                    <option value="armazens">Armazéns</option>
                    <option value="recursos">Recursos</option>
                    <option value="operacoes">Operações</option>
                    <option value="usuarios">Usuários</option>
                </select>
            </div>
            <div class="form-grid" id="fieldsGroup" style="display:none;">
                <!-- Campos serão adicionados dinamicamente aqui -->
            </div>
        </div>

        <div class="form-section">
            <div class="section-header">Preview dos Dados Existentes</div>
            <div class="help-text">
                Abaixo estão os 5 primeiros registros cadastrados no sistema, mostrando o formato dos campos selecionados.
            </div>
            <button class="btn-secondary" style="margin-bottom:10px;" onclick="exportarPreviewParaExcel()">Exportar para Excel</button>
            <div id="previewExistentes">
                <table class="preview-table">
                    <thead>
                        <tr id="previewHeaderExistentes"></tr>
                    </thead>
                    <tbody id="previewBodyExistentes"></tbody>
                </table>
            </div>
        </div>

        <div class="form-section">
            <div class="section-header">Dados para Importação</div>
            <div class="help-text">
                Cole aqui os dados copiados do Excel. Os dados devem estar organizados em colunas, separados por tabulação.
            </div>
            <!-- Instruções e exemplo para Estruturas -->
            <div id="estruturaImportHelp" style="display:none; margin-bottom:16px; background:#f8f9fa; border:1px solid #d4d4d4; border-radius:6px; padding:16px;">
                <b>Como preparar a planilha de importação de Estruturas</b><br><br>
                <ul style="margin-bottom:10px;">
                    <li>Não deixe campos obrigatórios em branco.</li>
                    <li>Não use espaços, acentos ou caracteres especiais em códigos e IDs (permitido apenas na descrição).</li>
                    <li>Use ponto (.) para decimais (ex: 1.5).</li>
                    <li>Cada linha representa um componente <b>ou</b> uma operação de uma estrutura.</li>
                    <li>Para componentes, preencha: Produto Pai, componentId, quantidade, unidade.</li>
                    <li>Para operações, preencha: Produto Pai, sequencia, operacaoId, recursoId, tempo, descricao.</li>
                    <li>Se uma linha for de componente, deixe os campos de operação em branco, e vice-versa.</li>
                    <li>Produto Pai deve ser o mesmo para todos os componentes/operacoes da mesma estrutura.</li>
                </ul>
                <b>Exemplo de planilha:</b>
                <div style="overflow-x:auto;">
                <table class="preview-table" style="margin:8px 0;">
                    <thead>
                        <tr>
                            <th>produtoPaiId</th><th>componentId</th><th>quantidade</th><th>unidade</th><th>sequencia</th><th>operacaoId</th><th>recursoId</th><th>tempo</th><th>descricao</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>PA001</td><td>MP001</td><td>2</td><td>KG</td><td></td><td></td><td></td><td></td><td></td></tr>
                        <tr><td>PA001</td><td>MP002</td><td>1.5</td><td>UN</td><td></td><td></td><td></td><td></td><td></td></tr>
                        <tr><td>PA001</td><td></td><td></td><td></td><td>10</td><td>OP001</td><td>RC001</td><td>15</td><td>Mistura inicial</td></tr>
                        <tr><td>PA001</td><td></td><td></td><td></td><td>20</td><td>OP002</td><td>RC002</td><td>30</td><td>Embalagem e inspeção</td></tr>
                        <tr><td>PA002</td><td>MP003</td><td>5</td><td>L</td><td></td><td></td><td></td><td></td><td></td></tr>
                        <tr><td>PA002</td><td></td><td></td><td></td><td>10</td><td>OP003</td><td>RC003</td><td>20</td><td>Processo especial</td></tr>
                    </tbody>
                </table>
                </div>
                <b>Legenda dos campos:</b>
                <ul style="margin-bottom:0;">
                    <li><b>produtoPaiId</b>: Código do produto acabado ou semiacabado (ex: PA001)</li>
                    <li><b>componentId</b>: Código do componente (ex: MP001, pode ser matéria-prima ou semiacabado)</li>
                    <li><b>quantidade</b>: Quantidade do componente (ex: 2, 1.5)</li>
                    <li><b>unidade</b>: Unidade do componente (ex: KG, UN, L)</li>
                    <li><b>sequencia</b>: Número da sequência da operação (ex: 10, 20)</li>
                    <li><b>operacaoId</b>: Código da operação (ex: OP001)</li>
                    <li><b>recursoId</b>: Código do recurso/máquina (ex: RC001)</li>
                    <li><b>tempo</b>: Tempo da operação em minutos (ex: 15)</li>
                    <li><b>descricao</b>: Descrição da operação (ex: Mistura inicial)</li>
                </ul>
                <div style="margin-top:8px; font-size:13px; color:#666;">
                    <b>Dicas:</b> Não use acentos, espaços ou caracteres especiais em códigos/IDs. Descrição pode conter acentos e espaços. Se não houver valor para um campo, deixe-o em branco. Todos os componentes e operações de uma estrutura devem ter o mesmo produtoPaiId.
                </div>
            </div>
            <textarea id="importData" placeholder="Cole aqui os dados do Excel..."></textarea>
            <input type="file" id="importFile" accept=".xlsx,.xls,.csv" style="margin-top:10px;">
            <button class="btn-secondary" style="margin-left:10px; margin-top:5px;" onclick="limparDadosColados()">Limpar dados colados</button>
            <button onclick="processarDados()">Preview</button>
        </div>

        <div class="form-section">
            <div class="section-header">Preview dos Dados</div>
            <div id="previewContainer">
                <table class="preview-table">
                    <thead>
                        <tr id="previewHeader"></tr>
                    </thead>
                    <tbody id="previewBody"></tbody>
                </table>
                <div class="button-group-right">
                    <button id="btnExcluirVermelhos" class="btn-secondary" onclick="excluirItensVermelhos()">Excluir itens vermelhos</button>
                    <button id="btnProcessarImportacao" class="btn-primary" onclick="processarImportacao()">Processar Importação</button>
                    <button class="btn-secondary" onclick="limparDados()">Limpar</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs,
            query,
            where,
            limit,
            setDoc,
            doc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import * as XLSX from 'https://cdn.sheetjs.com/xlsx-0.20.2/package/xlsx.mjs';

        // Dicionários simulados para preview amigável de estruturas
        const produtosSimulados = {
            'Sy8OtacUzL6eszemWohZ': { codigo: 'PA001', descricao: 'Produto Acabado 1' },
            'GNM6xC8kt0M4GI5bp5i5': { codigo: 'PA002', descricao: 'Produto Acabado 2' },
            'vryVVogWdAOPQ9tfj6SV': { codigo: 'MP001', descricao: 'Matéria-Prima 1' },
            'pw4nejRE5u2cODatG0Wj': { codigo: 'MP002', descricao: 'Matéria-Prima 2' },
            '7rV7YD1lWWvG4RaZxTOi': { codigo: 'SP001', descricao: 'Semiacabado 1' }
        };
        const operacoesSimuladas = {
            'OP001': { numero: '10', operacao: 'Mistura inicial' },
            'OP002': { numero: '20', operacao: 'Embalagem e inspeção' },
            'OP003': { numero: '10', operacao: 'Processo especial' }
        };
        const recursosSimulados = {
            'RC001': { codigo: 'RC001', maquina: 'Misturador' },
            'RC002': { codigo: 'RC002', maquina: 'Embaladora' },
            'RC003': { codigo: 'RC003', maquina: 'Especial' }
        };

        // Campos disponíveis por tabela
        const camposPorTabela = {
            produtos: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    descricao: { label: 'Descrição', required: true },
                    tipo: { label: 'Tipo', required: true },
                    unidade: { label: 'Unidade', required: true },
                    grupo: { label: 'Grupo', required: false },
                    familia: { label: 'Família', required: false },
                    status: { label: 'Status', required: false }
                },
                dadosFiscais: {
                    ncm: { label: 'NCM', required: false },
                    cest: { label: 'CEST', required: false },
                    origem: { label: 'Origem', required: false },
                    tipoItem: { label: 'Tipo Item', required: false }
                },
                dadosEstoque: {
                    estoqueMinimo: { label: 'Estoque Mínimo', required: false },
                    estoqueMaximo: { label: 'Estoque Máximo', required: false },
                    pontoPedido: { label: 'Ponto de Pedido', required: false },
                    loteCompra: { label: 'Lote de Compra', required: false }
                }
            },
            clientes: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    nome: { label: 'Nome', required: true },
                    cpfCnpj: { label: 'CPF/CNPJ', required: true },
                    email: { label: 'E-mail', required: false },
                    telefone: { label: 'Telefone', required: false },
                    status: { label: 'Status', required: false }
                }
            },
            fornecedores: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    razaoSocial: { label: 'Razão Social', required: true },
                    nomeFantasia: { label: 'Nome Fantasia', required: false },
                    cnpjCpf: { label: 'CNPJ/CPF', required: true },
                    inscricaoEstadual: { label: 'Inscrição Estadual', required: false },
                    email: { label: 'E-mail', required: false },
                    telefone1: { label: 'Telefone', required: false },
                    celular1: { label: 'Celular', required: false },
                    endereco: { label: 'Endereço', required: false },
                    numero: { label: 'Número', required: false },
                    bairro: { label: 'Bairro', required: false },
                    cidade: { label: 'Cidade', required: false },
                    estado: { label: 'Estado', required: false },
                    cep: { label: 'CEP', required: false },
                    pais: { label: 'País', required: false },
                    statusHomologacao: { label: 'Status Homologação', required: false },
                    ativo: { label: 'Ativo', required: false },
                    dataCadastro: { label: 'Data de Cadastro', required: false }
                }
            },
            grupos: {
                dadosBasicos: {
                    codigoGrupo: { label: 'Código', required: true },
                    nomeGrupo: { label: 'Descrição', required: true },
                    exemplosGrupo: { label: 'Exemplos', required: false },
                    dataCadastro: { label: 'Data de Cadastro', required: false }
                }
            },
            familias: {
                dadosBasicos: {
                    codigoFamilia: { label: 'Código', required: true },
                    nomeFamilia: { label: 'Descrição', required: true },
                    grupo: { label: 'Grupo', required: false },
                    dataCadastro: { label: 'Data de Cadastro', required: false }
                }
            },
            estruturas: {
                dadosBasicos: {
                    produtoPaiId: { label: 'Produto Pai', required: true },
                    // componentes e operacoes são arrays complexos, não simples
                },
                componentes: {
                    componentId: { label: 'ID do Componente', required: true },
                    quantidade: { label: 'Quantidade', required: true },
                    unidade: { label: 'Unidade', required: false }
                },
                operacoes: {
                    sequencia: { label: 'Sequência', required: true },
                    operacaoId: { label: 'ID da Operação', required: true },
                    recursoId: { label: 'ID do Recurso', required: true },
                    tempo: { label: 'Tempo (min)', required: true },
                    descricao: { label: 'Descrição da Operação', required: false }
                }
            },
            centrosCusto: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    descricao: { label: 'Descrição', required: true },
                    status: { label: 'Status', required: false }
                }
            },
            armazens: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    descricao: { label: 'Descrição', required: true },
                    status: { label: 'Status', required: false }
                }
            },
            recursos: {
                dadosBasicos: {
                    codigo: { label: 'Código', required: true },
                    maquina: { label: 'Máquina', required: true },
                    setor: { label: 'Setor', required: false },
                    centroCustoId: { label: 'Centro de Custo', required: false },
                    custoHora: { label: 'Custo Hora', required: false },
                    horasdia: { label: 'Horas/Dia', required: false },
                    dataCadastro: { label: 'Data de Cadastro', required: false }
                }
            },
            operacoes: {
                dadosBasicos: {
                    numero: { label: 'Número', required: true },
                    operacao: { label: 'Operação', required: true },
                    dataCadastro: { label: 'Data de Cadastro', required: false }
                }
            },
            usuarios: {
                dadosBasicos: {
                    uid: { label: 'UID', required: true },
                    nome: { label: 'Nome', required: true },
                    email: { label: 'E-mail', required: true },
                    perfil: { label: 'Perfil', required: false },
                    status: { label: 'Status', required: false }
                }
            }
        };

        let dadosExistentes = [];
        let dadosProcessados = [];

        // Carregar dados existentes ao selecionar tabela
        document.getElementById('tabelaSelect').addEventListener('change', async function() {
            const tabela = this.value;
            const fieldsGroup = document.getElementById('fieldsGroup');
            fieldsGroup.innerHTML = '';
            fieldsGroup.style.display = 'none';

            if (!tabela || !camposPorTabela[tabela]) return;

            // Criar grupos de campos
            Object.entries(camposPorTabela[tabela]).forEach(([grupo, campos]) => {
                const grupoDiv = document.createElement('div');
                grupoDiv.className = 'form-group';
                grupoDiv.innerHTML = `
                    <label>${grupo.charAt(0).toUpperCase() + grupo.slice(1).replace(/([A-Z])/g, ' $1')}</label>
                    <div class="fields-list">
                        ${Object.entries(campos).map(([campo, info]) => `
                            <div class="field-checkbox">
                                <input type="checkbox" value="${campo}" ${info.required ? 'checked' : ''} onchange="atualizarPreview()">
                                <label>${info.label}${info.required ? '<span class="required-field">*</span>' : ''}</label>
                            </div>
                        `).join('')}
                    </div>
                `;
                fieldsGroup.appendChild(grupoDiv);
            });

            fieldsGroup.style.display = 'block';
            await carregarDadosExistentes(tabela);

            // Mostrar instrução de estrutura se for o caso
            document.getElementById('estruturaImportHelp').style.display = (tabela === 'estruturas') ? 'block' : 'none';
        });

        // Carregar dados existentes
        async function carregarDadosExistentes(tabela) {
            try {
                const snapshot = await getDocs(query(collection(db, tabela), limit(5)));
                dadosExistentes = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                atualizarPreview();
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                showNotification("Erro ao carregar dados existentes", "error");
            }
        }

        // Funções de validação de CPF e CNPJ
        function validarCPF(cpf) {
            cpf = cpf.replace(/\D/g, '');
            if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;
            let soma = 0, resto;
            for (let i = 1; i <= 9; i++) soma += parseInt(cpf.substring(i-1, i)) * (11 - i);
            resto = (soma * 10) % 11;
            if (resto === 10 || resto === 11) resto = 0;
            if (resto !== parseInt(cpf.substring(9, 10))) return false;
            soma = 0;
            for (let i = 1; i <= 10; i++) soma += parseInt(cpf.substring(i-1, i)) * (12 - i);
            resto = (soma * 10) % 11;
            if (resto === 10 || resto === 11) resto = 0;
            if (resto !== parseInt(cpf.substring(10, 11))) return false;
            return true;
        }
        function validarCNPJ(cnpj) {
            cnpj = cnpj.replace(/\D/g, '');
            if (cnpj.length !== 14) return false;
            if (/^(\d)\1{13}$/.test(cnpj)) return false;
            let tamanho = cnpj.length - 2;
            let numeros = cnpj.substring(0, tamanho);
            let digitos = cnpj.substring(tamanho);
            let soma = 0;
            let pos = tamanho - 7;
            for (let i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2) pos = 9;
            }
            let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
            if (resultado !== parseInt(digitos.charAt(0))) return false;
            tamanho = tamanho + 1;
            numeros = cnpj.substring(0, tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (let i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2) pos = 9;
            }
            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
            if (resultado !== parseInt(digitos.charAt(1))) return false;
            return true;
        }

        // Atualizar preview
        window.atualizarPreview = function() {
            const tabela = document.getElementById('tabelaSelect').value;
            if (!tabela) return;

            const camposSelecionados = Array.from(document.querySelectorAll('.field-checkbox input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            // Preview dos dados existentes
            const headerExistentes = document.getElementById('previewHeaderExistentes');
            const bodyExistentes = document.getElementById('previewBodyExistentes');
            headerExistentes.innerHTML = '';
            bodyExistentes.innerHTML = '';

            camposSelecionados.forEach(campo => {
                const th = document.createElement('th');
                th.textContent = camposPorTabela[tabela].dadosBasicos?.[campo]?.label ||
                                 camposPorTabela[tabela].componentes?.[campo]?.label ||
                                 camposPorTabela[tabela].operacoes?.[campo]?.label || campo;
                headerExistentes.appendChild(th);
            });

            dadosExistentes.forEach(item => {
                const row = document.createElement('tr');
                let invalido = false;
                camposSelecionados.forEach(campo => {
                    const td = document.createElement('td');
                    let valor = item[campo] ?? '';
                    // Preview amigável para estruturas
                    if (tabela === 'estruturas') {
                        if (campo === 'produtoPaiId') {
                            const prod = produtosSimulados[valor];
                            valor = prod ? `${prod.codigo} - ${prod.descricao}` : valor;
                        }
                        if (campo === 'componentId') {
                            const comp = produtosSimulados[valor];
                            valor = comp ? `${comp.codigo} - ${comp.descricao}` : valor;
                        }
                        if (campo === 'operacaoId') {
                            const op = operacoesSimuladas[valor];
                            valor = op ? `${op.numero} - ${op.operacao}` : valor;
                        }
                        if (campo === 'recursoId') {
                            const rec = recursosSimulados[valor];
                            valor = rec ? `${rec.codigo} - ${rec.maquina}` : valor;
                        }
                    }
                    // Validação de CPF/CNPJ
                    if ((tabela === 'clientes' && campo === 'cpfCnpj' && valor) || (tabela === 'fornecedores' && campo === 'cnpj' && valor)) {
                        let valido = true;
                        if (tabela === 'clientes') {
                            const v = valor.replace(/\D/g, '');
                            if (v.length === 11) valido = validarCPF(v);
                            else if (v.length === 14) valido = validarCNPJ(v);
                            else valido = false;
                        } else if (tabela === 'fornecedores') {
                            valido = validarCNPJ(valor);
                        }
                        if (!valido) {
                            row.classList.add('error-row');
                            invalido = true;
                        }
                    }
                    td.textContent = valor;
                    row.appendChild(td);
                });
                bodyExistentes.appendChild(row);
            });

            // Preview dos dados colados
            if (dadosProcessados.length > 0) {
                const header = document.getElementById('previewHeader');
                const body = document.getElementById('previewBody');
                header.innerHTML = '';
                body.innerHTML = '';

                camposSelecionados.forEach(campo => {
                    const th = document.createElement('th');
                    th.textContent = camposPorTabela[tabela].dadosBasicos[campo]?.label || campo;
                    header.appendChild(th);
                });

                dadosProcessados.forEach((item, idx) => {
                    const row = document.createElement('tr');
                    const codigoExiste = dadosExistentes.find(e => e.codigo === item.codigo);
                    // Verifica se o código aparece mais de uma vez no lote
                    const codigoDuplicadoNoLote = dadosProcessados.findIndex((i, j) => i.codigo === item.codigo && j !== idx) !== -1;
                    if (codigoExiste || codigoDuplicadoNoLote) row.classList.add('error-row');
                    camposSelecionados.forEach(campo => {
                        const td = document.createElement('td');
                        td.textContent = item[campo] ?? '';
                        row.appendChild(td);
                    });
                    body.appendChild(row);
                });
            }
        };

        // Processar dados colados
        window.processarDados = function() {
            const tabela = document.getElementById('tabelaSelect').value;
            if (!tabela) {
                showNotification('Selecione uma tabela primeiro', 'warning');
                return;
            }

            const dados = document.getElementById('importData').value.trim();
            const file = document.getElementById('importFile').files[0];

            if (!dados && !file) {
                showNotification('Cole os dados ou selecione um arquivo', 'warning');
                return;
            }

            const camposSelecionados = Array.from(document.querySelectorAll('.field-checkbox input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            // Validar campos obrigatórios
            const camposObrigatorios = Object.entries(camposPorTabela[tabela].dadosBasicos)
                .filter(([_, info]) => info.required)
                .map(([campo]) => campo);

            const camposFaltantes = camposObrigatorios.filter(campo => !camposSelecionados.includes(campo));
            if (camposFaltantes.length > 0) {
                showNotification(`Campos obrigatórios não selecionados: ${camposFaltantes.join(', ')}`, 'error');
                return;
            }

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                    processarLinhas(jsonData, camposSelecionados);
                };
                reader.readAsArrayBuffer(file);
            } else {
                const linhas = dados.split('\n').map(linha => linha.split('\t'));
                processarLinhas(linhas, camposSelecionados);
            }
        };

        function processarLinhas(linhas, camposSelecionados) {
            dadosProcessados = linhas.map(linha => {
                const item = {};
                camposSelecionados.forEach((campo, index) => {
                    item[campo] = linha[index] || '';
                });
                return item;
            });
            atualizarPreview();
        }

        // Processar importação
        window.processarImportacao = async function() {
            const tabela = document.getElementById('tabelaSelect').value;
            if (!tabela || dadosProcessados.length === 0) {
                showNotification('Não há dados para importar', 'warning');
                return;
            }

            const erros = [];
            const avisos = [];
            const sucessos = [];

            for (const item of dadosProcessados) {
                try {
                    // Verificar campos obrigatórios
                    const camposObrigatorios = Object.entries(camposPorTabela[tabela].dadosBasicos)
                        .filter(([_, info]) => info.required)
                        .map(([campo]) => campo);

                    const camposFaltantes = camposObrigatorios.filter(campo => !item[campo]);
                    if (camposFaltantes.length > 0) {
                        erros.push(`Campos obrigatórios faltando: ${camposFaltantes.join(', ')}`);
                        continue;
                    }

                    // Validação de CPF/CNPJ
                    if (tabela === 'clientes' && item.cpfCnpj) {
                        const v = item.cpfCnpj.replace(/\D/g, '');
                        let valido = true;
                        if (v.length === 11) valido = validarCPF(v);
                        else if (v.length === 14) valido = validarCNPJ(v);
                        else valido = false;
                        if (!valido) {
                            erros.push(`CPF/CNPJ inválido para cliente: ${item.cpfCnpj}`);
                            continue;
                        }
                    }
                    if (tabela === 'fornecedores' && item.cnpj) {
                        if (!validarCNPJ(item.cnpj)) {
                            erros.push(`CNPJ inválido para fornecedor: ${item.cnpj}`);
                            continue;
                        }
                    }

                    // Verificar duplicidade
                    const itemExistente = dadosExistentes.find(e => e.codigo === item.codigo);
                    if (itemExistente) {
                        avisos.push(`Item ${item.codigo} já existe no sistema`);
                        continue;
                    }

                    // Salvar no Firestore
                    await setDoc(doc(collection(db, tabela), item.codigo), item);
                    sucessos.push(`Item ${item.codigo} importado com sucesso`);

                } catch (error) {
                    console.error("Erro ao importar item:", error);
                    erros.push(`Erro ao importar item ${item.codigo}: ${error.message}`);
                }
            }

            // Mostrar resultados
            if (erros.length > 0) {
                showNotification(`${erros.length} erros encontrados durante a importação`, "error");
            }
            if (avisos.length > 0) {
                showNotification(`${avisos.length} avisos durante a importação`, "warning");
            }
            if (sucessos.length > 0) {
                showNotification(`${sucessos.length} itens importados com sucesso`, "success");
            }

            // Atualizar lista de dados existentes
            await carregarDadosExistentes(tabela);
        };

        // Limpar dados
        window.limparDados = function() {
            document.getElementById('importData').value = '';
            document.getElementById('importFile').value = '';
            document.getElementById('previewHeader').innerHTML = '';
            document.getElementById('previewBody').innerHTML = '';
            dadosProcessados = [];
        };

        // Limpar dados colados
        window.limparDadosColados = function() {
            document.getElementById('importData').value = '';
            document.getElementById('importFile').value = '';
            document.getElementById('previewHeader').innerHTML = '';
            document.getElementById('previewBody').innerHTML = '';
            dadosProcessados = [];
        };

        // Excluir itens vermelhos
        window.excluirItensVermelhos = function() {
            dadosProcessados = dadosProcessados.filter((item, idx, arr) => {
                const existeNoSistema = dadosExistentes.find(e => e.codigo === item.codigo);
                const duplicadoNoLote = arr.findIndex((i, j) => i.codigo === item.codigo && j !== idx) !== -1;
                return !existeNoSistema && !duplicadoNoLote;
            });
            atualizarPreview();
        };

        // Exportar preview para Excel
        window.exportarPreviewParaExcel = function() {
            const tabela = document.getElementById('tabelaSelect').value;
            if (!tabela) {
                showNotification('Selecione uma tabela primeiro', 'warning');
                return;
            }

            const camposSelecionados = Array.from(document.querySelectorAll('.field-checkbox input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            if (camposSelecionados.length === 0) {
                showNotification('Selecione ao menos um campo para exportar', 'warning');
                return;
            }

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(dadosExistentes.map(item => {
                const row = {};
                camposSelecionados.forEach(campo => {
                    row[camposPorTabela[tabela].dadosBasicos[campo]?.label || campo] = item[campo] ?? '';
                });
                return row;
            }));

            XLSX.utils.book_append_sheet(wb, ws, "Preview");
            XLSX.writeFile(wb, `preview_${tabela}.xlsx`);
        };

        // Mostrar notificação
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                <span>${message}</span>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            // Adicionar evento para processar dados quando colados
            document.getElementById('importData').addEventListener('paste', function() {
                setTimeout(processarDados, 100);
            });
        });
    </script>
</body>
</html> 