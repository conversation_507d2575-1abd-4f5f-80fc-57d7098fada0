// Initialize Firebase configuration and handle authentication
// ===================================================================
// IMPORTAÇÃO CENTRALIZADA DO FIREBASE
// ===================================================================
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  getDoc,
  getDocs,
  query,
  where 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Export common functionality if needed
export async function loadUserData(userId) {
  try {
    const userDoc = await getDoc(doc(db, "usuarios", userId));
    return userDoc.exists() ? userDoc.data() : null;
  } catch (error) {
    console.error("Error loading user data:", error);
    return null;
  }
}

// Initialize any required event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Add initialization code here
  console.log('Application initialized successfully');
});
