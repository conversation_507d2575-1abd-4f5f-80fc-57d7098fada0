<!-- Modal Nova Cotação -->
<div id="newQuotationModal" class="modal">
    <div class="modal-content" style="max-width: 1000px;">
        <div class="modal-header">
            <h2><i class="fas fa-plus"></i> Nova Cotação</h2>
            <span class="close" onclick="closeModal('newQuotationModal')">&times;</span>
        </div>
        <div class="modal-body">
            <!-- Método de Criação -->
            <div class="creation-method">
                <h4><i class="fas fa-route"></i> Como deseja criar a cotação?</h4>
                <div class="method-options">
                    <div class="method-card active" onclick="selectCreationMethod('manual')">
                        <div class="method-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="method-title">Manual</div>
                        <div class="method-description">Criar cotação do zero inserindo itens manualmente</div>
                    </div>
                    <div class="method-card" onclick="selectCreationMethod('solicitacao')">
                        <div class="method-icon">
                            <i class="fas fa-file-import"></i>
                        </div>
                        <div class="method-title">Da Solicitação</div>
                        <div class="method-description">Importar itens de uma solicitação de compras aprovada</div>
                    </div>
                    <div class="method-card" onclick="selectCreationMethod('template')">
                        <div class="method-icon">
                            <i class="fas fa-copy"></i>
                        </div>
                        <div class="method-title">Template</div>
                        <div class="method-description">Usar uma cotação existente como modelo</div>
                    </div>
                </div>
            </div>

            <!-- Formulário Manual -->
            <div id="manualForm" class="creation-form active">
                <h4><i class="fas fa-edit"></i> Dados da Nova Cotação</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Número da Cotação</label>
                            <input type="text" class="form-control" id="newQuotationNumber" placeholder="Será gerado automaticamente">
                        </div>
                        
                        <div class="form-group">
                            <label>Data Limite para Resposta</label>
                            <input type="date" class="form-control" id="newQuotationDeadline">
                        </div>
                        
                        <div class="form-group">
                            <label>Prazo de Entrega (dias)</label>
                            <input type="number" class="form-control" id="newDeliveryTerm" placeholder="Ex: 30">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Condições de Pagamento</label>
                            <select class="form-control" id="newPaymentTerms">
                                <option value="">Selecione...</option>
                                <option value="A_VISTA">À Vista</option>
                                <option value="30_DIAS">30 dias</option>
                                <option value="60_DIAS">60 dias</option>
                                <option value="30_60_DIAS">30/60 dias</option>
                                <option value="30_60_90_DIAS">30/60/90 dias</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Local de Entrega</label>
                            <textarea class="form-control" id="newDeliveryLocation" rows="2" placeholder="Endereço para entrega..."></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Observações Gerais</label>
                    <textarea class="form-control" id="newGeneralNotes" rows="3" placeholder="Observações que serão enviadas aos fornecedores..."></textarea>
                </div>
            </div>

            <!-- Formulário de Solicitação -->
            <div id="solicitacaoForm" class="creation-form">
                <h4><i class="fas fa-file-import"></i> Importar de Solicitação de Compras</h4>
                
                <div class="form-group">
                    <label>Selecione a Solicitação</label>
                    <select class="form-control" id="selectedSolicitacao" onchange="loadSolicitacaoPreview()">
                        <option value="">Selecione uma solicitação...</option>
                    </select>
                </div>
                
                <div id="solicitacaoPreview" style="display: none;">
                    <h5>Preview da Solicitação</h5>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Unidade</th>
                                    <th>Quantidade</th>
                                    <th>Incluir</th>
                                </tr>
                            </thead>
                            <tbody id="solicitacaoItemsPreview">
                                <!-- Itens da solicitação -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Formulário de Template -->
            <div id="templateForm" class="creation-form">
                <h4><i class="fas fa-copy"></i> Usar Cotação como Template</h4>
                
                <div class="form-group">
                    <label>Selecione a Cotação Template</label>
                    <select class="form-control" id="selectedTemplate" onchange="loadTemplatePreview()">
                        <option value="">Selecione uma cotação...</option>
                    </select>
                </div>
                
                <div id="templatePreview" style="display: none;">
                    <h5>Preview do Template</h5>
                    <div class="template-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Número:</strong> <span id="templateNumber"></span><br>
                                <strong>Data:</strong> <span id="templateDate"></span><br>
                                <strong>Status:</strong> <span id="templateStatus"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>Itens:</strong> <span id="templateItemsCount"></span><br>
                                <strong>Fornecedores:</strong> <span id="templateSuppliersCount"></span><br>
                                <strong>Valor:</strong> <span id="templateValue"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-top: 15px;">
                        <label>
                            <input type="checkbox" id="copySuppliers" checked> 
                            Copiar fornecedores do template
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="copyConditions" checked> 
                            Copiar condições comerciais do template
                        </label>
                    </div>
                </div>
            </div>

            <!-- Botões de Ação -->
            <div class="modal-actions">
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newQuotationModal')">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-success" onclick="createNewQuotation()">
                        <i class="fas fa-plus"></i> Criar Cotação
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Estilos específicos para o modal de nova cotação */
.creation-method {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.method-options {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.method-card {
    flex: 1;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.method-card:hover {
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.method-card.active {
    border-color: #28a745;
    background: #d4edda;
}

.method-icon {
    font-size: 2rem;
    color: #28a745;
    margin-bottom: 10px;
}

.method-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 8px;
}

.method-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

.creation-form {
    display: none;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.creation-form.active {
    display: block;
}

.creation-form h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.template-info {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    .method-options {
        flex-direction: column;
    }
}
</style>
