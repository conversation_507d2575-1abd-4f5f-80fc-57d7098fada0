<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Auditoria de Movimentações</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .audit-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }
        .audit-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .error-card {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-left: 4px solid #e53e3e;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .warning-card {
            background: #fffbeb;
            border: 1px solid #fbd38d;
            border-left: 4px solid #ed8936;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success-card {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-left: 4px solid #38a169;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .info-card {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-left: 4px solid #3182ce;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .audit-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .audit-table th {
            background: #f7fafc;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            border-bottom: 2px solid #e2e8f0;
        }
        .audit-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #e2e8f0;
        }
        .audit-table tr:hover {
            background: #f7fafc;
        }
        .severity-critical {
            color: #e53e3e;
            font-weight: bold;
        }
        .severity-high {
            color: #dd6b20;
            font-weight: bold;
        }
        .severity-medium {
            color: #d69e2e;
        }
        .severity-low {
            color: #38a169;
        }
        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .btn-audit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .btn-audit:hover {
            transform: translateY(-2px);
        }
        .btn-fix {
            background: #38a169;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }
        .btn-details {
            background: #3182ce;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .collapsible {
            cursor: pointer;
            padding: 10px;
            background: #f7fafc;
            border: none;
            text-align: left;
            outline: none;
            font-size: 15px;
            font-weight: 600;
            width: 100%;
            border-radius: 4px;
            margin: 5px 0;
        }
        .collapsible:hover {
            background: #edf2f7;
        }
        .collapsible-content {
            padding: 0 15px;
            display: none;
            overflow: hidden;
            background: white;
            border-radius: 0 0 4px 4px;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin: 2px;
        }
        .tag-duplicate {
            background: #fed7d7;
            color: #c53030;
        }
        .tag-orphan {
            background: #fbd38d;
            color: #c05621;
        }
        .tag-inconsistent {
            background: #e9d8fd;
            color: #6b46c1;
        }
        .tag-missing {
            background: #bee3f8;
            color: #2c5282;
        }
    </style>
</head>
<body>
    <div class="audit-container">
        <div class="audit-header">
            <h1>🔍 Auditoria Completa de Movimentações</h1>
            <p>Análise detalhada de inconsistências, duplicidades e problemas de integridade</p>
        </div>

        <!-- Filtros e Controles -->
        <div class="filter-section">
            <h3>🎛️ Controles de Auditoria</h3>
            <div class="filter-grid">
                <div>
                    <label>Período de Análise:</label>
                    <select id="periodoAnalise">
                        <option value="7">Últimos 7 dias</option>
                        <option value="30" selected>Últimos 30 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="365">Último ano</option>
                        <option value="all">Todos os registros</option>
                    </select>
                </div>
                <div>
                    <label>Tipo de Problema:</label>
                    <select id="tipoProblema">
                        <option value="all">Todos os problemas</option>
                        <option value="duplicates">Duplicidades</option>
                        <option value="orphans">Registros órfãos</option>
                        <option value="inconsistencies">Inconsistências</option>
                        <option value="missing">Dados faltantes</option>
                    </select>
                </div>
                <div>
                    <label>Severidade:</label>
                    <select id="severidade">
                        <option value="all">Todas</option>
                        <option value="critical">Crítica</option>
                        <option value="high">Alta</option>
                        <option value="medium">Média</option>
                        <option value="low">Baixa</option>
                    </select>
                </div>
                <div>
                    <label>Produto/Armazém:</label>
                    <input type="text" id="filtroTexto" placeholder="Filtrar por código/nome...">
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="executarAuditoria()" class="btn-audit">🔍 Executar Auditoria Completa</button>
                <button onclick="corrigirProblemasAutomaticos()" class="btn-fix">🔧 Corrigir Problemas Automáticos</button>
                <button onclick="exportarRelatorio()" class="btn btn-secondary">📊 Exportar Relatório</button>
            </div>
        </div>

        <!-- Progresso -->
        <div id="progressSection" style="display: none;">
            <div class="info-card">
                <h4>🔄 Executando Auditoria...</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                </div>
                <p id="progressText">Iniciando análise...</p>
            </div>
        </div>

        <!-- Estatísticas Gerais -->
        <div id="statsSection" style="display: none;">
            <h3>📊 Estatísticas Gerais</h3>
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <!-- Resultados da Auditoria -->
        <div id="resultsSection" style="display: none;">
            <h3>🚨 Problemas Encontrados</h3>
            <div id="problemsContainer"></div>
        </div>

        <!-- Detalhes dos Problemas -->
        <div id="detailsSection" style="display: none;">
            <h3>📋 Detalhes dos Problemas</h3>
            <div id="detailsContainer"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            addDoc,
            runTransaction,
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let auditData = {};
        let problemsFound = [];
        let currentUser = JSON.parse(localStorage.getItem('currentUser'));

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        window.executarAuditoria = async function() {
            const progressSection = document.getElementById('progressSection');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            progressSection.style.display = 'block';
            document.getElementById('statsSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('detailsSection').style.display = 'none';

            try {
                // Etapa 1: Carregar dados
                progressText.textContent = 'Carregando dados do sistema...';
                progressBar.style.width = '10%';

                await carregarDadosAuditoria();

                // Etapa 2: Analisar duplicidades
                progressText.textContent = 'Analisando duplicidades...';
                progressBar.style.width = '30%';

                const duplicidades = analisarDuplicidades();

                // Etapa 3: Analisar registros órfãos
                progressText.textContent = 'Verificando registros órfãos...';
                progressBar.style.width = '50%';

                const orfaos = analisarRegistrosOrfaos();

                // Etapa 4: Analisar inconsistências
                progressText.textContent = 'Verificando inconsistências...';
                progressBar.style.width = '70%';

                const inconsistencias = analisarInconsistencias();

                // Etapa 5: Analisar dados faltantes
                progressText.textContent = 'Verificando dados faltantes...';
                progressBar.style.width = '90%';

                const faltantes = analisarDadosFaltantes();

                // Etapa 6: Compilar resultados
                progressText.textContent = 'Compilando resultados...';
                progressBar.style.width = '100%';

                problemsFound = [
                    ...duplicidades,
                    ...orfaos,
                    ...inconsistencias,
                    ...faltantes
                ];

                // Mostrar resultados
                setTimeout(() => {
                    progressSection.style.display = 'none';
                    mostrarEstatisticas();
                    mostrarProblemas();
                }, 500);

            } catch (error) {
                console.error('Erro na auditoria:', error);
                progressText.textContent = 'Erro na auditoria: ' + error.message;
                progressBar.style.width = '100%';
                progressBar.style.background = '#e53e3e';
            }
        };

        async function carregarDadosAuditoria() {
            const [
                movimentacoesSnap,
                estoquesSnap,
                produtosSnap,
                armazensSnap,
                recebimentosSnap,
                qualidadeSnap
            ] = await Promise.all([
                getDocs(collection(db, "movimentacoesEstoque")),
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "armazens")),
                getDocs(collection(db, "recebimentosDetalhes")),
                getDocs(collection(db, "estoqueQualidade"))
            ]);

            auditData = {
                movimentacoes: movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                estoques: estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                produtos: produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                armazens: armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                recebimentos: recebimentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                qualidade: qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
            };

            console.log('Dados carregados:', {
                movimentacoes: auditData.movimentacoes.length,
                estoques: auditData.estoques.length,
                produtos: auditData.produtos.length,
                armazens: auditData.armazens.length,
                recebimentos: auditData.recebimentos.length,
                qualidade: auditData.qualidade.length
            });
        }

        function analisarDuplicidades() {
            const problemas = [];

            // 1. Duplicidades em movimentações
            const movimentacoesMap = new Map();
            auditData.movimentacoes.forEach(mov => {
                const chave = `${mov.produtoId}_${mov.armazemId}_${mov.tipo}_${mov.quantidade}_${mov.numeroDocumento}`;

                if (movimentacoesMap.has(chave)) {
                    const original = movimentacoesMap.get(chave);
                    problemas.push({
                        tipo: 'DUPLICIDADE',
                        categoria: 'MOVIMENTACAO',
                        severidade: 'high',
                        titulo: 'Movimentação Duplicada',
                        descricao: `Movimentação duplicada encontrada`,
                        detalhes: {
                            original: original,
                            duplicata: mov,
                            chave: chave
                        },
                        impacto: 'Pode causar saldos incorretos',
                        solucao: 'Remover movimentação duplicada',
                        autofix: true
                    });
                } else {
                    movimentacoesMap.set(chave, mov);
                }
            });

            // 2. Duplicidades em estoques
            const estoquesMap = new Map();
            auditData.estoques.forEach(est => {
                const chave = `${est.produtoId}_${est.armazemId}`;

                if (estoquesMap.has(chave)) {
                    const original = estoquesMap.get(chave);
                    problemas.push({
                        tipo: 'DUPLICIDADE',
                        categoria: 'ESTOQUE',
                        severidade: 'critical',
                        titulo: 'Estoque Duplicado',
                        descricao: `Produto ${est.produtoId} duplicado no armazém ${est.armazemId}`,
                        detalhes: {
                            original: original,
                            duplicata: est,
                            chave: chave
                        },
                        impacto: 'Saldos incorretos e inconsistências graves',
                        solucao: 'Consolidar saldos e remover duplicata',
                        autofix: true
                    });
                } else {
                    estoquesMap.set(chave, est);
                }
            });

            // 3. Duplicidades em recebimentos
            const recebimentosMap = new Map();
            auditData.recebimentos.forEach(rec => {
                const chave = `${rec.produtoId}_${rec.numeroNF}_${rec.quantidade}`;

                if (recebimentosMap.has(chave)) {
                    const original = recebimentosMap.get(chave);
                    problemas.push({
                        tipo: 'DUPLICIDADE',
                        categoria: 'RECEBIMENTO',
                        severidade: 'medium',
                        titulo: 'Recebimento Duplicado',
                        descricao: `Recebimento duplicado na NF ${rec.numeroNF}`,
                        detalhes: {
                            original: original,
                            duplicata: rec,
                            chave: chave
                        },
                        impacto: 'Pode indicar entrada dupla de material',
                        solucao: 'Verificar e remover se confirmado como duplicata',
                        autofix: false
                    });
                } else {
                    recebimentosMap.set(chave, rec);
                }
            });

            return problemas;
        }

        function analisarRegistrosOrfaos() {
            const problemas = [];

            // 1. Movimentações sem estoque correspondente
            auditData.movimentacoes.forEach(mov => {
                const estoqueExiste = auditData.estoques.some(est =>
                    est.produtoId === mov.produtoId && est.armazemId === mov.armazemId
                );

                if (!estoqueExiste) {
                    problemas.push({
                        tipo: 'ORFAO',
                        categoria: 'MOVIMENTACAO',
                        severidade: 'high',
                        titulo: 'Movimentação Órfã',
                        descricao: `Movimentação sem estoque correspondente`,
                        detalhes: {
                            movimentacao: mov,
                            produtoId: mov.produtoId,
                            armazemId: mov.armazemId
                        },
                        impacto: 'Movimentação não refletida no saldo',
                        solucao: 'Criar estoque correspondente ou remover movimentação',
                        autofix: true
                    });
                }
            });

            // 2. Estoques sem produto correspondente
            auditData.estoques.forEach(est => {
                const produtoExiste = auditData.produtos.some(prod => prod.id === est.produtoId);

                if (!produtoExiste) {
                    problemas.push({
                        tipo: 'ORFAO',
                        categoria: 'ESTOQUE',
                        severidade: 'medium',
                        titulo: 'Estoque Órfão',
                        descricao: `Estoque de produto inexistente: ${est.produtoId}`,
                        detalhes: {
                            estoque: est,
                            produtoId: est.produtoId
                        },
                        impacto: 'Estoque de produto que não existe mais',
                        solucao: 'Remover estoque ou recriar produto',
                        autofix: false
                    });
                }
            });

            // 3. Estoques sem armazém correspondente
            auditData.estoques.forEach(est => {
                const armazemExiste = auditData.armazens.some(arm => arm.id === est.armazemId);

                if (!armazemExiste) {
                    problemas.push({
                        tipo: 'ORFAO',
                        categoria: 'ESTOQUE',
                        severidade: 'high',
                        titulo: 'Estoque em Armazém Inexistente',
                        descricao: `Estoque em armazém inexistente: ${est.armazemId}`,
                        detalhes: {
                            estoque: est,
                            armazemId: est.armazemId
                        },
                        impacto: 'Estoque em local que não existe',
                        solucao: 'Corrigir armazém ou remover estoque',
                        autofix: false
                    });
                }
            });

            return problemas;
        }

        function analisarInconsistencias() {
            const problemas = [];

            // 1. Saldos negativos não permitidos
            auditData.estoques.forEach(est => {
                if (est.saldo < 0) {
                    problemas.push({
                        tipo: 'INCONSISTENCIA',
                        categoria: 'SALDO',
                        severidade: 'high',
                        titulo: 'Saldo Negativo',
                        descricao: `Saldo negativo: ${est.saldo}`,
                        detalhes: {
                            estoque: est,
                            saldo: est.saldo
                        },
                        impacto: 'Saldo fisicamente impossível',
                        solucao: 'Ajustar saldo ou revisar movimentações',
                        autofix: false
                    });
                }
            });

            // 2. Saldo reservado maior que saldo total
            auditData.estoques.forEach(est => {
                if ((est.saldoReservado || 0) > est.saldo) {
                    problemas.push({
                        tipo: 'INCONSISTENCIA',
                        categoria: 'RESERVA',
                        severidade: 'critical',
                        titulo: 'Reserva Maior que Saldo',
                        descricao: `Reservado: ${est.saldoReservado}, Saldo: ${est.saldo}`,
                        detalhes: {
                            estoque: est,
                            saldoReservado: est.saldoReservado,
                            saldo: est.saldo
                        },
                        impacto: 'Impossibilidade lógica de reserva',
                        solucao: 'Ajustar reserva ou saldo',
                        autofix: true
                    });
                }
            });

            // 3. Movimentações com quantidade zero ou negativa
            auditData.movimentacoes.forEach(mov => {
                if (!mov.quantidade || mov.quantidade <= 0) {
                    problemas.push({
                        tipo: 'INCONSISTENCIA',
                        categoria: 'MOVIMENTACAO',
                        severidade: 'medium',
                        titulo: 'Quantidade Inválida',
                        descricao: `Movimentação com quantidade ${mov.quantidade}`,
                        detalhes: {
                            movimentacao: mov,
                            quantidade: mov.quantidade
                        },
                        impacto: 'Movimentação sem efeito real',
                        solucao: 'Corrigir quantidade ou remover movimentação',
                        autofix: false
                    });
                }
            });

            return problemas;
        }

        function analisarDadosFaltantes() {
            const problemas = [];

            // 1. Movimentações sem campos obrigatórios
            auditData.movimentacoes.forEach(mov => {
                const camposObrigatorios = ['produtoId', 'armazemId', 'tipo', 'quantidade'];
                const camposFaltantes = camposObrigatorios.filter(campo => !mov[campo]);

                if (camposFaltantes.length > 0) {
                    problemas.push({
                        tipo: 'DADOS_FALTANTES',
                        categoria: 'MOVIMENTACAO',
                        severidade: 'high',
                        titulo: 'Campos Obrigatórios Faltantes',
                        descricao: `Campos faltantes: ${camposFaltantes.join(', ')}`,
                        detalhes: {
                            movimentacao: mov,
                            camposFaltantes: camposFaltantes
                        },
                        impacto: 'Movimentação incompleta',
                        solucao: 'Completar campos ou remover registro',
                        autofix: false
                    });
                }
            });

            // 2. Estoques sem campos obrigatórios
            auditData.estoques.forEach(est => {
                const camposObrigatorios = ['produtoId', 'armazemId', 'saldo'];
                const camposFaltantes = camposObrigatorios.filter(campo => est[campo] === undefined || est[campo] === null);

                if (camposFaltantes.length > 0) {
                    problemas.push({
                        tipo: 'DADOS_FALTANTES',
                        categoria: 'ESTOQUE',
                        severidade: 'critical',
                        titulo: 'Campos Obrigatórios Faltantes',
                        descricao: `Campos faltantes: ${camposFaltantes.join(', ')}`,
                        detalhes: {
                            estoque: est,
                            camposFaltantes: camposFaltantes
                        },
                        impacto: 'Estoque inválido',
                        solucao: 'Completar campos obrigatórios',
                        autofix: false
                    });
                }
            });

            return problemas;
        }

        function mostrarEstatisticas() {
            const statsGrid = document.getElementById('statsGrid');
            const statsSection = document.getElementById('statsSection');

            const stats = {
                totalProblemas: problemsFound.length,
                criticos: problemsFound.filter(p => p.severidade === 'critical').length,
                altos: problemsFound.filter(p => p.severidade === 'high').length,
                medios: problemsFound.filter(p => p.severidade === 'medium').length,
                baixos: problemsFound.filter(p => p.severidade === 'low').length,
                duplicidades: problemsFound.filter(p => p.tipo === 'DUPLICIDADE').length,
                orfaos: problemsFound.filter(p => p.tipo === 'ORFAO').length,
                inconsistencias: problemsFound.filter(p => p.tipo === 'INCONSISTENCIA').length,
                faltantes: problemsFound.filter(p => p.tipo === 'DADOS_FALTANTES').length,
                autofix: problemsFound.filter(p => p.autofix).length
            };

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.totalProblemas}</div>
                    <div>Total de Problemas</div>
                </div>
                <div class="stat-card" style="border-left-color: #e53e3e;">
                    <div class="stat-number" style="color: #e53e3e;">${stats.criticos}</div>
                    <div>Críticos</div>
                </div>
                <div class="stat-card" style="border-left-color: #dd6b20;">
                    <div class="stat-number" style="color: #dd6b20;">${stats.altos}</div>
                    <div>Alta Prioridade</div>
                </div>
                <div class="stat-card" style="border-left-color: #d69e2e;">
                    <div class="stat-number" style="color: #d69e2e;">${stats.medios}</div>
                    <div>Média Prioridade</div>
                </div>
                <div class="stat-card" style="border-left-color: #38a169;">
                    <div class="stat-number" style="color: #38a169;">${stats.autofix}</div>
                    <div>Correção Automática</div>
                </div>
                <div class="stat-card" style="border-left-color: #e53e3e;">
                    <div class="stat-number" style="color: #e53e3e;">${stats.duplicidades}</div>
                    <div>Duplicidades</div>
                </div>
                <div class="stat-card" style="border-left-color: #dd6b20;">
                    <div class="stat-number" style="color: #dd6b20;">${stats.orfaos}</div>
                    <div>Registros Órfãos</div>
                </div>
                <div class="stat-card" style="border-left-color: #6b46c1;">
                    <div class="stat-number" style="color: #6b46c1;">${stats.inconsistencias}</div>
                    <div>Inconsistências</div>
                </div>
            `;

            statsSection.style.display = 'block';
        }

        function mostrarProblemas() {
            const problemsContainer = document.getElementById('problemsContainer');
            const resultsSection = document.getElementById('resultsSection');

            if (problemsFound.length === 0) {
                problemsContainer.innerHTML = `
                    <div class="success-card">
                        <h4>✅ Nenhum Problema Encontrado!</h4>
                        <p>Parabéns! Sua base de dados está íntegra e sem inconsistências detectadas.</p>
                    </div>
                `;
                resultsSection.style.display = 'block';
                return;
            }

            // Agrupar problemas por categoria
            const grupos = {};
            problemsFound.forEach(problema => {
                const chave = `${problema.tipo}_${problema.categoria}`;
                if (!grupos[chave]) {
                    grupos[chave] = {
                        tipo: problema.tipo,
                        categoria: problema.categoria,
                        problemas: []
                    };
                }
                grupos[chave].problemas.push(problema);
            });

            let html = '';
            Object.values(grupos).forEach(grupo => {
                const severidadeMaxima = grupo.problemas.reduce((max, p) => {
                    const severidades = { critical: 4, high: 3, medium: 2, low: 1 };
                    return severidades[p.severidade] > severidades[max] ? p.severidade : max;
                }, 'low');

                const cardClass = severidadeMaxima === 'critical' ? 'error-card' :
                                 severidadeMaxima === 'high' ? 'warning-card' : 'info-card';

                html += `
                    <div class="${cardClass}">
                        <button class="collapsible" onclick="toggleCollapsible(this)">
                            <strong>${grupo.tipo.replace('_', ' ')} - ${grupo.categoria}</strong>
                            <span class="tag tag-${grupo.tipo.toLowerCase()}">${grupo.problemas.length} problema(s)</span>
                            <span class="severity-${severidadeMaxima}">Severidade: ${severidadeMaxima.toUpperCase()}</span>
                        </button>
                        <div class="collapsible-content">
                            ${grupo.problemas.map((problema, index) => `
                                <div style="border-bottom: 1px solid #e2e8f0; padding: 10px 0; margin: 10px 0;">
                                    <h5>${problema.titulo}</h5>
                                    <p><strong>Descrição:</strong> ${problema.descricao}</p>
                                    <p><strong>Impacto:</strong> ${problema.impacto}</p>
                                    <p><strong>Solução:</strong> ${problema.solucao}</p>
                                    <div style="margin-top: 10px;">
                                        <button onclick="verDetalhes(${problemsFound.indexOf(problema)})" class="btn-details">Ver Detalhes</button>
                                        ${problema.autofix ? `<button onclick="corrigirProblema(${problemsFound.indexOf(problema)})" class="btn-fix">Corrigir</button>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });

            problemsContainer.innerHTML = html;
            resultsSection.style.display = 'block';
        }

        window.toggleCollapsible = function(element) {
            const content = element.nextElementSibling;
            if (content.style.display === "block") {
                content.style.display = "none";
            } else {
                content.style.display = "block";
            }
        };

        window.verDetalhes = function(index) {
            const problema = problemsFound[index];
            const detailsContainer = document.getElementById('detailsContainer');
            const detailsSection = document.getElementById('detailsSection');

            detailsContainer.innerHTML = `
                <div class="info-card">
                    <h4>${problema.titulo}</h4>
                    <p><strong>Tipo:</strong> ${problema.tipo}</p>
                    <p><strong>Categoria:</strong> ${problema.categoria}</p>
                    <p><strong>Severidade:</strong> <span class="severity-${problema.severidade}">${problema.severidade.toUpperCase()}</span></p>
                    <p><strong>Descrição:</strong> ${problema.descricao}</p>
                    <p><strong>Impacto:</strong> ${problema.impacto}</p>
                    <p><strong>Solução:</strong> ${problema.solucao}</p>

                    <h5>Detalhes Técnicos:</h5>
                    <pre style="background: #f7fafc; padding: 15px; border-radius: 4px; overflow-x: auto;">
${JSON.stringify(problema.detalhes, null, 2)}
                    </pre>

                    ${problema.autofix ? `
                        <div style="margin-top: 15px;">
                            <button onclick="corrigirProblema(${index})" class="btn-fix">🔧 Corrigir Automaticamente</button>
                        </div>
                    ` : ''}
                </div>
            `;

            detailsSection.style.display = 'block';
            detailsSection.scrollIntoView({ behavior: 'smooth' });
        };

        window.corrigirProblema = async function(index) {
            const problema = problemsFound[index];

            if (!problema.autofix) {
                alert('Este problema requer correção manual.');
                return;
            }

            if (!confirm(`Confirma a correção automática do problema:\n\n${problema.titulo}\n${problema.descricao}`)) {
                return;
            }

            try {
                await executarCorrecaoAutomatica(problema);
                alert('✅ Problema corrigido com sucesso!');

                // Remover da lista
                problemsFound.splice(index, 1);

                // Atualizar exibição
                mostrarEstatisticas();
                mostrarProblemas();

            } catch (error) {
                console.error('Erro na correção:', error);
                alert('❌ Erro ao corrigir problema: ' + error.message);
            }
        };

        async function executarCorrecaoAutomatica(problema) {
            switch (problema.tipo) {
                case 'DUPLICIDADE':
                    if (problema.categoria === 'MOVIMENTACAO') {
                        // Remover movimentação duplicada
                        await deleteDoc(doc(db, "movimentacoesEstoque", problema.detalhes.duplicata.id));
                    } else if (problema.categoria === 'ESTOQUE') {
                        // Consolidar estoques duplicados
                        const original = problema.detalhes.original;
                        const duplicata = problema.detalhes.duplicata;

                        await runTransaction(db, async (transaction) => {
                            // Somar saldos
                            transaction.update(doc(db, "estoques", original.id), {
                                saldo: original.saldo + duplicata.saldo,
                                saldoReservado: (original.saldoReservado || 0) + (duplicata.saldoReservado || 0),
                                ultimaMovimentacao: Timestamp.now()
                            });

                            // Remover duplicata
                            transaction.delete(doc(db, "estoques", duplicata.id));
                        });
                    }
                    break;

                case 'ORFAO':
                    if (problema.categoria === 'MOVIMENTACAO') {
                        // Criar estoque correspondente
                        await addDoc(collection(db, "estoques"), {
                            produtoId: problema.detalhes.produtoId,
                            armazemId: problema.detalhes.armazemId,
                            saldo: 0,
                            saldoReservado: 0,
                            ultimaMovimentacao: Timestamp.now(),
                            criadoPorAuditoria: true
                        });
                    }
                    break;

                case 'INCONSISTENCIA':
                    if (problema.categoria === 'RESERVA') {
                        // Ajustar reserva para não exceder saldo
                        const estoque = problema.detalhes.estoque;
                        await updateDoc(doc(db, "estoques", estoque.id), {
                            saldoReservado: Math.min(estoque.saldoReservado, estoque.saldo),
                            ultimaMovimentacao: Timestamp.now()
                        });
                    }
                    break;
            }
        }

        window.corrigirProblemasAutomaticos = async function() {
            const problemasAutomaticos = problemsFound.filter(p => p.autofix);

            if (problemasAutomaticos.length === 0) {
                alert('Não há problemas que possam ser corrigidos automaticamente.');
                return;
            }

            if (!confirm(`Confirma a correção automática de ${problemasAutomaticos.length} problema(s)?`)) {
                return;
            }

            let sucessos = 0;
            let erros = 0;

            for (const problema of problemasAutomaticos) {
                try {
                    await executarCorrecaoAutomatica(problema);
                    sucessos++;
                } catch (error) {
                    console.error('Erro na correção:', error);
                    erros++;
                }
            }

            alert(`✅ Correção automática concluída!\nSucessos: ${sucessos}\nErros: ${erros}`);

            // Reexecutar auditoria
            await executarAuditoria();
        };

        window.exportarRelatorio = function() {
            const relatorio = {
                dataAuditoria: new Date().toISOString(),
                auditorResponsavel: currentUser.nome,
                estatisticas: {
                    totalProblemas: problemsFound.length,
                    porSeveridade: {
                        criticos: problemsFound.filter(p => p.severidade === 'critical').length,
                        altos: problemsFound.filter(p => p.severidade === 'high').length,
                        medios: problemsFound.filter(p => p.severidade === 'medium').length,
                        baixos: problemsFound.filter(p => p.severidade === 'low').length
                    },
                    porTipo: {
                        duplicidades: problemsFound.filter(p => p.tipo === 'DUPLICIDADE').length,
                        orfaos: problemsFound.filter(p => p.tipo === 'ORFAO').length,
                        inconsistencias: problemsFound.filter(p => p.tipo === 'INCONSISTENCIA').length,
                        faltantes: problemsFound.filter(p => p.tipo === 'DADOS_FALTANTES').length
                    }
                },
                problemas: problemsFound.map(p => ({
                    tipo: p.tipo,
                    categoria: p.categoria,
                    severidade: p.severidade,
                    titulo: p.titulo,
                    descricao: p.descricao,
                    impacto: p.impacto,
                    solucao: p.solucao,
                    autofix: p.autofix
                }))
            };

            const blob = new Blob([JSON.stringify(relatorio, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `auditoria_movimentacoes_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };
    </script>
</body>
</html>