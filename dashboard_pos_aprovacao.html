<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Pós-Aprovação - Compras</title>
    <script type="module" src="js/main.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 1rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .status-header {
            padding: 20px;
            font-weight: bold;
            font-size: 1.2rem;
            border-bottom: 1px solid #eee;
        }

        .status-content {
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-aprovado { background: #d4edda; color: #155724; }
        .badge-enviado { background: #d1ecf1; color: #0c5460; }
        .badge-recebido { background: #d4edda; color: #155724; }
        .badge-atraso { background: #f8d7da; color: #721c24; }
        .badge-inspecao { background: #fff3cd; color: #856404; }

        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .alerts-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid;
        }

        .alert-high { 
            background: #f8d7da; 
            border-left-color: #dc3545; 
        }

        .alert-medium { 
            background: #fff3cd; 
            border-left-color: #ffc107; 
        }

        .alert-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #0a4d8c;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Dashboard Pós-Aprovação</h1>
            <p>Acompanhamento completo dos pedidos de compra após aprovação</p>
        </div>

        <!-- Métricas Principais -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon">📋</div>
                <div class="metric-value" id="totalPedidos">0</div>
                <div class="metric-label">Pedidos Aprovados</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">📦</div>
                <div class="metric-value" id="pedidosRecebidos">0</div>
                <div class="metric-label">Recebidos</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">⏰</div>
                <div class="metric-value" id="pedidosAtraso">0</div>
                <div class="metric-label">Em Atraso</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">🔍</div>
                <div class="metric-value" id="itensInspecao">0</div>
                <div class="metric-label">Aguardando Inspeção</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-value" id="valorTotal">R$ 0,00</div>
                <div class="metric-label">Valor Total</div>
            </div>
        </div>

        <!-- Status dos Pedidos -->
        <div class="status-grid">
            <div class="status-card">
                <div class="status-header">
                    📋 Pedidos por Status
                </div>
                <div class="status-content" id="pedidosPorStatus">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    ⏰ Pedidos em Atraso
                </div>
                <div class="status-content" id="pedidosEmAtraso">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    🔍 Itens Pendentes de Inspeção
                </div>
                <div class="status-content" id="itensPendentesInspecao">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
        </div>

        <!-- Gráfico de Progresso -->
        <div class="chart-container">
            <div class="chart-title">📈 Progresso dos Pedidos</div>
            <div id="progressoChart">
                <!-- Gráfico será carregado dinamicamente -->
            </div>
        </div>

        <!-- Alertas e Notificações -->
        <div class="alerts-section">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">🚨 Alertas e Notificações</h3>
            <div id="alertasContainer">
                <!-- Alertas serão carregados dinamicamente -->
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="pedidos_compra.html" class="btn btn-primary">📋 Ver Todos os Pedidos</a>
            <a href="recebimento_materiais.html" class="btn btn-primary">📦 Recebimento</a>
            <a href="controle_qualidade.html" class="btn btn-primary">🔍 Qualidade</a>
            <a href="contas_pagar.html" class="btn btn-primary">💰 Financeiro</a>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.1/firebase-app.js';
        import { 
            getFirestore, 
            collection, 
            query, 
            where, 
            getDocs,
            orderBy,
            limit
        } from 'https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore.js';

        // Configuração do Firebase
        const firebaseConfig = {
            // Sua configuração do Firebase aqui
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Carregar dados do dashboard
        async function carregarDashboard() {
            try {
                console.log('📊 Carregando dashboard...');
                
                await Promise.all([
                    carregarMetricas(),
                    carregarStatusPedidos(),
                    carregarPedidosAtraso(),
                    carregarItensInspecao(),
                    carregarAlertas()
                ]);
                
                console.log('✅ Dashboard carregado com sucesso!');
                
            } catch (error) {
                console.error('❌ Erro ao carregar dashboard:', error);
            }
        }

        // Carregar métricas principais
        async function carregarMetricas() {
            try {
                // Pedidos aprovados
                const pedidosQuery = query(
                    collection(db, "pedidosCompra"),
                    where("status", "in", ["APROVADO", "ENVIADO", "RECEBIDO", "PARCIALMENTE_RECEBIDO"])
                );
                const pedidosSnap = await getDocs(pedidosQuery);
                
                let totalPedidos = 0;
                let pedidosRecebidos = 0;
                let valorTotal = 0;
                let pedidosAtraso = 0;
                
                const hoje = new Date();
                
                pedidosSnap.forEach(doc => {
                    const pedido = doc.data();
                    totalPedidos++;
                    valorTotal += pedido.valorTotal || 0;
                    
                    if (pedido.status === 'RECEBIDO') {
                        pedidosRecebidos++;
                    }
                    
                    // Verificar atraso
                    const dataEntregaPrevista = pedido.dataEntregaPrevista?.toDate();
                    if (dataEntregaPrevista && dataEntregaPrevista < hoje && 
                        !['RECEBIDO'].includes(pedido.status)) {
                        pedidosAtraso++;
                    }
                });
                
                // Itens em inspeção
                const inspecaoQuery = query(
                    collection(db, "estoqueQualidade"),
                    where("status", "==", "PENDENTE")
                );
                const inspecaoSnap = await getDocs(inspecaoQuery);
                
                // Atualizar interface
                document.getElementById('totalPedidos').textContent = totalPedidos;
                document.getElementById('pedidosRecebidos').textContent = pedidosRecebidos;
                document.getElementById('pedidosAtraso').textContent = pedidosAtraso;
                document.getElementById('itensInspecao').textContent = inspecaoSnap.size;
                document.getElementById('valorTotal').textContent = 
                    `R$ ${valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
                
            } catch (error) {
                console.error('Erro ao carregar métricas:', error);
            }
        }

        // Carregar status dos pedidos
        async function carregarStatusPedidos() {
            try {
                const pedidosQuery = query(
                    collection(db, "pedidosCompra"),
                    where("status", "in", ["APROVADO", "ENVIADO", "RECEBIDO", "PARCIALMENTE_RECEBIDO"]),
                    orderBy("dataCriacao", "desc"),
                    limit(10)
                );
                
                const pedidosSnap = await getDocs(pedidosQuery);
                const container = document.getElementById('pedidosPorStatus');
                
                let html = '';
                pedidosSnap.forEach(doc => {
                    const pedido = doc.data();
                    const statusClass = `badge-${pedido.status.toLowerCase().replace('_', '')}`;
                    
                    html += `
                        <div class="status-item">
                            <div>
                                <strong>${pedido.numero || doc.id}</strong><br>
                                <small>R$ ${(pedido.valorTotal || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</small>
                            </div>
                            <span class="status-badge ${statusClass}">${pedido.status}</span>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<p>Nenhum pedido encontrado</p>';
                
            } catch (error) {
                console.error('Erro ao carregar status dos pedidos:', error);
            }
        }

        // Carregar pedidos em atraso
        async function carregarPedidosAtraso() {
            try {
                const hoje = new Date();
                const pedidosQuery = query(
                    collection(db, "pedidosCompra"),
                    where("status", "in", ["APROVADO", "ENVIADO"])
                );
                
                const pedidosSnap = await getDocs(pedidosQuery);
                const container = document.getElementById('pedidosEmAtraso');
                
                let html = '';
                let count = 0;
                
                pedidosSnap.forEach(doc => {
                    const pedido = doc.data();
                    const dataEntregaPrevista = pedido.dataEntregaPrevista?.toDate();
                    
                    if (dataEntregaPrevista && dataEntregaPrevista < hoje) {
                        const diasAtraso = Math.floor((hoje - dataEntregaPrevista) / (1000 * 60 * 60 * 24));
                        
                        html += `
                            <div class="status-item">
                                <div>
                                    <strong>${pedido.numero || doc.id}</strong><br>
                                    <small>${diasAtraso} dias de atraso</small>
                                </div>
                                <span class="status-badge badge-atraso">ATRASO</span>
                            </div>
                        `;
                        count++;
                    }
                });
                
                container.innerHTML = html || '<p>Nenhum pedido em atraso</p>';
                
            } catch (error) {
                console.error('Erro ao carregar pedidos em atraso:', error);
            }
        }

        // Carregar itens pendentes de inspeção
        async function carregarItensInspecao() {
            try {
                const inspecaoQuery = query(
                    collection(db, "estoqueQualidade"),
                    where("status", "==", "PENDENTE"),
                    orderBy("dataEntrada", "desc"),
                    limit(10)
                );
                
                const inspecaoSnap = await getDocs(inspecaoQuery);
                const container = document.getElementById('itensPendentesInspecao');
                
                let html = '';
                inspecaoSnap.forEach(doc => {
                    const item = doc.data();
                    const dataEntrada = item.dataEntrada?.toDate();
                    const diasPendente = dataEntrada ? 
                        Math.floor((new Date() - dataEntrada) / (1000 * 60 * 60 * 24)) : 0;
                    
                    html += `
                        <div class="status-item">
                            <div>
                                <strong>${item.codigo || 'N/A'}</strong><br>
                                <small>${diasPendente} dias pendente</small>
                            </div>
                            <span class="status-badge badge-inspecao">PENDENTE</span>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<p>Nenhum item pendente</p>';
                
            } catch (error) {
                console.error('Erro ao carregar itens de inspeção:', error);
            }
        }

        // Carregar alertas
        async function carregarAlertas() {
            try {
                const alertasQuery = query(
                    collection(db, "notificacoes"),
                    where("status", "==", "PENDENTE"),
                    orderBy("dataNotificacao", "desc"),
                    limit(5)
                );
                
                const alertasSnap = await getDocs(alertasQuery);
                const container = document.getElementById('alertasContainer');
                
                let html = '';
                alertasSnap.forEach(doc => {
                    const alerta = doc.data();
                    const prioridadeClass = `alert-${alerta.prioridade.toLowerCase()}`;
                    
                    html += `
                        <div class="alert-item ${prioridadeClass}">
                            <div class="alert-icon">🚨</div>
                            <div>
                                <strong>${alerta.titulo}</strong><br>
                                <small>${alerta.mensagem}</small>
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<p>Nenhum alerta pendente</p>';
                
            } catch (error) {
                console.error('Erro ao carregar alertas:', error);
            }
        }

        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', () => {
            carregarDashboard();
            
            // Atualizar a cada 5 minutos
            setInterval(carregarDashboard, 5 * 60 * 1000);
        });
    </script>
</body>
</html>
