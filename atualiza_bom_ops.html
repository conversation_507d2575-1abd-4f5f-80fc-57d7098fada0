<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Atualização de BOM em OPs</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    button {
      background-color: var(--success-color);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      margin-right: 10px;
    }

    button:hover {
      background-color: var(--success-hover);
    }

    .btn-warning {
      background-color: var(--warning-color);
    }

    .btn-warning:hover {
      background-color: #d2620a;
    }

    .btn-secondary {
      background-color: var(--secondary-color);
      color: var(--text-color);
    }

    .btn-secondary:hover {
      background-color: #e0e3e6;
    }

    .progress-container {
      margin: 20px 0;
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }

    .progress-bar {
      height: 20px;
      background-color: #e0e0e0;
      border-radius: 10px;
      margin: 10px 0;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      width: 0%;
      transition: width 0.3s ease;
    }

    .log-container {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;
    }

    .log-entry {
      margin-bottom: 5px;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }

    .log-success {
      color: var(--success-color);
    }

    .log-warning {
      color: var(--warning-color);
    }

    .log-error {
      color: var(--danger-color);
    }

    .filter-section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 4px;
      border: 1px solid var(--border-color);
    }

    .filter-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .filter-options {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .filter-group {
      flex: 1;
      min-width: 200px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    select, input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .summary {
      margin-top: 20px;
      padding: 15px;
      background-color: #e8f4fd;
      border: 1px solid #b8daff;
      border-radius: 4px;
      color: #004085;
    }

    .action-buttons {
      margin: 20px 0;
      display: flex;
      gap: 10px;
    }

    @media (max-width: 768px) {
      .filter-options {
        flex-direction: column;
      }
      .filter-group {
        min-width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Atualização de BOM em Ordens de Produção</h1>
      <div>
        <button class="btn-secondary" onclick="window.location.href='ordens_producao.html'">Voltar para OPs</button>
        <button class="btn-secondary" onclick="window.location.href='index.html'">Menu Principal</button>
      </div>
    </div>

    <div class="filter-section">
      <div class="filter-title">Filtros para Atualização</div>
      <div class="filter-options">
        <div class="filter-group">
          <label for="statusFilter">Status das OPs:</label>
          <select id="statusFilter">
            <option value="pendente">Pendente</option>
            <option value="em-producao">Em Produção</option>
            <option value="todos" selected>Todos (Pendente e Em Produção)</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="dateFilter">Data de Criação (a partir de):</label>
          <input type="date" id="dateFilter">
        </div>
        <div class="filter-group">
          <label for="productTypeFilter">Tipo de Produto:</label>
          <select id="productTypeFilter">
            <option value="todos" selected>Todos</option>
            <option value="PA">Produto Acabado (PA)</option>
            <option value="SP">Subproduto (SP)</option>
          </select>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <button id="btnAnalisar" class="btn-secondary"><i class="fas fa-search"></i> Analisar OPs</button>
      <button id="btnAtualizar" class="btn-warning"><i class="fas fa-sync"></i> Atualizar OPs</button>
    </div>

    <div class="progress-container" style="display: none;" id="progressContainer">
      <div id="progressStatus">Preparando...</div>
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
      <div id="progressDetails">0/0 OPs processadas</div>
    </div>

    <div class="summary" id="summaryContainer" style="display: none;">
      <div id="summaryText"></div>
    </div>

    <div class="log-container" id="logContainer" style="display: none;">
      <div id="logEntries"></div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      doc, 
      getDoc,
      getDocs,
      updateDoc,
      query,
      where,
      Timestamp,
      orderBy
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let ordensProducao = [];
    let estoques = [];
    let opsParaAtualizar = [];
    let isProcessing = false;

    window.onload = async function() {
      if (!localStorage.getItem('currentUser')) {
        window.location.href = 'login.html';
        return;
      }
      
      await loadInitialData();
      setupEventListeners();
    };

    async function loadInitialData() {
      try {
        const [produtosSnap, estruturasSnap, estoquesSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "estoques"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        logMessage("Dados iniciais carregados com sucesso.", "success");
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        logMessage("Erro ao carregar dados iniciais: " + error.message, "error");
      }
    }

    function setupEventListeners() {
      document.getElementById('btnAnalisar').addEventListener('click', analisarOPs);
      document.getElementById('btnAtualizar').addEventListener('click', atualizarOPs);
    }

    async function analisarOPs() {
      if (isProcessing) return;
      isProcessing = true;

      try {
        resetUI();
        showProgress(true);
        updateProgressStatus("Buscando ordens de produção...");

        // Aplicar filtros
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const productTypeFilter = document.getElementById('productTypeFilter').value;

        // Construir a query com base nos filtros
        let ordensQuery;
        if (statusFilter === 'todos') {
          ordensQuery = query(
            collection(db, "ordensProducao"),
            where("status", "in", ["Pendente", "Em Produção"])
          );
        } else {
          const status = statusFilter === 'pendente' ? 'Pendente' : 'Em Produção';
          ordensQuery = query(
            collection(db, "ordensProducao"),
            where("status", "==", status)
          );
        }

        const ordensSnap = await getDocs(ordensQuery);
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Filtrar por data se especificada
        if (dateFilter) {
          const filterDate = new Date(dateFilter);
          ordensProducao = ordensProducao.filter(op => {
            if (!op.dataCriacao) return true;
            const opDate = new Date(op.dataCriacao.seconds * 1000);
            return opDate >= filterDate;
          });
        }

        // Filtrar por tipo de produto se especificado
        if (productTypeFilter !== 'todos') {
          ordensProducao = ordensProducao.filter(op => {
            const produto = produtos.find(p => p.id === op.produtoId);
            return produto && produto.tipo === productTypeFilter;
          });
        }

        updateProgressStatus(`Analisando ${ordensProducao.length} ordens de produção...`);
        updateProgressDetails(`0/${ordensProducao.length} OPs analisadas`);

        opsParaAtualizar = [];
        let contador = 0;
        let opsComProblemas = 0;

        for (const op of ordensProducao) {
          contador++;
          updateProgressFill(contador / ordensProducao.length * 100);
          updateProgressDetails(`${contador}/${ordensProducao.length} OPs analisadas`);

          const estrutura = estruturas.find(e => e.produtoPaiId === op.produtoId);
          if (!estrutura) {
            logMessage(`OP ${op.numero}: Estrutura não encontrada para o produto.`, "warning");
            continue;
          }

          const materiaisAtuais = op.materiaisNecessarios || [];
          let precisaAtualizar = false;
          const materiaisFaltantes = [];

          // Verificar componentes na estrutura
          for (const componente of estrutura.componentes) {
            const produto = produtos.find(p => p.id === componente.componentId);
            if (!produto) {
              logMessage(`OP ${op.numero}: Produto componente não encontrado: ${componente.componentId}`, "warning");
              continue;
            }

            // Verificar se é SP/PA e não está na lista de materiais
            if ((produto.tipo === 'SP' || produto.tipo === 'PA') && 
                !materiaisAtuais.some(m => m.produtoId === componente.componentId)) {
              precisaAtualizar = true;
              materiaisFaltantes.push({
                produtoId: componente.componentId,
                codigo: produto.codigo,
                descricao: produto.descricao,
                tipo: produto.tipo,
                quantidade: op.quantidade * componente.quantidade
              });
            }
          }

          if (precisaAtualizar) {
            opsComProblemas++;
            opsParaAtualizar.push({
              op,
              materiaisFaltantes
            });

            logMessage(`OP ${op.numero}: Faltam ${materiaisFaltantes.length} componentes SP/PA na lista de materiais.`, "warning");
            for (const material of materiaisFaltantes) {
              logMessage(`  - ${material.codigo} - ${material.descricao} (${material.tipo}): ${material.quantidade}`, "info");
            }
          } else {
            logMessage(`OP ${op.numero}: BOM completo, não precisa de atualização.`, "success");
          }
        }

        updateProgressStatus("Análise concluída!");
        showSummary(`Análise concluída! ${opsComProblemas} de ${ordensProducao.length} OPs precisam ser atualizadas.`);

        if (opsParaAtualizar.length > 0) {
          document.getElementById('btnAtualizar').disabled = false;
        } else {
          document.getElementById('btnAtualizar').disabled = true;
        }

      } catch (error) {
        console.error("Erro ao analisar OPs:", error);
        logMessage("Erro ao analisar OPs: " + error.message, "error");
        updateProgressStatus("Erro ao analisar OPs!");
      } finally {
        isProcessing = false;
      }
    }

    async function atualizarOPs() {
      if (isProcessing || opsParaAtualizar.length === 0) return;
      isProcessing = true;

      try {
        if (!confirm(`Deseja atualizar ${opsParaAtualizar.length} ordens de produção para incluir componentes SP/PA faltantes?`)) {
          isProcessing = false;
          return;
        }

        resetUI();
        showProgress(true);
        updateProgressStatus("Atualizando ordens de produção...");
        updateProgressDetails(`0/${opsParaAtualizar.length} OPs atualizadas`);

        let contador = 0;
        let atualizadasComSucesso = 0;

        for (const item of opsParaAtualizar) {
          contador++;
          updateProgressFill(contador / opsParaAtualizar.length * 100);
          updateProgressDetails(`${contador}/${opsParaAtualizar.length} OPs atualizadas`);

          try {
            const op = item.op;
            const materiaisAtuais = op.materiaisNecessarios || [];
            const materiaisAtualizados = [...materiaisAtuais];

            for (const material of item.materiaisFaltantes) {
              const saldoDisponivel = await checkInventory(material.produtoId, op.armazemProducaoId);
              const quantidadeNecessaria = material.quantidade;
              const quantidadeReservada = Math.min(saldoDisponivel, quantidadeNecessaria);
              const necessidade = Math.max(0, quantidadeNecessaria - quantidadeReservada);

              materiaisAtualizados.push({
                produtoId: material.produtoId,
                quantidade: quantidadeNecessaria,
                saldoEstoque: saldoDisponivel,
                quantidadeReservada,
                necessidade,
                tipo: material.tipo
              });

              if (quantidadeReservada > 0) {
                await updateInventoryReservation(material.produtoId, quantidadeReservada, op.armazemProducaoId);
              }
            }

            await updateDoc(doc(db, "ordensProducao", op.id), {
              materiaisNecessarios: materiaisAtualizados
            });

            atualizadasComSucesso++;
            logMessage(`OP ${op.numero}: Atualizada com sucesso! Adicionados ${item.materiaisFaltantes.length} componentes.`, "success");
          } catch (error) {
            console.error(`Erro ao atualizar OP ${item.op.numero}:`, error);
            logMessage(`Erro ao atualizar OP ${item.op.numero}: ${error.message}`, "error");
          }
        }

        updateProgressStatus("Atualização concluída!");
        showSummary(`Atualização concluída! ${atualizadasComSucesso} de ${opsParaAtualizar.length} OPs foram atualizadas com sucesso.`);

      } catch (error) {
        console.error("Erro ao atualizar OPs:", error);
        logMessage("Erro ao atualizar OPs: " + error.message, "error");
        updateProgressStatus("Erro ao atualizar OPs!");
      } finally {
        isProcessing = false;
      }
    }

    // Função para verificar o estoque disponível de um produto
    async function checkInventory(produtoId, armazemId) {
      try {
        // Buscar estoque pelo produto e armazém
        const estoqueQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", produtoId),
          where("armazemId", "==", armazemId)
        );
        
        const estoqueSnap = await getDocs(estoqueQuery);
        if (estoqueSnap.empty) return 0;
        
        // Somar saldo disponível (saldo - saldoReservado)
        let saldoDisponivel = 0;
        estoqueSnap.forEach(doc => {
          const estoque = doc.data();
          const saldo = estoque.saldo || 0;
          const reservado = estoque.saldoReservado || 0;
          saldoDisponivel += Math.max(0, saldo - reservado);
        });
        
        return saldoDisponivel;
      } catch (error) {
        console.error("Erro ao verificar estoque:", error);
        return 0;
      }
    }

    // Função para atualizar a reserva de estoque
    async function updateInventoryReservation(produtoId, quantidade, armazemId) {
      try {
        // Buscar estoque pelo produto e armazém
        const estoqueQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", produtoId),
          where("armazemId", "==", armazemId)
        );
        
        const estoqueSnap = await getDocs(estoqueQuery);
        if (estoqueSnap.empty) return;
        
        // Atualizar o primeiro registro de estoque encontrado
        const estoqueDoc = estoqueSnap.docs[0];
        const estoque = estoqueDoc.data();
        const reservadoAtual = estoque.saldoReservado || 0;
        
        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
          saldoReservado: reservadoAtual + quantidade,
          ultimaMovimentacao: Timestamp.now()
        });
        
        // Registrar movimentação de estoque
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId: produtoId,
          tipo: 'RESERVA',
          quantidade: quantidade,
          tipoDocumento: 'OP',
          numeroDocumento: 'Atualização BOM',
          observacoes: `Reserva para atualização de BOM em OP`,
          dataHora: Timestamp.now()
        });
      } catch (error) {
        console.error("Erro ao atualizar reserva de estoque:", error);
        throw error;
      }
    }

    // Funções de UI
    function resetUI() {
      document.getElementById('logEntries').innerHTML = '';
      document.getElementById('summaryContainer').style.display = 'none';
      document.getElementById('logContainer').style.display = 'none';
      document.getElementById('progressFill').style.width = '0%';
      document.getElementById('progressStatus').textContent = 'Preparando...';
      document.getElementById('progressDetails').textContent = '0/0 OPs processadas';
    }

    function showProgress(show) {
      document.getElementById('progressContainer').style.display = show ? 'block' : 'none';
    }

    function updateProgressStatus(text) {
      document.getElementById('progressStatus').textContent = text;
    }

    function updateProgressDetails(text) {
      document.getElementById('progressDetails').textContent = text;
    }

    function updateProgressFill(percentage) {
      document.getElementById('progressFill').style.width = `${percentage}%`;
    }

    function showSummary(text) {
      const summaryContainer = document.getElementById('summaryContainer');
      document.getElementById('summaryText').textContent = text;
      summaryContainer.style.display = 'block';
    }

    function logMessage(message, type = "info") {
      const logContainer = document.getElementById('logContainer');
      const logEntries = document.getElementById('logEntries');
      
      const entry = document.createElement('div');
      entry.className = `log-entry log-${type}`;
      entry.textContent = message;
      
      logEntries.appendChild(entry);
      logContainer.style.display = 'block';
      
      // Auto-scroll para o final
      logContainer.scrollTop = logContainer.scrollHeight;
    }
  </script>
</body>
</html>
