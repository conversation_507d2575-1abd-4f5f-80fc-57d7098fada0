<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard Adaptativo - Fluxo de Materiais</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .dashboard-container {
            max-width: 1600px;
            margin: 20px auto;
            padding: 20px;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .config-section {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #007bff;
        }
        .flow-matrix {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1000px;
        }
        .matrix-table th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .matrix-table td {
            padding: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            position: relative;
            min-width: 80px;
            height: 60px;
        }
        .product-header {
            background: #e9ecef;
            font-weight: bold;
            text-align: left;
            padding: 8px 12px;
            position: sticky;
            left: 0;
            z-index: 5;
            min-width: 200px;
            max-width: 200px;
        }
        .warehouse-header {
            background: #f8f9fa;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 80px;
        }
        
        /* Status Colors */
        .status-pendente { background: #fff3cd; color: #856404; }
        .status-recebido { background: #e2e3e5; color: #383d41; }
        .status-qualidade { background: #ffeaa7; color: #6c5ce7; }
        .status-estoque { background: #74b9ff; color: #0984e3; }
        .status-producao { background: #fd79a8; color: #e84393; }
        .status-usado { background: #00b894; color: #00a085; }
        .status-vazio { background: #f8f9fa; }
        
        .material-cell {
            border-radius: 4px;
            margin: 2px;
            padding: 4px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 50px;
        }
        .material-cell:hover {
            transform: scale(1.1);
            z-index: 100;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .quantity-badge {
            font-size: 10px;
            background: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 1px 4px;
            margin-top: 2px;
        }
        .btn-refresh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        .btn-config {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>📊 Dashboard Adaptativo de Fluxo de Materiais</h1>
            <p>Visualização baseada nas suas coleções reais de dados</p>
        </div>

        <!-- Configuração -->
        <div class="config-section" id="configSection">
            <h3>⚙️ Configuração do Dashboard</h3>
            <p>Este dashboard se adapta automaticamente às coleções disponíveis no seu sistema.</p>
            <div style="text-align: center;">
                <button onclick="verificarColecoes()" class="btn-config">🔍 Verificar Coleções Disponíveis</button>
                <button onclick="window.open('verificar_colecoes_dashboard.html', '_blank')" class="btn-config">🛠️ Configuração Avançada</button>
            </div>
            <div id="configStatus"></div>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <h3>🎛️ Filtros e Controles</h3>
            <div class="filters-grid">
                <div>
                    <label>Período:</label>
                    <select id="periodoFiltro">
                        <option value="7">Últimos 7 dias</option>
                        <option value="15" selected>Últimos 15 dias</option>
                        <option value="30">Últimos 30 dias</option>
                        <option value="60">Últimos 60 dias</option>
                    </select>
                </div>
                <div>
                    <label>Produto:</label>
                    <input type="text" id="produtoFiltro" placeholder="Filtrar por código/nome...">
                </div>
                <div>
                    <label>Armazém:</label>
                    <select id="armazemFiltro">
                        <option value="all">Todos os armazéns</option>
                    </select>
                </div>
                <div>
                    <label>Status:</label>
                    <select id="statusFiltro">
                        <option value="all">Todos os status</option>
                        <option value="pendente">Pendentes</option>
                        <option value="disponivel">Disponível</option>
                    </select>
                </div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="carregarDashboard()" class="btn-refresh">🔄 Atualizar Dashboard</button>
                <button onclick="exportarDados()" class="btn btn-secondary">📊 Exportar Dados</button>
            </div>
        </div>

        <!-- Estatísticas -->
        <div id="statsSection">
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <!-- Legenda -->
        <div class="legend" id="legendaStatus">
            <div class="legend-item">
                <div class="legend-color status-pendente"></div>
                <span>Pendente</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-recebido"></div>
                <span>Recebido</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-qualidade"></div>
                <span>Em Qualidade</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-estoque"></div>
                <span>Em Estoque</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-producao"></div>
                <span>Na Produção</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-usado"></div>
                <span>Usado/Consumido</span>
            </div>
        </div>

        <!-- Matriz de Fluxo -->
        <div class="flow-matrix">
            <h3>🗺️ Mapa de Fluxo de Materiais</h3>
            <div id="matrixContainer" class="loading">
                <p>Clique em "Verificar Coleções Disponíveis" para começar...</p>
            </div>
        </div>
    </div>

    <!-- Modal para detalhes -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="fecharModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let dashboardConfig = {};
        let dashboardData = {};
        let materiaisFluxo = [];

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            
            // Tentar carregar configuração salva
            const configSalva = localStorage.getItem('dashboardConfig');
            if (configSalva) {
                dashboardConfig = JSON.parse(configSalva);
                mostrarConfiguracao();
            }
        };

        window.verificarColecoes = async function() {
            const statusDiv = document.getElementById('configStatus');
            statusDiv.innerHTML = '<p>🔄 Verificando coleções disponíveis...</p>';

            try {
                // Verificar coleções básicas que sabemos que existem
                const colecoesBasicas = [
                    'estoques',
                    'movimentacoesEstoque', 
                    'produtos',
                    'armazens',
                    'recebimento_materiais',
                    'ordens_producao'
                ];

                const colecoesEncontradas = {};
                
                for (const nomeColecao of colecoesBasicas) {
                    try {
                        const snapshot = await getDocs(collection(db, nomeColecao));
                        const quantidade = snapshot.docs.length;
                        
                        if (quantidade > 0) {
                            colecoesEncontradas[nomeColecao] = {
                                nome: nomeColecao,
                                quantidade: quantidade,
                                amostra: snapshot.docs.slice(0, 1).map(doc => ({ id: doc.id, ...doc.data() }))
                            };
                            console.log(`✅ ${nomeColecao}: ${quantidade} registros`);
                        }
                    } catch (error) {
                        console.log(`❌ ${nomeColecao}: não encontrada`);
                    }
                }

                dashboardConfig = {
                    colecoesEncontradas: colecoesEncontradas,
                    totalColecoes: Object.keys(colecoesEncontradas).length,
                    dataVerificacao: new Date().toISOString()
                };

                localStorage.setItem('dashboardConfig', JSON.stringify(dashboardConfig));
                mostrarConfiguracao();
                
                // Carregar armazéns no filtro
                if (colecoesEncontradas['armazens']) {
                    await carregarArmazensNoFiltro();
                }

            } catch (error) {
                console.error('Erro na verificação:', error);
                statusDiv.innerHTML = `<p style="color: #e53e3e;">❌ Erro: ${error.message}</p>`;
            }
        };

        function mostrarConfiguracao() {
            const statusDiv = document.getElementById('configStatus');
            const colecoes = dashboardConfig.colecoesEncontradas || {};
            
            let html = `
                <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <h4>✅ Configuração Carregada</h4>
                    <p><strong>Coleções encontradas:</strong> ${Object.keys(colecoes).length}</p>
                    <ul>
            `;
            
            Object.values(colecoes).forEach(col => {
                html += `<li><strong>${col.nome}:</strong> ${col.quantidade} registros</li>`;
            });
            
            html += `
                    </ul>
                    <p><small>Verificado em: ${new Date(dashboardConfig.dataVerificacao).toLocaleString()}</small></p>
                </div>
            `;
            
            statusDiv.innerHTML = html;
        }

        async function carregarArmazensNoFiltro() {
            try {
                const armazensSnap = await getDocs(collection(db, "armazens"));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                const select = document.getElementById('armazemFiltro');
                select.innerHTML = '<option value="all">Todos os armazéns</option>';
                
                armazens.forEach(arm => {
                    const option = document.createElement('option');
                    option.value = arm.id;
                    option.textContent = `${arm.codigo || arm.id} - ${arm.nome || 'Sem nome'}`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Erro ao carregar armazéns:', error);
            }
        }

        window.carregarDashboard = async function() {
            if (!dashboardConfig.colecoesEncontradas || Object.keys(dashboardConfig.colecoesEncontradas).length === 0) {
                alert('Execute primeiro a verificação de coleções!');
                return;
            }

            const container = document.getElementById('matrixContainer');
            container.innerHTML = '<div class="loading"><p>🔄 Carregando dados do dashboard...</p></div>';

            try {
                await carregarDadosAdaptativos();
                processarFluxoAdaptativo();
                mostrarEstatisticas();
                renderizarMatrizAdaptativa();

            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                container.innerHTML = `<div style="color: #e53e3e; text-align: center; padding: 20px;">❌ Erro: ${error.message}</div>`;
            }
        };

        async function carregarDadosAdaptativos() {
            const colecoes = dashboardConfig.colecoesEncontradas;
            dashboardData = {};

            // Carregar dados das coleções disponíveis
            const promises = [];

            if (colecoes['estoques']) {
                promises.push(
                    getDocs(collection(db, "estoques")).then(snap => {
                        dashboardData.estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            if (colecoes['movimentacoesEstoque']) {
                promises.push(
                    getDocs(query(collection(db, "movimentacoesEstoque"), orderBy("dataHora", "desc"), limit(200))).then(snap => {
                        dashboardData.movimentacoes = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            if (colecoes['produtos']) {
                promises.push(
                    getDocs(collection(db, "produtos")).then(snap => {
                        dashboardData.produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            if (colecoes['armazens']) {
                promises.push(
                    getDocs(collection(db, "armazens")).then(snap => {
                        dashboardData.armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            if (colecoes['recebimento_materiais']) {
                promises.push(
                    getDocs(query(collection(db, "recebimento_materiais"), orderBy("dataRecebimento", "desc"), limit(100))).then(snap => {
                        dashboardData.recebimentos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            if (colecoes['ordens_producao']) {
                promises.push(
                    getDocs(query(collection(db, "ordens_producao"), orderBy("dataAbertura", "desc"), limit(50))).then(snap => {
                        dashboardData.ordens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    })
                );
            }

            await Promise.all(promises);

            console.log('Dados carregados:', {
                estoques: dashboardData.estoques?.length || 0,
                movimentacoes: dashboardData.movimentacoes?.length || 0,
                produtos: dashboardData.produtos?.length || 0,
                armazens: dashboardData.armazens?.length || 0,
                recebimentos: dashboardData.recebimentos?.length || 0,
                ordens: dashboardData.ordens?.length || 0
            });
        }

        function processarFluxoAdaptativo() {
            materiaisFluxo = [];
            const materiaisMap = new Map();

            // Aplicar filtros
            const periodo = parseInt(document.getElementById('periodoFiltro').value);
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() - periodo);
            const produtoFiltro = document.getElementById('produtoFiltro').value.toLowerCase();
            const armazemFiltro = document.getElementById('armazemFiltro').value;

            // 1. Processar estoques atuais
            if (dashboardData.estoques) {
                dashboardData.estoques.forEach(estoque => {
                    if (estoque.saldo > 0) {
                        const produto = dashboardData.produtos?.find(p => p.id === estoque.produtoId);

                        // Aplicar filtros
                        if (produtoFiltro && produto) {
                            const textoFiltro = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                            if (!textoFiltro.includes(produtoFiltro)) return;
                        }

                        if (armazemFiltro !== 'all' && estoque.armazemId !== armazemFiltro) return;

                        const chave = `${estoque.produtoId}_${estoque.armazemId}`;
                        materiaisMap.set(chave, {
                            produtoId: estoque.produtoId,
                            armazemId: estoque.armazemId,
                            codigo: produto?.codigo || estoque.produtoId,
                            descricao: produto?.descricao || 'Sem descrição',
                            quantidade: estoque.saldo,
                            unidade: produto?.unidade || 'UN',
                            status: 'ESTOQUE',
                            etapa: 'Em Estoque',
                            localizacao: estoque.armazemId,
                            ultimaAtualizacao: estoque.ultimaMovimentacao || Timestamp.now(),
                            historico: [{
                                etapa: 'ESTOQUE',
                                data: estoque.ultimaMovimentacao || Timestamp.now(),
                                observacao: `Saldo atual: ${estoque.saldo}`
                            }]
                        });
                    }
                });
            }

            // 2. Processar recebimentos recentes
            if (dashboardData.recebimentos) {
                dashboardData.recebimentos.forEach(rec => {
                    const dataRec = rec.dataRecebimento?.seconds ? new Date(rec.dataRecebimento.seconds * 1000) : new Date();
                    if (dataRec >= dataLimite) {
                        const produto = dashboardData.produtos?.find(p => p.id === rec.produtoId || p.codigo === rec.codigo);

                        // Aplicar filtros
                        if (produtoFiltro && produto) {
                            const textoFiltro = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                            if (!textoFiltro.includes(produtoFiltro)) return;
                        }

                        const chave = `${rec.produtoId || rec.codigo}_${rec.armazemDestino || 'RECEBIMENTO'}_${rec.id}`;

                        let status = 'RECEBIDO';
                        let localizacao = rec.armazemDestino || 'RECEBIMENTO';

                        if (rec.status === 'PENDENTE_INSPECAO') {
                            status = 'QUALIDADE';
                            localizacao = 'QUALIDADE';
                        }

                        materiaisMap.set(chave, {
                            produtoId: rec.produtoId || rec.codigo,
                            armazemId: rec.armazemDestino,
                            codigo: produto?.codigo || rec.codigo,
                            descricao: produto?.descricao || rec.descricao || 'Sem descrição',
                            quantidade: rec.quantidadeRecebida || rec.quantidade,
                            unidade: produto?.unidade || rec.unidade || 'UN',
                            status: status,
                            etapa: status === 'QUALIDADE' ? 'Em Qualidade' : 'Recebido',
                            localizacao: localizacao,
                            recebimentoId: rec.id,
                            numeroNF: rec.numeroNF,
                            ultimaAtualizacao: rec.dataRecebimento,
                            historico: [{
                                etapa: status,
                                data: rec.dataRecebimento,
                                observacao: `Recebido - NF: ${rec.numeroNF || 'N/A'}`
                            }]
                        });
                    }
                });
            }

            // 3. Processar movimentações para produção
            if (dashboardData.movimentacoes) {
                dashboardData.movimentacoes.forEach(mov => {
                    if (mov.tipo === 'SAIDA' && mov.tipoDocumento === 'PRODUCAO') {
                        const dataMov = mov.dataHora?.seconds ? new Date(mov.dataHora.seconds * 1000) : new Date();
                        if (dataMov >= dataLimite) {
                            const produto = dashboardData.produtos?.find(p => p.id === mov.produtoId);

                            // Aplicar filtros
                            if (produtoFiltro && produto) {
                                const textoFiltro = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                                if (!textoFiltro.includes(produtoFiltro)) return;
                            }

                            const chave = `${mov.produtoId}_PRODUCAO_${mov.id}`;
                            materiaisMap.set(chave, {
                                produtoId: mov.produtoId,
                                armazemId: 'PRODUCAO',
                                codigo: produto?.codigo || mov.produtoId,
                                descricao: produto?.descricao || 'Sem descrição',
                                quantidade: mov.quantidade,
                                unidade: produto?.unidade || 'UN',
                                status: 'PRODUCAO',
                                etapa: 'Na Produção',
                                localizacao: 'PRODUCAO',
                                ordemProducaoId: mov.ordemProducaoId,
                                ultimaAtualizacao: mov.dataHora,
                                historico: [{
                                    etapa: 'PRODUCAO',
                                    data: mov.dataHora,
                                    observacao: `Enviado para produção - OP: ${mov.ordemProducaoId || 'N/A'}`
                                }]
                            });
                        }
                    }
                });
            }

            // Converter Map para Array
            materiaisFluxo = Array.from(materiaisMap.values());

            console.log('Materiais processados:', materiaisFluxo.length);
        }

        function mostrarEstatisticas() {
            const stats = {
                total: materiaisFluxo.length,
                recebidos: materiaisFluxo.filter(m => m.status === 'RECEBIDO').length,
                qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length,
                producao: materiaisFluxo.filter(m => m.status === 'PRODUCAO').length,
                quantidadeTotal: materiaisFluxo.reduce((sum, m) => sum + (m.quantidade || 0), 0)
            };

            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div>Total de Materiais</div>
                </div>
                <div class="stat-card" style="border-left-color: #6c757d;">
                    <div class="stat-number" style="color: #6c757d;">${stats.recebidos}</div>
                    <div>Recebidos</div>
                </div>
                <div class="stat-card" style="border-left-color: #fd7e14;">
                    <div class="stat-number" style="color: #fd7e14;">${stats.qualidade}</div>
                    <div>Em Qualidade</div>
                </div>
                <div class="stat-card" style="border-left-color: #007bff;">
                    <div class="stat-number" style="color: #007bff;">${stats.estoque}</div>
                    <div>Em Estoque</div>
                </div>
                <div class="stat-card" style="border-left-color: #e83e8c;">
                    <div class="stat-number" style="color: #e83e8c;">${stats.producao}</div>
                    <div>Na Produção</div>
                </div>
                <div class="stat-card" style="border-left-color: #28a745;">
                    <div class="stat-number" style="color: #28a745;">${stats.quantidadeTotal.toFixed(0)}</div>
                    <div>Quantidade Total</div>
                </div>
            `;
        }

        function renderizarMatrizAdaptativa() {
            const container = document.getElementById('matrixContainer');

            if (materiaisFluxo.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">Nenhum material encontrado no período selecionado com os filtros aplicados.</div>';
                return;
            }

            // Agrupar materiais por produto
            const produtosMap = new Map();
            materiaisFluxo.forEach(material => {
                const chave = material.produtoId;
                if (!produtosMap.has(chave)) {
                    produtosMap.set(chave, {
                        produtoId: chave,
                        codigo: material.codigo,
                        descricao: material.descricao,
                        materiais: []
                    });
                }
                produtosMap.get(chave).materiais.push(material);
            });

            // Definir colunas baseadas nos armazéns disponíveis + etapas especiais
            const colunas = [
                { id: 'QUALIDADE', nome: 'Qualidade', tipo: 'etapa' },
                ...dashboardData.armazens?.map(arm => ({
                    id: arm.id,
                    nome: arm.codigo || arm.nome,
                    tipo: 'armazem'
                })) || [],
                { id: 'PRODUCAO', nome: 'Produção', tipo: 'etapa' }
            ];

            // Construir tabela
            let html = `
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th class="product-header">Produto</th>
            `;

            colunas.forEach(col => {
                html += `<th class="warehouse-header">${col.nome}</th>`;
            });

            html += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Renderizar linhas dos produtos
            Array.from(produtosMap.values()).forEach(produto => {
                html += `
                    <tr>
                        <td class="product-header">
                            <strong>${produto.codigo}</strong><br>
                            <small>${produto.descricao}</small>
                        </td>
                `;

                colunas.forEach(coluna => {
                    const materiaisNaColuna = produto.materiais.filter(material => {
                        return material.localizacao === coluna.id;
                    });

                    html += '<td>';

                    if (materiaisNaColuna.length > 0) {
                        materiaisNaColuna.forEach(material => {
                            const statusClass = `status-${material.status.toLowerCase()}`;
                            html += `
                                <div class="material-cell ${statusClass}"
                                     onclick="mostrarDetalhes('${material.produtoId}', '${material.localizacao}')"
                                     title="${material.etapa} - ${material.quantidade} ${material.unidade}">
                                    <div>${material.etapa}</div>
                                    <div class="quantity-badge">${material.quantidade} ${material.unidade}</div>
                                </div>
                            `;
                        });
                    }

                    html += '</td>';
                });

                html += '</tr>';
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        window.mostrarDetalhes = function(produtoId, localizacao) {
            const material = materiaisFluxo.find(m =>
                m.produtoId === produtoId && m.localizacao === localizacao
            );

            if (!material) {
                alert('Material não encontrado');
                return;
            }

            const modal = document.getElementById('detailModal');
            const modalContent = document.getElementById('modalContent');

            let html = `
                <h3>📦 Detalhes do Material</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div>
                        <h4>Informações Básicas</h4>
                        <p><strong>Código:</strong> ${material.codigo}</p>
                        <p><strong>Descrição:</strong> ${material.descricao}</p>
                        <p><strong>Quantidade:</strong> ${material.quantidade} ${material.unidade}</p>
                        <p><strong>Status Atual:</strong> ${material.etapa}</p>
                        <p><strong>Localização:</strong> ${material.localizacao}</p>
                    </div>
                    <div>
                        <h4>Rastreamento</h4>
                        <p><strong>Produto ID:</strong> ${material.produtoId}</p>
                        <p><strong>Recebimento:</strong> ${material.recebimentoId || 'N/A'}</p>
                        <p><strong>NF:</strong> ${material.numeroNF || 'N/A'}</p>
                        <p><strong>OP:</strong> ${material.ordemProducaoId || 'N/A'}</p>
                        <p><strong>Última Atualização:</strong> ${material.ultimaAtualizacao ?
                            new Date(material.ultimaAtualizacao.seconds * 1000).toLocaleString() : 'N/A'}</p>
                    </div>
                </div>
            `;

            modalContent.innerHTML = html;
            modal.style.display = 'block';
        };

        window.fecharModal = function() {
            document.getElementById('detailModal').style.display = 'none';
        };

        window.exportarDados = function() {
            const dados = {
                dataExportacao: new Date().toISOString(),
                usuario: currentUser.nome,
                configuracao: dashboardConfig,
                filtros: {
                    periodo: document.getElementById('periodoFiltro').value + ' dias',
                    produto: document.getElementById('produtoFiltro').value,
                    armazem: document.getElementById('armazemFiltro').value,
                    status: document.getElementById('statusFiltro').value
                },
                estatisticas: {
                    totalMateriais: materiaisFluxo.length,
                    porStatus: {
                        recebidos: materiaisFluxo.filter(m => m.status === 'RECEBIDO').length,
                        qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                        estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length,
                        producao: materiaisFluxo.filter(m => m.status === 'PRODUCAO').length
                    }
                },
                materiais: materiaisFluxo
            };

            const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard_fluxo_adaptativo_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('detailModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };
    </script>
</body>
</html>
