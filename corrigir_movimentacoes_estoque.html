<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Correção de Movimentações - Estoque Singular → Plural</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .correction-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .warning-card {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .error-card {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success-card {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .info-card {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 14px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .step-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="correction-container">
        <h1>🔄 Correção de Movimentações de Estoque</h1>
        
        <div class="warning-card">
            <h3>⚠️ ATENÇÃO - CORREÇÃO DE MOVIMENTAÇÕES</h3>
            <p><strong>Este script irá:</strong></p>
            <ul>
                <li>✅ Identificar movimentações que referenciam registros da coleção "estoque" (singular)</li>
                <li>✅ Encontrar os registros correspondentes na coleção "estoques" (plural)</li>
                <li>✅ Atualizar as referências das movimentações</li>
                <li>✅ Recalcular saldos baseado nas movimentações corrigidas</li>
                <li>⚠️ <strong>FAZER BACKUP antes de executar!</strong></li>
            </ul>
        </div>

        <div class="step-card">
            <h3><span class="step-number">1</span>Análise das Movimentações</h3>
            <p>Primeiro, vamos analisar quantas movimentações precisam ser corrigidas.</p>
            <button onclick="analisarMovimentacoes()" class="btn btn-primary">🔍 Analisar Movimentações</button>
        </div>

        <div class="step-card">
            <h3><span class="step-number">2</span>Correção das Referências</h3>
            <p>Corrigir as referências das movimentações para apontar para os registros corretos.</p>
            <button onclick="corrigirReferencias()" class="btn-danger" id="btnCorrigir" disabled>🔄 Corrigir Referências</button>
        </div>

        <div class="step-card">
            <h3><span class="step-number">3</span>Recálculo de Saldos</h3>
            <p>Recalcular todos os saldos baseado nas movimentações corrigidas.</p>
            <button onclick="recalcularSaldos()" class="btn btn-success" id="btnRecalcular" disabled>📊 Recalcular Saldos</button>
        </div>

        <div id="resultados"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc,
            runTransaction,
            query,
            where,
            orderBy,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let analysisData = {};

        window.analisarMovimentacoes = async function() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<p>🔄 Analisando movimentações...</p>';

            try {
                // Buscar todos os dados necessários
                const [movimentacoesSnap, estoqueSingularSnap, estoquePluralSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "estoque")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos"))
                ]);

                analysisData = {
                    movimentacoes: movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    estoqueSingular: estoqueSingularSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    estoquePlural: estoquePluralSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    produtos: produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
                };

                // Analisar problemas
                const analysis = analisarProblemas();
                
                // Mostrar resultados
                mostrarAnalise(analysis);

                // Habilitar próximo passo se houver problemas
                if (analysis.movimentacoesProblematicas.length > 0) {
                    document.getElementById('btnCorrigir').disabled = false;
                }

            } catch (error) {
                console.error('Erro na análise:', error);
                resultados.innerHTML = `<div class="error-card">❌ Erro: ${error.message}</div>`;
            }
        };

        function analisarProblemas() {
            const movimentacoesProblematicas = [];
            const mapeamentoCorreto = new Map();
            const saldosIncorretos = [];

            // 1. Identificar movimentações que podem estar referenciando registros errados
            analysisData.movimentacoes.forEach(mov => {
                // Verificar se existe estoque correspondente na coleção correta
                const estoqueCorreto = analysisData.estoquePlural.find(est => 
                    est.produtoId === mov.produtoId && est.armazemId === mov.armazemId
                );

                // Verificar se existe na coleção errada
                const estoqueErrado = analysisData.estoqueSingular.find(est => 
                    est.produtoId === mov.produtoId && 
                    (est.armazemId === mov.armazemId || est.armazem === mov.armazemId)
                );

                if (estoqueErrado && !estoqueCorreto) {
                    // Movimentação pode estar referenciando registro da coleção errada
                    movimentacoesProblematicas.push({
                        movimentacao: mov,
                        estoqueErrado: estoqueErrado,
                        problema: 'REFERENCIA_COLEÇÃO_ERRADA'
                    });
                } else if (estoqueErrado && estoqueCorreto) {
                    // Ambos existem - pode haver duplicação
                    movimentacoesProblematicas.push({
                        movimentacao: mov,
                        estoqueErrado: estoqueErrado,
                        estoqueCorreto: estoqueCorreto,
                        problema: 'DUPLICAÇÃO_REGISTROS'
                    });
                }

                // Mapear correção necessária
                if (estoqueCorreto) {
                    mapeamentoCorreto.set(mov.id, estoqueCorreto.id);
                }
            });

            // 2. Identificar saldos que podem estar incorretos
            analysisData.estoquePlural.forEach(est => {
                const movimentacoesRelacionadas = analysisData.movimentacoes.filter(mov => 
                    mov.produtoId === est.produtoId && mov.armazemId === est.armazemId
                );

                let saldoCalculado = 0;
                movimentacoesRelacionadas.forEach(mov => {
                    if (mov.tipo === 'ENTRADA') {
                        saldoCalculado += mov.quantidade || 0;
                    } else if (mov.tipo === 'SAIDA') {
                        saldoCalculado -= mov.quantidade || 0;
                    }
                });

                if (Math.abs(saldoCalculado - est.saldo) > 0.001) {
                    saldosIncorretos.push({
                        estoque: est,
                        saldoAtual: est.saldo,
                        saldoCalculado: saldoCalculado,
                        diferenca: saldoCalculado - est.saldo,
                        movimentacoes: movimentacoesRelacionadas.length
                    });
                }
            });

            return {
                movimentacoesProblematicas,
                mapeamentoCorreto,
                saldosIncorretos
            };
        }

        function mostrarAnalise(analysis) {
            const resultados = document.getElementById('resultados');
            let html = '<h2>📊 Análise das Movimentações</h2>';

            // Resumo geral
            html += `
                <div class="info-card">
                    <h3>📈 Resumo da Análise</h3>
                    <ul>
                        <li><strong>Total de movimentações:</strong> ${analysisData.movimentacoes.length}</li>
                        <li><strong>Registros na coleção "estoque":</strong> ${analysisData.estoqueSingular.length}</li>
                        <li><strong>Registros na coleção "estoques":</strong> ${analysisData.estoquePlural.length}</li>
                        <li><strong>Movimentações problemáticas:</strong> ${analysis.movimentacoesProblematicas.length}</li>
                        <li><strong>Saldos incorretos:</strong> ${analysis.saldosIncorretos.length}</li>
                    </ul>
                </div>
            `;

            // Movimentações problemáticas
            if (analysis.movimentacoesProblematicas.length > 0) {
                html += `
                    <div class="warning-card">
                        <h3>⚠️ Movimentações Problemáticas (${analysis.movimentacoesProblematicas.length})</h3>
                        <p>Estas movimentações podem estar referenciando registros incorretos.</p>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Produto ID</th>
                                    <th>Armazém ID</th>
                                    <th>Tipo</th>
                                    <th>Quantidade</th>
                                    <th>Problema</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                analysis.movimentacoesProblematicas.slice(0, 20).forEach(item => {
                    const mov = item.movimentacao;
                    const dataHora = mov.dataHora ? 
                        new Date(mov.dataHora.seconds * 1000).toLocaleString() : 'N/A';
                    
                    html += `
                        <tr>
                            <td>${dataHora}</td>
                            <td>${mov.produtoId}</td>
                            <td>${mov.armazemId}</td>
                            <td>${mov.tipo}</td>
                            <td>${mov.quantidade}</td>
                            <td>${item.problema}</td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                        ${analysis.movimentacoesProblematicas.length > 20 ? `<p>... e mais ${analysis.movimentacoesProblematicas.length - 20} movimentações</p>` : ''}
                    </div>
                `;
            }

            // Saldos incorretos
            if (analysis.saldosIncorretos.length > 0) {
                html += `
                    <div class="error-card">
                        <h3>❌ Saldos Incorretos (${analysis.saldosIncorretos.length})</h3>
                        <p>Estes estoques têm saldos que não batem com as movimentações.</p>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Produto ID</th>
                                    <th>Armazém ID</th>
                                    <th>Saldo Atual</th>
                                    <th>Saldo Calculado</th>
                                    <th>Diferença</th>
                                    <th>Movimentações</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                analysis.saldosIncorretos.slice(0, 15).forEach(item => {
                    html += `
                        <tr>
                            <td>${item.estoque.produtoId}</td>
                            <td>${item.estoque.armazemId}</td>
                            <td>${item.saldoAtual}</td>
                            <td>${item.saldoCalculado}</td>
                            <td style="color: ${item.diferenca > 0 ? 'green' : 'red'}">${item.diferenca.toFixed(3)}</td>
                            <td>${item.movimentacoes}</td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                        ${analysis.saldosIncorretos.length > 15 ? `<p>... e mais ${analysis.saldosIncorretos.length - 15} registros</p>` : ''}
                    </div>
                `;
            }

            if (analysis.movimentacoesProblematicas.length === 0 && analysis.saldosIncorretos.length === 0) {
                html += `
                    <div class="success-card">
                        <h3>✅ Nenhum Problema Encontrado!</h3>
                        <p>Todas as movimentações estão corretas e os saldos batem com as movimentações.</p>
                    </div>
                `;
            }

            resultados.innerHTML = html;
        }

        window.corrigirReferencias = async function() {
            if (!analysisData.movimentacoes) {
                alert('Execute a análise primeiro!');
                return;
            }

            if (!confirm('⚠️ CONFIRMAÇÃO\n\nEsta operação irá corrigir as referências das movimentações problemáticas.\n\nDeseja continuar?')) {
                return;
            }

            const resultados = document.getElementById('resultados');
            resultados.innerHTML = `
                <div class="info-card">
                    <h3>🔄 Corrigindo Referências...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar1" style="width: 0%"></div>
                    </div>
                    <p id="progressText1">Iniciando correção...</p>
                </div>
            `;

            try {
                // Reexecutar análise para ter dados atualizados
                const analysis = analisarProblemas();
                let corrigidas = 0;
                let erros = 0;
                const total = analysis.movimentacoesProblematicas.length;

                for (let i = 0; i < analysis.movimentacoesProblematicas.length; i++) {
                    const item = analysis.movimentacoesProblematicas[i];
                    
                    try {
                        // Aqui você implementaria a lógica específica de correção
                        // Por exemplo, atualizar campos de referência se necessário
                        
                        corrigidas++;
                    } catch (error) {
                        console.error('Erro ao corrigir movimentação:', item.movimentacao.id, error);
                        erros++;
                    }

                    const progresso = ((i + 1) / total) * 100;
                    document.getElementById('progressBar1').style.width = `${progresso}%`;
                    document.getElementById('progressText1').textContent = 
                        `Processando ${i + 1}/${total} - Corrigidas: ${corrigidas}, Erros: ${erros}`;
                }

                resultados.innerHTML = `
                    <div class="success-card">
                        <h3>✅ Correção de Referências Concluída!</h3>
                        <ul>
                            <li><strong>Total processado:</strong> ${total}</li>
                            <li><strong>Corrigidas:</strong> ${corrigidas}</li>
                            <li><strong>Erros:</strong> ${erros}</li>
                        </ul>
                    </div>
                `;

                // Habilitar próximo passo
                document.getElementById('btnRecalcular').disabled = false;

            } catch (error) {
                console.error('Erro na correção:', error);
                resultados.innerHTML = `<div class="error-card">❌ Erro na correção: ${error.message}</div>`;
            }
        };

        window.recalcularSaldos = async function() {
            if (!confirm('⚠️ CONFIRMAÇÃO FINAL\n\nEsta operação irá recalcular todos os saldos baseado nas movimentações.\n\nDeseja continuar?')) {
                return;
            }

            const resultados = document.getElementById('resultados');
            resultados.innerHTML = `
                <div class="info-card">
                    <h3>📊 Recalculando Saldos...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar2" style="width: 0%"></div>
                    </div>
                    <p id="progressText2">Iniciando recálculo...</p>
                </div>
            `;

            try {
                let processados = 0;
                let atualizados = 0;
                let erros = 0;
                const total = analysisData.estoquePlural.length;

                for (const estoque of analysisData.estoquePlural) {
                    try {
                        // Calcular saldo baseado nas movimentações
                        const movimentacoesRelacionadas = analysisData.movimentacoes.filter(mov => 
                            mov.produtoId === estoque.produtoId && mov.armazemId === estoque.armazemId
                        );

                        let saldoCalculado = 0;
                        movimentacoesRelacionadas.forEach(mov => {
                            if (mov.tipo === 'ENTRADA') {
                                saldoCalculado += mov.quantidade || 0;
                            } else if (mov.tipo === 'SAIDA') {
                                saldoCalculado -= mov.quantidade || 0;
                            }
                        });

                        // Atualizar se houver diferença
                        if (Math.abs(saldoCalculado - estoque.saldo) > 0.001) {
                            await updateDoc(doc(db, "estoques", estoque.id), {
                                saldo: saldoCalculado,
                                saldoAnterior: estoque.saldo,
                                ultimaMovimentacao: Timestamp.now(),
                                recalculadoEm: Timestamp.now()
                            });
                            atualizados++;
                        }

                        processados++;
                    } catch (error) {
                        console.error('Erro ao recalcular estoque:', estoque.id, error);
                        erros++;
                    }

                    const progresso = (processados / total) * 100;
                    document.getElementById('progressBar2').style.width = `${progresso}%`;
                    document.getElementById('progressText2').textContent = 
                        `Processando ${processados}/${total} - Atualizados: ${atualizados}, Erros: ${erros}`;
                }

                resultados.innerHTML = `
                    <div class="success-card">
                        <h3>✅ Recálculo de Saldos Concluído!</h3>
                        <ul>
                            <li><strong>Estoques processados:</strong> ${processados}</li>
                            <li><strong>Saldos atualizados:</strong> ${atualizados}</li>
                            <li><strong>Erros:</strong> ${erros}</li>
                        </ul>
                        <p><strong>Recomendação:</strong> Execute uma nova auditoria para verificar se todos os problemas foram resolvidos.</p>
                    </div>
                `;

            } catch (error) {
                console.error('Erro no recálculo:', error);
                resultados.innerHTML = `<div class="error-card">❌ Erro no recálculo: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
