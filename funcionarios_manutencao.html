<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Funcionários da Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header h1::before {
      content: '👷‍♂️';
      font-size: 28px;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-section {
      margin-bottom: 25px;
    }

    .section-header {
      background: linear-gradient(135deg, var(--primary-color), #0a4d8c);
      color: white;
      padding: 10px 15px;
      border-radius: 6px;
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .funcionarios-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .funcionarios-table th,
    .funcionarios-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .funcionarios-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
      cursor: pointer;
    }

    .funcionarios-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      display: inline-block;
    }

    .status-ativo {
      background-color: #d4edda;
      color: #155724;
    }

    .status-inativo {
      background-color: #f8d7da;
      color: #721c24;
    }

    .especialidade-badge {
      display: inline-block;
      background-color: #e9ecef;
      color: #495057;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      margin: 1px;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      display: none;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }

    .search-container {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 10px;
    }

    .tag {
      background-color: var(--primary-color);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .tag-remove {
      cursor: pointer;
      font-weight: bold;
    }

    .input-with-button {
      display: flex;
      gap: 5px;
    }

    .input-with-button input,
    .input-with-button select {
      flex: 1;
    }

    .btn-action {
      padding: 5px 8px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      margin: 0 2px;
    }

    .btn-edit {
      background-color: #007bff;
      color: white;
    }

    .btn-delete {
      background-color: var(--danger-color);
      color: white;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Funcionários da Manutenção</h1>
      <div>
        <button class="btn btn-primary" onclick="showNewFuncionarioForm()">
          <i class="fas fa-plus"></i> Novo Funcionário
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Formulário de Funcionário -->
    <div id="funcionarioFormContainer" class="form-container" style="display: none;">
      <h2 class="form-title">Cadastro de Funcionário da Manutenção</h2>
      <form id="funcionarioForm">
        <input type="hidden" id="editingId">

        <!-- Dados Pessoais -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-user"></i> Dados Pessoais
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="nome" class="required">Nome Completo</label>
              <input type="text" id="nome" name="nome" required>
            </div>
            <div class="form-col">
              <label for="cpf" class="required">CPF</label>
              <input type="text" id="cpf" name="cpf" required>
            </div>
            <div class="form-col">
              <label for="telefone" class="required">Telefone</label>
              <input type="tel" id="telefone" name="telefone" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="email">Email</label>
              <input type="email" id="email" name="email">
            </div>
            <div class="form-col">
              <label for="endereco">Endereço</label>
              <input type="text" id="endereco" name="endereco">
            </div>
          </div>
        </div>

        <!-- Dados Profissionais -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-briefcase"></i> Dados Profissionais
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="matricula" class="required">Matrícula</label>
              <input type="text" id="matricula" name="matricula" required>
            </div>
            <div class="form-col">
              <label for="cargo" class="required">Cargo</label>
              <select id="cargo" name="cargo" required>
                <option value="">Selecione o cargo...</option>
                <option value="TECNICO_ELETRICA">Técnico em Elétrica</option>
                <option value="TECNICO_MECANICA">Técnico em Mecânica</option>
                <option value="TECNICO_HIDRAULICA">Técnico em Hidráulica</option>
                <option value="TECNICO_PNEUMATICA">Técnico em Pneumática</option>
                <option value="SOLDADOR">Soldador</option>
                <option value="AUXILIAR">Auxiliar de Manutenção</option>
                <option value="SUPERVISOR">Supervisor de Manutenção</option>
                <option value="COORDENADOR">Coordenador de Manutenção</option>
                <option value="GERENTE">Gerente de Manutenção</option>
              </select>
            </div>
            <div class="form-col">
              <label for="setor" class="required">Setor</label>
              <select id="setor" name="setor" required>
                <option value="">Selecione o setor...</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="turno" class="required">Turno de Trabalho</label>
              <select id="turno" name="turno" required>
                <option value="">Selecione o turno...</option>
                <option value="MANHA">Manhã (06:00 - 14:00)</option>
                <option value="TARDE">Tarde (14:00 - 22:00)</option>
                <option value="NOITE">Noite (22:00 - 06:00)</option>
                <option value="ADMINISTRATIVO">Administrativo (08:00 - 17:00)</option>
                <option value="SOBREAVISO">Sobreaviso</option>
              </select>
            </div>
            <div class="form-col">
              <label for="custoHora">Custo por Hora (R$)</label>
              <input type="number" id="custoHora" name="custoHora" min="0" step="0.01">
            </div>
            <div class="form-col">
              <label for="dataAdmissao">Data de Admissão</label>
              <input type="date" id="dataAdmissao" name="dataAdmissao">
            </div>
          </div>
        </div>

        <!-- Especialidades -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-tools"></i> Especialidades
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="especialidadeInput">Adicionar Especialidade</label>
              <div class="input-with-button">
                <select id="especialidadeInput">
                  <option value="">Selecione uma especialidade...</option>
                  <option value="MANUTENCAO_ELETRICA">Manutenção Elétrica</option>
                  <option value="MANUTENCAO_MECANICA">Manutenção Mecânica</option>
                  <option value="MANUTENCAO_HIDRAULICA">Manutenção Hidráulica</option>
                  <option value="SOLDAGEM_MIG">Soldagem MIG</option>
                  <option value="SOLDAGEM_TIG">Soldagem TIG</option>
                  <option value="PNEUMATICA">Sistemas Pneumáticos</option>
                  <option value="ELETRONICA_INDUSTRIAL">Eletrônica Industrial</option>
                  <option value="CLP">Controladores Lógicos (CLP)</option>
                  <option value="INSTRUMENTACAO">Instrumentação</option>
                </select>
                <button type="button" class="btn btn-primary" onclick="adicionarEspecialidade()">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
              <div id="especialidadesContainer" class="tags-container"></div>
            </div>
          </div>
        </div>

        <!-- Observações -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-comment"></i> Informações Adicionais
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="observacoes">Observações</label>
              <textarea id="observacoes" name="observacoes" rows="3"></textarea>
            </div>
            <div class="form-col">
              <label for="status" class="required">Status</label>
              <select id="status" name="status" required>
                <option value="ATIVO">Ativo</option>
                <option value="INATIVO">Inativo</option>
                <option value="AFASTADO">Afastado</option>
                <option value="FERIAS">Férias</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn btn-secondary" onclick="hideFuncionarioForm()">Cancelar</button>
          <button type="submit" class="btn btn-success" id="submitButton">Cadastrar Funcionário</button>
        </div>
      </form>
    </div>

    <!-- Lista de Funcionários -->
    <div class="form-container">
      <h2 class="form-title">Funcionários Cadastrados</h2>

      <div class="search-container">
        <input type="text" id="searchInput" placeholder="Pesquisar por nome, matrícula, cargo...">
        <select id="statusFilter">
          <option value="">Todos os Status</option>
          <option value="ATIVO">Ativo</option>
          <option value="INATIVO">Inativo</option>
          <option value="AFASTADO">Afastado</option>
          <option value="FERIAS">Férias</option>
        </select>
        <select id="cargoFilter">
          <option value="">Todos os Cargos</option>
          <option value="TECNICO_ELETRICA">Técnico Elétrica</option>
          <option value="TECNICO_MECANICA">Técnico Mecânica</option>
          <option value="SUPERVISOR">Supervisor</option>
        </select>
      </div>

      <table class="funcionarios-table">
        <thead>
          <tr>
            <th>Matrícula</th>
            <th>Nome</th>
            <th>Cargo</th>
            <th>Setor</th>
            <th>Turno</th>
            <th>Especialidades</th>
            <th>Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="funcionariosTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, addDoc, getDocs, doc, updateDoc, deleteDoc, serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let funcionariosList = [];
    let setoresList = [];
    let especialidadesSelecionadas = [];

    async function loadSetores() {
      try {
        const setoresRef = collection(db, "setores");
        const querySnapshot = await getDocs(setoresRef);
        
        setoresList = querySnapshot.docs.map(doc => ({
          id: doc.id, ...doc.data()
        }));

        const select = document.getElementById('setor');
        select.innerHTML = '<option value="">Selecione o setor...</option>' +
          setoresList.map(setor => 
            `<option value="${setor.id}">${setor.nome}</option>`
          ).join('');
      } catch (error) {
        console.error("Erro ao carregar setores:", error);
      }
    }

    async function loadFuncionarios() {
      try {
        const funcionariosRef = collection(db, "funcionariosManutencao");
        const querySnapshot = await getDocs(funcionariosRef);

        funcionariosList = querySnapshot.docs.map(doc => ({
          id: doc.id, ...doc.data()
        }));

        updateFuncionariosTable(funcionariosList);
      } catch (error) {
        console.error("Erro ao carregar funcionários:", error);
      }
    }

    function updateFuncionariosTable(funcionarios) {
      const tbody = document.querySelector('#funcionariosTableBody');
      tbody.innerHTML = funcionarios.map(funcionario => `
        <tr>
          <td>${funcionario.matricula}</td>
          <td>${funcionario.nome}</td>
          <td>${getCargoLabel(funcionario.cargo)}</td>
          <td>${getSetorNome(funcionario.setor)}</td>
          <td>${getTurnoLabel(funcionario.turno)}</td>
          <td>
            ${(funcionario.especialidades || []).map(esp => 
              `<span class="especialidade-badge">${getEspecialidadeLabel(esp)}</span>`
            ).join('')}
          </td>
          <td><span class="status-badge status-${funcionario.status.toLowerCase()}">${getStatusLabel(funcionario.status)}</span></td>
          <td>
            <button onclick="editarFuncionario('${funcionario.id}')" class="btn-action btn-edit">
              <i class="fas fa-edit"></i>
            </button>
            <button onclick="excluirFuncionario('${funcionario.id}')" class="btn-action btn-delete">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        </tr>
      `).join('');
    }

    function getCargoLabel(cargo) {
      const cargos = {
        'TECNICO_ELETRICA': 'Técnico Elétrica',
        'TECNICO_MECANICA': 'Técnico Mecânica',
        'TECNICO_HIDRAULICA': 'Técnico Hidráulica',
        'TECNICO_PNEUMATICA': 'Técnico Pneumática',
        'SOLDADOR': 'Soldador',
        'AUXILIAR': 'Auxiliar',
        'SUPERVISOR': 'Supervisor',
        'COORDENADOR': 'Coordenador',
        'GERENTE': 'Gerente'
      };
      return cargos[cargo] || cargo;
    }

    function getTurnoLabel(turno) {
      const turnos = {
        'MANHA': 'Manhã', 'TARDE': 'Tarde', 'NOITE': 'Noite',
        'ADMINISTRATIVO': 'Administrativo', 'SOBREAVISO': 'Sobreaviso'
      };
      return turnos[turno] || turno;
    }

    function getStatusLabel(status) {
      const statusLabels = {
        'ATIVO': 'Ativo', 'INATIVO': 'Inativo', 'AFASTADO': 'Afastado', 'FERIAS': 'Férias'
      };
      return statusLabels[status] || status;
    }

    function getEspecialidadeLabel(especialidade) {
      const especialidades = {
        'MANUTENCAO_ELETRICA': 'Elétrica',
        'MANUTENCAO_MECANICA': 'Mecânica',
        'MANUTENCAO_HIDRAULICA': 'Hidráulica',
        'SOLDAGEM_MIG': 'Soldagem MIG',
        'SOLDAGEM_TIG': 'Soldagem TIG',
        'PNEUMATICA': 'Pneumática',
        'ELETRONICA_INDUSTRIAL': 'Eletrônica',
        'CLP': 'CLP',
        'INSTRUMENTACAO': 'Instrumentação'
      };
      return especialidades[especialidade] || especialidade;
    }

    function getSetorNome(setorId) {
      const setor = setoresList.find(s => s.id === setorId);
      return setor ? setor.nome : 'N/A';
    }

    window.adicionarEspecialidade = function() {
      const select = document.getElementById('especialidadeInput');
      const valor = select.value;
      
      if (valor && !especialidadesSelecionadas.includes(valor)) {
        especialidadesSelecionadas.push(valor);
        atualizarEspecialidadesView();
        select.value = '';
      }
    };

    function atualizarEspecialidadesView() {
      const container = document.getElementById('especialidadesContainer');
      container.innerHTML = especialidadesSelecionadas.map(esp => `
        <div class="tag">
          ${getEspecialidadeLabel(esp)}
          <span class="tag-remove" onclick="removerEspecialidade('${esp}')">&times;</span>
        </div>
      `).join('');
    }

    window.removerEspecialidade = function(especialidade) {
      const index = especialidadesSelecionadas.indexOf(especialidade);
      if (index > -1) {
        especialidadesSelecionadas.splice(index, 1);
        atualizarEspecialidadesView();
      }
    };

    window.showNewFuncionarioForm = function() {
      document.getElementById('funcionarioFormContainer').style.display = 'block';
      document.getElementById('funcionarioForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').textContent = 'Cadastrar Funcionário';
      especialidadesSelecionadas = [];
      atualizarEspecialidadesView();
    };

    window.hideFuncionarioForm = function() {
      document.getElementById('funcionarioFormContainer').style.display = 'none';
      especialidadesSelecionadas = [];
    };

    async function criarFuncionario(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);
        const editingId = formData.get('editingId');

        const funcionarioData = {
          nome: formData.get('nome'),
          cpf: formData.get('cpf'),
          telefone: formData.get('telefone'),
          email: formData.get('email'),
          endereco: formData.get('endereco'),
          matricula: formData.get('matricula'),
          cargo: formData.get('cargo'),
          setor: formData.get('setor'),
          turno: formData.get('turno'),
          custoHora: parseFloat(formData.get('custoHora')) || 0,
          dataAdmissao: formData.get('dataAdmissao') ? new Date(formData.get('dataAdmissao')) : null,
          especialidades: especialidadesSelecionadas,
          observacoes: formData.get('observacoes'),
          status: formData.get('status')
        };

        if (editingId) {
          funcionarioData.dataUltimaAlteracao = serverTimestamp();
          await updateDoc(doc(db, "funcionariosManutencao", editingId), funcionarioData);
          showNotification("Funcionário atualizado com sucesso", "success");
        } else {
          funcionarioData.dataCadastro = serverTimestamp();
          await addDoc(collection(db, "funcionariosManutencao"), funcionarioData);
          showNotification("Funcionário cadastrado com sucesso", "success");
        }

        hideFuncionarioForm();
        loadFuncionarios();
      } catch (error) {
        console.error("Erro ao salvar funcionário:", error);
        showNotification("Erro ao salvar funcionário", "error");
      }
    }

    window.editarFuncionario = function(funcionarioId) {
      const funcionario = funcionariosList.find(f => f.id === funcionarioId);
      if (!funcionario) return;

      showNewFuncionarioForm();

      document.getElementById('editingId').value = funcionarioId;
      document.getElementById('nome').value = funcionario.nome;
      document.getElementById('cpf').value = funcionario.cpf || '';
      document.getElementById('telefone').value = funcionario.telefone;
      document.getElementById('email').value = funcionario.email || '';
      document.getElementById('endereco').value = funcionario.endereco || '';
      document.getElementById('matricula').value = funcionario.matricula;
      document.getElementById('cargo').value = funcionario.cargo;
      document.getElementById('setor').value = funcionario.setor;
      document.getElementById('turno').value = funcionario.turno;
      document.getElementById('custoHora').value = funcionario.custoHora || '';
      
      if (funcionario.dataAdmissao) {
        const date = funcionario.dataAdmissao.toDate ? funcionario.dataAdmissao.toDate() : new Date(funcionario.dataAdmissao);
        document.getElementById('dataAdmissao').value = date.toISOString().split('T')[0];
      }
      
      document.getElementById('observacoes').value = funcionario.observacoes || '';
      document.getElementById('status').value = funcionario.status;

      especialidadesSelecionadas = funcionario.especialidades || [];
      atualizarEspecialidadesView();

      document.getElementById('submitButton').textContent = 'Atualizar Funcionário';
    };

    window.excluirFuncionario = async function(funcionarioId) {
      if (!confirm('Tem certeza que deseja excluir este funcionário?')) return;

      try {
        await deleteDoc(doc(db, "funcionariosManutencao", funcionarioId));
        showNotification("Funcionário excluído com sucesso", "success");
        loadFuncionarios();
      } catch (error) {
        console.error("Erro ao excluir funcionário:", error);
        showNotification("Erro ao excluir funcionário", "error");
      }
    };

    window.showNotification = function(message, type = 'info') {
      const notification = document.getElementById('notification');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, 3000);
    };

    function filtrarFuncionarios() {
      const status = document.getElementById('statusFilter').value;
      const cargo = document.getElementById('cargoFilter').value;
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      const funcionariosFiltrados = funcionariosList.filter(funcionario => {
        const matchStatus = !status || funcionario.status === status;
        const matchCargo = !cargo || funcionario.cargo === cargo;
        const matchSearch = !searchTerm || 
          funcionario.nome.toLowerCase().includes(searchTerm) ||
          funcionario.matricula.toLowerCase().includes(searchTerm);

        return matchStatus && matchCargo && matchSearch;
      });

      updateFuncionariosTable(funcionariosFiltrados);
    }

    document.addEventListener('DOMContentLoaded', () => {
      loadSetores();
      loadFuncionarios();

      document.getElementById('statusFilter').addEventListener('change', filtrarFuncionarios);
      document.getElementById('cargoFilter').addEventListener('change', filtrarFuncionarios);
      document.getElementById('searchInput').addEventListener('input', filtrarFuncionarios);
      document.getElementById('funcionarioForm').addEventListener('submit', criarFuncionario);
    });
  </script>
</body>
</html> 