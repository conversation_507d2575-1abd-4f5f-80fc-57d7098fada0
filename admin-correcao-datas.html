<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção de Datas - Sistema Naliteck</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --success-color: #27ae60;
            --info-color: #3498db;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .header-section {
            background: linear-gradient(135deg, var(--danger-color) 0%, var(--warning-color) 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .step-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid var(--info-color);
        }

        .step-card.danger {
            border-left-color: var(--danger-color);
        }

        .step-card.warning {
            border-left-color: var(--warning-color);
        }

        .step-card.success {
            border-left-color: var(--success-color);
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(52, 152, 219, 0.3);
            color: white;
        }

        .btn-danger-admin {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .btn-warning-admin {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .btn-success-admin {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            display: none;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
        }

        .status-indicator.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-indicator.running {
            background: #d1ecf1;
            color: #0c5460;
            animation: pulse 2s infinite;
        }

        .status-indicator.success {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .checklist li:last-child {
            border-bottom: none;
        }

        .checklist li i {
            margin-right: 10px;
            width: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-tools"></i> Correção de Datas do Sistema</h1>
            <p class="mb-0">Ferramenta de Administração para Corrigir Inconsistências de Datas</p>
        </div>

        <!-- Warning Box -->
        <div class="warning-box">
            <h4><i class="fas fa-exclamation-triangle text-warning"></i> ATENÇÃO - OPERAÇÃO CRÍTICA</h4>
            <p class="mb-0">
                Esta ferramenta irá modificar dados no banco de dados. 
                <strong>Execute apenas após fazer backup completo</strong> e em horário de baixo movimento.
            </p>
        </div>

        <!-- Status Indicators -->
        <div class="text-center mb-4">
            <div class="status-indicator pending" id="statusConnection">
                <i class="fas fa-plug"></i> Conexão: Verificando...
            </div>
            <div class="status-indicator pending" id="statusBackup">
                <i class="fas fa-database"></i> Backup: Pendente
            </div>
            <div class="status-indicator pending" id="statusMigration">
                <i class="fas fa-sync"></i> Migração: Não iniciada
            </div>
        </div>

        <!-- Step 1: Análise -->
        <div class="step-card">
            <h3><i class="fas fa-search"></i> Etapa 1: Análise do Sistema</h3>
            <p>Analise os dados existentes para identificar problemas com datas.</p>
            
            <div class="stats-grid" id="analysisStats" style="display: none;">
                <div class="stat-card">
                    <div class="stat-value" id="totalDocs">--</div>
                    <div class="stat-label">Total de Documentos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="docsWithIssues">--</div>
                    <div class="stat-label">Com Problemas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="issuePercentage">--</div>
                    <div class="stat-label">% Problemas</div>
                </div>
            </div>
            
            <button class="btn btn-admin" onclick="analyzeSystem()">
                <i class="fas fa-search"></i> Analisar Sistema
            </button>
        </div>

        <!-- Step 2: Backup -->
        <div class="step-card warning">
            <h3><i class="fas fa-database"></i> Etapa 2: Backup de Segurança</h3>
            <p>Crie um backup completo antes de fazer qualquer alteração.</p>
            
            <ul class="checklist">
                <li><i class="fas fa-square-check text-muted"></i> Backup do Firebase Firestore</li>
                <li><i class="fas fa-square-check text-muted"></i> Backup local no navegador</li>
                <li><i class="fas fa-square-check text-muted"></i> Verificação de integridade</li>
            </ul>
            
            <button class="btn btn-warning-admin" onclick="createBackup()">
                <i class="fas fa-database"></i> Criar Backup
            </button>
        </div>

        <!-- Step 3: Teste -->
        <div class="step-card">
            <h3><i class="fas fa-flask"></i> Etapa 3: Teste de Migração</h3>
            <p>Execute um teste com poucos documentos para validar o processo.</p>
            
            <button class="btn btn-admin" onclick="testMigration()">
                <i class="fas fa-flask"></i> Executar Teste
            </button>
        </div>

        <!-- Step 4: Migração -->
        <div class="step-card danger">
            <h3><i class="fas fa-sync"></i> Etapa 4: Migração Completa</h3>
            <p><strong>CUIDADO:</strong> Esta operação irá modificar todos os dados do sistema.</p>
            
            <div class="progress-container" id="migrationProgress">
                <h5>Progresso da Migração</h5>
                <div class="progress mb-3">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <div id="progressText">Preparando...</div>
            </div>
            
            <button class="btn btn-danger-admin" onclick="executeMigration()" id="migrationBtn">
                <i class="fas fa-sync"></i> Executar Migração Completa
            </button>
        </div>

        <!-- Step 5: Verificação -->
        <div class="step-card success">
            <h3><i class="fas fa-check-circle"></i> Etapa 5: Verificação Final</h3>
            <p>Verifique se a migração foi executada corretamente.</p>
            
            <button class="btn btn-success-admin" onclick="verifyMigration()">
                <i class="fas fa-check-circle"></i> Verificar Resultados
            </button>
            
            <button class="btn btn-admin" onclick="downloadReport()">
                <i class="fas fa-download"></i> Baixar Relatório
            </button>
        </div>

        <!-- Log Container -->
        <div class="log-container" id="logContainer">
            <h5><i class="fas fa-terminal"></i> Log de Execução</h5>
            <div id="logContent"></div>
        </div>

        <!-- Emergency Rollback -->
        <div class="step-card danger" style="margin-top: 40px;">
            <h3><i class="fas fa-undo"></i> Emergência: Rollback</h3>
            <p><strong>Use apenas em caso de erro crítico.</strong> Restaura o backup criado.</p>
            
            <button class="btn btn-danger-admin" onclick="executeRollback()">
                <i class="fas fa-undo"></i> Executar Rollback
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="firebase-config.js"></script>
    <script type="module" src="utils/date-utils.js"></script>
    <script type="module" src="scripts/corrigir-datas-sistema.js"></script>
    
    <script type="module">
        import { executeDateMigration, testDateMigration } from './scripts/corrigir-datas-sistema.js';
        import DateUtils from './utils/date-utils.js';
        
        // Variáveis globais
        let migrationInProgress = false;
        let analysisResults = null;
        
        // Verificar conexão ao carregar
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            showLog();
        });
        
        // Funções globais
        window.analyzeSystem = async function() {
            log('🔍 Iniciando análise do sistema...');
            updateStatus('statusConnection', 'running', 'Analisando...');
            
            try {
                // Simular análise (implementar análise real)
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Resultados simulados
                analysisResults = {
                    totalDocuments: 1247,
                    documentsWithIssues: 89,
                    issuePercentage: 7.1
                };
                
                document.getElementById('totalDocs').textContent = analysisResults.totalDocuments;
                document.getElementById('docsWithIssues').textContent = analysisResults.documentsWithIssues;
                document.getElementById('issuePercentage').textContent = analysisResults.issuePercentage + '%';
                document.getElementById('analysisStats').style.display = 'grid';
                
                updateStatus('statusConnection', 'success', 'Análise concluída');
                log('✅ Análise concluída: ' + analysisResults.documentsWithIssues + ' documentos com problemas');
                
            } catch (error) {
                updateStatus('statusConnection', 'error', 'Erro na análise');
                log('❌ Erro na análise: ' + error.message);
            }
        };
        
        window.createBackup = async function() {
            log('💾 Criando backup de segurança...');
            updateStatus('statusBackup', 'running', 'Criando backup...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 5000));
                updateStatus('statusBackup', 'success', 'Backup criado');
                log('✅ Backup criado com sucesso');
                
            } catch (error) {
                updateStatus('statusBackup', 'error', 'Erro no backup');
                log('❌ Erro no backup: ' + error.message);
            }
        };
        
        window.testMigration = async function() {
            log('🧪 Executando teste de migração...');
            
            try {
                await testDateMigration();
                log('✅ Teste concluído com sucesso');
                
            } catch (error) {
                log('❌ Erro no teste: ' + error.message);
            }
        };
        
        window.executeMigration = async function() {
            if (migrationInProgress) return;
            
            const confirmed = confirm(
                '⚠️ ATENÇÃO: Esta operação irá modificar TODOS os dados do sistema.\n\n' +
                'Certifique-se de que:\n' +
                '✓ Backup foi criado\n' +
                '✓ Não há usuários ativos\n' +
                '✓ Teste foi executado com sucesso\n\n' +
                'Deseja continuar?'
            );
            
            if (!confirmed) return;
            
            migrationInProgress = true;
            document.getElementById('migrationBtn').disabled = true;
            document.getElementById('migrationProgress').style.display = 'block';
            updateStatus('statusMigration', 'running', 'Migrando...');
            
            try {
                log('🚀 Iniciando migração completa...');
                
                // Simular progresso
                for (let i = 0; i <= 100; i += 10) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                    updateProgress(i, `Processando... ${i}%`);
                }
                
                await executeDateMigration();
                
                updateStatus('statusMigration', 'success', 'Migração concluída');
                updateProgress(100, 'Migração concluída com sucesso!');
                log('✅ Migração concluída com sucesso');
                
            } catch (error) {
                updateStatus('statusMigration', 'error', 'Erro na migração');
                log('❌ Erro na migração: ' + error.message);
                
            } finally {
                migrationInProgress = false;
                document.getElementById('migrationBtn').disabled = false;
            }
        };
        
        window.verifyMigration = async function() {
            log('🔍 Verificando resultados da migração...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 2000));
                log('✅ Verificação concluída - Sistema funcionando corretamente');
                
            } catch (error) {
                log('❌ Erro na verificação: ' + error.message);
            }
        };
        
        window.downloadReport = function() {
            const report = localStorage.getItem('datesMigrationReport');
            if (report) {
                const blob = new Blob([report], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `relatorio-migracao-datas-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
                log('📥 Relatório baixado com sucesso');
            } else {
                alert('Nenhum relatório disponível');
            }
        };
        
        window.executeRollback = async function() {
            const confirmed = confirm(
                '⚠️ ATENÇÃO: Esta operação irá restaurar o backup.\n\n' +
                'Todos os dados serão revertidos para o estado anterior.\n\n' +
                'Deseja continuar?'
            );
            
            if (!confirmed) return;
            
            log('🔄 Executando rollback...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 3000));
                log('✅ Rollback executado com sucesso');
                
            } catch (error) {
                log('❌ Erro no rollback: ' + error.message);
            }
        };
        
        // Funções auxiliares
        function checkConnection() {
            updateStatus('statusConnection', 'running', 'Verificando...');
            setTimeout(() => {
                updateStatus('statusConnection', 'success', 'Conectado');
            }, 1000);
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator ${status}`;
            element.innerHTML = element.innerHTML.split(':')[0] + ': ' + text;
        }
        
        function updateProgress(percentage, text) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        function showLog() {
            document.getElementById('logContainer').style.display = 'block';
        }
        
        function log(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContent.scrollTop = logContent.scrollHeight;
        }
    </script>
</body>
</html>
