# ⚡ SISTEMA DE EMPENHOS - WIZHAR ERP

## 🎯 **VISÃO GERAL**

O Sistema de Empenhos é uma evolução do sistema de reservas, proporcionando controle total sobre materiais em produção. É o próximo nível de gestão de estoque para operações de manufatura.

---

## 🔄 **FLUXO COMPLETO: RESERVAS → EMPENHOS → CONSUMO**

### **📋 1. CRIAÇÃO DA OP (Reservas)**
```
OP Criada → Materiais Reservados → Saldo Disponível Reduzido
```
- **Campo:** `saldoReservado` no estoque
- **Status:** Material "separado" virtualmente
- **Reversível:** Sim (cancelar OP)

### **🚀 2. INÍCIO DA PRODUÇÃO (Empenhos)**
```
OP Iniciada → Reservas → Empenhos → Material Comprometido
```
- **Campo:** `saldoEmpenhado` no estoque
- **Status:** Material "comprometido" para produção
- **Reversível:** Difícil (produção em andamento)

### **⚡ 3. APONTAMENTO (Consumo)**
```
Apontamento → Empenhos Consumidos → Saldo Físico Reduzido
```
- **Campo:** `quantidadeConsumida` no empenho
- **Status:** Material fisicamente consumido
- **Reversível:** Não (já consumido)

### **🔓 4. FINALIZAÇÃO (Liberação)**
```
OP Finalizada → Empenhos Restantes Liberados → Saldo Disponível
```
- **Campo:** `status: 'LIBERADO'` no empenho
- **Status:** Material liberado para outras OPs
- **Resultado:** Volta ao estoque disponível

---

## 📊 **ESTRUTURA DE DADOS**

### **🏪 ESTOQUES (Coleção: `estoques`)**
```javascript
{
  produtoId: "produto_123",
  armazemId: "armazem_01",
  saldo: 100,              // Total físico
  saldoReservado: 20,      // Reservado para OPs
  saldoEmpenhado: 15,      // Empenhado em produção
  saldoDisponivel: 65,     // Disponível = saldo - reservado - empenhado
  ultimaMovimentacao: Timestamp
}
```

### **⚡ EMPENHOS (Coleção: `empenhos`)**
```javascript
{
  ordemProducaoId: "OP_123",
  produtoId: "produto_123",
  armazemId: "armazem_01",
  quantidadeEmpenhada: 15,     // Quantidade empenhada
  quantidadeConsumida: 10,     // Já consumida
  quantidadeLiberada: 0,       // Liberada (se cancelada)
  status: "ATIVO",             // ATIVO | CONSUMIDO | LIBERADO
  dataEmpenho: Timestamp,
  ultimoConsumo: Timestamp,
  dataLiberacao: Timestamp,
  motivoLiberacao: "OP_FINALIZADA"
}
```

### **📋 ORDENS DE PRODUÇÃO (Campo adicional)**
```javascript
{
  // ... campos existentes ...
  empenhosAtivos: 3,           // Número de empenhos ativos
  dataInicioProducao: Timestamp
}
```

---

## 🛠️ **SERVIÇOS IMPLEMENTADOS**

### **📁 `services/empenho-service.js`**

#### **🔄 `transferirReservasParaEmpenhos(ordemProducaoId)`**
- **Quando:** OP muda status para "Em Produção"
- **O que faz:** Converte reservas em empenhos
- **Resultado:** Material comprometido para produção

#### **⚡ `consumirMaterialEmpenhado(ordemProducaoId, consumos)`**
- **Quando:** Apontamento de produção
- **O que faz:** Consome materiais empenhados
- **Resultado:** Reduz saldo físico e empenho

#### **🔓 `liberarEmpenhosRestantes(ordemProducaoId, motivo)`**
- **Quando:** OP finalizada ou cancelada
- **O que faz:** Libera empenhos não consumidos
- **Resultado:** Material volta ao estoque disponível

#### **📊 `consultarEmpenhosOP(ordemProducaoId)`**
- **Quando:** Consulta de status
- **O que faz:** Lista empenhos de uma OP
- **Resultado:** Relatório detalhado

#### **🔧 `inicializarCampoEmpenho()`**
- **Quando:** Primeira instalação
- **O que faz:** Adiciona campo `saldoEmpenhado` nos estoques
- **Resultado:** Sistema pronto para usar

---

## 🖥️ **INTERFACES IMPLEMENTADAS**

### **📋 `ordens_producao.html`**
- **Botão:** "🚀 Iniciar Produção" (OPs Pendentes)
- **Integração:** Consumo automático no apontamento
- **Liberação:** Automática ao finalizar/cancelar OP

### **📊 `painel_empenhos.html`**
- **Estatísticas:** Resumo geral dos empenhos
- **Tabelas:** Empenhos ativos e resumo por OP
- **Ações:** Consultar, liberar empenhos

### **🔧 `inicializar_empenhos.html`**
- **Verificação:** Status do sistema
- **Inicialização:** Campo `saldoEmpenhado` nos estoques
- **Guia:** Passo a passo para ativação

---

## 🚀 **COMO USAR**

### **1. 🔧 INICIALIZAÇÃO (Uma vez)**
1. Abrir `inicializar_empenhos.html`
2. Clicar "🔍 Verificar Sistema"
3. Clicar "🚀 Inicializar Empenhos"
4. Aguardar conclusão

### **2. 📋 OPERAÇÃO DIÁRIA**
1. **Criar OP** (reservas automáticas)
2. **Iniciar Produção** (botão "🚀 Iniciar Produção")
3. **Apontar Produção** (consumo automático)
4. **Finalizar OP** (liberação automática)

### **3. 📊 MONITORAMENTO**
1. Abrir `painel_empenhos.html`
2. Visualizar estatísticas
3. Consultar empenhos por OP
4. Liberar empenhos se necessário

---

## 💡 **BENEFÍCIOS**

### **🎯 CONTROLE PRECISO**
- **Rastreabilidade:** Cada material tem histórico completo
- **Visibilidade:** Status em tempo real
- **Prevenção:** Evita conflitos entre OPs

### **📊 GESTÃO AVANÇADA**
- **Planejamento:** Reservas garantem disponibilidade
- **Execução:** Empenhos controlam produção
- **Análise:** Relatórios detalhados de consumo

### **🔄 AUTOMAÇÃO**
- **Transferências:** Reserva → Empenho automático
- **Consumo:** Baixa automática no apontamento
- **Liberação:** Automática ao finalizar OP

---

## ⚠️ **CONSIDERAÇÕES IMPORTANTES**

### **🔄 COMPATIBILIDADE**
- **Sistema atual:** Mantém funcionamento das reservas
- **Migração:** Gradual, sem impacto nas operações
- **Rollback:** Possível desativar se necessário

### **📊 PERFORMANCE**
- **Transações:** Usa Firebase Transactions para consistência
- **Batch:** Operações em lote quando possível
- **Cache:** Dados carregados uma vez por sessão

### **🛡️ SEGURANÇA**
- **Validações:** Quantidade não pode exceder empenhado
- **Logs:** Todas as operações são registradas
- **Rollback:** Possível reverter operações

---

## 🔧 **MANUTENÇÃO**

### **📊 MONITORAMENTO**
- Verificar empenhos órfãos (OP inexistente)
- Validar consistência entre estoque e empenhos
- Limpar empenhos antigos liberados

### **🔄 CORREÇÕES**
- Recalcular saldos se necessário
- Sincronizar empenhos com OPs
- Corrigir inconsistências

---

## 📞 **SUPORTE**

Para dúvidas ou problemas:
1. Verificar logs no console do navegador
2. Usar `painel_empenhos.html` para diagnóstico
3. Consultar este documento
4. Contatar suporte técnico

---

**🎉 Sistema de Empenhos implementado com sucesso!**
**Controle total sobre materiais em produção! 🚀**
