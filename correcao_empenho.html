<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção de Empenho de Materiais</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .material-row {
            cursor: pointer;
        }
        .material-row:hover {
            background-color: #f8f9fa;
        }
        .op-details {
            display: none;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            margin-top: 5px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        .status-pendente {
            background-color: #ffc107;
            color: #000;
        }
        .status-em-producao {
            background-color: #17a2b8;
            color: #fff;
        }
        .status-concluida {
            background-color: #28a745;
            color: #fff;
        }
        .status-cancelada {
            background-color: #dc3545;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h2 class="mt-4 mb-4">Correção de Empenho de Materiais</h2>
        
        <!-- Filtros -->
        <div class="row mb-4">
            <div class="col-md-3">
                <select id="grupoFilter" class="form-select">
                    <option value="">Todos os Grupos</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="familiaFilter" class="form-select">
                    <option value="">Todas as Famílias</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="text" id="searchInput" class="form-control" placeholder="Buscar por código ou descrição">
            </div>
            <div class="col-md-3">
                <button class="btn btn-primary" onclick="carregarDados()">Atualizar</button>
            </div>
        </div>

        <!-- Tabela de Materiais -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Grupo</th>
                        <th>Família</th>
                        <th>Saldo Total</th>
                        <th>Empenhado</th>
                        <th>Necessidade OPs</th>
                        <th>Disponível</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="materiaisTableBody">
                </tbody>
            </table>
        </div>

        <!-- Modal de Detalhes -->
        <div class="modal fade" id="detalhesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detalhes do Empenho</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="detalhesConteudo"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
    <!-- Firebase Firestore -->
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-firestore-compat.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script type="module">
    // ===================================================================
    // CORREÇÃO EMPENHO - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
    // ===================================================================
    import { db } from './firebase-config.js';
    import { collection, getDocs, doc, updateDoc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    window.addEventListener('DOMContentLoaded', function() {
        // Disponibilizar funções globalmente para compatibilidade
        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.query = query;
        window.where = where;

        let produtos = [];
        let estoques = [];
        let ordensProducao = [];
        let grupos = [];
        let familias = [];
        let armazens = [];

        async function carregarDados() {
            try {
                // Carregar dados básicos
                const [produtosSnap, estoquesSnap, opsSnap, gruposSnap, familiasSnap, armazensSnap] = await Promise.all([
                    db.collection("produtos").get(),
                    db.collection("estoques").get(),
                    db.collection("ordensProducao").where("status", "in", ["Pendente", "Em Produção"]).get(),
                    db.collection("grupos").get(),
                    db.collection("familias").get(),
                    db.collection("armazens").get()
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Preencher filtros
                preencherFiltros();
                
                // Renderizar tabela
                renderizarTabela();
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados: " + error.message);
            }
        }
        window.carregarDados = carregarDados;

        function preencherFiltros() {
            const grupoFilter = document.getElementById('grupoFilter');
            const familiaFilter = document.getElementById('familiaFilter');

            // Limpar opções existentes
            grupoFilter.innerHTML = '<option value="">Todos os Grupos</option>';
            familiaFilter.innerHTML = '<option value="">Todas as Famílias</option>';

            // Preencher grupos
            grupos.forEach(grupo => {
                grupoFilter.innerHTML += `<option value="${grupo.codigoGrupo}">${grupo.nomeGrupo}</option>`;
            });

            // Preencher famílias
            familias.forEach(familia => {
                familiaFilter.innerHTML += `<option value="${familia.codigoFamilia}">${familia.nomeFamilia}</option>`;
            });
        }

        function renderizarTabela() {
            const tbody = document.getElementById('materiaisTableBody');
            tbody.innerHTML = '';

            const grupoFilter = document.getElementById('grupoFilter').value;
            const familiaFilter = document.getElementById('familiaFilter').value;
            const searchText = document.getElementById('searchInput').value.toLowerCase();

            // Filtrar produtos
            const produtosFiltrados = produtos.filter(produto => {
                const matchGrupo = !grupoFilter || produto.grupo === grupoFilter;
                const matchFamilia = !familiaFilter || produto.familia === familiaFilter;
                const matchSearch = !searchText || 
                    produto.codigo.toLowerCase().includes(searchText) || 
                    produto.descricao.toLowerCase().includes(searchText);
                return matchGrupo && matchFamilia && matchSearch;
            });

            // Agrupar estoques por produto
            produtosFiltrados.forEach(produto => {
                const estoquesProduto = estoques.filter(e => e.produtoId === produto.id);
                const saldoTotal = estoquesProduto.reduce((sum, e) => sum + (e.saldo || 0), 0);
                const empenhado = estoquesProduto.reduce((sum, e) => sum + (e.saldoReservado || 0), 0);
                const disponivel = saldoTotal - empenhado;

                // Encontrar OPs que empenharam este material
                const opsEmpenho = ordensProducao.filter(op => 
                    op.materiaisNecessarios?.some(m => m.produtoId === produto.id)
                );

                // Soma das necessidades nas OPs pendentes
                const necessidadeOPs = opsEmpenho.reduce((sum, op) => {
                    const material = op.materiaisNecessarios.find(m => m.produtoId === produto.id);
                    return sum + (material ? material.quantidade : 0);
                }, 0);

                if (empenhado > 0 || opsEmpenho.length > 0) {
                    const row = document.createElement('tr');
                    row.className = 'material-row';
                    row.onclick = () => mostrarDetalhes(produto, opsEmpenho, necessidadeOPs);
                    
                    row.innerHTML = `
                        <td>${produto.codigo}</td>
                        <td>${produto.descricao}</td>
                        <td>${grupos.find(g => g.codigoGrupo === produto.grupo)?.nomeGrupo || produto.grupo}</td>
                        <td>${familias.find(f => f.codigoFamilia === produto.familia)?.nomeFamilia || produto.familia}</td>
                        <td>${saldoTotal.toFixed(3)}</td>
                        <td>${empenhado.toFixed(3)}</td>
                        <td>${necessidadeOPs.toFixed(3)}</td>
                        <td>${disponivel.toFixed(3)}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); corrigirEmpenho('${produto.id}')">
                                Corrigir
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                }
            });
        }

        function mostrarDetalhes(produto, opsEmpenho, necessidadeOPs) {
            const modal = new bootstrap.Modal(document.getElementById('detalhesModal'));
            const conteudo = document.getElementById('detalhesConteudo');
            
            let html = `
                <h4>${produto.codigo} - ${produto.descricao}</h4>
                <p><strong>Necessidade total nas OPs pendentes:</strong> ${necessidadeOPs.toFixed(3)}</p>
                <h5 class="mt-4">Ordens de Produção com Empenho</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>OP</th>
                                <th>Status</th>
                                <th>Quantidade</th>
                                <th>Empenhado</th>
                                <th>Data</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            opsEmpenho.forEach(op => {
                const material = op.materiaisNecessarios.find(m => m.produtoId === produto.id);
                if (material) {
                    html += `
                        <tr>
                            <td>${op.numero}</td>
                            <td><span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span></td>
                            <td>${material.quantidade.toFixed(3)}</td>
                            <td>${material.quantidadeReservada?.toFixed(3) || '0.000'}</td>
                            <td>${new Date(op.dataCriacao.seconds * 1000).toLocaleDateString()}</td>
                        </tr>
                    `;
                }
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            conteudo.innerHTML = html;
            modal.show();
        }

        async function corrigirEmpenho(produtoId) {
            if (!confirm('Deseja corrigir o empenho deste material? Isso irá recalcular o empenho com base nas OPs pendentes.')) {
                return;
            }

            try {
                // Encontrar o produto selecionado
                const produto = produtos.find(p => p.id === produtoId);
                console.log('Produto exibido:', produto);
                console.log('Todos estoques:', estoques);

                const estoquesProduto = estoques.filter(e => e.produtoId === produtoId);
                console.log('Estoques encontrados para produtoId', produtoId, ':', estoquesProduto);

                const opsPendentes = ordensProducao.filter(op => 
                    op.status === 'Pendente' && 
                    op.materiaisNecessarios?.some(m => m.produtoId === produtoId)
                );

                // Calcular novo empenho
                const novoEmpenho = {};
                opsPendentes.forEach(op => {
                    const material = op.materiaisNecessarios.find(m => m.produtoId === produtoId);
                    if (material) {
                        const armazemId = op.armazemProducaoId;
                        novoEmpenho[armazemId] = (novoEmpenho[armazemId] || 0) + material.quantidade;
                    }
                });

                // Log de depuração: estoques e valores a serem atualizados
                const debugInfo = estoquesProduto.map(e => ({
                    estoqueId: e.id,
                    armazemId: e.armazemId,
                    empenhoCorrigido: novoEmpenho[e.armazemId] || 0
                }));
                console.log('Empenho corrigido para cada estoque:', debugInfo);

                if (estoquesProduto.length === 0) {
                    alert('Nenhum estoque encontrado para este produto. Verifique se o produto possui estoque cadastrado.');
                    return;
                }

                // Atualizar estoques
                const batch = db.batch();
                let algumAtualizado = false;
                for (const estoque of estoquesProduto) {
                    const empenhoCorrigido = novoEmpenho[estoque.armazemId] || 0;
                    batch.update(db.collection("estoques").doc(estoque.id), {
                        saldoReservado: empenhoCorrigido,
                        ultimaMovimentacao: firebase.firestore.FieldValue.serverTimestamp()
                    });
                    algumAtualizado = true;
                }

                if (!algumAtualizado) {
                    alert('Nenhum estoque foi atualizado. Verifique se há correspondência de armazém entre as OPs e os estoques.');
                    return;
                }

                await batch.commit();
                alert('Empenho corrigido com sucesso!');
                await carregarDados();
            } catch (error) {
                console.error("Erro ao corrigir empenho:", error);
                alert("Erro ao corrigir empenho: " + error.message);
            }
        }
        window.corrigirEmpenho = corrigirEmpenho;

        // Event Listeners
        document.getElementById('grupoFilter').addEventListener('change', renderizarTabela);
        document.getElementById('familiaFilter').addEventListener('change', renderizarTabela);
        document.getElementById('searchInput').addEventListener('input', renderizarTabela);

        // Carregar dados iniciais
        carregarDados();
    });
    </script>
</body>
</html> 