import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc,
    getDocs,
    updateDoc,
    addDoc,
    query,
    where,
    Timestamp,
    orderBy,
    limit 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class CRMService {
    static async getCustomerProfile(customerId) {
        try {
            const customerDoc = await getDoc(doc(db, "clientes", customerId));
            if (!customerDoc.exists()) {
                throw new Error("Customer not found");
            }

            const customer = customerDoc.data();
            
            // Get recent interactions
            const interactions = await this.getCustomerInteractions(customerId);
            
            // Get purchase history
            const orders = await this.getCustomerOrders(customerId);
            
            // Get customer preferences
            const preferences = await this.getCustomerPreferences(customerId);
            
            // Generate recommendations
            const recommendations = await this.generateRecommendations(customerId, orders);

            return {
                profile: customer,
                interactions,
                orders,
                preferences,
                recommendations,
                metrics: {
                    totalOrders: orders.length,
                    totalSpent: orders.reduce((sum, order) => sum + order.valorTotal, 0),
                    averageOrderValue: orders.length > 0 
                        ? orders.reduce((sum, order) => sum + order.valorTotal, 0) / orders.length 
                        : 0,
                    lastPurchase: orders.length > 0 
                        ? orders[0].dataCriacao 
                        : null
                }
            };
        } catch (error) {
            console.error("Error getting customer profile:", error);
            throw error;
        }
    }

    static async getCustomerInteractions(customerId) {
        const interactionsQuery = query(
            collection(db, "interacoesCliente"),
            where("clienteId", "==", customerId),
            orderBy("dataHora", "desc"),
            limit(50)
        );
        const interactionsSnap = await getDocs(interactionsQuery);
        return interactionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }

    static async getCustomerOrders(customerId) {
        const ordersQuery = query(
            collection(db, "pedidosVenda"),
            where("clienteId", "==", customerId),
            orderBy("dataCriacao", "desc")
        );
        const ordersSnap = await getDocs(ordersQuery);
        return ordersSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }

    static async getCustomerPreferences(customerId) {
        const preferencesDoc = await getDoc(doc(db, "preferenciasCliente", customerId));
        return preferencesDoc.exists() ? preferencesDoc.data() : {};
    }

    static async generateRecommendations(customerId, orders) {
        try {
            // Get product categories from past orders
            const categories = new Set();
            const products = new Set();
            orders.forEach(order => {
                order.itens.forEach(item => {
                    products.add(item.produtoId);
                });
            });

            // Get product details
            const productDocs = await Promise.all(
                Array.from(products).map(id => getDoc(doc(db, "produtos", id)))
            );
            productDocs.forEach(doc => {
                if (doc.exists()) {
                    const product = doc.data();
                    if (product.categoria) {
                        categories.add(product.categoria);
                    }
                }
            });

            // Find similar products in the same categories
            const recommendationsQuery = query(
                collection(db, "produtos"),
                where("categoria", "in", Array.from(categories)),
                where("ativo", "==", true),
                limit(10)
            );
            const recommendationsSnap = await getDocs(recommendationsQuery);
            
            // Filter out products already purchased
            const recommendations = recommendationsSnap.docs
                .map(doc => ({ id: doc.id, ...doc.data() }))
                .filter(product => !products.has(product.id));

            // Sort by relevance (you can implement your own sorting logic)
            recommendations.sort((a, b) => {
                // Example: sort by price similarity to average purchase
                const avgOrderValue = orders.reduce((sum, order) => sum + order.valorTotal, 0) / orders.length;
                return Math.abs(a.precoVenda - avgOrderValue) - Math.abs(b.precoVenda - avgOrderValue);
            });

            return recommendations.slice(0, 5); // Return top 5 recommendations
        } catch (error) {
            console.error("Error generating recommendations:", error);
            return [];
        }
    }

    static async recordInteraction(customerId, type, details) {
        try {
            const interaction = {
                clienteId: customerId,
                tipo: type,
                detalhes: details,
                dataHora: Timestamp.now()
            };

            const interactionRef = await addDoc(collection(db, "interacoesCliente"), interaction);

            // Update last interaction in customer profile
            await updateDoc(doc(db, "clientes", customerId), {
                ultimaInteracao: {
                    tipo: type,
                    dataHora: interaction.dataHora
                }
            });

            return interactionRef.id;
        } catch (error) {
            console.error("Error recording interaction:", error);
            throw error;
        }
    }

    static async updateCustomerPreferences(customerId, preferences) {
        try {
            const preferencesRef = doc(db, "preferenciasCliente", customerId);
            const preferencesDoc = await getDoc(preferencesRef);

            if (preferencesDoc.exists()) {
                await updateDoc(preferencesRef, {
                    ...preferences,
                    ultimaAtualizacao: Timestamp.now()
                });
            } else {
                await addDoc(preferencesRef, {
                    clienteId: customerId,
                    ...preferences,
                    dataCriacao: Timestamp.now(),
                    ultimaAtualizacao: Timestamp.now()
                });
            }

            // Record the preference update as an interaction
            await this.recordInteraction(customerId, 'ATUALIZACAO_PREFERENCIAS', {
                preferencias: preferences
            });

            return true;
        } catch (error) {
            console.error("Error updating customer preferences:", error);
            throw error;
        }
    }

    static async getCustomerSegments() {
        try {
            const segmentsSnap = await getDocs(collection(db, "segmentosCliente"));
            return segmentsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        } catch (error) {
            console.error("Error getting customer segments:", error);
            throw error;
        }
    }

    static async assignCustomerToSegment(customerId, segmentId) {
        try {
            await updateDoc(doc(db, "clientes", customerId), {
                segmentoId: segmentId,
                dataAtualizacaoSegmento: Timestamp.now()
            });

            await this.recordInteraction(customerId, 'ATUALIZACAO_SEGMENTO', {
                segmentoId: segmentId
            });

            return true;
        } catch (error) {
            console.error("Error assigning customer to segment:", error);
            throw error;
        }
    }
} 