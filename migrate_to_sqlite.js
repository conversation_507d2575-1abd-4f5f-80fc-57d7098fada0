
// ===================================================================
// MIGRATE TO SQLITE - CONFIGURAÇÃO CENTRALIZADA DO FIREBASE
// ===================================================================
// Para scripts Node.js, mantemos a configuração local por compatibilidade
// ===================================================================

const sqlite3 = require('sqlite3').verbose();
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs } = require('firebase/firestore');

// Configuração Firebase centralizada (copiada do firebase-config.js)
const firebaseConfig = {
  apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
  authDomain: "banco-mrp.firebaseapp.com",
  projectId: "banco-mrp",
  storageBucket: "banco-mrp.firebasestorage.app",
  messagingSenderId: "740147152218",
  appId: "1:740147152218:web:2d301340bf314e68d75f63",
  measurementId: "G-YNNQ1VX1EH"
};

const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);
const db = new sqlite3.Database('./database.sqlite');

async function migrateCollection(collectionName, tableName) {
  console.log(`Migrando ${collectionName}...`);
  const snapshot = await getDocs(collection(firestore, collectionName));
  const data = snapshot.docs.map(doc => ({id: doc.id, ...doc.data()}));
  
  if (data.length > 0) {
    const columns = Object.keys(data[0]).filter(key => key !== 'id');
    const placeholders = columns.map(() => '?').join(',');
    
    const stmt = db.prepare(`INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${placeholders})`);
    
    data.forEach(item => {
      const values = columns.map(col => item[col]);
      stmt.run(values);
    });
    
    stmt.finalize();
  }
  
  console.log(`${data.length} registros migrados de ${collectionName}`);
}

async function migrateTables() {
  try {
    console.log('Iniciando migração...');
    
    // Lista de coleções para migrar
    const collections = [
      ['usuarios', 'usuarios'],
      ['produtos', 'produtos'],
      ['estruturas', 'estruturas'],
      ['operacoes', 'operacoes'],
      ['recursos', 'recursos'],
      ['ordens_producao', 'ordens_producao'],
      ['movimentacoes', 'movimentacoes']
    ];
    
    for (const [collection, table] of collections) {
      await migrateCollection(collection, table);
    }
    
    console.log('Migração concluída com sucesso!');
    db.close();
  } catch (error) {
    console.error('Erro durante a migração:', error);
    db.close();
  }
}

migrateTables();
