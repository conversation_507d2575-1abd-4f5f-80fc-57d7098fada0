<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Mostrar Contadores Atuais - Firebase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            padding: 30px;
        }

        .action-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            display: none;
        }

        .results.show {
            display: block;
        }

        .counter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .counter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }

        .counter-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .counter-card.exists {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .counter-card.not-exists {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .counter-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .counter-info {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #6c757d;
            display: inline-block;
            width: 120px;
        }

        .info-value {
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .raw-data {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .raw-data h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .raw-data pre {
            background: #2c3e50;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .summary {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #2196f3;
        }

        .summary h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .stat-item {
            display: inline-block;
            margin-right: 30px;
            margin-bottom: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #1976d2;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .copy-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
        }

        .copy-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Mostrar Contadores Atuais</h1>
            <p>Visualize o status dos contadores no Firebase</p>
        </div>

        <div class="main-content">
            <button class="action-button" onclick="carregarContadores()">
                🔍 Carregar Contadores do Firebase
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Carregando dados do Firebase...</p>
            </div>

            <div class="results" id="results">
                <!-- Resumo -->
                <div class="summary" id="summary"></div>

                <!-- Grid de Contadores -->
                <div class="counter-grid" id="counterGrid"></div>

                <!-- Dados Brutos -->
                <div class="raw-data">
                    <h3>📋 Dados Brutos (JSON)</h3>
                    <button class="copy-button" onclick="copiarDados()">📋 Copiar JSON</button>
                    <pre id="rawDataPre"></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        // ===================================================================
        // MOSTRAR CONTADORES - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            getDoc 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Dados globais
        let dadosContadores = {};

        // ===================================================================
        // FUNÇÃO PRINCIPAL
        // ===================================================================

        window.carregarContadores = async function() {
            try {
                mostrarLoading();
                
                // Lista de contadores esperados
                const tiposContadores = [
                    'solicitacoes',
                    'cotacoes', 
                    'pedidosCompra',
                    'ordensProducao',
                    'transferencias',
                    'pedidosVenda',
                    'notasFiscais',
                    'ordens' // Contador antigo que pode existir
                ];

                dadosContadores = {};
                
                // Verificar cada contador
                for (const tipo of tiposContadores) {
                    try {
                        const contadorRef = doc(db, "contadores", tipo);
                        const contadorDoc = await getDoc(contadorRef);
                        
                        if (contadorDoc.exists()) {
                            dadosContadores[tipo] = {
                                exists: true,
                                data: contadorDoc.data(),
                                id: contadorDoc.id
                            };
                        } else {
                            dadosContadores[tipo] = {
                                exists: false,
                                data: null,
                                id: tipo
                            };
                        }
                    } catch (error) {
                        dadosContadores[tipo] = {
                            exists: false,
                            error: error.message,
                            id: tipo
                        };
                    }
                }

                // Também verificar se há outros contadores não listados
                try {
                    const contadoresSnap = await getDocs(collection(db, "contadores"));
                    contadoresSnap.forEach((doc) => {
                        if (!dadosContadores[doc.id]) {
                            dadosContadores[doc.id] = {
                                exists: true,
                                data: doc.data(),
                                id: doc.id,
                                unexpected: true // Contador não esperado
                            };
                        }
                    });
                } catch (error) {
                    console.warn('Erro ao buscar contadores adicionais:', error);
                }

                exibirResultados();
                esconderLoading();

            } catch (error) {
                console.error('Erro ao carregar contadores:', error);
                alert('Erro ao carregar contadores: ' + error.message);
                esconderLoading();
            }
        };

        // ===================================================================
        // EXIBIÇÃO DOS RESULTADOS
        // ===================================================================

        function exibirResultados() {
            exibirResumo();
            exibirContadores();
            exibirDadosBrutos();
            
            document.getElementById('results').classList.add('show');
        }

        function exibirResumo() {
            const summary = document.getElementById('summary');
            
            let totalContadores = 0;
            let contadoresExistentes = 0;
            let contadoresNaoEsperados = 0;
            let totalDocumentosGerados = 0;

            for (const [tipo, info] of Object.entries(dadosContadores)) {
                totalContadores++;
                if (info.exists) {
                    contadoresExistentes++;
                    if (info.data && info.data.totalGenerated) {
                        totalDocumentosGerados += info.data.totalGenerated;
                    }
                    if (info.data && info.data.valor) {
                        totalDocumentosGerados += info.data.valor;
                    }
                }
                if (info.unexpected) {
                    contadoresNaoEsperados++;
                }
            }

            summary.innerHTML = `
                <h3>📊 Resumo dos Contadores</h3>
                <div class="stat-item">
                    <div class="stat-number">${totalContadores}</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${contadoresExistentes}</div>
                    <div class="stat-label">Existentes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${totalContadores - contadoresExistentes}</div>
                    <div class="stat-label">Não Criados</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${totalDocumentosGerados}</div>
                    <div class="stat-label">Docs Gerados</div>
                </div>
                ${contadoresNaoEsperados > 0 ? `
                <div class="alert alert-warning">
                    ⚠️ Encontrados ${contadoresNaoEsperados} contadores não esperados
                </div>
                ` : ''}
            `;
        }

        function exibirContadores() {
            const grid = document.getElementById('counterGrid');
            grid.innerHTML = '';

            for (const [tipo, info] of Object.entries(dadosContadores)) {
                const card = document.createElement('div');
                card.className = `counter-card ${info.exists ? 'exists' : 'not-exists'}`;
                
                let conteudo = `
                    <div class="counter-title">
                        ${info.exists ? '✅' : '❌'} ${tipo}
                        ${info.unexpected ? ' (Não Esperado)' : ''}
                    </div>
                `;

                if (info.exists && info.data) {
                    const data = info.data;
                    conteudo += `
                        <div class="counter-info">
                            <span class="info-label">Status:</span>
                            <span class="info-value">EXISTE</span>
                        </div>
                    `;

                    // Mostrar campos disponíveis
                    if (data.sequence !== undefined) {
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Sequência:</span>
                                <span class="info-value">${data.sequence}</span>
                            </div>
                        `;
                    }

                    if (data.valor !== undefined) {
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Valor:</span>
                                <span class="info-value">${data.valor}</span>
                            </div>
                        `;
                    }

                    if (data.yearMonth) {
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Período:</span>
                                <span class="info-value">${data.yearMonth}</span>
                            </div>
                        `;
                    }

                    if (data.documentType) {
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Tipo:</span>
                                <span class="info-value">${data.documentType}</span>
                            </div>
                        `;
                    }

                    if (data.totalGenerated !== undefined) {
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Total Gerado:</span>
                                <span class="info-value">${data.totalGenerated}</span>
                            </div>
                        `;
                    }

                    if (data.lastGenerated) {
                        const lastDate = new Date(data.lastGenerated.seconds * 1000);
                        conteudo += `
                            <div class="counter-info">
                                <span class="info-label">Último:</span>
                                <span class="info-value">${lastDate.toLocaleString('pt-BR')}</span>
                            </div>
                        `;
                    }

                } else if (info.error) {
                    conteudo += `
                        <div class="counter-info">
                            <span class="info-label">Status:</span>
                            <span class="info-value">ERRO</span>
                        </div>
                        <div class="counter-info">
                            <span class="info-label">Erro:</span>
                            <span class="info-value">${info.error}</span>
                        </div>
                    `;
                } else {
                    conteudo += `
                        <div class="counter-info">
                            <span class="info-label">Status:</span>
                            <span class="info-value">NÃO EXISTE</span>
                        </div>
                    `;
                }

                card.innerHTML = conteudo;
                grid.appendChild(card);
            }
        }

        function exibirDadosBrutos() {
            const pre = document.getElementById('rawDataPre');
            pre.textContent = JSON.stringify(dadosContadores, null, 2);
        }

        // ===================================================================
        // FUNÇÕES AUXILIARES
        // ===================================================================

        function mostrarLoading() {
            document.getElementById('loading').classList.add('show');
            document.getElementById('results').classList.remove('show');
        }

        function esconderLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        window.copiarDados = function() {
            const dados = JSON.stringify(dadosContadores, null, 2);
            navigator.clipboard.writeText(dados).then(() => {
                alert('📋 Dados copiados para a área de transferência!');
            }).catch(err => {
                console.error('Erro ao copiar:', err);
                alert('❌ Erro ao copiar dados');
            });
        };

        // ===================================================================
        // INICIALIZAÇÃO
        // ===================================================================

        console.log('📊 Visualizador de Contadores carregado');
        
        // Mostrar alerta inicial
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info';
        alertDiv.innerHTML = '💡 <strong>Instruções:</strong> Clique no botão acima para carregar e visualizar todos os contadores do Firebase.';
        document.querySelector('.main-content').insertBefore(alertDiv, document.querySelector('.action-button').nextSibling);
    </script>
</body>
</html>
