<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cópia de Estrutura</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #333;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .form-section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #333;
    }

    input, select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    button {
      padding: 10px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }

    button:hover {
      background-color: #0056b3;
    }

    .back-button {
      background-color: #6c757d;
    }

    .back-button:hover {
      background-color: #5a6268;
    }

    .preview-section {
      margin-top: 30px;
      padding: 20px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .preview-title {
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
      color: #333;
    }

    .structure-tree {
      margin-left: 20px;
    }

    .component-item {
      margin: 10px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-left: 3px solid #007bff;
      border-radius: 0 4px 4px 0;
    }

    .operations-list {
      margin-left: 20px;
      font-size: 0.9em;
      color: #666;
    }

    .operation-item {
      margin: 5px 0;
      padding: 5px;
      background-color: #fff;
      border: 1px solid #eee;
      border-radius: 4px;
    }

    .alert {
      padding: 15px;
      margin-bottom: 20px;
      border: 1px solid transparent;
      border-radius: 4px;
    }

    .alert-success {
      color: #155724;
      background-color: #d4edda;
      border-color: #c3e6cb;
    }

    .alert-warning {
      color: #856404;
      background-color: #fff3cd;
      border-color: #ffeeba;
    }

    .alert-danger {
      color: #721c24;
      background-color: #f8d7da;
      border-color: #f5c6cb;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://www.naliteck.com.br/img/logo.png" alt="Logo" class="logo">
      <div>
        <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
      </div>
    </div>

    <div class="form-section">
      <h2>Cópia de Estrutura</h2>
      
      <div class="form-group">
        <label>Produto de Origem:</label>
        <select id="sourceProduct" onchange="loadSourceStructure()">
          <option value="">Selecione o produto...</option>
        </select>
      </div>

      <div id="sourcePreview" class="preview-section" style="display: none;">
        <h3 class="preview-title">Estrutura Original</h3>
        <div id="sourceStructureContent"></div>
      </div>

      <div id="targetSection" class="form-group" style="display: none;">
        <label>Produto de Destino:</label>
        <select id="targetProduct" onchange="checkTargetProduct()">
          <option value="">Selecione o produto...</option>
        </select>
        <div id="targetWarning" class="alert alert-warning" style="display: none; margin-top: 10px;"></div>

        <button onclick="copyStructure()" id="copyButton" style="display: none;">Copiar Estrutura</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      query,
      where 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let operacoes = [];
    let recursos = [];
    let selectedStructure = null;

    window.onload = async function() {
      await loadInitialData();
      updateProductSelect();
    };

    async function loadInitialData() {
      try {
        const [produtosSnap, estruturasSnap, operacoesSnap, recursosSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "operacoes")),
          getDocs(collection(db, "recursos"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function updateProductSelect() {
      const select = document.getElementById('sourceProduct');
      const targetSelect = document.getElementById('targetProduct');
      select.innerHTML = '<option value="">Selecione o produto...</option>';
      targetSelect.innerHTML = '<option value="">Selecione o produto...</option>';
      
      produtos
        .filter(p => p.tipo === 'PA' || p.tipo === 'SP')
        .forEach(produto => {
          select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
          targetSelect.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
        });
    }

    window.loadSourceStructure = async function() {
      const produtoId = document.getElementById('sourceProduct').value;
      if (!produtoId) {
        document.getElementById('sourcePreview').style.display = 'none';
        document.getElementById('targetSection').style.display = 'none';
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!estrutura) {
        alert('Este produto não possui estrutura cadastrada.');
        return;
      }

      selectedStructure = estrutura;
      
      // Exibir preview da estrutura
      const preview = document.getElementById('sourceStructureContent');
      preview.innerHTML = await generateStructurePreview(produto, estrutura);
      
      document.getElementById('sourcePreview').style.display = 'block';
      document.getElementById('targetSection').style.display = 'block';
    };

    async function generateStructurePreview(produto, estrutura, level = 0) {
      let html = `
        <div class="component-item" style="margin-left: ${level * 20}px">
          <strong>${produto.codigo} - ${produto.descricao}</strong>
          <div>Tipo: ${produto.tipo} | Unidade: ${produto.unidade}</div>
      `;

      if (estrutura.operacoes && estrutura.operacoes.length > 0) {
        html += '<div class="operations-list">';
        for (const op of estrutura.operacoes) {
          const operacao = operacoes.find(o => o.id === op.operacaoId);
          const recurso = recursos.find(r => r.id === op.recursoId);
          if (operacao && recurso) {
            html += `
              <div class="operation-item">
                ${op.sequencia}. ${operacao.operacao} - 
                Recurso: ${recurso.codigo} - ${recurso.maquina} |
                Tempo: ${op.tempo} min
              </div>
            `;
          }
        }
        html += '</div>';
      }

      if (estrutura.componentes && estrutura.componentes.length > 0) {
        for (const comp of estrutura.componentes) {
          const componenteProduto = produtos.find(p => p.id === comp.componentId);
          const componenteEstrutura = estruturas.find(e => e.produtoPaiId === comp.componentId);
          
          html += `
            <div style="margin-left: ${(level + 1) * 20}px">
              <div>→ ${componenteProduto.codigo} - ${componenteProduto.descricao}</div>
              <div>Quantidade: ${comp.quantidade} ${comp.unidade}</div>
          `;

          if (componenteEstrutura) {
            html += await generateStructurePreview(componenteProduto, componenteEstrutura, level + 1);
          }

          html += '</div>';
        }
      }

      html += '</div>';
      return html;
    }

    window.checkTargetProduct = async function() {
      const targetId = document.getElementById('targetProduct').value;
      if (!targetId) {
        document.getElementById('copyButton').style.display = 'none';
        document.getElementById('targetWarning').style.display = 'none';
        return;
      }

      // Verificar se é o mesmo produto de origem
      const sourceId = document.getElementById('sourceProduct').value;
      if (targetId === sourceId) {
        document.getElementById('targetWarning').innerHTML = 'O produto de destino não pode ser o mesmo que o de origem.';
        document.getElementById('targetWarning').style.display = 'block';
        document.getElementById('copyButton').style.display = 'none';
        return;
      }

      // Verificar se já possui estrutura
      const existingStructure = estruturas.find(e => e.produtoPaiId === targetId);
      if (existingStructure) {
        document.getElementById('targetWarning').innerHTML = 'ATENÇÃO: Este produto já possui uma estrutura cadastrada. A estrutura existente será substituída.';
        document.getElementById('targetWarning').style.display = 'block';
      } else {
        document.getElementById('targetWarning').style.display = 'none';
      }
      
      document.getElementById('copyButton').style.display = 'block';
    };

    window.copyStructure = async function() {
      const targetId = document.getElementById('targetProduct').value;
      const sourceId = document.getElementById('sourceProduct').value;

      if (!targetId || !sourceId) {
        alert('Por favor, selecione os produtos de origem e destino.');
        return;
      }

      if (targetId === sourceId) {
        alert('O produto de destino não pode ser o mesmo que o de origem.');
        return;
      }

      // Verificar se já possui estrutura
      const existingStructure = estruturas.find(e => e.produtoPaiId === targetId);
      if (existingStructure) {
        if (!confirm('Este produto já possui uma estrutura cadastrada. Deseja substituí-la?')) {
          return;
        }
      }

      try {
        // Copiar estrutura
        const novaEstrutura = {
          ...selectedStructure,
          produtoPaiId: targetId,
          dataCadastro: new Date()
        };
        delete novaEstrutura.id;

        // Se existir estrutura antiga, excluir
        if (existingStructure) {
          await deleteDoc(doc(db, "estruturas", existingStructure.id));
        }

        await addDoc(collection(db, "estruturas"), novaEstrutura);

        alert('Estrutura copiada com sucesso!');
        window.location.reload();
      } catch (error) {
        console.error("Erro ao copiar estrutura:", error);
        alert("Erro ao copiar estrutura.");
      }
    };
  </script>
</body>
</html>