<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importação Rápida de Produtos</title>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --danger-color: #bb0000;
            --warning-color: #e9730c;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .page-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .form-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .section-header {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .field-selection {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .field-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .field-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .field-checkbox label {
            font-size: 14px;
            color: var(--text-color);
        }

        .required-field {
            color: var(--danger-color);
            margin-left: 4px;
        }

        textarea {
            width: 100%;
            min-height: 200px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }

        .preview-table th,
        .preview-table td {
            border: 1px solid var(--border-color);
            padding: 8px;
            text-align: left;
        }

        .preview-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
        }

        .preview-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .error-row {
            background-color: #ffeaea !important;
        }

        .warning-row {
            background-color: #fff3e5 !important;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: opacity 0.3s ease;
        }

        .notification-success {
            background-color: var(--success-color);
        }

        .notification-error {
            background-color: var(--danger-color);
        }

        .notification-warning {
            background-color: var(--warning-color);
            color: #000;
        }

        .notification-info {
            background-color: var(--primary-color);
        }

        .help-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 10px;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-color);
            cursor: pointer;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .form-group {
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

        .form-group > label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border: 1px solid var(--border-color);
        }

        .results-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .results-table tr:hover {
            background-color: #f8f9fa;
        }

        .results-table td {
            font-size: 14px;
        }

        #resultados {
            margin-top: 20px;
        }

        #resultados button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>Importação Rápida de Produtos</h1>
            <button class="btn-secondary" onclick="window.location.href='cadastro_produto.html'">Voltar</button>
        </div>

        <div class="form-section">
            <div class="section-header">Selecione os Campos</div>
            <div class="form-grid">
                <div class="form-group">
                    <label>Dados Básicos</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="codigo" checked onchange="atualizarPreviewProdutos()"> Código</label>
                        <label><input type="checkbox" value="descricao" checked onchange="atualizarPreviewProdutos()"> Descrição</label>
                        <label><input type="checkbox" value="tipo" checked onchange="atualizarPreviewProdutos()"> Tipo</label>
                        <label><input type="checkbox" value="unidade" checked onchange="atualizarPreviewProdutos()"> Unidade Principal</label>
                        <label><input type="checkbox" value="unidadeSecundaria" onchange="atualizarPreviewProdutos()"> Unidade Secundária</label>
                        <label><input type="checkbox" value="fatorConversao" onchange="atualizarPreviewProdutos()"> Fator de Conversão</label>
                        <label><input type="checkbox" value="status" onchange="atualizarPreviewProdutos()"> Status do Produto</label>
                        <label><input type="checkbox" value="grupo" onchange="atualizarPreviewProdutos()"> Grupo</label>
                        <label><input type="checkbox" value="familia" onchange="atualizarPreviewProdutos()"> Família</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Dados Fiscais</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="ncm" onchange="atualizarPreviewProdutos()"> NCM</label>
                        <label><input type="checkbox" value="cest" onchange="atualizarPreviewProdutos()"> CEST</label>
                        <label><input type="checkbox" value="origem" onchange="atualizarPreviewProdutos()"> Origem</label>
                        <label><input type="checkbox" value="tipoItem" onchange="atualizarPreviewProdutos()"> Tipo Item</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Dados de Custos</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="custoMedio" onchange="atualizarPreviewProdutos()"> Custo Médio</label>
                        <label><input type="checkbox" value="ultimoCusto" onchange="atualizarPreviewProdutos()"> Último Custo</label>
                        <label><input type="checkbox" value="precoVenda" onchange="atualizarPreviewProdutos()"> Preço de Venda</label>
                        <label><input type="checkbox" value="margemLucro" onchange="atualizarPreviewProdutos()"> Margem de Lucro (%)</label>
                        <label><input type="checkbox" value="metodoCusteio" onchange="atualizarPreviewProdutos()"> Método de Custeio</label>
                        <label><input type="checkbox" value="centroCustoObrigatorio" onchange="atualizarPreviewProdutos()"> Centro de Custos Obrigatório</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Dados de Estoque</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="estoqueMinimo" onchange="atualizarPreviewProdutos()"> Estoque Mínimo</label>
                        <label><input type="checkbox" value="estoqueMaximo" onchange="atualizarPreviewProdutos()"> Estoque Máximo</label>
                        <label><input type="checkbox" value="pontoPedido" onchange="atualizarPreviewProdutos()"> Ponto de Pedido</label>
                        <label><input type="checkbox" value="loteCompra" onchange="atualizarPreviewProdutos()"> Lote de Compra</label>
                        <label><input type="checkbox" value="rastreabilidadeLote" onchange="atualizarPreviewProdutos()"> Rastreabilidade por Lote</label>
                        <label><input type="checkbox" value="inspecaoRecebimento" onchange="atualizarPreviewProdutos()"> Inspeção no Recebimento</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Dados de Endereçamento</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="armazemPadraoId" onchange="atualizarPreviewProdutos()"> Armazém Padrão</label>
                        <label><input type="checkbox" value="corredor" onchange="atualizarPreviewProdutos()"> Corredor</label>
                        <label><input type="checkbox" value="prateleira" onchange="atualizarPreviewProdutos()"> Prateleira</label>
                        <label><input type="checkbox" value="posicao" onchange="atualizarPreviewProdutos()"> Posição</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-section">
            <div class="section-header">Preview dos Produtos Existentes</div>
            <div class="help-text">
                Abaixo estão os 5 primeiros produtos cadastrados no sistema, mostrando o formato dos campos selecionados.
            </div>
            <button class="btn-secondary" style="margin-bottom:10px;" onclick="exportarPreviewProdutosParaExcel()">Exportar para Excel</button>
            <div id="previewProdutosExistentes">
                <table class="preview-table">
                    <thead>
                        <tr id="previewHeaderExistentes"></tr>
                    </thead>
                    <tbody id="previewBodyExistentes"></tbody>
                </table>
            </div>
        </div>

        <div class="form-section">
            <div class="section-header">Dados para Importação</div>
            <div class="help-text">
                Cole aqui os dados copiados do Excel. Os dados devem estar organizados em colunas, separados por tabulação.
            </div>
            <textarea id="importData" placeholder="Cole aqui os dados do Excel..."></textarea>
            <button class="btn-secondary" style="margin-left:10px; margin-top:5px;" onclick="limparDadosColados()">Limpar dados colados</button>
            <button onclick="processarDados()">Preview</button>
        </div>

        <div class="form-section">
            <div class="section-header">Preview dos Dados</div>
            <div id="previewContainer">
                <table class="preview-table">
                    <thead>
                        <tr id="previewHeader"></tr>
                    </thead>
                    <tbody id="previewBody"></tbody>
                </table>
                <button id="btnExcluirVermelhos" class="btn-secondary" style="margin-top:10px;" onclick="excluirItensVermelhos()">Excluir itens vermelhos</button>
            </div>
        </div>

        <div class="button-group">
            <button id="btnProcessarImportacao" class="btn-primary" onclick="processarImportacao()">Processar Importação</button>
            <button class="btn-secondary" onclick="limparDados()">Limpar</button>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs,
            query,
            where,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtosExistentes = [];
        let dadosProcessados = [];

        // Carregar produtos existentes ao iniciar
        async function carregarProdutosExistentes() {
            try {
                const produtosSnapshot = await getDocs(query(collection(db, "produtos"), limit(1000)));
                produtosExistentes = produtosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                atualizarPreviewProdutos();
            } catch (error) {
                console.error("Erro ao carregar produtos:", error);
                showNotification("Erro ao carregar produtos existentes", "error");
            }
        }

        // Atualizar preview dos produtos existentes
        window.atualizarPreviewProdutos = function() {
            const camposSelecionados = Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            const headerRow = document.getElementById('previewHeaderExistentes');
            const bodyContainer = document.getElementById('previewBodyExistentes');

            // Limpar conteúdo atual
            headerRow.innerHTML = '';
            bodyContainer.innerHTML = '';

            // Adicionar cabeçalho
            camposSelecionados.forEach(campo => {
                const th = document.createElement('th');
                th.textContent = campo.charAt(0).toUpperCase() + campo.slice(1);
                headerRow.appendChild(th);
            });

            // Filtrar produtos que tenham todos os campos selecionados preenchidos
            const produtosFiltrados = produtosExistentes.filter(produto =>
                camposSelecionados.every(campo =>
                    produto[campo] !== undefined &&
                    produto[campo] !== null &&
                    String(produto[campo]).trim() !== ''
                )
            ).slice(0, 5); // Pega só os 5 primeiros

            // Adicionar linhas com dados
            produtosFiltrados.forEach(produto => {
                const row = document.createElement('tr');
                camposSelecionados.forEach(campo => {
                    const td = document.createElement('td');
                    let valor = produto[campo];

                    // Formatar o valor baseado no tipo do campo
                    if (typeof valor === 'boolean') {
                        valor = valor ? 'Sim' : 'Não';
                    } else if (typeof valor === 'number') {
                        valor = valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                    } else if (valor instanceof Date) {
                        valor = valor.toLocaleDateString('pt-BR');
                    }

                    td.textContent = valor ?? '';
                    row.appendChild(td);
                });
                bodyContainer.appendChild(row);
            });
        };

        // Processar dados colados
        window.processarDados = function() {
            const dados = document.getElementById('importData').value.trim();
            if (!dados) {
                showNotification('Por favor, cole os dados da tabela.', 'error');
                return;
            }

            // Obter campos selecionados
            const camposSelecionados = Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            // Validar campos obrigatórios
            const camposObrigatorios = ['codigo', 'descricao', 'tipo', 'unidade'];
            const camposFaltantes = camposObrigatorios.filter(campo => !camposSelecionados.includes(campo));

            if (camposFaltantes.length > 0) {
                showNotification(`Os campos ${camposFaltantes.join(', ')} são obrigatórios.`, 'error');
                return;
            }

            // Processar linhas
            const linhas = dados.split('\n').filter(linha => linha.trim());
            dadosProcessados = [];
            for (let i = 0; i < linhas.length; i++) {
                const valores = linhas[i].split('\t');
                const produto = {};
                camposSelecionados.forEach((campo, index) => {
                    if (valores[index]) {
                        switch (campo) {
                            case 'tipo':
                                produto[campo] = valores[index].toUpperCase();
                                break;
                            case 'unidade':
                            case 'unidadeSecundaria':
                                produto[campo] = valores[index].toUpperCase();
                                break;
                            case 'status':
                                produto[campo] = valores[index].toLowerCase() === 'ativo' ? 'ativo' : 'inativo';
                                break;
                            case 'rastreabilidadeLote':
                            case 'centroCustoObrigatorio':
                                produto[campo] = valores[index].toLowerCase() === 'sim';
                                break;
                            case 'custoMedio':
                            case 'ultimoCusto':
                            case 'precoVenda':
                            case 'margemLucro':
                            case 'estoqueMinimo':
                            case 'estoqueMaximo':
                            case 'pontoPedido':
                            case 'loteCompra':
                            case 'fatorConversao':
                                produto[campo] = parseFloat(valores[index].replace(',', '.')) || 0;
                                break;
                            default:
                                produto[campo] = valores[index];
                        }
                    }
                });
                // Adicionar data de cadastro
                produto.dataCadastro = new Date();
                dadosProcessados.push(produto);
            }
            atualizarPreviewDadosColados(camposSelecionados);
            atualizarEstadoBotaoImportacao();
        };

        // Processar importação
        window.processarImportacao = async function() {
            if (dadosProcessados.length === 0) {
                showNotification("Não há dados para importar", "warning");
                return;
            }

            const erros = [];
            const avisos = [];
            const sucessos = [];

            for (const linha of dadosProcessados) {
                try {
                    // Verificar campos obrigatórios
                    const camposObrigatorios = ['codigo', 'descricao', 'tipo', 'unidade'];
                    const camposFaltantes = camposObrigatorios.filter(campo => !linha[campo]);

                    if (camposFaltantes.length > 0) {
                        erros.push(`Linha ${linha.codigo}: Campos obrigatórios faltando: ${camposFaltantes.join(', ')}`);
                        continue;
                    }

                    // Verificar duplicidade
                    const produtoExistente = produtosExistentes.find(p => p.codigo === linha.codigo);
                    if (produtoExistente) {
                        avisos.push(`Produto ${linha.codigo} já existe no sistema`);
                        continue;
                    }

                    // Preparar dados do produto
                    const produtoData = {
                        codigo: linha.codigo,
                        descricao: linha.descricao,
                        tipo: linha.tipo,
                        unidade: linha.unidade,
                        grupo: linha.grupo || null,
                        familia: linha.familia || null,
                        ncm: linha.ncm || null,
                        cest: linha.cest || null,
                        origem: linha.origem || '0',
                        tipoItem: linha.tipoItem || '00',
                        status: 'ativo',
                        dataCadastro: new Date()
                    };

                    // Salvar no Firestore
                    await addDoc(collection(db, "produtos"), produtoData);
                    sucessos.push(`Produto ${linha.codigo} importado com sucesso`);

                } catch (error) {
                    console.error("Erro ao importar produto:", error);
                    erros.push(`Erro ao importar produto ${linha.codigo}: ${error.message}`);
                }
            }

            // Mostrar resultados
            if (erros.length > 0) {
                showNotification(`${erros.length} erros encontrados durante a importação`, "error");
            }
            if (avisos.length > 0) {
                showNotification(`${avisos.length} avisos durante a importação`, "warning");
            }
            if (sucessos.length > 0) {
                showNotification(`${sucessos.length} produtos importados com sucesso`, "success");
            }

            // Atualizar lista de produtos existentes
            await carregarProdutosExistentes();
        };

        // Limpar dados
        window.limparDados = function() {
            document.getElementById('importData').value = '';
            document.getElementById('previewHeader').innerHTML = '';
            document.getElementById('previewBody').innerHTML = '';
            dadosProcessados = [];
        };

        // Função para limpar os dados colados
        window.limparDadosColados = function() {
            document.getElementById('importData').value = '';
            document.getElementById('previewHeader').innerHTML = '';
            document.getElementById('previewBody').innerHTML = '';
            dadosProcessados = [];
            atualizarEstadoBotaoImportacao();
        };

        // Função para mostrar notificações
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                <span>${message}</span>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Adicionar função para exportar preview para Excel (CSV)
        window.exportarPreviewProdutosParaExcel = function() {
            const camposSelecionados = Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked'))
                .map(cb => cb.value);
            if (camposSelecionados.length === 0) {
                showNotification('Selecione ao menos um campo para exportar.', 'warning');
                return;
            }
            const nomesAmigaveis = {
                codigo: 'Código',
                descricao: 'Descrição',
                tipo: 'Tipo',
                unidade: 'Unidade',
                unidadeSecundaria: 'Unidade Secundária',
                fatorConversao: 'Fator de Conversão',
                status: 'Status do Produto',
                grupo: 'Grupo',
                familia: 'Família',
                ncm: 'NCM',
                cest: 'CEST',
                origem: 'Origem',
                tipoItem: 'Tipo Item',
                custoMedio: 'Custo Médio',
                ultimoCusto: 'Último Custo',
                precoVenda: 'Preço de Venda',
                margemLucro: 'Margem de Lucro (%)',
                metodoCusteio: 'Método de Custeio',
                centroCustoObrigatorio: 'Centro de Custos Obrigatório',
                estoqueMinimo: 'Estoque Mínimo',
                estoqueMaximo: 'Estoque Máximo',
                pontoPedido: 'Ponto de Pedido',
                loteCompra: 'Lote de Compra',
                rastreabilidadeLote: 'Rastreabilidade por Lote',
                inspecaoRecebimento: 'Inspeção no Recebimento',
                armazemPadraoId: 'Armazém Padrão',
                corredor: 'Corredor',
                prateleira: 'Prateleira',
                posicao: 'Posição',
            };
            const produtosFiltrados = produtosExistentes.filter(produto =>
                camposSelecionados.every(campo =>
                    produto[campo] !== undefined &&
                    produto[campo] !== null &&
                    String(produto[campo]).trim() !== ''
                )
            ).slice(0, 50);
            if (produtosFiltrados.length === 0) {
                showNotification('Nenhum produto para exportar.', 'warning');
                return;
            }
            // Montar os dados para o Excel
            const data = [
                camposSelecionados.map(c => nomesAmigaveis[c] || c) // Cabeçalho
            ];
            produtosFiltrados.forEach(produto => {
                const row = camposSelecionados.map(campo => {
                    let valor = produto[campo];
                    if (typeof valor === 'boolean') {
                        valor = valor ? 'Sim' : 'Não';
                    } else if (typeof valor === 'number') {
                        valor = valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                    } else if (valor instanceof Date) {
                        valor = valor.toLocaleDateString('pt-BR');
                    }
                    return valor ?? '';
                });
                data.push(row);
            });
            // Criar a planilha e exportar
            const ws = XLSX.utils.aoa_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Produtos');
            XLSX.writeFile(wb, 'produtos_preview.xls', { bookType: 'xls' });
        };

        // Função para excluir itens vermelhos do preview dos dados colados
        window.excluirItensVermelhos = function() {
            // Remove do array dadosProcessados todos os itens cujo código já existe
            const camposSelecionados = Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked')).map(cb => cb.value);
            dadosProcessados = dadosProcessados.filter(produto => {
                return !produtosExistentes.find(p => p.codigo === produto.codigo);
            });
            // Atualiza o preview
            atualizarPreviewDadosColados(camposSelecionados);
            // Atualiza o estado do botão de importação
            atualizarEstadoBotaoImportacao();
        };

        // Função para atualizar o preview dos dados colados (usada após exclusão)
        function atualizarPreviewDadosColados(camposSelecionados) {
            const previewHeader = document.getElementById('previewHeader');
            const previewBody = document.getElementById('previewBody');
            previewHeader.innerHTML = '';
            previewBody.innerHTML = '';
            // Cabeçalho
            camposSelecionados.forEach(campo => {
                const th = document.createElement('th');
                th.textContent = campo.charAt(0).toUpperCase() + campo.slice(1);
                previewHeader.appendChild(th);
            });
            // Corpo
            dadosProcessados.forEach(produto => {
                const row = document.createElement('tr');
                // Verificar se o código já existe
                const codigoExiste = produtosExistentes.find(p => p.codigo === produto.codigo);
                if (codigoExiste) {
                    row.classList.add('error-row');
                }
                camposSelecionados.forEach(campo => {
                    const td = document.createElement('td');
                    td.textContent = produto[campo] !== undefined ? produto[campo] : '';
                    row.appendChild(td);
                });
                previewBody.appendChild(row);
            });
        }

        // Função para atualizar o estado do botão de importação
        function atualizarEstadoBotaoImportacao() {
            const btn = document.getElementById('btnProcessarImportacao');
            const existeVermelho = dadosProcessados.some(produto => produtosExistentes.find(p => p.codigo === produto.codigo));
            btn.disabled = existeVermelho;
        }

        // Inicializar
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarProdutosExistentes();

            // Adicionar evento para processar dados quando colados
            document.getElementById('importData').addEventListener('paste', function() {
                setTimeout(processarDados, 100);
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
</body>
</html> 