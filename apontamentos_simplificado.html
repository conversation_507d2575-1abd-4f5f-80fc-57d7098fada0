<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apontamento de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      padding: 20px;
    }
    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .nav-buttons {
      display: flex;
      gap: 10px;
    }
    .nav-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }
    .nav-btn:hover {
      background: linear-gradient(135deg, #20c997, #17a2b8);
      transform: translateY(-2px);
    }
    .nav-btn.secondary {
      background: linear-gradient(135deg, #fd7e14, #e55a00);
    }
    .nav-btn.danger {
      background: linear-gradient(135deg, #dc3545, #c82333);
    }
    .search-bar {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 25px;
    }
    .form-col {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #495057;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    input, select {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      margin-bottom: 8px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    input:focus, select:focus {
      outline: none;
      border-color: #0854a0;
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-1px);
    }
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      background: var(--primary-color);
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
    }
    button:hover {
      background: var(--primary-hover);
    }
    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      font-size: 13px;
    }
    .orders-table th, .orders-table td {
      padding: 10px 8px;
      border: 1px solid var(--border-color);
      text-align: left;
    }
    .orders-table th {
      background: var(--secondary-color);
      font-weight: 600;
      color: var(--primary-color);
    }
    .orders-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .orders-table tr:hover {
      background-color: #e6f2ff;
    }
    .status-badge {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-pendente { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-em-producao { background: #e5f2f9; color: #0854a0; border: 1px solid #0854a0; }
    .status-concluida { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-cancelada { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .metric-card {
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      color: white;
      font-weight: bold;
    }
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 1000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    }
    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }
    .notification.success { background: #28a745; }
    .notification.error { background: #dc3545; }
    .notification.warning { background: #ffc107; color: #212529; }
    .notification.info { background: #17a2b8; }

    /* Estilos do Modal de Bloqueio */
    .modal {
      position: fixed;
      z-index: 10000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.6);
      display: flex;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(3px);
    }

    .modal-content {
      background-color: #fefefe;
      border-radius: 12px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
      animation: modalSlideIn 0.3s ease-out;
      border: none;
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .close:hover {
      background: rgba(255,255,255,0.2) !important;
      transform: scale(1.1);
    }

    /* Estilos dos botões da tabela */
    .orders-table button {
      padding: 8px 12px !important;
      border: none !important;
      border-radius: 6px !important;
      color: white !important;
      cursor: pointer !important;
      font-size: 12px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;
      margin-right: 5px !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 5px !important;
    }

    .orders-table button:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
      filter: brightness(1.1) !important;
    }

    .orders-table button:active {
      transform: translateY(0) !important;
    }

    /* Responsividade do Modal */
    @media (max-width: 768px) {
      .modal-content {
        max-width: 95% !important;
        margin: 10px;
      }

      .modal-content table {
        font-size: 12px;
      }

      .modal-content th,
      .modal-content td {
        padding: 8px 4px !important;
      }

      .orders-table button {
        padding: 6px 8px !important;
        font-size: 11px !important;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏭 Apontamento de Produção</h1>
      <div class="nav-buttons">
        <a href="analise_producao.html" class="nav-btn">
          <i class="fas fa-chart-line"></i> Análise de Produção
        </a>
        <a href="gestao_estoque.html" class="nav-btn secondary">
          <i class="fas fa-boxes"></i> Gestão de Estoque
        </a>
        <button onclick="forcarAtualizacao()" class="nav-btn danger">
          <i class="fas fa-sync-alt"></i> Atualizar
        </button>
        <button onclick="verificarColecoes()" class="nav-btn" style="background: linear-gradient(135deg, #6f42c1, #5a32a3);">
          <i class="fas fa-database"></i> Verificar BD
        </button>
      </div>
    </div>

    <!-- Métricas do Dashboard -->
    <div class="metrics-grid">
      <div class="metric-card" style="background: linear-gradient(135deg, #0854a0, #0a4d8c);">
        <div style="font-size: 24px; font-weight: bold;" id="totalOPs">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Total de OPs</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #107e3e, #0d6e36);">
        <div style="font-size: 24px; font-weight: bold;" id="emProducao">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Em Produção</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #e9730c, #d66a0b);">
        <div style="font-size: 24px; font-weight: bold;" id="materiaisFalta">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Materiais em Falta</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #bb0000, #a30000);">
        <div style="font-size: 24px; font-weight: bold;" id="opsAtrasadas">0</div>
        <div style="font-size: 12px; opacity: 0.9;">OPs Atrasadas</div>
      </div>
    </div>

    <!-- Barra de Pesquisa -->
    <div class="search-bar">
      <div class="form-row">
        <div class="form-col">
          <input type="text" id="searchInput" placeholder="Buscar por número da ordem ou produto..." oninput="filterOrders()">
        </div>
        <div class="form-col">
          <select id="statusFilter" onchange="filterOrders()">
            <option value="">Todos os status</option>
            <option value="Pendente">Pendente</option>
            <option value="Em Produção">Em Produção</option>
            <option value="Concluída">Concluída</option>
            <option value="Cancelada">Cancelada</option>
          </select>
        </div>
        <div class="form-col">
          <select id="prioridadeFilter" onchange="filterOrders()">
            <option value="">Todas as prioridades</option>
            <option value="Alta">Alta</option>
            <option value="Média">Média</option>
            <option value="Baixa">Baixa</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Tabela de Ordens -->
    <div id="ordersContainer">
      <table class="orders-table" id="ordersTable">
        <thead>
          <tr>
            <th>Número</th>
            <th>Produto</th>
            <th>Quantidade</th>
            <th>Status</th>
            <th>Prioridade</th>
            <th>Data Entrega</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="ordersTableBody">
          <!-- Ordens serão carregadas aqui -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Modal de Apontamento Bloqueado -->
  <div id="modalApontamentoBloqueado" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
      <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
        <div style="display: flex; align-items: center; gap: 15px;">
          <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
          </div>
          <div>
            <h2 style="margin: 0; font-size: 20px;">⚠️ Apontamento Bloqueado - Materiais em Falta</h2>
            <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">Não é possível realizar o apontamento</p>
          </div>
        </div>
        <span class="close" onclick="fecharModalBloqueio()" style="background: rgba(255,255,255,0.1); border: none; color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; cursor: pointer; transition: all 0.3s ease;">&times;</span>
      </div>

      <div style="padding: 25px;">
        <!-- Alerta Principal -->
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
            <i class="fas fa-ban" style="color: #721c24; font-size: 18px;"></i>
            <h4 style="color: #721c24; margin: 0;">Não é possível realizar o apontamento</h4>
          </div>
          <p style="color: #721c24; margin: 0; line-height: 1.5;">
            Os materiais abaixo não possuem saldo suficiente no armazém de produção. É necessário transferir os materiais
            do almoxarifado para o armazém de produção antes de realizar o apontamento.
          </p>
        </div>

        <!-- Informações da OP -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h4 style="color: #495057; margin: 0 0 15px 0; display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-clipboard-list"></i> Ordem de Produção: <span id="opNumeroModal">-</span>
          </h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
              <strong>Produto:</strong> <span id="opProdutoModal">-</span>
            </div>
            <div>
              <strong>Armazém de Produção:</strong> <span id="opArmazemModal">-</span>
            </div>
          </div>
        </div>

        <!-- Tabela de Materiais em Falta -->
        <div style="background: white; border-radius: 8px; border: 1px solid #dee2e6;">
          <div style="background: #dc3545; color: white; padding: 15px; border-radius: 8px 8px 0 0;">
            <h4 style="margin: 0; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-circle"></i> Materiais em Falta:
            </h4>
          </div>

          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f8f9fa;">
                  <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; font-weight: 600;">Código</th>
                  <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; font-weight: 600;">Descrição</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; font-weight: 600;">Tipo</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; font-weight: 600;">Necessário</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; font-weight: 600;">Disponível</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; font-weight: 600;">Falta</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; font-weight: 600;">Unidade</th>
                </tr>
              </thead>
              <tbody id="tabelaMateriaisFalta">
                <!-- Materiais serão inseridos aqui -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Ações -->
        <div style="margin-top: 25px; text-align: right; display: flex; gap: 10px; justify-content: flex-end;">
          <button onclick="abrirGestaoTransferencias()" style="background: #17a2b8; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
            <i class="fas fa-exchange-alt"></i> Gerenciar Transferências
          </button>
          <button onclick="abrirGestaoEstoque()" style="background: #fd7e14; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
            <i class="fas fa-boxes"></i> Gestão de Estoque
          </button>
          <button onclick="fecharModalBloqueio()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
            Fechar
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Área de impressão (oculta) -->
  <div id="printArea" style="display: none;"></div>

  <!-- Modal de Apontamento de Produção -->
  <div id="modalApontamento" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
      <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
        <div style="display: flex; align-items: center; gap: 15px;">
          <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <i class="fas fa-clipboard-check" style="font-size: 24px;"></i>
          </div>
          <div>
            <h2 style="margin: 0; font-size: 20px;">📋 Apontamento de Produção</h2>
            <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">Registrar produção realizada</p>
          </div>
        </div>
        <span class="close" onclick="fecharModalApontamento()" style="background: rgba(255,255,255,0.1); border: none; color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; cursor: pointer; transition: all 0.3s ease;">&times;</span>
      </div>

      <div style="padding: 25px;">
        <!-- Informações da OP -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h4 style="color: #495057; margin: 0 0 15px 0; display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-clipboard-list"></i> Ordem de Produção: <span id="opNumeroApontamento">-</span>
          </h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
              <strong>Produto:</strong> <span id="opProdutoApontamento">-</span>
            </div>
            <div>
              <strong>Quantidade Total:</strong> <span id="opQuantidadeApontamento">-</span>
            </div>
            <div>
              <strong>Já Produzido:</strong> <span id="opJaProduzidoApontamento">-</span>
            </div>
            <div>
              <strong>Restante:</strong> <span id="opRestanteApontamento">-</span>
            </div>
          </div>
        </div>

        <!-- Lista de Materiais Necessários -->
        <div id="materiaisNecessariosApontamento" style="background: #fff; border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 25px;">
          <div style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #dee2e6;">
            <h4 style="margin: 0; color: #495057; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-list-ul"></i> Materiais Necessários
            </h4>
          </div>
          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead style="background: #f8f9fa;">
                <tr>
                  <th style="padding: 12px 8px; text-align: left; border-bottom: 1px solid #dee2e6;">Código</th>
                  <th style="padding: 12px 8px; text-align: left; border-bottom: 1px solid #dee2e6;">Descrição</th>
                  <th style="padding: 12px 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Necessário</th>
                  <th style="padding: 12px 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Disponível</th>
                  <th style="padding: 12px 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Status</th>
                </tr>
              </thead>
              <tbody id="tabelaMateriaisApontamento">
                <!-- Preenchido dinamicamente -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Formulário de Apontamento -->
        <form id="formApontamento" onsubmit="confirmarApontamento(event)">
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div>
              <label for="quantidadeProduzida" style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
                <i class="fas fa-check-circle"></i> Quantidade Produzida *
              </label>
              <input type="number" id="quantidadeProduzida" min="0.001" step="0.001" required
                     style="width: 100%; padding: 12px; border: 1px solid #ced4da; border-radius: 6px; font-size: 14px;"
                     placeholder="Digite a quantidade produzida">
            </div>
            <div>
              <label for="quantidadeRefugo" style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
                <i class="fas fa-times-circle"></i> Quantidade de Refugo
              </label>
              <input type="number" id="quantidadeRefugo" min="0" step="0.001" value="0"
                     style="width: 100%; padding: 12px; border: 1px solid #ced4da; border-radius: 6px; font-size: 14px;"
                     placeholder="Digite a quantidade de refugo">
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="observacoesApontamento" style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
              <i class="fas fa-comment"></i> Observações
            </label>
            <textarea id="observacoesApontamento" rows="3"
                      style="width: 100%; padding: 12px; border: 1px solid #ced4da; border-radius: 6px; font-size: 14px; resize: vertical;"
                      placeholder="Digite observações sobre a produção (opcional)"></textarea>
          </div>
        </form>

        <!-- Botões de Ação -->
        <div style="display: flex; gap: 15px; justify-content: flex-end; border-top: 1px solid #dee2e6; padding-top: 20px;">
          <button onclick="fecharModalApontamento()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
            <i class="fas fa-times"></i> Cancelar
          </button>
          <button type="submit" form="formApontamento" id="btnConfirmarApontamento" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
            <i class="fas fa-check"></i> Confirmar Apontamento
          </button>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    // Importar configurações do Firebase
    import { getFirestore, collection, getDocs, doc, updateDoc, addDoc, query, where, orderBy } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
    import { db, firebaseConfig } from './firebase-config.js';

    // Firebase já inicializado no firebase-config.js
    // const app = initializeApp(firebaseConfig);
    // const db = getFirestore(app);

    // Variáveis globais
    let ordensProducao = [];
    let produtos = [];
    let estoques = [];
    let armazens = [];
    let carregandoDados = false;

    // Função para mostrar notificação
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 3000) {
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.textContent = mensagem;
      document.body.appendChild(notification);

      setTimeout(() => notification.classList.add('show'), 100);
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, duracao);
    }

    // Função para forçar atualização
    window.forcarAtualizacao = function() {
      if (carregandoDados) {
        mostrarNotificacao('⏳ Carregamento já em andamento...', 'warning', 2000);
        return;
      }
      mostrarNotificacao('🔄 Atualizando dados...', 'info', 2000);
      carregarDadosSeguro();
    };

    // Carregar dados do Firebase
    async function carregarDados() {
      try {
        const startTime = Date.now();
        console.log('🔄 Carregando dados...');

        // Mostrar indicador de carregamento
        mostrarNotificacao('🔄 Carregando dados...', 'info', 1000);

        // Carregar dados em paralelo
        const [opsSnapshot, produtosSnapshot, estoquesSnapshot, armazensSnapshot] = await Promise.all([
          getDocs(collection(db, 'ordensProducao')),
          getDocs(collection(db, 'produtos')),
          getDocs(collection(db, 'estoques')),
          getDocs(collection(db, 'armazens'))
        ]);

        // Processar dados
        ordensProducao = opsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        const loadTime = Date.now() - startTime;
        console.log(`✅ Dados carregados em ${loadTime}ms: ${ordensProducao.length} OPs, ${produtos.length} produtos, ${estoques.length} estoques`);

        // Atualizar interface
        atualizarDashboard();
        renderizarOrdens();

        // Feedback de sucesso
        if (ordensProducao.length === 0) {
          mostrarNotificacao('ℹ️ Nenhuma OP encontrada', 'warning', 3000);
        } else {
          mostrarNotificacao(`✅ ${ordensProducao.length} OPs carregadas`, 'success', 2000);
        }

      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        mostrarNotificacao('❌ Erro ao carregar dados: ' + error.message, 'error', 5000);
      }
    }

    // Atualizar métricas do dashboard
    function atualizarDashboard() {
      const totalOPs = ordensProducao.length;
      const emProducao = ordensProducao.filter(op => op.status === 'Em Produção').length;
      const hoje = new Date();
      const opsAtrasadas = ordensProducao.filter(op => {
        if (!op.dataEntrega?.seconds) return false;
        const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
        return dataEntrega < hoje && op.status !== 'Concluída';
      }).length;

      // Calcular materiais em falta (simplificado)
      let materiaisEmFalta = 0;
      ordensProducao.forEach(ordem => {
        if (ordem.materiaisNecessarios) {
          ordem.materiaisNecessarios.forEach(material => {
            const estoque = estoques.find(e => 
              e.produtoId === material.produtoId && 
              e.armazemId === ordem.armazemProducaoId
            ) || { saldo: 0 };
            
            if (estoque.saldo < material.quantidade) {
              materiaisEmFalta++;
            }
          });
        }
      });

      // Atualizar elementos
      document.getElementById('totalOPs').textContent = totalOPs;
      document.getElementById('emProducao').textContent = emProducao;
      document.getElementById('materiaisFalta').textContent = materiaisEmFalta;
      document.getElementById('opsAtrasadas').textContent = opsAtrasadas;
    }

    // Renderizar tabela de ordens
    function renderizarOrdens() {
      const tbody = document.getElementById('ordersTableBody');
      tbody.innerHTML = '';

      if (ordensProducao.length === 0) {
        tbody.innerHTML = `
          <tr>
            <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
              <div style="font-size: 48px; margin-bottom: 15px;">📋</div>
              <h4 style="margin-bottom: 10px;">Nenhuma Ordem de Produção encontrada</h4>
              <p style="margin: 0;">Verifique se existem OPs cadastradas na coleção 'ordensProducao'</p>
              <button onclick="forcarAtualizacao()" style="margin-top: 15px; background: var(--primary-color); color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                <i class="fas fa-sync-alt"></i> Tentar Novamente
              </button>
            </td>
          </tr>
        `;
        return;
      }

      ordensProducao.forEach(ordem => {
        const produto = produtos.find(p => p.id === ordem.produtoId) || {};
        const row = document.createElement('tr');
        
        const dataEntrega = ordem.dataEntrega?.seconds 
          ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString()
          : 'N/A';

        row.innerHTML = `
          <td>${ordem.numero || ordem.id}</td>
          <td>
            <div style="font-weight: bold;">${produto.codigo || 'N/A'}</div>
            <div style="font-size: 12px; color: #666;">${produto.descricao || 'N/A'}</div>
          </td>
          <td>${ordem.quantidade} ${produto.unidade || 'UN'}</td>
          <td><span class="status-badge status-${ordem.status?.toLowerCase().replace(' ', '-')}">${ordem.status || 'N/A'}</span></td>
          <td>${ordem.prioridade || 'N/A'}</td>
          <td>${dataEntrega}</td>
          <td>
            <button onclick="apontarProducao('${ordem.id}')" style="background: #28a745; margin-right: 5px;">
              <i class="fas fa-clipboard-check"></i> Apontar
            </button>
            <button onclick="iniciarProducao('${ordem.id}')" style="background: #0854a0;">
              <i class="fas fa-play-circle"></i> Iniciar Produção
            </button>
          </td>
        `;
        
        tbody.appendChild(row);
      });
    }

    // Filtrar ordens
    window.filterOrders = function() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;
      const prioridadeFilter = document.getElementById('prioridadeFilter').value;

      const rows = document.querySelectorAll('#ordersTableBody tr');
      
      rows.forEach(row => {
        const numero = row.cells[0].textContent.toLowerCase();
        const produto = row.cells[1].textContent.toLowerCase();
        const status = row.cells[3].textContent;
        const prioridade = row.cells[4].textContent;

        const matchSearch = numero.includes(searchTerm) || produto.includes(searchTerm);
        const matchStatus = !statusFilter || status.includes(statusFilter);
        const matchPrioridade = !prioridadeFilter || prioridade.includes(prioridadeFilter);

        row.style.display = matchSearch && matchStatus && matchPrioridade ? '' : 'none';
      });
    };

    // Função para verificar coleções do banco
    window.verificarColecoes = async function() {
      try {
        mostrarNotificacao('🔍 Verificando coleções...', 'info', 2000);

        const colecoes = [
          'ordensProducao',
          'produtos',
          'estoques',
          'armazens',
          'estruturas'
        ];

        let relatorio = '📊 RELATÓRIO DE COLEÇÕES:\n\n';

        for (const colecao of colecoes) {
          try {
            const snapshot = await getDocs(collection(db, colecao));
            const count = snapshot.docs.length;
            relatorio += `✅ ${colecao}: ${count} documentos\n`;
          } catch (error) {
            relatorio += `❌ ${colecao}: Erro - ${error.message}\n`;
          }
        }

        relatorio += '\n💡 Se alguma coleção tem 0 documentos, verifique se os dados foram importados corretamente.';

        alert(relatorio);

      } catch (error) {
        console.error('❌ Erro ao verificar coleções:', error);
        mostrarNotificacao('❌ Erro ao verificar banco de dados', 'error', 3000);
      }
    };

    // Função para validar estoque antes do apontamento
    async function validarEstoqueParaApontamento(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        if (!ordem) {
          throw new Error('Ordem de produção não encontrada');
        }

        const materiaisInsuficientes = [];

        // Verificar cada material necessário
        if (ordem.materiaisNecessarios && ordem.materiaisNecessarios.length > 0) {
          for (const material of ordem.materiaisNecessarios) {
            const produto = produtos.find(p => p.id === material.produtoId) || {};

            // Buscar estoque no armazém de produção
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === ordem.armazemProducaoId
            ) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };

            const saldoDisponivel = Math.max(0,
              (estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0)
            );

            if (saldoDisponivel < material.quantidade) {
              materiaisInsuficientes.push({
                produtoId: material.produtoId,
                codigo: produto.codigo || material.produtoId,
                descricao: produto.descricao || 'Produto não encontrado',
                tipo: produto.tipo || 'MP',
                necessario: material.quantidade,
                disponivel: saldoDisponivel,
                falta: material.quantidade - saldoDisponivel,
                unidade: produto.unidade || 'UN'
              });
            }
          }
        }

        return {
          podeApontar: materiaisInsuficientes.length === 0,
          materiaisInsuficientes: materiaisInsuficientes
        };

      } catch (error) {
        console.error('❌ Erro ao validar estoque:', error);
        return {
          podeApontar: false,
          materiaisInsuficientes: [],
          erro: error.message
        };
      }
    }

    // Função principal para apontar produção
    window.apontarProducao = async function(orderId) {
      try {
        console.log(`🔍 Validando estoque para apontamento da OP: ${orderId}`);
        mostrarNotificacao('🔍 Validando materiais...', 'info', 2000);

        // 1. VALIDAR ESTOQUE ANTES DE PERMITIR APONTAMENTO
        const validacao = await validarEstoqueParaApontamento(orderId);

        if (validacao.erro) {
          mostrarNotificacao('❌ Erro na validação: ' + validacao.erro, 'error', 5000);
          return;
        }

        if (!validacao.podeApontar) {
          // Mostrar modal de bloqueio com detalhes
          mostrarModalBloqueio(orderId, validacao.materiaisInsuficientes);
          return;
        }

        // Se chegou aqui, pode apontar
        mostrarNotificacao('✅ Materiais validados! Abrindo tela de apontamento...', 'success', 3000);
        // Abrir modal de apontamento
        abrirModalApontamento(orderId);

      } catch (error) {
        console.error('❌ Erro no apontamento:', error);
        mostrarNotificacao('❌ Erro ao processar apontamento', 'error', 5000);
      }
    };

    // Função para mostrar modal de bloqueio
    function mostrarModalBloqueio(opId, materiaisInsuficientes) {
      const ordem = ordensProducao.find(op => op.id === opId);
      const produto = produtos.find(p => p.id === ordem?.produtoId) || {};
      const armazem = armazens.find(a => a.id === ordem?.armazemProducaoId) || {};

      // Preencher informações da OP
      document.getElementById('opNumeroModal').textContent = ordem?.numero || opId;
      document.getElementById('opProdutoModal').textContent = `${produto.codigo || ''} - ${produto.descricao || 'Produto não encontrado'}`;
      document.getElementById('opArmazemModal').textContent = `${armazem.codigo || ''} - ${armazem.nome || 'Armazém não encontrado'}`;

      // Preencher tabela de materiais em falta
      const tbody = document.getElementById('tabelaMateriaisFalta');
      tbody.innerHTML = '';

      materiaisInsuficientes.forEach(material => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; font-family: monospace; font-weight: 600;">${material.codigo}</td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">${material.descricao}</td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center;">
            <span style="background: ${material.tipo === 'MP' ? '#17a2b8' : '#6f42c1'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
              ${material.tipo}
            </span>
          </td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">${material.necessario.toFixed(3)}</td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center;">${material.disponivel.toFixed(3)}</td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">${material.falta.toFixed(3)}</td>
          <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">${material.unidade}</td>
        `;
        tbody.appendChild(row);
      });

      // Mostrar modal
      document.getElementById('modalApontamentoBloqueado').style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }

    // Função para fechar modal de bloqueio
    window.fecharModalBloqueio = function() {
      document.getElementById('modalApontamentoBloqueado').style.display = 'none';
      document.body.style.overflow = 'auto';
    };

    // Funções de navegação do modal
    window.abrirGestaoTransferencias = function() {
      fecharModalBloqueio();
      mostrarNotificacao('🔄 Redirecionando para gestão de transferências...', 'info', 2000);
      // Aqui você pode implementar a navegação para tela de transferências
      console.log('🔄 Abrir gestão de transferências');
    };

    window.abrirGestaoEstoque = function() {
      fecharModalBloqueio();
      mostrarNotificacao('📦 Redirecionando para gestão de estoque...', 'info', 2000);
      window.open('gestao_estoque.html', '_blank');
    };

    // Função para iniciar produção (com validação e impressão)
    window.iniciarProducao = async function(orderId) {
      try {
        console.log(`🔍 Validando estoque para iniciar produção da OP: ${orderId}`);
        mostrarNotificacao('🔍 Validando materiais para produção...', 'info', 2000);

        // 1. VALIDAR ESTOQUE ANTES DE INICIAR PRODUÇÃO
        const validacao = await validarEstoqueParaApontamento(orderId);

        if (validacao.erro) {
          mostrarNotificacao('❌ Erro na validação: ' + validacao.erro, 'error', 5000);
          return;
        }

        if (!validacao.podeApontar) {
          // Mostrar modal de bloqueio com detalhes
          mostrarModalBloqueio(orderId, validacao.materiaisInsuficientes);
          return;
        }

        // 2. SE CHEGOU AQUI, PODE INICIAR PRODUÇÃO
        console.log('✅ Materiais validados! Iniciando produção...');
        mostrarNotificacao('✅ Materiais validados! Iniciando produção...', 'success', 3000);

        // 3. ATUALIZAR STATUS DA OP PARA "EM PRODUÇÃO"
        await atualizarStatusOP(orderId, 'Em Produção');

        // 4. GERAR E EXIBIR IMPRESSÃO DA OP
        await gerarImpressaoOP(orderId);

      } catch (error) {
        console.error('❌ Erro ao iniciar produção:', error);
        mostrarNotificacao('❌ Erro ao iniciar produção: ' + error.message, 'error', 5000);
      }
    };

    // Função para atualizar status da OP
    async function atualizarStatusOP(opId, novoStatus) {
      try {
        console.log(`🔄 Atualizando status da OP ${opId} para: ${novoStatus}`);

        await updateDoc(doc(db, 'ordensProducao', opId), {
          status: novoStatus,
          dataInicioProducao: new Date(),
          ultimaAtualizacao: new Date()
        });

        // Atualizar dados locais
        const ordem = ordensProducao.find(op => op.id === opId);
        if (ordem) {
          ordem.status = novoStatus;
          ordem.dataInicioProducao = new Date();
        }

        // Atualizar interface
        renderizarOrdens();
        atualizarDashboard();

        console.log(`✅ Status da OP ${opId} atualizado para: ${novoStatus}`);
        mostrarNotificacao(`✅ OP atualizada para: ${novoStatus}`, 'success', 3000);

      } catch (error) {
        console.error('❌ Erro ao atualizar status da OP:', error);
        throw new Error('Falha ao atualizar status da OP: ' + error.message);
      }
    }

    // Função para gerar impressão da OP
    async function gerarImpressaoOP(opId) {
      try {
        console.log(`🖨️ Gerando impressão da OP: ${opId}`);

        const ordem = ordensProducao.find(op => op.id === opId);
        if (!ordem) {
          throw new Error('Ordem de produção não encontrada');
        }

        const produto = produtos.find(p => p.id === ordem.produtoId) || {};
        const armazem = armazens.find(a => a.id === ordem.armazemProducaoId) || {};

        // Gerar HTML da impressão
        const htmlImpressao = gerarHTMLImpressao(ordem, produto, armazem);

        // Abrir janela de impressão
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(htmlImpressao);
        printWindow.document.close();

        // Aguardar carregamento e imprimir
        printWindow.onload = function() {
          printWindow.print();
        };

        mostrarNotificacao('🖨️ Ordem de produção enviada para impressão', 'success', 3000);

      } catch (error) {
        console.error('❌ Erro ao gerar impressão:', error);
        throw new Error('Falha na impressão: ' + error.message);
      }
    }

    // Função para gerar HTML da impressão
    function gerarHTMLImpressao(ordem, produto, armazem) {
      const dataAtual = new Date().toLocaleString('pt-BR');
      const dataEntrega = ordem.dataEntrega?.seconds
        ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString('pt-BR')
        : 'N/A';

      // Gerar tabela de materiais
      let tabelaMateriais = '';
      if (ordem.materiaisNecessarios && ordem.materiaisNecessarios.length > 0) {
        tabelaMateriais = `
          <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
              <tr style="background: #f8f9fa;">
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Código</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Descrição</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Quantidade</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Unidade</th>
              </tr>
            </thead>
            <tbody>
        `;

        ordem.materiaisNecessarios.forEach(material => {
          const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
          tabelaMateriais += `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px; font-family: monospace;">${materialProduto.codigo || material.produtoId}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${materialProduto.descricao || 'Produto não encontrado'}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${material.quantidade.toFixed(3)}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${materialProduto.unidade || 'UN'}</td>
            </tr>
          `;
        });

        tabelaMateriais += `
            </tbody>
          </table>
        `;
      } else {
        tabelaMateriais = '<p style="margin-top: 20px; color: #6c757d; font-style: italic;">Nenhum material cadastrado para esta OP.</p>';
      }

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Ordem de Produção - ${ordem.numero || ordem.id}</title>
          <style>
            @page { size: A4; margin: 20mm; }
            @media print { .no-print { display: none; } }
            body { font-family: Arial, sans-serif; font-size: 12px; line-height: 1.4; }
            .header { text-align: center; border-bottom: 2px solid #0854a0; padding-bottom: 15px; margin-bottom: 20px; }
            .company-name { font-size: 18px; font-weight: bold; color: #0854a0; margin-bottom: 5px; }
            .document-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
            .info-section { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
            .info-title { font-weight: bold; color: #0854a0; margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
            .info-row { margin-bottom: 8px; }
            .label { font-weight: bold; display: inline-block; width: 120px; }
            .materials-section { margin-top: 20px; }
            .footer { margin-top: 30px; text-align: center; font-size: 10px; color: #6c757d; border-top: 1px solid #eee; padding-top: 10px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">SISTEMA DE PRODUÇÃO</div>
            <div class="document-title">ORDEM DE PRODUÇÃO</div>
            <div>Número: <strong>${ordem.numero || ordem.id}</strong></div>
          </div>

          <div class="info-grid">
            <div class="info-section">
              <div class="info-title">📋 INFORMAÇÕES DA ORDEM</div>
              <div class="info-row">
                <span class="label">Número:</span> ${ordem.numero || ordem.id}
              </div>
              <div class="info-row">
                <span class="label">Status:</span> <strong>${ordem.status || 'N/A'}</strong>
              </div>
              <div class="info-row">
                <span class="label">Quantidade:</span> ${ordem.quantidade || 0} ${produto.unidade || 'UN'}
              </div>
              <div class="info-row">
                <span class="label">Data Entrega:</span> ${dataEntrega}
              </div>
              <div class="info-row">
                <span class="label">Prioridade:</span> ${ordem.prioridade || 'Normal'}
              </div>
            </div>

            <div class="info-section">
              <div class="info-title">🏭 PRODUTO E ARMAZÉM</div>
              <div class="info-row">
                <span class="label">Código:</span> ${produto.codigo || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Descrição:</span> ${produto.descricao || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Tipo:</span> ${produto.tipo || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Armazém:</span> ${armazem.codigo || 'N/A'} - ${armazem.nome || 'N/A'}
              </div>
            </div>
          </div>

          <div class="materials-section">
            <div class="info-title">📦 MATERIAIS NECESSÁRIOS</div>
            ${tabelaMateriais}
          </div>

          <div class="footer">
            <p>Documento gerado em: ${dataAtual}</p>
            <p>Sistema de Gestão de Produção - Apontamentos Simplificado</p>
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
          <\/script>
        </body>
        </html>
      `;
    }

    // Função de carregamento com controle
    async function carregarDadosSeguro() {
      if (carregandoDados) {
        console.log('⏳ Carregamento já em andamento, aguardando...');
        return;
      }

      carregandoDados = true;
      try {
        await carregarDados();
      } finally {
        carregandoDados = false;
      }
    }

    // Função para abrir modal de apontamento
    window.abrirModalApontamento = async function(orderId) {
      try {
        const ordem = ordensProducao.find(op => op.id === orderId);
        if (!ordem) {
          mostrarNotificacao('❌ Ordem de produção não encontrada', 'error');
          return;
        }

        const produto = produtos.find(p => p.id === ordem.produtoId) || {};

        // Preencher informações da OP
        document.getElementById('opNumeroApontamento').textContent = ordem.numero || orderId;
        document.getElementById('opProdutoApontamento').textContent = `${produto.codigo || ''} - ${produto.descricao || 'Produto não encontrado'}`;
        document.getElementById('opQuantidadeApontamento').textContent = `${ordem.quantidade || 0} ${produto.unidade || 'UN'}`;
        document.getElementById('opJaProduzidoApontamento').textContent = `${ordem.quantidadeProduzida || 0} ${produto.unidade || 'UN'}`;

        const restante = (ordem.quantidade || 0) - (ordem.quantidadeProduzida || 0);
        document.getElementById('opRestanteApontamento').textContent = `${restante} ${produto.unidade || 'UN'}`;

        // Preencher tabela de materiais
        await preencherTabelaMateriaisApontamento(ordem);

        // Limpar formulário
        document.getElementById('formApontamento').reset();
        document.getElementById('quantidadeProduzida').max = restante;

        // Mostrar modal
        document.getElementById('modalApontamento').style.display = 'flex';
        document.body.style.overflow = 'hidden';

      } catch (error) {
        console.error('❌ Erro ao abrir modal de apontamento:', error);
        mostrarNotificacao('❌ Erro ao abrir modal de apontamento', 'error');
      }
    };

    // Função para preencher tabela de materiais do apontamento
    async function preencherTabelaMateriaisApontamento(ordem) {
      const tbody = document.getElementById('tabelaMateriaisApontamento');
      tbody.innerHTML = '';

      if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td colspan="5" style="padding: 20px; text-align: center; color: #666; font-style: italic;">
            Nenhum material necessário cadastrado
          </td>
        `;
        tbody.appendChild(row);
        return;
      }

      const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId);

      ordem.materiaisNecessarios.forEach(material => {
        const produto = produtos.find(p => p.id === material.produtoId) || {};
        const estoque = estoques.find(e =>
          e.produtoId === material.produtoId &&
          e.armazemId === ordem.armazemProducaoId
        );

        const saldoDisponivel = estoque ? estoque.saldo : 0;
        const quantidadeNecessaria = material.quantidade || 0;
        const suficiente = saldoDisponivel >= quantidadeNecessaria;

        const row = document.createElement('tr');
        row.innerHTML = `
          <td style="padding: 12px 8px; border-bottom: 1px solid #dee2e6; font-weight: 600;">${produto.codigo || 'N/A'}</td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #dee2e6;">${produto.descricao || 'Produto não encontrado'}</td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">${quantidadeNecessaria.toFixed(3)} ${produto.unidade || 'UN'}</td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600; color: ${suficiente ? '#28a745' : '#dc3545'};">${saldoDisponivel.toFixed(3)} ${produto.unidade || 'UN'}</td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #dee2e6; text-align: center;">
            <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; color: white; background: ${suficiente ? '#28a745' : '#dc3545'};">
              ${suficiente ? '✅ OK' : '❌ FALTA'}
            </span>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    // Função para fechar modal de apontamento
    window.fecharModalApontamento = function() {
      document.getElementById('modalApontamento').style.display = 'none';
      document.body.style.overflow = 'auto';
      document.getElementById('formApontamento').reset();
    };

    // Evento para fechar modal com ESC
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        // Fechar modal de bloqueio se estiver aberto
        const modalBloqueio = document.getElementById('modalApontamentoBloqueado');
        if (modalBloqueio && modalBloqueio.style.display === 'flex') {
          fecharModalBloqueio();
        }

        // Fechar modal de apontamento se estiver aberto
        const modalApontamento = document.getElementById('modalApontamento');
        if (modalApontamento && modalApontamento.style.display === 'flex') {
          fecharModalApontamento();
        }
      }
    });

    // Função para confirmar apontamento
    window.confirmarApontamento = async function(event) {
      event.preventDefault();

      try {
        const quantidadeProduzida = parseFloat(document.getElementById('quantidadeProduzida').value);
        const quantidadeRefugo = parseFloat(document.getElementById('quantidadeRefugo').value) || 0;
        const observacoes = document.getElementById('observacoesApontamento').value.trim();

        if (!quantidadeProduzida || quantidadeProduzida <= 0) {
          mostrarNotificacao('❌ Informe uma quantidade produzida válida', 'error');
          return;
        }

        // Buscar a ordem atual
        const opNumero = document.getElementById('opNumeroApontamento').textContent;
        const ordem = ordensProducao.find(op => op.numero === opNumero);

        if (!ordem) {
          mostrarNotificacao('❌ Ordem de produção não encontrada', 'error');
          return;
        }

        // Validar se não excede o restante
        const jaProduzido = ordem.quantidadeProduzida || 0;
        const restante = ordem.quantidade - jaProduzido;

        if (quantidadeProduzida > restante) {
          mostrarNotificacao(`❌ Quantidade produzida (${quantidadeProduzida}) não pode ser maior que o restante (${restante})`, 'error');
          return;
        }

        // Desabilitar botão durante processamento
        const btnConfirmar = document.getElementById('btnConfirmarApontamento');
        btnConfirmar.disabled = true;
        btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';

        mostrarNotificacao('⏳ Processando apontamento...', 'info');

        // Simular processamento do apontamento
        // Aqui você implementaria a lógica real de apontamento
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Sucesso
        mostrarNotificacao('✅ Apontamento realizado com sucesso!', 'success', 3000);
        fecharModalApontamento();

        // Recarregar dados
        await carregarDadosSeguro();

      } catch (error) {
        console.error('❌ Erro ao confirmar apontamento:', error);
        mostrarNotificacao('❌ Erro ao processar apontamento: ' + error.message, 'error');
      } finally {
        // Reabilitar botão
        const btnConfirmar = document.getElementById('btnConfirmarApontamento');
        if (btnConfirmar) {
          btnConfirmar.disabled = false;
          btnConfirmar.innerHTML = '<i class="fas fa-check"></i> Confirmar Apontamento';
        }
      }
    };

    // Inicializar aplicação
    document.addEventListener('DOMContentLoaded', function() {
      carregarDadosSeguro();

      // Atualizar dados a cada 2 minutos (menos frequente)
      setInterval(carregarDadosSeguro, 120000);
    });
  </script>
</body>
</html>
