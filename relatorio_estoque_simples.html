<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Relatório de Estoque</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .report-container {
            max-width: 1600px;
            margin: 20px auto;
            padding: 20px;
        }
        .report-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #6f42c1;
        }
        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #6f42c1;
            margin-bottom: 10px;
        }
        .report-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 25px 0;
        }
        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
            font-weight: bold;
        }
        .table-content {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        .data-table th {
            background: #6f42c1;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-low { background: #fff3cd; color: #856404; }
        .status-critical { background: #f8d7da; color: #721c24; }
        .status-zero { background: #e2e3e5; color: #383d41; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .font-weight-bold { font-weight: bold; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            margin: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>📊 Relatório de Controle de Estoque</h1>
            <p>Gestão completa de estoque e necessidades de compra</p>
            <p><strong>Status:</strong> <span id="reportStatus">Pronto para gerar</span></p>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Controles do Relatório</h3>
            <button onclick="gerarRelatorio()" class="btn btn-primary">📊 Gerar Relatório Completo</button>
            <button onclick="exportarCSV()" class="btn btn-success" id="btnExport" disabled>📄 Exportar CSV</button>
            <button onclick="gerarListaCompras()" class="btn btn-warning" id="btnCompras" disabled>🛒 Lista de Compras</button>
        </div>

        <!-- Busca -->
        <div class="search-section">
            <h4>🔍 Filtrar Resultados</h4>
            <input type="text" id="searchInput" class="search-input" placeholder="Buscar por código ou descrição..." onkeyup="filtrarTabela()">
            <select id="statusFilter" class="search-input" onchange="filtrarTabela()">
                <option value="">Todos os Status</option>
                <option value="ok">Estoque OK</option>
                <option value="low">Estoque Baixo</option>
                <option value="critical">Estoque Crítico</option>
                <option value="zero">Sem Estoque</option>
            </select>
        </div>

        <!-- Resumo -->
        <div class="summary-cards" id="summaryCards" style="display: none;">
            <div class="summary-card">
                <div class="summary-number" id="totalItems">0</div>
                <div>Total de Itens</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="stockValue">R$ 0</div>
                <div>Valor do Estoque</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="lowStockItems">0</div>
                <div>Estoque Baixo</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="zeroStockItems">0</div>
                <div>Sem Estoque</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="needPurchase">0</div>
                <div>Necessita Compra</div>
            </div>
        </div>

        <!-- Tabela -->
        <div class="report-table" id="reportTable" style="display: none;">
            <div class="table-header">
                <h3>📋 Relatório Detalhado de Estoque</h3>
                <p>Clique nos cabeçalhos para ordenar | <span id="itemCount">0 itens</span></p>
            </div>
            <div class="table-content">
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th onclick="ordenarTabela(0)">Código</th>
                            <th onclick="ordenarTabela(1)">Descrição</th>
                            <th onclick="ordenarTabela(2)">Unidade</th>
                            <th onclick="ordenarTabela(3)">Estoque Atual</th>
                            <th onclick="ordenarTabela(4)">Estoque Mín.</th>
                            <th onclick="ordenarTabela(5)">Valor Unit.</th>
                            <th onclick="ordenarTabela(6)">Valor Total</th>
                            <th onclick="ordenarTabela(7)">Armazém</th>
                            <th onclick="ordenarTabela(8)">Status</th>
                            <th onclick="ordenarTabela(9)">Sugestão Compra</th>
                            <th onclick="ordenarTabela(10)">Última Movimentação</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Dados serão inseridos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Loading -->
        <div class="loading" id="loadingIndicator">
            <h3>📊 Carregando dados do relatório...</h3>
            <p>Aguarde enquanto processamos as informações do estoque.</p>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let allData = [];
        let filteredData = [];

        function updateStatus(message) {
            document.getElementById('reportStatus').textContent = message;
        }

        window.gerarRelatorio = async function() {
            updateStatus('Carregando dados...');
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('summaryCards').style.display = 'none';
            document.getElementById('reportTable').style.display = 'none';

            try {
                // Carregar dados básicos
                updateStatus('Carregando produtos...');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                updateStatus('Carregando estoques...');
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Debug: verificar estrutura dos estoques
                console.log('Estoques carregados:', estoques.length);
                if (estoques.length > 0) {
                    console.log('Exemplo de estoque:', estoques[0]);
                }

                updateStatus('Carregando armazéns...');
                const armazensSnap = await getDocs(collection(db, "armazens"));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Debug: verificar estrutura dos armazéns
                console.log('Armazéns carregados:', armazens);
                if (armazens.length > 0) {
                    console.log('Exemplo de armazém:', armazens[0]);
                }

                updateStatus('Processando dados...');

                // Processar dados
                allData = [];

                // Agrupar estoques por produto para melhor performance
                const estoquesPorProduto = {};
                estoques.forEach(estoque => {
                    if (!estoquesPorProduto[estoque.produtoId]) {
                        estoquesPorProduto[estoque.produtoId] = [];
                    }
                    estoquesPorProduto[estoque.produtoId].push(estoque);
                });

                produtos.forEach(produto => {
                    const estoquesItem = estoquesPorProduto[produto.id] || [];

                    if (estoquesItem.length === 0) {
                        // Produto sem estoque
                        allData.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            unidade: produto.unidade || 'PC',
                            saldoAtual: 0,
                            estoqueMinimo: produto.estoqueMinimo || 0,
                            valorUnitario: produto.valorUnitario || 0,
                            valorTotal: 0,
                            armazem: 'SEM ESTOQUE',
                            armazemId: null,
                            status: 'zero',
                            sugestaoCompra: produto.estoqueMinimo || 10,
                            ultimaMovimentacao: 'N/A'
                        });
                    } else {
                        // Produto com estoque - pode ter múltiplos armazéns
                        estoquesItem.forEach(estoque => {
                            // Verificar diferentes possibilidades para o campo do armazém
                            let armazemId = estoque.armazemId || estoque.armazem || estoque.warehouse || estoque.warehouseId;

                            const armazem = armazemId ?
                                armazens.find(a => a.id === armazemId || a.codigo === armazemId) : null;

                            const saldoAtual = estoque.saldo || 0;
                            const estoqueMinimo = produto.estoqueMinimo || 0;
                            const valorUnitario = produto.valorUnitario || 0;

                            // Determinar status
                            let status = 'ok';
                            let sugestaoCompra = 0;

                            if (saldoAtual === 0) {
                                status = 'zero';
                                sugestaoCompra = estoqueMinimo || 10;
                            } else if (saldoAtual <= estoqueMinimo && estoqueMinimo > 0) {
                                status = saldoAtual <= (estoqueMinimo * 0.5) ? 'critical' : 'low';
                                sugestaoCompra = Math.max(0, estoqueMinimo * 2 - saldoAtual);
                            }

                            // Tentar diferentes propriedades para o nome do armazém
                            let nomeArmazem = 'N/A';
                            if (armazem) {
                                nomeArmazem = armazem.nome || armazem.codigo || armazem.descricao || `ID: ${armazem.id}`;
                            } else if (armazemId) {
                                nomeArmazem = `ID: ${armazemId}`;
                            }

                            allData.push({
                                codigo: produto.codigo,
                                descricao: produto.descricao,
                                unidade: produto.unidade || 'PC',
                                saldoAtual,
                                estoqueMinimo,
                                valorUnitario,
                                valorTotal: saldoAtual * valorUnitario,
                                armazem: nomeArmazem,
                                armazemId: armazemId,
                                status,
                                sugestaoCompra,
                                ultimaMovimentacao: estoque.ultimaMovimentacao ?
                                    new Date(estoque.ultimaMovimentacao.seconds * 1000).toLocaleDateString() : 'N/A'
                            });
                        });
                    }
                });

                // Ordenar por código
                allData.sort((a, b) => a.codigo.localeCompare(b.codigo));
                filteredData = [...allData];

                // Mostrar resultados
                atualizarResumo();
                renderizarTabela();
                
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('summaryCards').style.display = 'grid';
                document.getElementById('reportTable').style.display = 'block';
                document.getElementById('btnExport').disabled = false;
                document.getElementById('btnCompras').disabled = false;

                updateStatus('Relatório gerado com sucesso');

            } catch (error) {
                console.error('Erro ao gerar relatório:', error);
                updateStatus('Erro ao gerar relatório');
                alert('Erro ao gerar relatório: ' + error.message);
            }
        };

        function atualizarResumo() {
            const totalItems = filteredData.length;
            const valorTotal = filteredData.reduce((sum, item) => sum + item.valorTotal, 0);
            const estoqueBaixo = filteredData.filter(item => item.status === 'low' || item.status === 'critical').length;
            const semEstoque = filteredData.filter(item => item.status === 'zero').length;
            const necessitaCompra = filteredData.filter(item => item.sugestaoCompra > 0).length;

            document.getElementById('totalItems').textContent = totalItems.toLocaleString();
            document.getElementById('stockValue').textContent = 'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
            document.getElementById('lowStockItems').textContent = estoqueBaixo;
            document.getElementById('zeroStockItems').textContent = semEstoque;
            document.getElementById('needPurchase').textContent = necessitaCompra;
        }

        function renderizarTabela() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            filteredData.forEach(item => {
                const row = document.createElement('tr');

                row.innerHTML =
                    '<td class="font-weight-bold">' + item.codigo + '</td>' +
                    '<td>' + item.descricao + '</td>' +
                    '<td class="text-center">' + item.unidade + '</td>' +
                    '<td class="text-right font-weight-bold">' + item.saldoAtual.toLocaleString() + '</td>' +
                    '<td class="text-right">' + item.estoqueMinimo.toLocaleString() + '</td>' +
                    '<td class="text-right">R$ ' + item.valorUnitario.toLocaleString('pt-BR', { minimumFractionDigits: 2 }) + '</td>' +
                    '<td class="text-right font-weight-bold">R$ ' + item.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 }) + '</td>' +
                    '<td>' + item.armazem + '</td>' +
                    '<td class="text-center"><span class="status-badge status-' + item.status + '">' + getStatusText(item.status) + '</span></td>' +
                    '<td class="text-right">' + (item.sugestaoCompra > 0 ? item.sugestaoCompra.toLocaleString() : '-') + '</td>' +
                    '<td class="text-center">' + item.ultimaMovimentacao + '</td>';

                tbody.appendChild(row);
            });

            document.getElementById('itemCount').textContent = filteredData.length + ' itens';
        }

        function getStatusText(status) {
            const statusMap = {
                'ok': 'OK',
                'low': 'Baixo',
                'critical': 'Crítico',
                'zero': 'Zero'
            };
            return statusMap[status] || status;
        }

        window.filtrarTabela = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            filteredData = allData.filter(item => {
                const matchSearch = !searchTerm ||
                    item.codigo.toLowerCase().includes(searchTerm) ||
                    item.descricao.toLowerCase().includes(searchTerm);

                const matchStatus = !statusFilter || item.status === statusFilter;

                return matchSearch && matchStatus;
            });

            atualizarResumo();
            renderizarTabela();
        };

        window.exportarCSV = function() {
            if (filteredData.length === 0) {
                alert('Nenhum dado para exportar!');
                return;
            }

            let csv = 'Código,Descrição,Unidade,Estoque Atual,Estoque Mín,Valor Unit,Valor Total,Armazém,Status,Sugestão Compra,Última Movimentação\n';

            filteredData.forEach(item => {
                csv += '"' + item.codigo + '","' + item.descricao + '","' + item.unidade + '",' +
                       item.saldoAtual + ',' + item.estoqueMinimo + ',"R$ ' + item.valorUnitario.toFixed(2) +
                       '","R$ ' + item.valorTotal.toFixed(2) + '","' + item.armazem + '","' +
                       getStatusText(item.status) + '",' + item.sugestaoCompra + ',"' + item.ultimaMovimentacao + '"\n';
            });

            baixarArquivo(csv, 'relatorio_estoque_' + new Date().toISOString().split('T')[0] + '.csv', 'text/csv');
        };

        window.gerarListaCompras = function() {
            const itensCompra = filteredData.filter(item => item.sugestaoCompra > 0);

            if (itensCompra.length === 0) {
                alert('Nenhum item necessita compra!');
                return;
            }

            let csv = 'Código,Descrição,Unidade,Estoque Atual,Estoque Mín,Sugestão Compra,Status,Valor Unit,Valor Total Compra\n';

            itensCompra.forEach(item => {
                const valorTotalCompra = item.sugestaoCompra * item.valorUnitario;
                csv += '"' + item.codigo + '","' + item.descricao + '","' + item.unidade + '",' +
                       item.saldoAtual + ',' + item.estoqueMinimo + ',' + item.sugestaoCompra + ',"' +
                       getStatusText(item.status) + '","R$ ' + item.valorUnitario.toFixed(2) +
                       '","R$ ' + valorTotalCompra.toFixed(2) + '"\n';
            });

            baixarArquivo(csv, 'lista_compras_' + new Date().toISOString().split('T')[0] + '.csv', 'text/csv');
        };

        function baixarArquivo(conteudo, nomeArquivo, tipo) {
            const blob = new Blob([conteudo], { type: tipo + ';charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', nomeArquivo);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        let sortDirection = {};
        window.ordenarTabela = function(columnIndex) {
            const direction = sortDirection[columnIndex] === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = direction;

            const columns = ['codigo', 'descricao', 'unidade', 'saldoAtual', 'estoqueMinimo', 'valorUnitario', 'valorTotal', 'armazem', 'status', 'sugestaoCompra', 'ultimaMovimentacao'];
            const column = columns[columnIndex];

            filteredData.sort((a, b) => {
                let valueA = a[column];
                let valueB = b[column];

                if (typeof valueA === 'string') {
                    valueA = valueA.toLowerCase();
                    valueB = valueB.toLowerCase();
                }

                if (direction === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });

            renderizarTabela();
        };

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            updateStatus('Pronto para gerar relatório');
        };
    </script>
</body>
</html>
