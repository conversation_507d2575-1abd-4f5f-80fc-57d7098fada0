<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IA Monitor de Compras - Sistema Naliteck</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --ai-color: #9b59b6;
            --neural-color: #e67e22;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .header-section {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--neural-color) 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/></circle></svg>');
        }

        .ai-status {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            margin-top: 15px;
        }

        .ai-pulse {
            width: 12px;
            height: 12px;
            background: #2ecc71;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .monitor-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .monitor-card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
        }

        .card-title {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .card-title i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .card-body {
            padding: 20px;
        }

        .alert-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .alert-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-item.critical {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-item.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .alert-item.info {
            background: #d1ecf1;
            border-color: #bee5eb;
        }

        .alert-item.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            min-width: 30px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .alert-description {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .alert-action {
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 500;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--danger-color);
        }

        .trend-stable {
            color: var(--info-color);
        }

        .ai-recommendation {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--neural-color) 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .ai-recommendation h5 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .ai-recommendation h5 i {
            margin-right: 10px;
        }

        .recommendation-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .recommendation-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
        }

        .recommendation-list li:last-child {
            border-bottom: none;
        }

        .recommendation-list li i {
            margin-right: 10px;
            min-width: 20px;
        }

        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }

        .flow-step {
            text-align: center;
            min-width: 120px;
            position: relative;
        }

        .flow-step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: var(--secondary-color);
        }

        .flow-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 1.5rem;
            color: white;
        }

        .flow-icon.active {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .flow-icon.warning {
            background: var(--warning-color);
        }

        .flow-icon.error {
            background: var(--danger-color);
        }

        .flow-icon.normal {
            background: var(--secondary-color);
        }

        .flow-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .control-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .btn-ai {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--neural-color) 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-ai:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(155, 89, 182, 0.3);
            color: white;
        }

        .loading-ai {
            text-align: center;
            padding: 40px;
        }

        .ai-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--ai-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: var(--success-color); }
        .notification.error { background: var(--danger-color); }
        .notification.info { background: var(--info-color); }
        .notification.warning { background: var(--warning-color); }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-brain"></i> IA Monitor de Compras</h1>
            <p class="mb-0">Sistema Inteligente de Monitoramento e Prevenção de Erros</p>
            <div class="ai-status">
                <div class="ai-pulse"></div>
                <span>IA Ativa - Monitorando em Tempo Real</span>
            </div>
        </div>

        <!-- Fluxo do Processo -->
        <div class="process-flow">
            <div class="flow-step">
                <div class="flow-icon active" id="step-solicitacao">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="flow-label">Solicitação</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-aprovacao">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="flow-label">Aprovação</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-cotacao">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="flow-label">Cotação</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-pedido">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="flow-label">Pedido</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-recebimento">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="flow-label">Recebimento</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-qualidade">
                    <i class="fas fa-search"></i>
                </div>
                <div class="flow-label">Qualidade</div>
            </div>
            <div class="flow-step">
                <div class="flow-icon normal" id="step-estoque">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="flow-label">Estoque</div>
            </div>
        </div>

        <!-- Grid de Monitoramento -->
        <div class="monitoring-grid">
            <!-- Alertas Críticos -->
            <div class="monitor-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        Alertas Críticos
                    </h5>
                </div>
                <div class="card-body" id="critical-alerts">
                    <div class="loading-ai">
                        <div class="ai-spinner"></div>
                        <p>IA analisando processos...</p>
                    </div>
                </div>
            </div>

            <!-- Métricas de Performance -->
            <div class="monitor-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-chart-line text-success"></i>
                        Performance do Processo
                    </h5>
                </div>
                <div class="card-body" id="performance-metrics">
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-value" id="efficiency-rate">--</div>
                            <div class="metric-label">Eficiência</div>
                            <div class="trend-indicator trend-up" id="efficiency-trend">
                                <i class="fas fa-arrow-up"></i> ****%
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-value" id="error-rate">--</div>
                            <div class="metric-label">Taxa de Erro</div>
                            <div class="trend-indicator trend-down" id="error-trend">
                                <i class="fas fa-arrow-down"></i> -1.1%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Previsões IA -->
            <div class="monitor-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-crystal-ball text-info"></i>
                        Previsões IA
                    </h5>
                </div>
                <div class="card-body" id="ai-predictions">
                    <div class="loading-ai">
                        <div class="ai-spinner"></div>
                        <p>Processando previsões...</p>
                    </div>
                </div>
            </div>

            <!-- Anomalias Detectadas -->
            <div class="monitor-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-search-plus text-warning"></i>
                        Anomalias Detectadas
                    </h5>
                </div>
                <div class="card-body" id="anomalies">
                    <div class="loading-ai">
                        <div class="ai-spinner"></div>
                        <p>Detectando anomalias...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recomendações IA -->
        <div class="ai-recommendation">
            <h5><i class="fas fa-lightbulb"></i> Recomendações Inteligentes</h5>
            <ul class="recommendation-list" id="ai-recommendations">
                <li><i class="fas fa-cog"></i> Analisando padrões de compras...</li>
                <li><i class="fas fa-chart-bar"></i> Identificando oportunidades de melhoria...</li>
                <li><i class="fas fa-shield-alt"></i> Verificando pontos de risco...</li>
            </ul>
        </div>

        <!-- Painel de Controle -->
        <div class="control-panel">
            <h5><i class="fas fa-sliders-h"></i> Painel de Controle IA</h5>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-ai" onclick="runFullAnalysis()">
                        <i class="fas fa-search"></i> Análise Completa
                    </button>
                    <button class="btn btn-ai" onclick="predictIssues()">
                        <i class="fas fa-crystal-ball"></i> Prever Problemas
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-ai" onclick="optimizeProcess()">
                        <i class="fas fa-rocket"></i> Otimizar Processo
                    </button>
                    <button class="btn btn-ai" onclick="generateReport()">
                        <i class="fas fa-file-pdf"></i> Relatório IA
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase -->
    <script type="module" src="firebase-config.js"></script>
    
    <!-- IA Monitor Script -->
    <script type="module" src="services/ia-monitor-service.js"></script>
    
    <script type="module">
        import iaMonitor from './services/ia-monitor-service.js';

        // Inicialização do sistema
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 IA Monitor de Compras inicializado');
            initializeAIMonitor();
            setupEventListeners();
        });

        function initializeAIMonitor() {
            // Aguardar inicialização da IA
            setTimeout(() => {
                loadRealTimeData();
                startRealTimeUpdates();
            }, 3000);
        }

        function setupEventListeners() {
            // Escutar eventos da IA
            window.addEventListener('newAlert', (event) => {
                updateAlertsDisplay();
                showNotification(`Novo alerta: ${event.detail.title}`, 'warning');
            });

            window.addEventListener('metricsUpdated', (event) => {
                updateMetricsDisplay(event.detail);
            });
        }

        function loadRealTimeData() {
            updateAlertsDisplay();
            updateMetricsDisplay();
            updatePredictionsDisplay();
            updateAnomaliesDisplay();
            updateRecommendationsDisplay();
            updateProcessFlow();
        }

        function startRealTimeUpdates() {
            // Atualizar dados a cada 30 segundos
            setInterval(() => {
                loadRealTimeData();
            }, 30000);
        }

        function updateAlertsDisplay() {
            const alerts = window.IAMonitor ? window.IAMonitor.getAlerts() : [];
            const container = document.getElementById('critical-alerts');

            if (alerts.length === 0) {
                container.innerHTML = `
                    <div class="alert-item success">
                        <div class="alert-icon"><i class="fas fa-check-circle text-success"></i></div>
                        <div class="alert-content">
                            <div class="alert-title">Sistema Funcionando Normalmente</div>
                            <div class="alert-description">Nenhum alerta crítico detectado</div>
                            <div class="alert-action">Monitoramento ativo</div>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = alerts.slice(0, 5).map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="alert-icon">
                        <i class="fas fa-${getAlertIcon(alert.type)} text-${getAlertColor(alert.type)}"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">${alert.title}</div>
                        <div class="alert-description">${alert.description}</div>
                        <div class="alert-action">${alert.action}</div>
                    </div>
                </div>
            `).join('');
        }

        function updateMetricsDisplay(metrics = {}) {
            const defaultMetrics = window.IAMonitor ? window.IAMonitor.getMetrics() : {};
            const data = { ...defaultMetrics, ...metrics };

            document.getElementById('efficiency-rate').textContent = `${data.efficiency || 94.2}%`;
            document.getElementById('error-rate').textContent = `${data.errorRate || 2.1}%`;

            // Atualizar tendências
            updateTrend('efficiency-trend', data.efficiencyTrend || 'up', '****%');
            updateTrend('error-trend', data.errorTrend || 'down', '-1.1%');
        }

        function updatePredictionsDisplay() {
            const predictions = window.IAMonitor ? window.IAMonitor.getPredictions() : [];
            const container = document.getElementById('ai-predictions');

            if (predictions.length === 0) {
                container.innerHTML = generateMockPredictions();
                return;
            }

            container.innerHTML = predictions.map(prediction => `
                <div class="alert-item info">
                    <div class="alert-icon"><i class="fas fa-chart-line text-info"></i></div>
                    <div class="alert-content">
                        <div class="alert-title">${prediction.title}</div>
                        <div class="alert-description">${prediction.description}</div>
                        <div class="alert-action">${prediction.recommendation}</div>
                    </div>
                </div>
            `).join('');
        }

        function updateAnomaliesDisplay() {
            const anomalies = window.IAMonitor ? window.IAMonitor.getAlerts().filter(a => a.type === 'warning') : [];
            const container = document.getElementById('anomalies');

            if (anomalies.length === 0) {
                container.innerHTML = `
                    <div class="alert-item success">
                        <div class="alert-icon"><i class="fas fa-shield-alt text-success"></i></div>
                        <div class="alert-content">
                            <div class="alert-title">Nenhuma Anomalia Detectada</div>
                            <div class="alert-description">Todos os processos dentro dos padrões</div>
                            <div class="alert-action">Monitoramento contínuo ativo</div>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = anomalies.slice(0, 3).map(anomaly => `
                <div class="alert-item warning">
                    <div class="alert-icon"><i class="fas fa-search text-warning"></i></div>
                    <div class="alert-content">
                        <div class="alert-title">${anomaly.title}</div>
                        <div class="alert-description">${anomaly.description}</div>
                        <div class="alert-action">${anomaly.action}</div>
                    </div>
                </div>
            `).join('');
        }

        function updateRecommendationsDisplay() {
            const recommendations = window.IAMonitor ? window.IAMonitor.getRecommendations() : [];
            const container = document.getElementById('ai-recommendations');

            if (recommendations.length === 0) {
                container.innerHTML = generateMockRecommendations();
                return;
            }

            container.innerHTML = recommendations.map(rec => `
                <li>
                    <i class="fas fa-${getRecommendationIcon(rec.type)}"></i>
                    ${rec.description} - ${rec.impact}
                </li>
            `).join('');
        }

        function updateProcessFlow() {
            // Simular status dos processos
            const steps = ['solicitacao', 'aprovacao', 'cotacao', 'pedido', 'recebimento', 'qualidade', 'estoque'];
            const statuses = ['active', 'normal', 'warning', 'normal', 'normal', 'normal', 'normal'];

            steps.forEach((step, index) => {
                const element = document.getElementById(`step-${step}`);
                if (element) {
                    element.className = `flow-icon ${statuses[index]}`;
                }
            });
        }

        // Funções auxiliares
        function getAlertIcon(type) {
            const icons = {
                critical: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle',
                success: 'check-circle'
            };
            return icons[type] || 'info-circle';
        }

        function getAlertColor(type) {
            const colors = {
                critical: 'danger',
                warning: 'warning',
                info: 'info',
                success: 'success'
            };
            return colors[type] || 'info';
        }

        function getRecommendationIcon(type) {
            const icons = {
                process: 'cog',
                quality: 'shield-alt',
                supplier: 'users',
                cost: 'dollar-sign',
                time: 'clock'
            };
            return icons[type] || 'lightbulb';
        }

        function updateTrend(elementId, direction, value) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const iconClass = direction === 'up' ? 'fa-arrow-up' :
                            direction === 'down' ? 'fa-arrow-down' : 'fa-minus';
            const colorClass = direction === 'up' ? 'trend-up' :
                             direction === 'down' ? 'trend-down' : 'trend-stable';

            element.className = `trend-indicator ${colorClass}`;
            element.innerHTML = `<i class="fas ${iconClass}"></i> ${value}`;
        }

        function generateMockPredictions() {
            return `
                <div class="alert-item info">
                    <div class="alert-icon"><i class="fas fa-chart-line text-info"></i></div>
                    <div class="alert-content">
                        <div class="alert-title">Previsão de Atraso</div>
                        <div class="alert-description">85% chance de atraso no PC-2024-047</div>
                        <div class="alert-action">Recomendação: Acelerar aprovação</div>
                    </div>
                </div>
                <div class="alert-item info">
                    <div class="alert-icon"><i class="fas fa-trending-up text-info"></i></div>
                    <div class="alert-content">
                        <div class="alert-title">Aumento de Demanda</div>
                        <div class="alert-description">Previsão de 20% mais solicitações na próxima semana</div>
                        <div class="alert-action">Preparar recursos adicionais</div>
                    </div>
                </div>
            `;
        }

        function generateMockRecommendations() {
            return `
                <li><i class="fas fa-check"></i> Implementar aprovação automática para pedidos até R$ 1.000</li>
                <li><i class="fas fa-clock"></i> Reduzir prazo de cotação de 7 para 5 dias</li>
                <li><i class="fas fa-users"></i> Diversificar fornecedores para material crítico X</li>
                <li><i class="fas fa-chart-bar"></i> Otimizar fluxo de aprovação - economia de 2 dias</li>
                <li><i class="fas fa-robot"></i> Automatizar 60% das solicitações de baixo valor</li>
            `;
        }

        // Funções do painel de controle
        async function runFullAnalysis() {
            showNotification('🔍 Iniciando análise completa do processo...', 'info');

            // Simular análise
            const steps = [
                'Coletando dados históricos...',
                'Analisando padrões de comportamento...',
                'Detectando anomalias...',
                'Calculando métricas de performance...',
                'Gerando insights...'
            ];

            for (let i = 0; i < steps.length; i++) {
                setTimeout(() => {
                    showNotification(steps[i], 'info');
                }, i * 1000);
            }

            setTimeout(() => {
                showNotification('✅ Análise completa concluída! 47 insights gerados.', 'success');
                loadRealTimeData(); // Atualizar dados
            }, steps.length * 1000);
        }

        async function predictIssues() {
            showNotification('🔮 Executando algoritmos de previsão...', 'info');

            setTimeout(() => {
                showNotification('⚠️ 3 possíveis problemas identificados nos próximos 7 dias', 'warning');

                // Adicionar previsões específicas
                const predictions = [
                    'Fornecedor XYZ: 78% chance de atraso',
                    'Material ABC: Risco de falta de estoque',
                    'Processo aprovação: Gargalo previsto'
                ];

                predictions.forEach((pred, index) => {
                    setTimeout(() => {
                        showNotification(`📊 ${pred}`, 'warning');
                    }, (index + 1) * 1500);
                });
            }, 2000);
        }

        async function optimizeProcess() {
            showNotification('🚀 Iniciando otimização com IA...', 'info');

            const optimizations = [
                'Analisando gargalos do processo...',
                'Identificando oportunidades de automação...',
                'Calculando impacto das melhorias...',
                'Gerando plano de otimização...'
            ];

            for (let i = 0; i < optimizations.length; i++) {
                setTimeout(() => {
                    showNotification(optimizations[i], 'info');
                }, i * 1200);
            }

            setTimeout(() => {
                showNotification('✅ Otimização concluída! Economia prevista: 35% no tempo de processo', 'success');

                // Simular melhoria nas métricas
                setTimeout(() => {
                    document.getElementById('efficiency-rate').textContent = '96.8%';
                    updateTrend('efficiency-trend', 'up', '****%');
                    showNotification('📈 Métricas atualizadas com as otimizações', 'success');
                }, 1000);
            }, optimizations.length * 1200);
        }

        async function generateReport() {
            showNotification('📄 Gerando relatório inteligente...', 'info');

            const reportSteps = [
                'Compilando dados de performance...',
                'Analisando tendências...',
                'Gerando gráficos e insights...',
                'Formatando relatório executivo...'
            ];

            for (let i = 0; i < reportSteps.length; i++) {
                setTimeout(() => {
                    showNotification(reportSteps[i], 'info');
                }, i * 1000);
            }

            setTimeout(() => {
                showNotification('✅ Relatório gerado com sucesso!', 'success');

                // Simular download do relatório
                const reportData = generateReportData();
                downloadReport(reportData);
            }, reportSteps.length * 1000);
        }

        function generateReportData() {
            const now = new Date();
            const reportDate = now.toLocaleDateString('pt-BR');

            return {
                title: 'Relatório IA - Monitor de Compras',
                date: reportDate,
                period: 'Últimos 30 dias',
                metrics: {
                    efficiency: '94.2%',
                    errorRate: '2.1%',
                    avgProcessTime: '12.5 dias',
                    supplierPerformance: '87.3%'
                },
                insights: [
                    'Processo de aprovação pode ser otimizado em 35%',
                    'Fornecedor ABC apresenta alta taxa de atraso (45%)',
                    'Material XYZ com variação de preço anômala',
                    'Oportunidade de automação em 60% das solicitações'
                ],
                recommendations: [
                    'Implementar aprovação automática para valores até R$ 1.000',
                    'Diversificar fornecedores para materiais críticos',
                    'Reduzir prazo de cotação de 7 para 5 dias',
                    'Implementar alertas proativos de estoque'
                ]
            };
        }

        function downloadReport(data) {
            const reportContent = `
RELATÓRIO IA - MONITOR DE COMPRAS
${data.title}
Data: ${data.date}
Período: ${data.period}

=== MÉTRICAS DE PERFORMANCE ===
Eficiência: ${data.metrics.efficiency}
Taxa de Erro: ${data.metrics.errorRate}
Tempo Médio de Processo: ${data.metrics.avgProcessTime}
Performance de Fornecedores: ${data.metrics.supplierPerformance}

=== INSIGHTS PRINCIPAIS ===
${data.insights.map((insight, i) => `${i + 1}. ${insight}`).join('\n')}

=== RECOMENDAÇÕES ===
${data.recommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n')}

Relatório gerado automaticamente pelo Sistema IA Naliteck
            `;

            const blob = new Blob([reportContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `relatorio-ia-compras-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification('📥 Relatório baixado com sucesso!', 'success');
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
    </script>
</body>
</html>
