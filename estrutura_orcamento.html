<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orçamento de Estruturas</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
            --orcamento-color: #28a745;
            --orcamento-hover: #218838;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--orcamento-color), var(--orcamento-hover));
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header .user-info {
            font-size: 14px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .date-time-info {
            font-size: 12px;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 120px);
        }

        .search-panel {
            width: 30%;
            padding: 20px;
            background-color: #ffffff;
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
        }

        .structure-panel {
            width: 70%;
            padding: 20px;
            background-color: #ffffff;
            overflow-y: auto;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--orcamento-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--orcamento-color);
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--orcamento-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--orcamento-hover);
        }

        .btn-success:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: #d39e00;
        }

        .filters-section {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--orcamento-color);
        }

        .orcamento-info {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid var(--orcamento-color);
        }

        .orcamento-info h3 {
            color: var(--orcamento-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .product-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .product-item {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            background-color: #fff;
            cursor: move;
            transition: background-color 0.2s;
        }

        .product-item:hover {
            background-color: var(--secondary-color);
        }

        .product-item.dragging {
            opacity: 0.5;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading.active {
            display: flex;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--orcamento-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            padding: 20px;
            background-color: var(--secondary-color);
            border-top: 1px solid var(--border-color);
        }

        .footer-left {
            display: flex;
            gap: 10px;
        }

        .footer-right {
            display: flex;
            gap: 10px;
        }

        .type-badge {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }

        .type-MP { background-color: #e3f2fd; color: #1976d2; }
        .type-SP { background-color: #f3e5f5; color: #7b1fa2; }
        .type-PA { background-color: #e8f5e8; color: #388e3c; }
        .type-AC { background-color: #fff3e0; color: #f57c00; }
        .type-SV { background-color: #fce4ec; color: #c2185b; }

        /* Estilos do Modal */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f8f9fa;
            border-radius: 0 0 8px 8px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .budget-table th,
        .budget-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            text-align: left;
        }

        .budget-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-ATIVO {
            background-color: var(--orcamento-color);
            color: white;
        }

        .status-RASCUNHO {
            background-color: var(--warning-color);
            color: white;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success { background-color: var(--orcamento-color); }
        .notification.error { background-color: var(--danger-color); }
        .notification.warning { background-color: var(--warning-color); }
        .notification.info { background-color: var(--primary-color); }

        /* Estilos específicos do orçamento */
        .budget-container {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: #fff;
            margin-bottom: 20px;
        }

        .budget-content {
            padding: 20px;
        }

        .summary-section {
            margin-bottom: 25px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .summary-number {
            font-size: 20px;
            font-weight: bold;
            color: var(--orcamento-color);
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--orcamento-color);
        }

        .component, .operation {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: #fff;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .component:nth-child(even), .operation:nth-child(even) {
            background-color: #f8f9fa;
        }

        .component:hover, .operation:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .sequence-input {
            width: 60px !important;
            text-align: center;
        }

        .quantity-input {
            width: 80px !important;
        }

        .price-input {
            width: 100px !important;
        }

        .total-price {
            width: 100px;
            text-align: right;
            font-weight: bold;
            color: var(--orcamento-color);
        }

        .operation-select {
            width: 150px !important;
        }

        .resource-select {
            width: 150px !important;
        }

        .time-input {
            width: 80px !important;
        }

        .hour-value-input {
            width: 100px !important;
        }

        .operation-total {
            width: 100px;
            text-align: right;
            font-weight: bold;
            color: var(--orcamento-color);
        }

        .description-input {
            flex: 1;
            min-width: 150px;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .status-badge {
            background-color: var(--warning-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .components-list, .operations-list {
            min-height: 100px;
            border: 2px dashed var(--border-color);
            border-radius: 4px;
            padding: 10px;
            transition: border-color 0.2s;
        }

        .components-list:empty::before {
            content: "Arraste componentes aqui ou use a busca ao lado";
            color: var(--text-secondary);
            font-style: italic;
            display: block;
            text-align: center;
            padding: 20px;
        }

        .operations-list:empty::before {
            content: "Clique em 'Adicionar Operação' para incluir processos de fabricação";
            color: var(--text-secondary);
            font-style: italic;
            display: block;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="loading-spinner"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-calculator"></i>
                Orçamento de Estruturas
            </h1>
            <div class="user-info">
                <div>Usuário: <span id="currentUserName">-</span></div>
                <div class="date-time-info" id="currentDateTime">-</div>
            </div>
        </div>

        <div class="main-content">
            <div class="search-panel">
                <div class="panel-title">
                    <i class="fas fa-search"></i> Buscar Produtos
                </div>

                <div class="filters-section">
                    <div class="form-group">
                        <label for="clienteSelect">
                            <i class="fas fa-user"></i> Cliente
                        </label>
                        <select id="clienteSelect">
                            <option value="">Selecione o cliente...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="numeroOrcamentoExterno">
                            <i class="fas fa-hashtag"></i> Número do Orçamento (Vendas)
                        </label>
                        <input type="text" id="numeroOrcamentoExterno" placeholder="Ex: ORC-2506-0001" onchange="createBudgetFromExternal()">
                    </div>

                    <div class="form-group">
                        <label for="descricaoProdutoServico">
                            <i class="fas fa-tag"></i> Descrição do Produto/Serviço
                        </label>
                        <input type="text" id="descricaoProdutoServico" placeholder="Descreva o produto ou serviço a ser orçado..." onchange="updateBudgetCalculations()">
                    </div>

                    <div class="form-group">
                        <label for="quantidadeOrcamento">
                            <i class="fas fa-hashtag"></i> Quantidade
                        </label>
                        <input type="number" id="quantidadeOrcamento" value="1" min="1" step="1" onchange="updateBudgetCalculations()">
                    </div>
                </div>

                <!-- Seção de Detalhes do Serviço -->
                <div class="service-details-section" style="margin-top: 20px; padding: 20px; border: 1px solid var(--border-color); border-radius: 6px; background-color: #f8f9fa;">
                    <h4 style="margin: 0 0 15px 0; color: var(--primary-color); display: flex; align-items: center;">
                        <i class="fas fa-tools" style="margin-right: 8px;"></i>
                        Detalhes do Serviço
                    </h4>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                        <div class="form-group">
                            <label for="servicoDetalhado">
                                <i class="fas fa-file-alt"></i> Descrição Detalhada do Serviço
                            </label>
                            <textarea id="servicoDetalhado" rows="4" placeholder="Descreva detalhadamente o serviço a ser prestado..." onchange="updateBudgetCalculations()"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="numeroSerieMaquina">
                                <i class="fas fa-barcode"></i> Número de Série da Máquina
                            </label>
                            <input type="text" id="numeroSerieMaquina" placeholder="Ex: ABC123456789" onchange="updateBudgetCalculations()">
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div class="form-group">
                            <label for="custoFrete">
                                <i class="fas fa-truck"></i> Custo com Frete (R$)
                            </label>
                            <input type="number" id="custoFrete" value="0" min="0" step="0.01" placeholder="0,00" onchange="updateBudgetCalculations()">
                        </div>

                        <div class="form-group">
                            <label for="horasAssistencia">
                                <i class="fas fa-clock"></i> Horas de Assistência Técnica
                            </label>
                            <input type="number" id="horasAssistencia" value="0" min="0" step="0.5" placeholder="0" onchange="updateBudgetCalculations()">
                        </div>

                        <div class="form-group">
                            <label for="valorHoraAssistencia">
                                <i class="fas fa-dollar-sign"></i> Valor/Hora Assistência (R$)
                            </label>
                            <input type="number" id="valorHoraAssistencia" value="80.00" min="0" step="0.01" placeholder="80,00" onchange="updateBudgetCalculations()">
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div class="form-group">
                            <label for="custoViagem">
                                <i class="fas fa-plane"></i> Custo com Viagem (R$)
                            </label>
                            <input type="number" id="custoViagem" value="0" min="0" step="0.01" placeholder="0,00" onchange="updateBudgetCalculations()">
                        </div>

                        <div class="form-group">
                            <label for="custoTransporte">
                                <i class="fas fa-car"></i> Custo com Transporte (R$)
                            </label>
                            <input type="number" id="custoTransporte" value="0" min="0" step="0.01" placeholder="0,00" onchange="updateBudgetCalculations()">
                        </div>

                        <div class="form-group">
                            <label for="horasTreinamento">
                                <i class="fas fa-graduation-cap"></i> Horas de Treinamento
                            </label>
                            <input type="number" id="horasTreinamento" value="0" min="0" step="0.5" placeholder="0" onchange="updateBudgetCalculations()">
                        </div>

                        <div class="form-group">
                            <label for="valorHoraTreinamento">
                                <i class="fas fa-chalkboard-teacher"></i> Valor/Hora Treinamento (R$)
                            </label>
                            <input type="number" id="valorHoraTreinamento" value="120.00" min="0" step="0.01" placeholder="120,00" onchange="updateBudgetCalculations()">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="searchInput">
                        <i class="fas fa-search"></i> Buscar Componentes
                    </label>
                    <input type="text" id="searchInput" placeholder="Digite código ou descrição..." oninput="searchProducts()">
                </div>

                <div class="product-list" id="productList">
                    <!-- Lista de produtos será carregada aqui -->
                </div>

                <div class="btn-group">
                    <button class="btn btn-primary" onclick="openProductModal()">
                        <i class="fas fa-plus"></i> Novo Produto
                    </button>
                    <button class="btn btn-info" onclick="openBudgetListModal()">
                        <i class="fas fa-search"></i> Buscar Orçamentos
                    </button>
                </div>
            </div>

            <div class="structure-panel">
                <div class="panel-title">
                    <i class="fas fa-calculator"></i> Estrutura do Orçamento
                </div>

                <div id="budgetTree">
                    <!-- Estrutura do orçamento será carregada aqui -->
                </div>
            </div>
        </div>

        <div class="footer-actions">
            <div class="footer-left">
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
                <button class="btn btn-primary" onclick="openHistoryModal()">
                    <i class="fas fa-history"></i> Histórico
                </button>
            </div>
            <div class="footer-right">
                <button class="btn btn-warning" onclick="generatePDF()">
                    <i class="fas fa-file-pdf"></i> Gerar PDF
                </button>
                <button class="btn btn-success" onclick="saveBudget()" disabled>
                    <i class="fas fa-save"></i> Salvar Orçamento
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para Buscar Orçamentos -->
    <div id="budgetListModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1000px; width: 90%;">
            <div class="modal-header">
                <h3><i class="fas fa-search"></i> Buscar Orçamentos Salvos</h3>
                <span class="close" onclick="closeBudgetListModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 20px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div class="form-group">
                            <label for="searchBudgetNumber">
                                <i class="fas fa-hashtag"></i> Número do Orçamento
                            </label>
                            <input type="text" id="searchBudgetNumber" placeholder="Ex: ORC-2025-0001" oninput="filterBudgets()">
                        </div>
                        <div class="form-group">
                            <label for="searchBudgetClient">
                                <i class="fas fa-user"></i> Cliente
                            </label>
                            <select id="searchBudgetClient" onchange="filterBudgets()">
                                <option value="">Todos os clientes</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchBudgetStatus">
                                <i class="fas fa-flag"></i> Status
                            </label>
                            <select id="searchBudgetStatus" onchange="filterBudgets()">
                                <option value="">Todos os status</option>
                                <option value="RASCUNHO">Rascunho</option>
                                <option value="ATIVO">Ativo</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="budget-list-container" style="max-height: 400px; overflow-y: auto;">
                    <table class="budget-table" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: var(--primary-color); color: white;">
                                <th style="padding: 12px; text-align: left;">Número</th>
                                <th style="padding: 12px; text-align: left;">Cliente</th>
                                <th style="padding: 12px; text-align: left;">Produto</th>
                                <th style="padding: 12px; text-align: center;">Status</th>
                                <th style="padding: 12px; text-align: right;">Valor Total</th>
                                <th style="padding: 12px; text-align: center;">Data</th>
                                <th style="padding: 12px; text-align: center;">Ações</th>
                            </tr>
                        </thead>
                        <tbody id="budgetListBody">
                            <!-- Lista de orçamentos será carregada aqui -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeBudgetListModal()">
                    <i class="fas fa-times"></i> Fechar
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        // Importações do Firebase
        import { db } from './firebase-config.js';
        import { 
            collection, 
            doc, 
            getDocs, 
            addDoc, 
            updateDoc, 
            deleteDoc, 
            query, 
            orderBy, 
            where,
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let produtos = [];
        let clientes = [];
        let operacoes = [];
        let recursos = [];
        let estruturas = [];
        let orcamentos = [];
        let currentBudget = null;
        let hasChanges = false;
        let isNavigatingAway = false;

        // Inicialização
        window.onload = async function() {
            document.querySelector('.loading').classList.add('active');
            
            try {
                await loadInitialData();
                updateDateTime();
                setInterval(updateDateTime, 1000);
                
                document.getElementById('currentUserName').textContent = currentUser?.nome || 'Sistema';
                
            } catch (error) {
                console.error('Erro ao carregar dados iniciais:', error);
                showNotification('Erro ao carregar dados do sistema', 'error');
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        // Carregar dados iniciais
        async function loadInitialData() {
            try {
                const [produtosSnap, fornecedoresSnap, operacoesSnap, recursosSnap, estruturasSnap, orcamentosSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "operacoes")),
                    getDocs(collection(db, "recursos")),
                    getDocs(collection(db, "estruturas")),
                    getDocs(collection(db, "orcamentos"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar fornecedores que são clientes (tipo "cliente" ou "ambos")
                const todosFornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log('Total fornecedores carregados:', todosFornecedores.length);
                console.log('Fornecedores:', todosFornecedores);

                // Filtrar com diferentes possibilidades de campo tipo
                clientes = todosFornecedores.filter(fornecedor => {
                    const tipo = fornecedor.tipo || fornecedor.tipoFornecedor || fornecedor.categoria || '';
                    const tipoLower = tipo.toLowerCase();

                    return tipoLower === 'cliente' ||
                           tipoLower === 'ambos' ||
                           tipoLower === 'both' ||
                           tipoLower === 'cliente/fornecedor' ||
                           tipoLower.includes('cliente');
                });

                console.log('Clientes filtrados:', clientes.length);
                console.log('Clientes:', clientes);

                // Se não encontrou nenhum cliente, usar todos os fornecedores como fallback
                if (clientes.length === 0 && todosFornecedores.length > 0) {
                    console.log('Nenhum cliente encontrado, usando todos os fornecedores como fallback');
                    clientes = todosFornecedores;
                }

                operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                orcamentos = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                populateClienteSelect();

                // Carregar alguns produtos iniciais para facilitar a busca
                loadInitialProducts();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                throw error;
            }
        }

        // Função para atualizar data/hora
        function updateDateTime() {
            const now = new Date();
            const dateTimeString = now.toLocaleString('pt-BR');
            document.getElementById('currentDateTime').textContent = dateTimeString;
        }

        // Função para mostrar notificações
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Função para popular select de clientes
        function populateClienteSelect() {
            console.log('Iniciando populateClienteSelect...');
            console.log('Clientes disponíveis:', clientes);

            const select = document.getElementById('clienteSelect');
            if (!select) {
                console.error('Elemento clienteSelect não encontrado!');
                return;
            }

            select.innerHTML = '<option value="">Selecione o cliente...</option>';

            if (clientes.length === 0) {
                console.log('Nenhum cliente encontrado');
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'Nenhum cliente encontrado no cadastro de fornecedores';
                option.disabled = true;
                select.appendChild(option);
                return;
            }

            // Ordenar clientes por nome/razão social
            const clientesOrdenados = [...clientes].sort((a, b) => {
                const nomeA = (a.razaoSocial || a.nomeFantasia || a.nome || '').toUpperCase();
                const nomeB = (b.razaoSocial || b.nomeFantasia || b.nome || '').toUpperCase();
                return nomeA.localeCompare(nomeB);
            });

            console.log('Clientes ordenados:', clientesOrdenados);

            clientesOrdenados.forEach((cliente, index) => {
                console.log(`Processando cliente ${index + 1}:`, cliente);

                const option = document.createElement('option');
                option.value = cliente.id;

                // Priorizar razão social, depois nome fantasia, depois nome
                const nomeExibicao = cliente.razaoSocial || cliente.nomeFantasia || cliente.nome || 'Sem nome';
                const documento = cliente.cnpj || cliente.cpf || '';
                const tipoIndicador = cliente.tipo === 'ambos' ? ' (Cliente/Fornecedor)' : ' (Cliente)';

                const textoCompleto = `${nomeExibicao}${documento ? ' - ' + documento : ''}${tipoIndicador}`;
                option.textContent = textoCompleto;

                console.log(`Adicionando opção: ${textoCompleto} (ID: ${cliente.id})`);
                select.appendChild(option);
            });

            console.log(`Total de ${clientesOrdenados.length} clientes adicionados ao select`);
        }



        // Função para criar orçamento a partir de número externo
        async function createBudgetFromExternal() {
            const numeroExterno = document.getElementById('numeroOrcamentoExterno').value.trim();
            const descricao = document.getElementById('descricaoProdutoServico').value.trim();
            const budgetTree = document.getElementById('budgetTree');

            budgetTree.innerHTML = '';

            if (!numeroExterno) {
                return;
            }

            try {
                document.querySelector('.loading').classList.add('active');

                // Criar objeto produto virtual
                const produtoVirtual = {
                    id: 'virtual_' + Date.now(),
                    codigo: numeroExterno,
                    descricao: descricao || 'Produto/Serviço do orçamento ' + numeroExterno,
                    tipo: 'ORCAMENTO',
                    unidade: 'UN'
                };

                currentBudget = {
                    clienteId: document.getElementById('clienteSelect').value,
                    numeroOrcamentoExterno: numeroExterno,
                    descricaoProdutoServico: descricao,
                    quantidade: parseInt(document.getElementById('quantidadeOrcamento').value) || 1,
                    componentes: [],
                    operacoes: [],
                    valorTotal: 0,
                    dataCriacao: new Date(),
                    status: 'RASCUNHO'
                };

                const container = createBudgetContainer(produtoVirtual, null);
                budgetTree.appendChild(container);
                setupDragAndDrop();
                updateBudgetCalculations();

                // Habilitar botão salvar
                document.querySelector('.btn-success').disabled = false;
                hasChanges = true;

                showNotification(`Orçamento criado para ${numeroExterno}`, 'success');
            } catch (error) {
                console.error('Erro ao criar orçamento:', error);
                showNotification('Erro ao criar orçamento', 'error');
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        }

        // Função para criar container do orçamento
        function createBudgetContainer(produto, estrutura = null) {
            const container = document.createElement('div');
            container.className = 'budget-container';
            container.dataset.id = produto.id;

            const numeroOrcamento = generateBudgetNumber();
            const numeroExterno = document.getElementById('numeroOrcamentoExterno').value || produto.codigo;

            container.innerHTML = `
                <div class="orcamento-info">
                    <h3>
                        <i class="fas fa-calculator"></i>
                        Orçamento Interno: ${numeroOrcamento} | Vendas: ${numeroExterno}
                        <span class="type-badge type-${produto.tipo}">${produto.tipo}</span>
                    </h3>
                    <div style="margin-top: 8px; padding: 8px; background-color: #f0f8ff; border-radius: 4px; border-left: 4px solid var(--primary-color);">
                        <strong>Descrição:</strong> ${produto.descricao}
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
                        <div><strong>Cliente:</strong> <span id="clienteInfo">-</span></div>
                        <div><strong>Quantidade:</strong> <span id="quantidadeInfo">1</span></div>
                        <div><strong>Valor Total:</strong> <span id="valorTotalInfo">R$ 0,00</span></div>
                        <div><strong>Status:</strong> <span class="status-badge">RASCUNHO</span></div>
                    </div>
                </div>

                <div class="budget-content">
                    <div class="summary-section">
                        <div class="section-title" style="margin-bottom: 15px;">
                            <i class="fas fa-chart-bar"></i> Resumo do Orçamento
                        </div>
                        <div class="summary-grid" id="budgetSummary">
                            <div class="summary-card">
                                <div class="summary-number" id="componentsCount">0</div>
                                <div class="summary-label">Componentes</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="operationsCount">0</div>
                                <div class="summary-label">Operações</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="materialCost">R$ 0,00</div>
                                <div class="summary-label">Custo Material</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="laborCost">R$ 0,00</div>
                                <div class="summary-label">Custo Mão de Obra</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="serviceCost">R$ 0,00</div>
                                <div class="summary-label">Custo Serviços</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-cubes"></i> Componentes do Orçamento
                            </div>
                        </div>
                        <div class="components-list" id="componentsList"></div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-cogs"></i> Operações e Mão de Obra
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="addOperation(this)">
                                <i class="fas fa-plus"></i> Adicionar Operação
                            </button>
                        </div>
                        <div class="operations-list" id="operationsList"></div>
                    </div>
                </div>
            `;

            return container;
        }

        // Função para gerar número do orçamento
        function generateBudgetNumber() {
            const year = new Date().getFullYear();
            const existingNumbers = orcamentos
                .filter(o => o.numero && o.numero.startsWith(`ORC-${year}`))
                .map(o => parseInt(o.numero.split('-')[2]) || 0);

            const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
            return `ORC-${year}-${String(nextNumber).padStart(4, '0')}`;
        }

        // Função para popular orçamento com dados da estrutura
        function populateBudgetWithData(container, estrutura) {
            const componentsList = container.querySelector('#componentsList');
            const operationsList = container.querySelector('#operationsList');

            // Adicionar componentes
            if (estrutura.componentes) {
                estrutura.componentes.forEach((comp, index) => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        const component = createBudgetComponentElement(produto, comp.quantidade, index + 1);
                        componentsList.appendChild(component);
                    }
                });
            }

            // Adicionar operações
            if (estrutura.operacoes) {
                estrutura.operacoes.forEach(op => {
                    const operation = createBudgetOperationElement(op);
                    operationsList.appendChild(operation);
                });
            }

            updateBudgetSummary(container);
        }

        // Função para criar elemento de componente do orçamento
        function createBudgetComponentElement(produto, quantidade = 1, sequence = 1) {
            const component = document.createElement('div');
            component.className = 'component';
            component.dataset.id = produto.id;

            // Buscar preço do produto com prioridade: precoVenda > custoMedio > precoCompra > 0
            const precoUnitario = produto.precoVenda || produto.custoMedio || produto.precoCompra || produto.custoUltimo || 0;
            const precoTotal = precoUnitario * quantidade;

            component.innerHTML = `
                <input type="number" class="sequence-input" value="${sequence}" readonly>
                <div style="flex: 1; display: flex; flex-direction: column; min-width: 200px;">
                    <div style="display: flex; align-items: center; margin-bottom: 2px;">
                        <strong style="color: var(--primary-color);">${produto.codigo}</strong>
                        <span class="type-badge type-${produto.tipo}" style="margin-left: 8px;">${produto.tipo}</span>
                    </div>
                    <div style="font-size: 12px; color: #666; line-height: 1.2;">${produto.descricao}</div>
                </div>
                <div style="display: flex; flex-direction: column; align-items: center; min-width: 80px;">
                    <label style="font-size: 10px; color: #888; margin-bottom: 2px;">Qtd</label>
                    <input type="number" class="quantity-input" value="${quantidade}" min="0.001" step="0.001" onchange="updateBudgetCalculations()" style="width: 70px; text-align: center;">
                </div>
                <div style="display: flex; flex-direction: column; align-items: center; min-width: 60px;">
                    <label style="font-size: 10px; color: #888; margin-bottom: 2px;">Un</label>
                    <span style="font-size: 12px; color: #666;">${produto.unidade}</span>
                </div>
                <div style="display: flex; flex-direction: column; align-items: center; min-width: 100px;">
                    <label style="font-size: 10px; color: #888; margin-bottom: 2px;">Preço Unit.</label>
                    <input type="number" class="price-input" value="${precoUnitario.toFixed(2)}" min="0" step="0.01" placeholder="0,00" onchange="updateBudgetCalculations()" style="width: 90px; text-align: center;">
                </div>
                <div style="display: flex; flex-direction: column; align-items: center; min-width: 100px;">
                    <label style="font-size: 10px; color: #888; margin-bottom: 2px;">Total</label>
                    <span class="total-price" style="font-weight: bold; color: var(--orcamento-color);">R$ ${precoTotal.toFixed(2)}</span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-danger btn-sm" onclick="removeComponent(this)" title="Remover componente">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            return component;
        }

        // Função para criar elemento de operação do orçamento
        function createBudgetOperationElement(operationData) {
            const operation = document.createElement('div');
            operation.className = 'operation';

            const sortedOperacoes = [...operacoes].sort((a, b) => a.numero.localeCompare(b.numero));
            const sortedRecursos = [...recursos].sort((a, b) => a.codigo.localeCompare(b.codigo));

            // Valor padrão por hora (pode vir de configuração)
            const valorHoraPadrao = operationData.valorHora || 50.00;
            const tempo = operationData.tempo || 1;
            const valorTotal = valorHoraPadrao * tempo;

            operation.innerHTML = `
                <input type="number" class="sequence-input" value="${operationData.sequencia || 1}">
                <select class="operation-select" onchange="updateBudgetCalculations()">
                    <option value="">Selecione...</option>
                    ${sortedOperacoes.map(op => `
                        <option value="${op.id}" ${op.id === operationData.operacaoId ? 'selected' : ''}>
                            ${op.numero} - ${op.operacao}
                        </option>
                    `).join('')}
                </select>
                <select class="resource-select" onchange="updateBudgetCalculations()">
                    <option value="">Selecione...</option>
                    ${sortedRecursos.map(rec => `
                        <option value="${rec.id}" ${rec.id === operationData.recursoId ? 'selected' : ''}>
                            ${rec.codigo} - ${rec.maquina}
                        </option>
                    `).join('')}
                </select>
                <input type="number" class="time-input" value="${tempo}" min="0.1" step="0.1" placeholder="Tempo (h)" onchange="updateBudgetCalculations()">
                <input type="number" class="hour-value-input" value="${valorHoraPadrao.toFixed(2)}" min="0" step="0.01" placeholder="Valor/hora" onchange="updateBudgetCalculations()">
                <span class="operation-total">R$ ${valorTotal.toFixed(2)}</span>
                <input type="text" class="description-input" value="${operationData.descricao || ''}" placeholder="Descrição">
                <div class="action-buttons">
                    <button class="btn btn-secondary btn-sm" onclick="moveOperation(this, -1)">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="moveOperation(this, 1)">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="removeOperation(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            return operation;
        }

        // Função para atualizar cálculos do orçamento
        function updateBudgetCalculations() {
            const container = document.querySelector('.budget-container');
            if (!container) return;

            const quantidade = parseInt(document.getElementById('quantidadeOrcamento').value) || 1;
            const components = container.querySelectorAll('.component');
            const operations = container.querySelectorAll('.operation');

            let custoMaterial = 0;
            let custoMaoObra = 0;
            let custoServicos = 0;

            // Calcular custo dos materiais
            components.forEach(comp => {
                const qty = parseFloat(comp.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(comp.querySelector('.price-input').value) || 0;
                const total = qty * price * quantidade;

                comp.querySelector('.total-price').textContent = `R$ ${total.toFixed(2)}`;
                custoMaterial += total;
            });

            // Calcular custo da mão de obra
            operations.forEach(op => {
                const tempo = parseFloat(op.querySelector('.time-input').value) || 0;
                const valorHora = parseFloat(op.querySelector('.hour-value-input').value) || 0;
                const total = tempo * valorHora * quantidade;

                op.querySelector('.operation-total').textContent = `R$ ${total.toFixed(2)}`;
                custoMaoObra += total;
            });

            // Calcular custos de serviços adicionais
            const custoFrete = parseFloat(document.getElementById('custoFrete')?.value) || 0;
            const horasAssistencia = parseFloat(document.getElementById('horasAssistencia')?.value) || 0;
            const valorHoraAssistencia = parseFloat(document.getElementById('valorHoraAssistencia')?.value) || 0;
            const custoViagem = parseFloat(document.getElementById('custoViagem')?.value) || 0;
            const custoTransporte = parseFloat(document.getElementById('custoTransporte')?.value) || 0;
            const horasTreinamento = parseFloat(document.getElementById('horasTreinamento')?.value) || 0;
            const valorHoraTreinamento = parseFloat(document.getElementById('valorHoraTreinamento')?.value) || 0;

            const custoAssistencia = horasAssistencia * valorHoraAssistencia;
            const custoTreinamento = horasTreinamento * valorHoraTreinamento;

            custoServicos = custoFrete + custoAssistencia + custoViagem + custoTransporte + custoTreinamento;

            const valorTotal = custoMaterial + custoMaoObra + custoServicos;

            // Atualizar resumo
            updateBudgetSummary(container, {
                componentsCount: components.length,
                operationsCount: operations.length,
                materialCost: custoMaterial,
                laborCost: custoMaoObra,
                serviceCost: custoServicos,
                totalValue: valorTotal
            });

            // Atualizar informações do orçamento
            const clienteSelect = document.getElementById('clienteSelect');
            const clienteInfo = clienteSelect.options[clienteSelect.selectedIndex]?.text || 'Não selecionado';
            const statusBadge = container.querySelector('.status-badge');

            container.querySelector('#clienteInfo').textContent = clienteInfo;
            container.querySelector('#quantidadeInfo').textContent = quantidade;
            container.querySelector('#valorTotalInfo').textContent = `R$ ${valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

            // Atualizar status baseado no cliente
            if (clienteSelect.value) {
                statusBadge.textContent = 'ATIVO';
                statusBadge.style.backgroundColor = 'var(--orcamento-color)';
            } else {
                statusBadge.textContent = 'RASCUNHO';
                statusBadge.style.backgroundColor = 'var(--warning-color)';
            }

            hasChanges = true;
        }

        // Função para atualizar resumo do orçamento
        function updateBudgetSummary(container, summary = null) {
            if (!summary) {
                const components = container.querySelectorAll('.component');
                const operations = container.querySelectorAll('.operation');

                summary = {
                    componentsCount: components.length,
                    operationsCount: operations.length,
                    materialCost: 0,
                    laborCost: 0,
                    serviceCost: 0,
                    totalValue: 0
                };
            }

            container.querySelector('#componentsCount').textContent = summary.componentsCount;
            container.querySelector('#operationsCount').textContent = summary.operationsCount;
            container.querySelector('#materialCost').textContent = `R$ ${summary.materialCost.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
            container.querySelector('#laborCost').textContent = `R$ ${summary.laborCost.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
            container.querySelector('#serviceCost').textContent = `R$ ${(summary.serviceCost || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        }

        // Função para carregar produtos iniciais
        function loadInitialProducts() {
            const productList = document.getElementById('productList');

            // Mostrar os 10 produtos mais comuns (MP, SP, AC) ordenados por código
            const initialProducts = produtos
                .filter(p => ['MP', 'SP', 'AC'].includes(p.tipo))
                .sort((a, b) => a.codigo.localeCompare(b.codigo))
                .slice(0, 10);

            if (initialProducts.length === 0) {
                productList.innerHTML = '<div style="padding: 15px; text-align: center; color: #666; font-style: italic;">Use a busca acima para encontrar componentes...</div>';
                return;
            }

            productList.innerHTML = '';
            initialProducts.forEach(produto => {
                const item = document.createElement('div');
                item.className = 'product-item';
                item.draggable = true;
                item.dataset.id = produto.id;

                const preco = produto.precoVenda || produto.custoMedio || produto.precoCompra || 0;
                const precoFormatado = preco > 0 ? `R$ ${preco.toFixed(2)}` : 'Sem preço';

                item.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: var(--primary-color);">${produto.codigo}</div>
                            <div style="font-size: 12px; color: #666; margin-top: 2px;">${produto.descricao}</div>
                            <div style="margin-top: 4px;">
                                <span class="type-badge type-${produto.tipo}">${produto.tipo}</span>
                                <span style="margin-left: 8px; font-size: 11px; color: #888;">${produto.unidade}</span>
                            </div>
                        </div>
                        <div style="text-align: right; margin-left: 10px;">
                            <div style="font-weight: bold; color: var(--orcamento-color);">${precoFormatado}</div>
                            <div style="font-size: 10px; color: #888;">Arraste →</div>
                        </div>
                    </div>
                `;
                productList.appendChild(item);
            });
        }

        // Função para buscar produtos
        function searchProducts() {
            const searchInput = document.getElementById('searchInput');
            const productList = document.getElementById('productList');

            if (!searchInput || !productList) {
                console.warn('Elementos de busca não encontrados');
                return;
            }

            const searchTerm = searchInput.value.toUpperCase();
            productList.innerHTML = '';

            if (searchTerm.length < 2) {
                productList.innerHTML = '<div style="padding: 15px; text-align: center; color: #666; font-style: italic;">Digite pelo menos 2 caracteres para buscar...</div>';
                return;
            }

            // Obter ID do produto pai atual (se houver orçamento ativo)
            const currentContainer = document.querySelector('.budget-container');
            const parentProductId = currentContainer ? currentContainer.dataset.id : null;

            const filteredProducts = produtos.filter(p => {
                if (!p || !p.id) return false;

                const codigo = p.codigo || '';
                const descricao = p.descricao || '';
                const tipo = p.tipo || '';

                const matchesSearch = codigo.toUpperCase().includes(searchTerm) ||
                                    descricao.toUpperCase().includes(searchTerm);

                const isNotExcluded = !['PA', 'SV'].includes(tipo);
                const isNotParent = p.id !== parentProductId;

                return matchesSearch && isNotExcluded && isNotParent;
            }).slice(0, 20);

            if (filteredProducts.length === 0) {
                productList.innerHTML = '<div style="padding: 15px; text-align: center; color: #666; font-style: italic;">Nenhum produto encontrado...</div>';
                return;
            }

            filteredProducts.forEach(produto => {
                if (!produto || !produto.id) return;

                const item = document.createElement('div');
                item.className = 'product-item';
                item.draggable = true;
                item.dataset.id = produto.id;

                // Buscar preço do produto (prioridade: precoVenda > custoMedio > precoCompra)
                const preco = produto.precoVenda || produto.custoMedio || produto.precoCompra || 0;
                const precoFormatado = preco > 0 ? `R$ ${preco.toFixed(2)}` : 'Sem preço';

                // Valores seguros com fallbacks
                const codigo = produto.codigo || 'SEM CÓDIGO';
                const descricao = produto.descricao || 'Sem descrição';
                const tipo = produto.tipo || 'N/A';
                const unidade = produto.unidade || 'UN';

                item.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: var(--primary-color);">${codigo}</div>
                            <div style="font-size: 12px; color: #666; margin-top: 2px;">${descricao}</div>
                            <div style="margin-top: 4px;">
                                <span class="type-badge type-${tipo}">${tipo}</span>
                                <span style="margin-left: 8px; font-size: 11px; color: #888;">${unidade}</span>
                            </div>
                        </div>
                        <div style="text-align: right; margin-left: 10px;">
                            <div style="font-weight: bold; color: var(--orcamento-color);">${precoFormatado}</div>
                            <div style="font-size: 10px; color: #888;">Arraste →</div>
                        </div>
                    </div>
                `;
                productList.appendChild(item);
            });
        }

        // Função para configurar drag and drop
        function setupDragAndDrop() {
            document.addEventListener('dragstart', handleDragStart);
            document.addEventListener('dragend', handleDragEnd);
            document.addEventListener('dragover', handleDragOver);
            document.addEventListener('drop', handleDrop);

            function handleDragStart(e) {
                if (e.target.classList.contains('product-item')) {
                    e.target.classList.add('dragging');
                    e.dataTransfer.setData('text/plain', e.target.dataset.id);
                }
            }

            function handleDragEnd(e) {
                if (e.target.classList.contains('product-item')) {
                    e.target.classList.remove('dragging');
                }
            }

            function handleDragOver(e) {
                if (e.target.closest('.budget-container')) {
                    e.preventDefault();
                }
            }

            function handleDrop(e) {
                if (e.target.closest('.budget-container')) {
                    e.preventDefault();
                    const container = e.target.closest('.budget-container');
                    const productId = e.dataTransfer.getData('text/plain');
                    const produto = produtos.find(p => p.id === productId);
                    if (produto) {
                        addComponentToBudget(container, produto);
                    }
                }
            }
        }

        // Função para adicionar componente ao orçamento
        function addComponentToBudget(container, produto) {
            const componentsList = container.querySelector('#componentsList');
            const existingComponent = componentsList.querySelector(`.component[data-id="${produto.id}"]`);

            if (existingComponent) {
                showNotification('Este componente já foi adicionado ao orçamento', 'warning');
                return;
            }

            const sequence = componentsList.querySelectorAll('.component').length + 1;
            const component = createBudgetComponentElement(produto, 1, sequence);
            componentsList.appendChild(component);

            updateBudgetCalculations();
            hasChanges = true;
        }

        // Função para adicionar operação
        function addOperation(button) {
            const container = button.closest('.budget-container');
            const operationsList = container.querySelector('#operationsList');

            const operationData = {
                sequencia: operationsList.querySelectorAll('.operation').length + 1,
                operacaoId: '',
                recursoId: '',
                tempo: 1,
                valorHora: 50.00,
                descricao: ''
            };

            const operation = createBudgetOperationElement(operationData);
            operationsList.appendChild(operation);

            updateBudgetCalculations();
            hasChanges = true;
        }

        // Função para remover componente
        function removeComponent(button) {
            const container = button.closest('.budget-container');
            button.closest('.component').remove();
            updateBudgetCalculations();
            hasChanges = true;
        }

        // Função para remover operação
        function removeOperation(button) {
            const container = button.closest('.budget-container');
            button.closest('.operation').remove();
            updateBudgetCalculations();
            hasChanges = true;
        }

        // Função para mover operação
        function moveOperation(button, direction) {
            const operation = button.closest('.operation');
            const operationsList = operation.parentElement;
            const operations = Array.from(operationsList.querySelectorAll('.operation'));
            const index = operations.indexOf(operation);
            const newIndex = index + direction;

            if (newIndex >= 0 && newIndex < operations.length) {
                if (direction === 1) {
                    operationsList.insertBefore(operations[newIndex], operation);
                } else {
                    operationsList.insertBefore(operation, operations[newIndex]);
                }
                hasChanges = true;
            }
        }

        // Função para salvar orçamento
        async function saveBudget() {
            const container = document.querySelector('.budget-container');
            if (!container) {
                showNotification('Selecione um produto para criar um orçamento', 'error');
                return;
            }

            // Permitir salvar sem cliente (como rascunho)
            const clienteId = document.getElementById('clienteSelect').value;
            const isRascunho = !clienteId;

            if (isRascunho) {
                showNotification('Salvando orçamento como rascunho (sem cliente definido)', 'info');
            }

            try {
                document.querySelector('.loading').classList.add('active');

                const productId = container.dataset.id;
                const quantidade = parseInt(document.getElementById('quantidadeOrcamento').value) || 1;
                const numeroOrcamentoExterno = document.getElementById('numeroOrcamentoExterno').value || '';
                const descricaoProdutoServico = document.getElementById('descricaoProdutoServico').value || '';
                const components = [];
                const operations = [];

                // Coletar componentes
                container.querySelectorAll('.component').forEach(comp => {
                    const componentId = comp.dataset.id;
                    const qty = parseFloat(comp.querySelector('.quantity-input').value) || 1;
                    const price = parseFloat(comp.querySelector('.price-input').value) || 0;

                    if (qty > 0) {
                        const produto = produtos.find(p => p.id === componentId);
                        components.push({
                            componentId,
                            quantidade: qty,
                            precoUnitario: price,
                            precoTotal: qty * price * quantidade,
                            unidade: produto.unidade
                        });
                    }
                });

                // Coletar operações
                container.querySelectorAll('.operation').forEach((op, index) => {
                    const sequencia = parseInt(op.querySelector('.sequence-input').value) || index + 1;
                    const operacaoId = op.querySelector('.operation-select').value;
                    const recursoId = op.querySelector('.resource-select').value;
                    const tempo = parseFloat(op.querySelector('.time-input').value) || 1;
                    const valorHora = parseFloat(op.querySelector('.hour-value-input').value) || 0;
                    const descricao = op.querySelector('.description-input').value;

                    if (operacaoId && recursoId && tempo > 0) {
                        operations.push({
                            sequencia,
                            operacaoId,
                            recursoId,
                            tempo,
                            valorHora,
                            valorTotal: tempo * valorHora * quantidade,
                            descricao: descricao || ''
                        });
                    }
                });

                const now = new Date();
                const numeroOrcamento = generateBudgetNumber();

                // Coletar dados de serviços
                const servicoDetalhado = document.getElementById('servicoDetalhado')?.value || '';
                const numeroSerieMaquina = document.getElementById('numeroSerieMaquina')?.value || '';
                const custoFrete = parseFloat(document.getElementById('custoFrete')?.value) || 0;
                const horasAssistencia = parseFloat(document.getElementById('horasAssistencia')?.value) || 0;
                const valorHoraAssistencia = parseFloat(document.getElementById('valorHoraAssistencia')?.value) || 0;
                const custoViagem = parseFloat(document.getElementById('custoViagem')?.value) || 0;
                const custoTransporte = parseFloat(document.getElementById('custoTransporte')?.value) || 0;
                const horasTreinamento = parseFloat(document.getElementById('horasTreinamento')?.value) || 0;
                const valorHoraTreinamento = parseFloat(document.getElementById('valorHoraTreinamento')?.value) || 0;

                // Calcular valores totais
                const custoMaterial = components.reduce((sum, comp) => sum + comp.precoTotal, 0);
                const custoMaoObra = operations.reduce((sum, op) => sum + op.valorTotal, 0);
                const custoAssistencia = horasAssistencia * valorHoraAssistencia;
                const custoTreinamento = horasTreinamento * valorHoraTreinamento;
                const custoServicos = custoFrete + custoAssistencia + custoViagem + custoTransporte + custoTreinamento;
                const valorTotal = custoMaterial + custoMaoObra + custoServicos;

                const budgetData = {
                    numero: numeroOrcamento,
                    numeroOrcamentoExterno: numeroOrcamentoExterno,
                    descricaoProdutoServico: descricaoProdutoServico,
                    clienteId: clienteId || null,
                    produtoId: productId,
                    quantidade: quantidade,
                    componentes: components,
                    operacoes: operations,
                    custoMaterial: custoMaterial,
                    custoMaoObra: custoMaoObra,
                    custoServicos: custoServicos,
                    valorTotal: valorTotal,
                    // Dados detalhados do serviço
                    detalhesServico: {
                        servicoDetalhado: servicoDetalhado,
                        numeroSerieMaquina: numeroSerieMaquina,
                        custoFrete: custoFrete,
                        assistenciaTecnica: {
                            horas: horasAssistencia,
                            valorHora: valorHoraAssistencia,
                            custoTotal: custoAssistencia
                        },
                        custoViagem: custoViagem,
                        custoTransporte: custoTransporte,
                        treinamento: {
                            horas: horasTreinamento,
                            valorHora: valorHoraTreinamento,
                            custoTotal: custoTreinamento
                        }
                    },
                    status: isRascunho ? 'RASCUNHO' : 'ATIVO',
                    dataCriacao: Timestamp.fromDate(now),
                    dataUltimaAlteracao: Timestamp.fromDate(now),
                    usuarioCriacao: {
                        id: currentUser?.id || 'sistema',
                        nome: currentUser?.nome || 'Sistema',
                        email: currentUser?.email || '<EMAIL>'
                    }
                };

                const docRef = await addDoc(collection(db, "orcamentos"), budgetData);
                orcamentos.push({ id: docRef.id, ...budgetData });

                const statusMessage = isRascunho ?
                    `Orçamento ${numeroOrcamento} salvo como rascunho!` :
                    `Orçamento ${numeroOrcamento} salvo com sucesso!`;

                showNotification(statusMessage, 'success');
                hasChanges = false;
                document.querySelector('.btn-success').disabled = true;

            } catch (error) {
                console.error("Erro ao salvar orçamento:", error);
                showNotification("Erro ao salvar orçamento: " + error.message, 'error');
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        }

        // Função para gerar PDF
        function generatePDF() {
            const container = document.querySelector('.budget-container');
            if (!container) {
                showNotification('Nenhum orçamento carregado para gerar PDF', 'error');
                return;
            }

            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Configurações
                const pageWidth = doc.internal.pageSize.width;
                const pageHeight = doc.internal.pageSize.height;
                const margin = 20;
                let yPosition = margin;

                // Cabeçalho
                doc.setFontSize(20);
                doc.setFont(undefined, 'bold');
                doc.text('ORÇAMENTO DE ESTRUTURA', pageWidth / 2, yPosition, { align: 'center' });
                yPosition += 15;

                // Informações do orçamento
                const clienteInfo = container.querySelector('#clienteInfo').textContent;
                const quantidadeInfo = container.querySelector('#quantidadeInfo').textContent;
                const valorTotalInfo = container.querySelector('#valorTotalInfo').textContent;
                const numeroExterno = document.getElementById('numeroOrcamentoExterno').value;
                const descricaoProduto = document.getElementById('descricaoProdutoServico').value;

                doc.setFontSize(12);
                doc.setFont(undefined, 'normal');

                doc.text(`Cliente: ${clienteInfo}`, margin, yPosition);
                yPosition += 8;
                if (numeroExterno) {
                    doc.text(`Orçamento Vendas: ${numeroExterno}`, margin, yPosition);
                    yPosition += 8;
                }
                doc.text(`Produto/Serviço: ${descricaoProduto || 'Não especificado'}`, margin, yPosition);
                yPosition += 8;
                doc.text(`Quantidade: ${quantidadeInfo}`, margin, yPosition);
                yPosition += 8;
                doc.text(`Data: ${new Date().toLocaleDateString('pt-BR')}`, margin, yPosition);
                yPosition += 15;

                // Componentes
                const components = container.querySelectorAll('.component');
                if (components.length > 0) {
                    doc.setFont(undefined, 'bold');
                    doc.text('COMPONENTES:', margin, yPosition);
                    yPosition += 10;

                    doc.setFont(undefined, 'normal');
                    doc.setFontSize(10);

                    // Cabeçalho da tabela
                    doc.text('Seq', margin, yPosition);
                    doc.text('Código', margin + 20, yPosition);
                    doc.text('Descrição', margin + 60, yPosition);
                    doc.text('Qtd', margin + 120, yPosition);
                    doc.text('Preço Unit.', margin + 140, yPosition);
                    doc.text('Total', margin + 170, yPosition);
                    yPosition += 8;

                    // Linha separadora
                    doc.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
                    yPosition += 5;

                    components.forEach((comp, index) => {
                        if (yPosition > pageHeight - 30) {
                            doc.addPage();
                            yPosition = margin;
                        }

                        const produtoComp = produtos.find(p => p.id === comp.dataset.id);
                        const qty = comp.querySelector('.quantity-input').value;
                        const price = comp.querySelector('.price-input').value;
                        const total = comp.querySelector('.total-price').textContent;

                        doc.text((index + 1).toString(), margin, yPosition);
                        doc.text(produtoComp?.codigo || '', margin + 20, yPosition);
                        doc.text((produtoComp?.descricao || '').substring(0, 30), margin + 60, yPosition);
                        doc.text(qty, margin + 120, yPosition);
                        doc.text(`R$ ${parseFloat(price).toFixed(2)}`, margin + 140, yPosition);
                        doc.text(total, margin + 170, yPosition);
                        yPosition += 6;
                    });

                    yPosition += 10;
                }

                // Operações
                const operations = container.querySelectorAll('.operation');
                if (operations.length > 0) {
                    if (yPosition > pageHeight - 50) {
                        doc.addPage();
                        yPosition = margin;
                    }

                    doc.setFontSize(12);
                    doc.setFont(undefined, 'bold');
                    doc.text('OPERAÇÕES:', margin, yPosition);
                    yPosition += 10;

                    doc.setFont(undefined, 'normal');
                    doc.setFontSize(10);

                    // Cabeçalho da tabela
                    doc.text('Seq', margin, yPosition);
                    doc.text('Operação', margin + 20, yPosition);
                    doc.text('Recurso', margin + 80, yPosition);
                    doc.text('Tempo (h)', margin + 120, yPosition);
                    doc.text('Valor/h', margin + 150, yPosition);
                    doc.text('Total', margin + 170, yPosition);
                    yPosition += 8;

                    // Linha separadora
                    doc.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);
                    yPosition += 5;

                    operations.forEach((op, index) => {
                        if (yPosition > pageHeight - 30) {
                            doc.addPage();
                            yPosition = margin;
                        }

                        const operacaoId = op.querySelector('.operation-select').value;
                        const recursoId = op.querySelector('.resource-select').value;
                        const operacao = operacoes.find(o => o.id === operacaoId);
                        const recurso = recursos.find(r => r.id === recursoId);
                        const tempo = op.querySelector('.time-input').value;
                        const valorHora = op.querySelector('.hour-value-input').value;
                        const total = op.querySelector('.operation-total').textContent;

                        doc.text((index + 1).toString(), margin, yPosition);
                        doc.text((operacao?.operacao || '').substring(0, 25), margin + 20, yPosition);
                        doc.text((recurso?.maquina || '').substring(0, 15), margin + 80, yPosition);
                        doc.text(tempo, margin + 120, yPosition);
                        doc.text(`R$ ${parseFloat(valorHora).toFixed(2)}`, margin + 150, yPosition);
                        doc.text(total, margin + 170, yPosition);
                        yPosition += 6;
                    });

                    yPosition += 10;
                }

                // Detalhes do Serviço
                const servicoDetalhado = document.getElementById('servicoDetalhado')?.value;
                const numeroSerieMaquina = document.getElementById('numeroSerieMaquina')?.value;

                if (servicoDetalhado || numeroSerieMaquina) {
                    if (yPosition > pageHeight - 60) {
                        doc.addPage();
                        yPosition = margin;
                    }

                    doc.setFontSize(12);
                    doc.setFont(undefined, 'bold');
                    doc.text('DETALHES DO SERVIÇO:', margin, yPosition);
                    yPosition += 10;

                    doc.setFont(undefined, 'normal');
                    doc.setFontSize(10);

                    if (numeroSerieMaquina) {
                        doc.text(`Número de Série da Máquina: ${numeroSerieMaquina}`, margin, yPosition);
                        yPosition += 6;
                    }

                    if (servicoDetalhado) {
                        doc.text('Descrição do Serviço:', margin, yPosition);
                        yPosition += 6;

                        // Quebrar texto longo em múltiplas linhas
                        const lines = doc.splitTextToSize(servicoDetalhado, pageWidth - 2 * margin);
                        lines.forEach(line => {
                            if (yPosition > pageHeight - 20) {
                                doc.addPage();
                                yPosition = margin;
                            }
                            doc.text(line, margin + 5, yPosition);
                            yPosition += 5;
                        });
                    }
                    yPosition += 10;
                }

                // Resumo final
                if (yPosition > pageHeight - 60) {
                    doc.addPage();
                    yPosition = margin;
                }

                doc.setFontSize(12);
                doc.setFont(undefined, 'bold');
                doc.text('RESUMO DO ORÇAMENTO:', margin, yPosition);
                yPosition += 10;

                const materialCost = container.querySelector('#materialCost').textContent;
                const laborCost = container.querySelector('#laborCost').textContent;
                const serviceCost = container.querySelector('#serviceCost').textContent;

                doc.setFont(undefined, 'normal');
                doc.text(`Custo de Material: ${materialCost}`, margin, yPosition);
                yPosition += 8;
                doc.text(`Custo de Mão de Obra: ${laborCost}`, margin, yPosition);
                yPosition += 8;
                doc.text(`Custo de Serviços: ${serviceCost}`, margin, yPosition);
                yPosition += 8;

                doc.setFont(undefined, 'bold');
                doc.setFontSize(14);
                doc.text(`VALOR TOTAL: ${valorTotalInfo}`, margin, yPosition);

                // Salvar PDF
                const numeroOrcamento = generateBudgetNumber();
                doc.save(`Orcamento_${numeroOrcamento}_${new Date().toISOString().split('T')[0]}.pdf`);

                showNotification('PDF gerado com sucesso!', 'success');

            } catch (error) {
                console.error('Erro ao gerar PDF:', error);
                showNotification('Erro ao gerar PDF: ' + error.message, 'error');
            }
        }

        // Função para abrir modal de produto (placeholder)
        function openProductModal() {
            showNotification('Modal de cadastro de produto em desenvolvimento', 'info');
        }

        // Função para abrir modal de histórico (placeholder)
        function openHistoryModal() {
            showNotification('Modal de histórico em desenvolvimento', 'info');
        }

        // Função para abrir modal de busca de orçamentos
        function openBudgetListModal() {
            const modal = document.getElementById('budgetListModal');
            modal.style.display = 'flex';

            // Carregar clientes no filtro
            populateBudgetClientFilter();

            // Carregar lista de orçamentos
            loadBudgetList();
        }

        // Função para fechar modal de busca de orçamentos
        function closeBudgetListModal() {
            const modal = document.getElementById('budgetListModal');
            modal.style.display = 'none';
        }

        // Função para popular filtro de clientes no modal
        function populateBudgetClientFilter() {
            const select = document.getElementById('searchBudgetClient');
            select.innerHTML = '<option value="">Todos os clientes</option>';

            const clientesUnicos = [...new Set(orcamentos
                .filter(orc => orc.clienteId)
                .map(orc => orc.clienteId)
            )];

            clientesUnicos.forEach(clienteId => {
                const cliente = clientes.find(c => c.id === clienteId);
                if (cliente) {
                    const option = document.createElement('option');
                    option.value = clienteId;
                    option.textContent = cliente.razaoSocial || cliente.nomeFantasia || cliente.nome || 'Cliente sem nome';
                    select.appendChild(option);
                }
            });
        }

        // Função para carregar lista de orçamentos
        function loadBudgetList() {
            const tbody = document.getElementById('budgetListBody');
            tbody.innerHTML = '';

            if (orcamentos.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 30px; color: #666; font-style: italic;">
                            <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                            Nenhum orçamento encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            // Ordenar orçamentos por data (mais recente primeiro)
            const orcamentosOrdenados = [...orcamentos].sort((a, b) => {
                const dataA = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao);
                const dataB = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao);
                return dataB - dataA;
            });

            orcamentosOrdenados.forEach(orcamento => {
                const row = createBudgetRow(orcamento);
                tbody.appendChild(row);
            });
        }

        // Função para criar linha da tabela de orçamentos
        function createBudgetRow(orcamento) {
            const row = document.createElement('tr');

            // Buscar dados relacionados
            const cliente = clientes.find(c => c.id === orcamento.clienteId);

            const clienteNome = cliente ?
                (cliente.razaoSocial || cliente.nomeFantasia || cliente.nome || 'Cliente sem nome') :
                'Sem cliente';

            // Usar descrição do produto/serviço ou buscar no cadastro como fallback
            const produtoNome = orcamento.descricaoProdutoServico ||
                (orcamento.produtoId ?
                    (() => {
                        const produto = produtos.find(p => p.id === orcamento.produtoId);
                        return produto ? `${produto.codigo} - ${produto.descricao}` : 'Produto não encontrado';
                    })() :
                    'Produto/Serviço não especificado'
                );

            // Mostrar número do orçamento externo se disponível
            const numeroExibicao = orcamento.numeroOrcamentoExterno ?
                `${orcamento.numero} (${orcamento.numeroOrcamentoExterno})` :
                orcamento.numero;

            const dataFormatada = orcamento.dataCriacao?.toDate ?
                orcamento.dataCriacao.toDate().toLocaleDateString('pt-BR') :
                new Date(orcamento.dataCriacao).toLocaleDateString('pt-BR');

            const valorFormatado = orcamento.valorTotal?.toLocaleString('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }) || 'R$ 0,00';

            row.innerHTML = `
                <td style="font-weight: bold; color: var(--primary-color);">${numeroExibicao}</td>
                <td>${clienteNome}</td>
                <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${produtoNome}">${produtoNome}</td>
                <td style="text-align: center;">
                    <span class="status-badge status-${orcamento.status}">${orcamento.status}</span>
                </td>
                <td style="text-align: right; font-weight: bold;">${valorFormatado}</td>
                <td style="text-align: center;">${dataFormatada}</td>
                <td style="text-align: center;">
                    <button class="btn btn-primary btn-sm" onclick="loadBudget('${orcamento.id}')" title="Carregar orçamento">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-success btn-sm" onclick="duplicateBudget('${orcamento.id}')" title="Duplicar orçamento">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteBudget('${orcamento.id}')" title="Excluir orçamento">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            return row;
        }

        // Função para filtrar orçamentos
        function filterBudgets() {
            const numeroFilter = document.getElementById('searchBudgetNumber').value.toUpperCase();
            const clienteFilter = document.getElementById('searchBudgetClient').value;
            const statusFilter = document.getElementById('searchBudgetStatus').value;

            const tbody = document.getElementById('budgetListBody');
            const rows = tbody.querySelectorAll('tr');

            rows.forEach(row => {
                if (row.cells.length === 1) return; // Pular linha de "nenhum orçamento"

                const numero = row.cells[0].textContent.toUpperCase();
                const clienteText = row.cells[1].textContent;
                const status = row.querySelector('.status-badge')?.textContent || '';

                const numeroMatch = !numeroFilter || numero.includes(numeroFilter);
                const clienteMatch = !clienteFilter || (clienteFilter && clienteText !== 'Sem cliente');
                const statusMatch = !statusFilter || status === statusFilter;

                row.style.display = (numeroMatch && clienteMatch && statusMatch) ? '' : 'none';
            });
        }

        // Função para carregar um orçamento existente
        async function loadBudget(budgetId) {
            try {
                const orcamento = orcamentos.find(o => o.id === budgetId);
                if (!orcamento) {
                    showNotification('Orçamento não encontrado', 'error');
                    return;
                }

                // Fechar modal
                closeBudgetListModal();

                // Limpar orçamento atual
                document.getElementById('budgetTree').innerHTML = '';

                // Carregar dados do orçamento
                document.getElementById('clienteSelect').value = orcamento.clienteId || '';
                document.getElementById('numeroOrcamentoExterno').value = orcamento.numeroOrcamentoExterno || '';
                document.getElementById('descricaoProdutoServico').value = orcamento.descricaoProdutoServico || '';
                document.getElementById('quantidadeOrcamento').value = orcamento.quantidade || 1;

                // Carregar detalhes do serviço se existirem
                if (orcamento.detalhesServico) {
                    const detalhes = orcamento.detalhesServico;
                    document.getElementById('servicoDetalhado').value = detalhes.servicoDetalhado || '';
                    document.getElementById('numeroSerieMaquina').value = detalhes.numeroSerieMaquina || '';
                    document.getElementById('custoFrete').value = detalhes.custoFrete || 0;
                    document.getElementById('horasAssistencia').value = detalhes.assistenciaTecnica?.horas || 0;
                    document.getElementById('valorHoraAssistencia').value = detalhes.assistenciaTecnica?.valorHora || 80;
                    document.getElementById('custoViagem').value = detalhes.custoViagem || 0;
                    document.getElementById('custoTransporte').value = detalhes.custoTransporte || 0;
                    document.getElementById('horasTreinamento').value = detalhes.treinamento?.horas || 0;
                    document.getElementById('valorHoraTreinamento').value = detalhes.treinamento?.valorHora || 120;
                }

                // Criar estrutura do orçamento
                await createBudgetFromExternal();

                // Carregar componentes salvos
                if (orcamento.componentes && orcamento.componentes.length > 0) {
                    const container = document.querySelector('.budget-container');
                    if (container) {
                        const componentsList = container.querySelector('#componentsList');
                        if (componentsList) {
                            orcamento.componentes.forEach((comp, index) => {
                                const produto = produtos.find(p => p.id === comp.componentId);
                                if (produto) {
                                    const component = createBudgetComponentElement(produto, comp.quantidade, index + 1);
                                    // Atualizar preço salvo
                                    const priceInput = component.querySelector('.price-input');
                                    if (priceInput) {
                                        priceInput.value = comp.precoUnitario.toFixed(2);
                                    }
                                    componentsList.appendChild(component);
                                }
                            });
                        }
                    }
                }

                // Carregar operações salvas
                if (orcamento.operacoes && orcamento.operacoes.length > 0) {
                    const container = document.querySelector('.budget-container');
                    if (container) {
                        const operationsList = container.querySelector('#operationsList');
                        if (operationsList) {
                            orcamento.operacoes.forEach(op => {
                                const operation = createBudgetOperationElement(op);
                                operationsList.appendChild(operation);
                            });
                        }
                    }
                }

                // Atualizar cálculos
                updateBudgetCalculations();

                showNotification(`Orçamento ${orcamento.numero} carregado com sucesso!`, 'success');

            } catch (error) {
                console.error('Erro ao carregar orçamento:', error);
                showNotification('Erro ao carregar orçamento: ' + error.message, 'error');
            }
        }

        // Função para duplicar um orçamento
        async function duplicateBudget(budgetId) {
            try {
                const orcamento = orcamentos.find(o => o.id === budgetId);
                if (!orcamento) {
                    showNotification('Orçamento não encontrado', 'error');
                    return;
                }

                // Carregar o orçamento primeiro
                await loadBudget(budgetId);

                // Limpar campos que devem ser únicos
                const novoNumero = generateBudgetNumber();

                showNotification(`Orçamento duplicado! Novo número: ${novoNumero}`, 'info');

            } catch (error) {
                console.error('Erro ao duplicar orçamento:', error);
                showNotification('Erro ao duplicar orçamento: ' + error.message, 'error');
            }
        }

        // Função para excluir um orçamento
        async function deleteBudget(budgetId) {
            try {
                const orcamento = orcamentos.find(o => o.id === budgetId);
                if (!orcamento) {
                    showNotification('Orçamento não encontrado', 'error');
                    return;
                }

                if (!confirm(`Tem certeza que deseja excluir o orçamento ${orcamento.numero}?\n\nEsta ação não pode ser desfeita.`)) {
                    return;
                }

                // Excluir do Firebase
                await deleteDoc(doc(db, "orcamentos", budgetId));

                // Remover da lista local
                const index = orcamentos.findIndex(o => o.id === budgetId);
                if (index > -1) {
                    orcamentos.splice(index, 1);
                }

                // Recarregar lista
                loadBudgetList();

                showNotification(`Orçamento ${orcamento.numero} excluído com sucesso!`, 'success');

            } catch (error) {
                console.error('Erro ao excluir orçamento:', error);
                showNotification('Erro ao excluir orçamento: ' + error.message, 'error');
            }
        }

        // Função de teste para verificar dados
        window.testFirebaseData = function() {
            console.log('=== TESTE DE DADOS FIREBASE ===');
            console.log('Produtos:', produtos.length);
            console.log('Clientes:', clientes.length);
            console.log('Operações:', operacoes.length);
            console.log('Recursos:', recursos.length);
            console.log('Estruturas:', estruturas.length);
            console.log('Orçamentos:', orcamentos.length);

            if (clientes.length > 0) {
                console.log('Primeiro cliente:', clientes[0]);
            }

            // Recarregar select de clientes
            populateClienteSelect();
        };

        // Exportar funções globais
        window.createBudgetFromExternal = createBudgetFromExternal;
        window.updateBudgetCalculations = updateBudgetCalculations;
        window.searchProducts = searchProducts;
        window.addOperation = addOperation;
        window.removeComponent = removeComponent;
        window.removeOperation = removeOperation;
        window.moveOperation = moveOperation;
        window.openProductModal = openProductModal;
        window.openHistoryModal = openHistoryModal;
        window.generatePDF = generatePDF;
        window.saveBudget = saveBudget;
        window.openBudgetListModal = openBudgetListModal;
        window.closeBudgetListModal = closeBudgetListModal;
        window.filterBudgets = filterBudgets;
        window.loadBudget = loadBudget;
        window.duplicateBudget = duplicateBudget;
        window.deleteBudget = deleteBudget;
    </script>
</body>
</html>
