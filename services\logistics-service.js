import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc,
    getDocs,
    updateDoc,
    addDoc,
    query,
    where,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class LogisticsService {
    static async calculateShippingCost(order, destination) {
        try {
            // Get shipping parameters
            const paramsDoc = await getDoc(doc(db, "parametros", "logistica"));
            const params = paramsDoc.data() || {};
            
            // Basic validation
            if (!order || !destination) {
                throw new Error("Invalid order or destination data");
            }

            // Calculate total weight and volume
            let totalWeight = 0;
            let totalVolume = 0;
            
            for (const item of order.itens) {
                const productDoc = await getDoc(doc(db, "produtos", item.produtoId));
                const product = productDoc.data();
                
                totalWeight += (product.pesoUnitario || 0) * item.quantidade;
                totalVolume += (product.volumeUnitario || 0) * item.quantidade;
            }

            // Get shipping rates based on destination
            const ratesQuery = query(
                collection(db, "tabelaFretes"),
                where("regiaoDestino", "==", destination.regiao),
                where("ativo", "==", true)
            );
            const ratesSnap = await getDocs(ratesQuery);
            const rates = ratesSnap.docs.map(doc => doc.data());

            // Calculate costs for each available carrier
            const shippingOptions = rates.map(rate => ({
                carrier: rate.transportadora,
                cost: this.calculateCost(rate, totalWeight, totalVolume, destination),
                estimatedDays: rate.prazoEntrega,
                restrictions: rate.restricoes || []
            }));

            return {
                totalWeight,
                totalVolume,
                options: shippingOptions.sort((a, b) => a.cost - b.cost)
            };
        } catch (error) {
            console.error("Error calculating shipping cost:", error);
            throw error;
        }
    }

    static calculateCost(rate, weight, volume, destination) {
        const baseRate = rate.valorBase || 0;
        const weightRate = (rate.valorKg || 0) * weight;
        const volumeRate = (rate.valorM3 || 0) * volume;
        const distanceRate = (rate.valorKm || 0) * (destination.distancia || 0);
        
        return baseRate + weightRate + volumeRate + distanceRate;
    }

    static async trackDelivery(deliveryId) {
        try {
            const deliveryDoc = await getDoc(doc(db, "entregas", deliveryId));
            if (!deliveryDoc.exists()) {
                throw new Error("Delivery not found");
            }

            const delivery = deliveryDoc.data();
            const events = await this.getDeliveryEvents(deliveryId);

            return {
                ...delivery,
                events: events.sort((a, b) => b.timestamp.seconds - a.timestamp.seconds)
            };
        } catch (error) {
            console.error("Error tracking delivery:", error);
            throw error;
        }
    }

    static async getDeliveryEvents(deliveryId) {
        const eventsQuery = query(
            collection(db, "eventosEntrega"),
            where("entregaId", "==", deliveryId)
        );
        const eventsSnap = await getDocs(eventsQuery);
        return eventsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }

    static async createDelivery(order, shippingOption) {
        try {
            const delivery = {
                pedidoId: order.id,
                transportadora: shippingOption.carrier,
                status: 'AGUARDANDO_COLETA',
                valorFrete: shippingOption.cost,
                prazoEstimado: shippingOption.estimatedDays,
                dataCriacao: Timestamp.now(),
                eventos: [{
                    tipo: 'CRIACAO',
                    descricao: 'Entrega criada',
                    timestamp: Timestamp.now()
                }]
            };

            const deliveryRef = await addDoc(collection(db, "entregas"), delivery);
            
            // Update order with delivery information
            await updateDoc(doc(db, "pedidosVenda", order.id), {
                entregaId: deliveryRef.id,
                statusEntrega: 'AGUARDANDO_COLETA'
            });

            return deliveryRef.id;
        } catch (error) {
            console.error("Error creating delivery:", error);
            throw error;
        }
    }

    static async updateDeliveryStatus(deliveryId, status, details = {}) {
        try {
            const deliveryRef = doc(db, "entregas", deliveryId);
            const deliveryDoc = await getDoc(deliveryRef);
            
            if (!deliveryDoc.exists()) {
                throw new Error("Delivery not found");
            }

            const delivery = deliveryDoc.data();
            const event = {
                tipo: status,
                descricao: details.description || status,
                localizacao: details.location,
                timestamp: Timestamp.now()
            };

            await updateDoc(deliveryRef, {
                status: status,
                eventos: [...delivery.eventos, event],
                ultimaAtualizacao: Timestamp.now()
            });

            // Update order status
            if (delivery.pedidoId) {
                await updateDoc(doc(db, "pedidosVenda", delivery.pedidoId), {
                    statusEntrega: status
                });
            }

            return true;
        } catch (error) {
            console.error("Error updating delivery status:", error);
            throw error;
        }
    }
} 