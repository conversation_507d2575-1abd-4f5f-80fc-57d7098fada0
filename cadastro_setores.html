
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 Cadastro de Setores</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* ========================================
           🎨 CSS PADRONIZADO - CADASTRO SETORES
           Baseado em: gestao_compras_integrada.html
           ======================================== */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .form-container {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .form-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
            display: block;
        }

        .required::after {
            content: " *";
            color: #e74c3c;
            font-weight: bold;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .info-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
            font-style: italic;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .search-container {
            margin-bottom: 20px;
        }

        .search-container input {
            width: 100%;
            max-width: 400px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-container input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .status-inactive {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .edit-btn {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .delete-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .edit-btn:hover, .delete-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .notification {
            display: none;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
            border-left: 4px solid;
        }

        .notification.success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .notification.error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .table-container {
                overflow-x: auto;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-building"></i>
                Cadastro de Setores
            </h1>
            <div class="header-actions">
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <div id="notification" class="notification"></div>

            <!-- Formulário de Cadastro -->
            <div class="form-container">
                <h2 class="form-title">
                    <i class="fas fa-plus-circle"></i>
                    Cadastrar Novo Setor
                </h2>
                <form id="sectorForm" onsubmit="registerSector(event)">
                    <input type="hidden" id="editingId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="sectorCode" class="required">Código do Setor</label>
                            <input type="text" id="sectorCode" class="form-control" required maxlength="10">
                            <div class="info-text">
                                <i class="fas fa-info-circle"></i>
                                Código único para identificação do setor
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="sectorName" class="required">Nome do Setor</label>
                            <input type="text" id="sectorName" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="sectorManager">Responsável</label>
                            <input type="text" id="sectorManager" class="form-control">
                            <div class="info-text">
                                <i class="fas fa-user"></i>
                                Nome do responsável pelo setor
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="sectorStatus" class="required">Status</label>
                            <select id="sectorStatus" class="form-control" required>
                                <option value="true">✅ Ativo</option>
                                <option value="false">❌ Inativo</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="sectorDescription">Descrição</label>
                        <textarea id="sectorDescription" class="form-control" rows="3"></textarea>
                        <div class="info-text">
                            <i class="fas fa-file-alt"></i>
                            Informações adicionais sobre o setor
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-success" id="submitButton">
                            <i class="fas fa-save"></i>
                            Cadastrar Setor
                        </button>
                    </div>
                </form>
            </div>

            <!-- Lista de Setores -->
            <div class="form-container">
                <h2 class="form-title">
                    <i class="fas fa-list"></i>
                    Setores Cadastrados
                </h2>

                <div class="search-container">
                    <input type="text" id="searchInput" class="form-control" placeholder="🔍 Pesquisar setores..." oninput="filterSectors()">
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-code"></i>
                                    Código
                                </th>
                                <th>
                                    <i class="fas fa-tag"></i>
                                    Nome
                                </th>
                                <th>
                                    <i class="fas fa-user"></i>
                                    Responsável
                                </th>
                                <th>
                                    <i class="fas fa-toggle-on"></i>
                                    Status
                                </th>
                                <th>
                                    <i class="fas fa-cogs"></i>
                                    Ações
                                </th>
                            </tr>
                        </thead>
                        <tbody id="sectorsTableBody">
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 40px;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    Carregando setores...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs, 
            doc, 
            updateDoc, 
            deleteDoc 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let setores = [];
        let editingId = null;

        window.onload = async function() {
            await loadSectors();
            displaySectors();
        };

        async function loadSectors() {
            try {
                const snapshot = await getDocs(collection(db, "setores"));
                setores = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error("Erro ao carregar setores:", error);
                showNotification("Erro ao carregar setores.", "error");
            }
        }

        function displaySectors(filteredSectors = setores) {
            const tableBody = document.getElementById('sectorsTableBody');
            tableBody.innerHTML = '';

            filteredSectors.forEach(setor => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><i class="fas fa-code"></i> ${setor.codigo}</td>
                    <td><i class="fas fa-tag"></i> ${setor.nome}</td>
                    <td><i class="fas fa-user"></i> ${setor.responsavel || '-'}</td>
                    <td><span class="status-badge ${setor.ativo ? 'status-active' : 'status-inactive'}">
                        <i class="fas fa-${setor.ativo ? 'check-circle' : 'times-circle'}"></i>
                        ${setor.ativo ? 'Ativo' : 'Inativo'}
                    </span></td>
                    <td class="action-buttons">
                        <button class="edit-btn" onclick="editSector('${setor.id}')">
                            <i class="fas fa-edit"></i> Editar
                        </button>
                        <button class="delete-btn" onclick="deleteSector('${setor.id}')">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        window.showNotification = function(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            setTimeout(() => notification.style.display = 'none', 3000);
        };

        window.registerSector = async function(event) {
            event.preventDefault();

            const codigo = document.getElementById('sectorCode').value.toUpperCase();
            const nome = document.getElementById('sectorName').value;
            const responsavel = document.getElementById('sectorManager').value;
            const ativo = document.getElementById('sectorStatus').value === 'true';
            const descricao = document.getElementById('sectorDescription').value;

            if (!editingId) {
                const existingSector = setores.find(s => s.codigo === codigo);
                if (existingSector) {
                    showNotification("Já existe um setor com este código.", "error");
                    return;
                }
            }

            try {
                const setor = {
                    codigo,
                    nome,
                    responsavel,
                    ativo,
                    descricao,
                    dataCadastro: new Date()
                };

                if (editingId) {
                    await updateDoc(doc(db, "setores", editingId), setor);
                    showNotification("Setor atualizado com sucesso!", "success");
                } else {
                    await addDoc(collection(db, "setores"), setor);
                    showNotification("Setor cadastrado com sucesso!", "success");
                }

                await loadSectors();
                displaySectors();
                cancelEdit();
            } catch (error) {
                console.error("Erro ao salvar setor:", error);
                showNotification("Erro ao salvar setor.", "error");
            }
        };

        window.editSector = function(sectorId) {
            const setor = setores.find(s => s.id === sectorId);
            if (setor) {
                editingId = sectorId;
                document.getElementById('sectorCode').value = setor.codigo;
                document.getElementById('sectorName').value = setor.nome;
                document.getElementById('sectorManager').value = setor.responsavel || '';
                document.getElementById('sectorStatus').value = setor.ativo.toString();
                document.getElementById('sectorDescription').value = setor.descricao || '';
                document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Atualizar Setor';
            }
        };

        window.deleteSector = async function(sectorId) {
            if (confirm('Tem certeza que deseja excluir este setor?')) {
                try {
                    await deleteDoc(doc(db, "setores", sectorId));
                    await loadSectors();
                    displaySectors();
                    showNotification("Setor excluído com sucesso!", "success");
                } catch (error) {
                    console.error("Erro ao excluir setor:", error);
                    showNotification("Erro ao excluir setor.", "error");
                }
            }
        };

        window.cancelEdit = function() {
            editingId = null;
            document.getElementById('sectorForm').reset();
            document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Cadastrar Setor';
        };

        window.filterSectors = function() {
            const searchText = document.getElementById('searchInput').value.toLowerCase();
            const filteredSectors = setores.filter(setor => 
                setor.codigo.toLowerCase().includes(searchText) ||
                setor.nome.toLowerCase().includes(searchText) ||
                (setor.responsavel && setor.responsavel.toLowerCase().includes(searchText))
            );
            displaySectors(filteredSectors);
        };
    </script>
</body>
</html>
