<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico de Estoque</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .problem-card {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-card {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success-card {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
        }
        .btn-fix {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-fix:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 Diagnóstico Completo do Estoque</h1>
        
        <div class="problem-card">
            <h3>⚠️ Problemas Identificados</h3>
            <ul>
                <li><strong>Múltiplas coleções:</strong> "estoque" vs "estoques"</li>
                <li><strong>Estruturas diferentes:</strong> "quantidade" vs "saldo"</li>
                <li><strong>Recebimento não atualiza saldos existentes</strong></li>
                <li><strong>Falta de transações atômicas</strong></li>
            </ul>
        </div>

        <div class="form-group">
            <button onclick="diagnosticarEstoque()" class="btn btn-primary">🔍 Executar Diagnóstico</button>
            <button onclick="corrigirProblemas()" class="btn-fix">🔧 Corrigir Problemas</button>
        </div>

        <div id="resultados"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc, 
            addDoc,
            deleteDoc,
            runTransaction,
            query,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let diagnosticoData = {};

        window.diagnosticarEstoque = async function() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<p>🔄 Executando diagnóstico...</p>';

            try {
                // 1. Buscar dados das duas coleções
                const [estoqueSingular, estoquePlural, movimentacoes, produtos, armazens] = await Promise.all([
                    getDocs(collection(db, "estoque")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens"))
                ]);

                diagnosticoData = {
                    estoqueSingular: estoqueSingular.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    estoquePlural: estoquePlural.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    movimentacoes: movimentacoes.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    produtos: produtos.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    armazens: armazens.docs.map(doc => ({ id: doc.id, ...doc.data() }))
                };

                // 2. Analisar problemas
                const problemas = analisarProblemas();
                
                // 3. Mostrar resultados
                mostrarResultados(problemas);

            } catch (error) {
                console.error('Erro no diagnóstico:', error);
                resultados.innerHTML = `<div class="error-card">❌ Erro: ${error.message}</div>`;
            }
        };

        function analisarProblemas() {
            const problemas = {
                colecoesDuplicadas: diagnosticoData.estoqueSingular.length > 0 && diagnosticoData.estoquePlural.length > 0,
                registrosDuplicados: [],
                estruturasInconsistentes: [],
                movimentacoesSemEstoque: [],
                saldosInconsistentes: []
            };

            // Verificar duplicações
            diagnosticoData.estoqueSingular.forEach(itemSingular => {
                const itemPlural = diagnosticoData.estoquePlural.find(p => 
                    p.produtoId === itemSingular.produtoId && 
                    p.armazemId === itemSingular.armazemId
                );
                
                if (itemPlural) {
                    problemas.registrosDuplicados.push({
                        produtoId: itemSingular.produtoId,
                        armazemId: itemSingular.armazemId,
                        quantidadeSingular: itemSingular.quantidade || itemSingular.saldo || 0,
                        saldoPlural: itemPlural.saldo || 0
                    });
                }
            });

            // Verificar estruturas
            diagnosticoData.estoqueSingular.forEach(item => {
                if (!item.saldo && item.quantidade) {
                    problemas.estruturasInconsistentes.push({
                        id: item.id,
                        problema: 'Usa "quantidade" em vez de "saldo"',
                        coleção: 'estoque'
                    });
                }
            });

            // Verificar movimentações órfãs
            diagnosticoData.movimentacoes.forEach(mov => {
                const temEstoque = diagnosticoData.estoquePlural.some(est => 
                    est.produtoId === mov.produtoId && est.armazemId === mov.armazemId
                );
                
                if (!temEstoque) {
                    problemas.movimentacoesSemEstoque.push({
                        movimentacaoId: mov.id,
                        produtoId: mov.produtoId,
                        armazemId: mov.armazemId,
                        tipo: mov.tipo,
                        quantidade: mov.quantidade
                    });
                }
            });

            return problemas;
        }

        function mostrarResultados(problemas) {
            const resultados = document.getElementById('resultados');
            let html = '<h2>📊 Resultados do Diagnóstico</h2>';

            // Resumo geral
            html += `
                <div class="success-card">
                    <h3>📈 Resumo Geral</h3>
                    <ul>
                        <li><strong>Coleção "estoque":</strong> ${diagnosticoData.estoqueSingular.length} registros</li>
                        <li><strong>Coleção "estoques":</strong> ${diagnosticoData.estoquePlural.length} registros</li>
                        <li><strong>Movimentações:</strong> ${diagnosticoData.movimentacoes.length} registros</li>
                        <li><strong>Produtos:</strong> ${diagnosticoData.produtos.length} registros</li>
                        <li><strong>Armazéns:</strong> ${diagnosticoData.armazens.length} registros</li>
                    </ul>
                </div>
            `;

            // Problemas encontrados
            if (problemas.colecoesDuplicadas) {
                html += `
                    <div class="error-card">
                        <h3>❌ Coleções Duplicadas</h3>
                        <p>Você tem dados nas duas coleções "estoque" e "estoques". Isso causa inconsistências!</p>
                    </div>
                `;
            }

            if (problemas.registrosDuplicados.length > 0) {
                html += `
                    <div class="error-card">
                        <h3>❌ Registros Duplicados (${problemas.registrosDuplicados.length})</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Produto ID</th>
                                    <th>Armazém ID</th>
                                    <th>Qtd "estoque"</th>
                                    <th>Saldo "estoques"</th>
                                    <th>Diferença</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                problemas.registrosDuplicados.slice(0, 10).forEach(dup => {
                    const diferenca = dup.saldoPlural - dup.quantidadeSingular;
                    html += `
                        <tr>
                            <td>${dup.produtoId}</td>
                            <td>${dup.armazemId}</td>
                            <td>${dup.quantidadeSingular}</td>
                            <td>${dup.saldoPlural}</td>
                            <td style="color: ${diferenca !== 0 ? 'red' : 'green'}">${diferenca}</td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                        ${problemas.registrosDuplicados.length > 10 ? `<p>... e mais ${problemas.registrosDuplicados.length - 10} registros</p>` : ''}
                    </div>
                `;
            }

            if (problemas.movimentacoesSemEstoque.length > 0) {
                html += `
                    <div class="problem-card">
                        <h3>⚠️ Movimentações Órfãs (${problemas.movimentacoesSemEstoque.length})</h3>
                        <p>Movimentações que não têm registro correspondente na coleção "estoques"</p>
                    </div>
                `;
            }

            // Dados do recebimento de hoje
            const recebimentosHoje = diagnosticoData.estoqueSingular.filter(item => {
                const dataEntrada = item.dataEntrada;
                if (!dataEntrada) return false;
                
                const hoje = new Date();
                const dataItem = dataEntrada.toDate ? dataEntrada.toDate() : new Date(dataEntrada);
                
                return dataItem.toDateString() === hoje.toDateString();
            });

            if (recebimentosHoje.length > 0) {
                html += `
                    <div class="problem-card">
                        <h3>📦 Recebimentos de Hoje (${recebimentosHoje.length})</h3>
                        <p><strong>PROBLEMA:</strong> Estes itens foram gravados na coleção "estoque" (errada) em vez de "estoques"</p>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Armazém</th>
                                    <th>NF</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                recebimentosHoje.forEach(item => {
                    const produto = diagnosticoData.produtos.find(p => p.id === item.produtoId);
                    html += `
                        <tr>
                            <td>${item.codigo || produto?.codigo || '-'}</td>
                            <td>${item.descricao || produto?.descricao || '-'}</td>
                            <td>${item.quantidade || item.saldo || 0}</td>
                            <td>${item.armazem || item.armazemId || '-'}</td>
                            <td>${item.notaFiscal?.numero || '-'}</td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            resultados.innerHTML = html;
        }

        window.corrigirProblemas = async function() {
            if (!diagnosticoData.estoqueSingular) {
                alert('Execute o diagnóstico primeiro!');
                return;
            }

            if (!confirm('⚠️ ATENÇÃO: Esta operação irá:\n\n1. Migrar dados da coleção "estoque" para "estoques"\n2. Consolidar saldos duplicados\n3. Criar movimentações faltantes\n4. Limpar dados inconsistentes\n\nDeseja continuar?')) {
                return;
            }

            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<p>🔄 Corrigindo problemas...</p>';

            try {
                let corrigidos = 0;
                let erros = 0;

                // Migrar dados da coleção "estoque" para "estoques"
                for (const item of diagnosticoData.estoqueSingular) {
                    try {
                        // Verificar se já existe na coleção correta
                        const existeNaCorreta = diagnosticoData.estoquePlural.find(p => 
                            p.produtoId === item.produtoId && 
                            (p.armazemId === item.armazemId || p.armazemId === item.armazem)
                        );

                        const quantidade = item.quantidade || item.saldo || 0;
                        const armazemId = item.armazemId || item.armazem;

                        if (existeNaCorreta) {
                            // Somar ao saldo existente
                            await updateDoc(doc(db, "estoques", existeNaCorreta.id), {
                                saldo: existeNaCorreta.saldo + quantidade,
                                ultimaMovimentacao: Timestamp.now()
                            });
                        } else {
                            // Criar novo registro na coleção correta
                            await addDoc(collection(db, "estoques"), {
                                produtoId: item.produtoId,
                                armazemId: armazemId,
                                saldo: quantidade,
                                saldoReservado: 0,
                                ultimaMovimentacao: Timestamp.now(),
                                origem: 'MIGRACAO_RECEBIMENTO'
                            });
                        }

                        // Criar movimentação se não existir
                        const temMovimentacao = diagnosticoData.movimentacoes.some(mov => 
                            mov.produtoId === item.produtoId && 
                            mov.numeroDocumento === item.notaFiscal?.numero
                        );

                        if (!temMovimentacao && item.notaFiscal?.numero) {
                            await addDoc(collection(db, "movimentacoesEstoque"), {
                                produtoId: item.produtoId,
                                armazemId: armazemId,
                                tipo: 'ENTRADA',
                                quantidade: quantidade,
                                tipoDocumento: 'COMPRA',
                                numeroDocumento: item.notaFiscal.numero,
                                observacoes: 'Movimentação criada na migração de dados',
                                dataHora: item.dataEntrada || Timestamp.now()
                            });
                        }

                        // Remover da coleção incorreta
                        await deleteDoc(doc(db, "estoque", item.id));
                        
                        corrigidos++;
                    } catch (error) {
                        console.error('Erro ao corrigir item:', item.id, error);
                        erros++;
                    }
                }

                resultados.innerHTML = `
                    <div class="success-card">
                        <h3>✅ Correção Concluída!</h3>
                        <ul>
                            <li><strong>Registros corrigidos:</strong> ${corrigidos}</li>
                            <li><strong>Erros:</strong> ${erros}</li>
                        </ul>
                        <p>Execute o diagnóstico novamente para verificar os resultados.</p>
                    </div>
                `;

            } catch (error) {
                console.error('Erro na correção:', error);
                resultados.innerHTML = `<div class="error-card">❌ Erro na correção: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
