<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema MRP</title>
    <link href="dist/output.css" rel="stylesheet">
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-firestore-compat.js"></script>
    <style>
        .sidebar {
            transition: width 0.3s ease;
            width: 280px;
            background-color: #2d3748; /* Dark gray-blue */
        }
        .sidebar-collapsed {
            width: 50px;
        }
        .sidebar-expanded {
            width: 280px;
        }
        .submenu {
            transition: max-height 0.3s ease, opacity 0.3s ease;
            max-height: 1000px;
            opacity: 1;
        }
        .submenu-collapsed {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
        }
        .category-btn {
            font-weight: bold;
            color: #ffffff;
            font-size: 1.125rem;
            padding: 0.5rem 1rem;
        }
        .subitem {
            padding-left: 2rem;
            color: #a0aec0;
            font-size: 1rem;
            padding: 0.25rem 1rem;
        }
        .subitem:hover {
            background-color: #4a5568;
            color: #ffffff;
        }
        [draggable="true"] {
            cursor: move;
        }
        .drag-over {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .category-item {
            margin-bottom: 0.5rem;
        }
        .hamburger {
            font-size: 1.5rem;
            color: #ffffff;
        }
        .menu-text {
            transition: opacity 0.2s ease;
        }
        .sidebar-collapsed .menu-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-100 flex flex-col h-screen">
    <div class="flex flex-1">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar sidebar-expanded text-white p-4 flex flex-col">
            <div class="flex items-center justify-between mb-6">
                <h1 id="sidebar-title" class="text-xl font-bold menu-text">Sistema MRP</h1>
                <button id="toggle-sidebar" class="hamburger focus:outline-none">☰</button>
            </div>
            <button id="manage-menu-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded mb-6 menu-text">Gerenciar Menu</button>
            <nav id="menu" class="space-y-2 flex-1"></nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <header class="flex justify-between items-center mb-6 bg-white p-4 rounded shadow">
                <div class="text-gray-800">
                    <span>Usuário: <span id="username">-</span></span>
                    <span class="ml-4">Nível: <span id="user-level">-</span></span>
                </div>
                <a href="/logout" class="text-blue-600 hover:underline">Sair</a>
            </header>
            <div id="content" class="bg-white p-6 rounded shadow">
                <h2 class="text-2xl font-semibold mb-4">Bem-vindo ao Sistema MRP</h2>
                <p>Selecione uma opção no menu à esquerda para começar.</p>
                <!-- Menu Management Form (Hidden by Default) -->
                <div id="menu-form" class="hidden mt-6">
                    <h3 class="text-xl font-semibold mb-4">Gerenciar Categorias e Subitens</h3>
                    <div class="mb-4">
                        <h4 class="font-semibold">Adicionar/Editar Categoria</h4>
                        <input id="category-name" type="text" placeholder="Nome da Categoria" class="border p-2 w-full mb-2 rounded">
                        <input id="category-id" type="hidden">
                        <button id="save-category" class="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">Salvar Categoria</button>
                        <button id="clear-category" class="bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700">Limpar</button>
                    </div>
                    <div>
                        <h4 class="font-semibold">Adicionar/Editar Subitem</h4>
                        <select id="subitem-category" class="border p-2 w-full mb-2 rounded">
                            <option value="">Selecione uma Categoria</option>
                        </select>
                        <input id="subitem-name" type="text" placeholder="Nome do Subitem" class="border p-2 w-full mb-2 rounded">
                        <input id="subitem-file" type="text" placeholder="Arquivo HTML (ex: page.html)" class="border p-2 w-full mb-2 rounded">
                        <input id="subitem-id" type="hidden">
                        <button id="save-subitem" class="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">Salvar Subitem</button>
                        <button id="clear-subitem" class="bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700">Limpar</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white text-center p-4">
        Versão 1.0.0 - Sistema MRP HTS Tecnologia © 2025 - Todos os direitos reservados.
    </footer>

    <script>
        // Initialize Firebase (replace with your actual Firebase config)
        const firebaseConfig = {
            apiKey: "AIzaSyCG-VW7vhGZf2EvXzVkssajO10x0krfHCM",
               authDomain: "naliteck-mrp.firebaseapp.com",
               projectId: "naliteck-mrp",
               storageBucket: "naliteck-mrp.firebasestorage.app",
               messagingSenderId: "755022520906",
               appId: "1:755022520906:web:efc8b69186289325c6fcb3",
               measurementId: "G-C7W8FG17KG"
        };
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        // Initialize Firestore with original menu data (run once, then comment out)
        /*
        async function initializeMenu() {
            const menuData = [
                {
                    id: 'financeiro',
                    name: 'Financeiro',
                    order: 0,
                    items: [
                        { name: 'Contas a Pagar', file: 'contas_pagar.html', order: 0 },
                        { name: 'Contas a Receber', file: 'contas_receber.html', order: 1 },
                        { name: 'Condições de Pagamento', file: 'condicoes_pagamento.html', order: 2 },
                        { name: 'Fluxo de Caixa', file: 'fluxo_caixa.html', order: 3 },
                        { name: 'Análise de Crédito', file: 'analise_credito.html', order: 4 },
                        { name: 'Faturamento', file: 'faturamento.html', order: 5 }
                    ]
                },
                {
                    id: 'vendas',
                    name: 'Vendas',
                    order: 1,
                    items: [
                        { name: 'Orçamentos', file: 'orcamentos.html', order: 0 },
                        { name: 'Pedidos de Venda', file: 'pedidos_venda.html', order: 1 },
                        { name: 'Tabela de Preços', file: 'tabela_precos.html', order: 2 },
                        { name: 'Condições Especiais', file: 'condicoes_especiais.html', order: 3 }
                    ]
                },
                {
                    id: 'compras',
                    name: 'Compras',
                    order: 2,
                    items: [
                        { name: 'Solicitação de Compras', file: 'solicitacao_compras.html', order: 0 },
                        { name: 'Cotações', file: 'cotacoes.html', order: 1 },
                        { name: 'Pedidos de Compra', file: 'pedidos_compra.html', order: 2 },
                        { name: 'Necessidades de Compras', file: 'necessidades_compras.html', order: 3 },
                        { name: 'Produtos x OPs', file: 'produtos_ops.html', order: 4 }
                    ]
                },
                {
                    id: 'engenharia',
                    name: 'Engenharia',
                    order: 3,
                    items: [
                        { name: 'Cadastro de Produtos', file: 'cadastro_produtos.html', order: 0 },
                        { name: 'Cadastro de Grupo', file: 'cadastro_grupo.html', order: 1 },
                        { name: 'Cadastro de Família', file: 'cadastro_familia.html', order: 2 },
                        { name: 'Cadastro de Recursos', file: 'cadastro_recursos.html', order: 3 },
                        { name: 'Cadastro de Armazem', file: 'cadastro_armazem.html', order: 4 },
                        { name: 'Estrutura de Produtos', file: 'estrutura_produtos.html', order: 5 },
                        { name: 'Estrutura Drop', file: 'estrutura_drop.html', order: 6 },
                        { name: 'Explosão Estruturas', file: 'explosao_estruturas.html', order: 7 }
                    ]
                },
                {
                    id: 'cadastros',
                    name: 'Cadastros',
                    order: 4,
                    items: [
                        { name: 'Central de Documentos', file: 'central_documentos.html', order: 0 },
                        { name: 'Cadastro de Clientes', file: 'cadastro_clientes.html', order: 1 },
                        { name: 'Cadastro de Operações', file: 'cadastro_operacoes.html', order: 2 },
                        { name: 'Cadastro de Parceiros', file: 'cadastro_parceiros.html', order: 3 },
                        { name: 'Cadastro de Centro de Custo', file: 'cadastro_centro_custo.html', order: 4 },
                        { name: 'Cadastro de Setores', file: 'cadastro_setores.html', order: 5 },
                        { name: 'Cadastro de Usuários', file: 'cadastro_usuarios.html', order: 6 },
                        { name: 'Produtos x Fornecedores', file: 'produtos_fornecedores.html', order: 7 },
                        { name: 'Saldos Iniciais', file: 'saldos_iniciais.html', order: 8 }
                    ]
                },
                {
                    id: 'producao',
                    name: 'Produção',
                    order: 5,
                    items: [
                        { name: 'Ordens de Produção', file: 'ordens_producao.html', order: 0 },
                        { name: 'Explosão Produto', file: 'explosao_produto.html', order: 1 },
                        { name: 'Zerar Ordens', file: 'zerar_ordens.html', order: 2 },
                        { name: 'Alterar OP', file: 'alterar_op.html', order: 3 },
                        { name: 'Registrar Apontamentos', file: 'registrar_apontamentos.html', order: 4 },
                        { name: 'Controle de Estoques', file: 'controle_estoques.html', order: 5 },
                        { name: 'Estorno de Movimentos', file: 'estorno_movimentos.html', order: 6 }
                    ]
                },
                {
                    id: 'almoxarifado',
                    name: 'Almoxarifado',
                    order: 6,
                    items: [
                        { name: 'Movimentação entre Armazéns', file: 'movimentacao_armazens.html', order: 0 },
                        { name: 'Relatório de Inventário', file: 'relatorio_inventario.html', order: 1 }
                    ]
                },
                {
                    id: 'qualidade',
                    name: 'Qualidade',
                    order: 7,
                    items: [
                        { name: 'Especificações de Produtos', file: 'especificacoes_produtos.html', order: 0 },
                        { name: 'Homologação de Fornecedores', file: 'homologacao_fornecedores.html', order: 1 },
                        { name: 'Recebimento de Materiais', file: 'recebimento_materiais.html', order: 2 },
                        { name: 'Inspeção Qualidade', file: 'inspecao_qualidade.html', order: 3 }
                    ]
                },
                {
                    id: 'relatorios',
                    name: 'Relatórios',
                    order: 8,
                    items: [
                        { name: 'Relatório Financeiro', file: 'relatorio_financeiro.html', order: 0 },
                        { name: 'Relatório de Inventário', file: 'relatorio_inventario.html', order: 1 },
                        { name: 'Relatório MRP e Compras', file: 'relatorio_mrp_compras.html', order: 2 },
                        { name: 'Relatório de Estrutura', file: 'relatorio_estrutura.html', order: 3 },
                        { name: 'Relatório de OP (SAP)', file: 'relatorio_op_sap.html', order: 4 },
                        { name: 'Relatório Ordens x Setor', file: 'relatorio_ordens_setor.html', order: 5 },
                        { name: 'Onde é Usado', file: 'onde_usado.html', order: 6 },
                        { name: 'Substituir Material', file: 'substituir_material.html', order: 7 },
                        { name: 'Cópia de Estrutura', file: 'copia_estrutura.html', order: 8 },
                        { name: 'Exportar todas estruturas', file: 'exportar_estruturas.html', order: 9 },
                        { name: 'Relatório de Custo', file: 'relatorio_custo.html', order: 10 },
                        { name: 'Relatório de Inspeções', file: 'relatorio_inspecoes.html', order: 11 },
                        { name: 'Produtos Sem Pai', file: 'produtos_sem_pai.html', order: 12 }
                    ]
                },
                {
                    id: 'configuracoes',
                    name: 'Configurações',
                    order: 9,
                    items: [
                        { name: 'Dados da Empresa', file: 'dados_empresa.html', order: 0 },
                        { name: 'Parâmetros do Sistema', file: 'parametros_sistema.html', order: 1 },
                        { name: 'Backup e Restauração', file: 'backup_restauracao.html', order: 2 },
                        { name: 'Restore do Sistema', file: 'restore_sistema.html', order: 3 },
                        { name: 'Logs do Sistema', file: 'logs_sistema.html', order: 4 },
                        { name: 'Permissões de Usuário', file: 'permissoes_usuario.html', order: 5 },
                        { name: 'Importar CFOPs', file: 'importar_cfops.html', order: 6 },
                        { name: 'Importação de Tabelas', file: 'importacao_tabelas.html', order: 7 }
                    ]
                }
            ];
            const batch = db.batch();
            menuData.forEach(category => {
                batch.set(db.collection('menu').doc(category.id), {
                    name: category.name,
                    order: category.order,
                    items: category.items
                });
            });
            await batch.commit();
            console.log('Menu initialized in Firestore');
        }
        // initializeMenu(); // Uncomment to run once, then comment out
        */

        // Render menu from Firestore
        async function renderMenu() {
            try {
                const menu = document.getElementById('menu');
                menu.innerHTML = '<p class="text-white">Carregando menu...</p>';
                const snapshot = await db.collection('menu').orderBy('order').get();
                if (snapshot.empty) {
                    menu.innerHTML = '<p class="text-yellow-400">Nenhuma categoria encontrada. Adicione uma categoria.</p>';
                    document.getElementById('subitem-category').innerHTML = '<option value="">Selecione uma Categoria</option>';
                    return;
                }
                const menuData = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    items: doc.data().items || []
                }));

                menu.innerHTML = '';
                menuData.forEach(category => {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.setAttribute('draggable', 'true');
                    categoryDiv.setAttribute('data-id', category.id);
                    categoryDiv.classList.add('category-item');
                    categoryDiv.innerHTML = `
                        <div class="flex justify-between items-center">
                            <button class="category-btn w-full text-left hover:bg-gray-700 rounded flex justify-between items-center menu-text">
                                ${category.name}
                                <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <button class="edit-category text-gray-400 hover:text-white ml-2 menu-text" data-id="${category.id}" data-name="${category.name}">✏️</button>
                            <button class="delete-category text-red-400 hover:text-red-600 ml-2 menu-text" data-id="${category.id}" data-name="${category.name}">🗑️</button>
                        </div>
                        <div class="submenu pl-4 space-y-1 ${category.items.length ? '' : 'submenu-collapsed'}">
                            ${category.items.length ? category.items.sort((a, b) => a.order - b.order).map((item, index) => `
                                <div class="flex items-center">
                                    <a href="${item.file}" draggable="true" data-id="${index}" data-category-id="${category.id}" class="subitem block rounded menu-text">${item.name}</a>
                                    <button class="edit-subitem text-gray-400 hover:text-white ml-2 menu-text" data-category-id="${category.id}" data-id="${index}" data-name="${item.name}" data-file="${item.file}">✏️</button>
                                    <button class="delete-subitem text-red-400 hover:text-red-600 ml-2 menu-text" data-category-id="${category.id}" data-id="${index}" data-name="${item.name}">🗑️</button>
                                </div>
                            `).join('') : '<p class="text-gray-400 pl-4 menu-text">Nenhum subitem</p>'}
                        </div>
                    `;
                    menu.appendChild(categoryDiv);
                });

                // Populate category dropdown for subitem form
                const subitemCategory = document.getElementById('subitem-category');
                subitemCategory.innerHTML = '<option value="">Selecione uma Categoria</option>' + 
                    menuData.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('');

                // Add event listeners for category buttons
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const submenu = btn.closest('.flex').nextElementSibling;
                        submenu.classList.toggle('submenu-collapsed');
                        const arrow = btn.querySelector('svg');
                        arrow.classList.toggle('rotate-180');
                        console.log(`Toggled submenu for category: ${btn.textContent.trim()}`);
                    });
                });

                // Drag-and-drop for categories
                document.querySelectorAll('.category-item').forEach(item => {
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', item.dataset.id);
                        item.classList.add('opacity-50');
                        console.log(`Dragging category: ${item.dataset.id}`);
                    });
                    item.addEventListener('dragend', () => {
                        item.classList.remove('opacity-50');
                        console.log(`Drag ended for category: ${item.dataset.id}`);
                    });
                    item.addEventListener('dragover', (e) => e.preventDefault());
                    item.addEventListener('dragenter', () => item.classList.add('drag-over'));
                    item.addEventListener('dragleave', () => item.classList.remove('drag-over'));
                    item.addEventListener('drop', (e) => {
                        e.preventDefault();
                        item.classList.remove('drag-over');
                        const draggedId = e.dataTransfer.getData('text/plain');
                        if (draggedId !== item.dataset.id) {
                            console.log(`Dropping category ${draggedId} onto ${item.dataset.id}`);
                            reorderCategories(draggedId, item.dataset.id);
                        }
                    });
                });

                // Drag-and-drop for subitems
                document.querySelectorAll('.subitem').forEach(item => {
                    item.addEventListener('dragstart', (e) => {
                        const categoryId = item.dataset.categoryId;
                        const subitemId = item.dataset.id;
                        e.dataTransfer.setData('text/plain', `${categoryId}:${subitemId}`);
                        item.classList.add('opacity-50');
                        console.log(`Dragging subitem ${subitemId} in category ${categoryId}`);
                    });
                    item.addEventListener('dragend', () => {
                        item.classList.remove('opacity-50');
                        console.log(`Drag ended for subitem: ${item.dataset.id}`);
                    });
                    item.addEventListener('dragover', (e) => e.preventDefault());
                    item.addEventListener('dragenter', () => item.classList.add('drag-over'));
                    item.addEventListener('dragleave', () => item.classList.remove('drag-over'));
                    item.addEventListener('drop', (e) => {
                        e.preventDefault();
                        item.classList.remove('drag-over');
                        const [catId, subitemId] = e.dataTransfer.getData('text/plain').split(':');
                        if (catId === item.dataset.categoryId && subitemId !== item.dataset.id) {
                            console.log(`Dropping subitem ${subitemId} onto ${item.dataset.id} in category ${catId}`);
                            reorderSubitems(catId, parseInt(subitemId), parseInt(item.dataset.id));
                        }
                    });
                });

                // Edit and delete category buttons
                document.querySelectorAll('.edit-category').forEach(btn => {
                    btn.addEventListener('click', () => {
                        document.getElementById('category-name').value = btn.dataset.name;
                        document.getElementById('category-id').value = btn.dataset.id;
                        document.getElementById('menu-form').classList.remove('hidden');
                        console.log(`Editing category: ${btn.dataset.id}`);
                    });
                });
                document.querySelectorAll('.delete-category').forEach(btn => {
                    btn.addEventListener('click', async () => {
                        if (confirm(`Deseja excluir a categoria "${btn.dataset.name}"?`)) {
                            try {
                                await db.collection('menu').doc(btn.dataset.id).delete();
                                console.log(`Deleted category ${btn.dataset.id}`);
                                renderMenu();
                            } catch (error) {
                                console.error('Error deleting category:', error);
                                alert('Erro ao excluir categoria: ' + error.message);
                            }
                        }
                    });
                });

                // Edit and delete subitem buttons
                document.querySelectorAll('.edit-subitem').forEach(btn => {
                    btn.addEventListener('click', () => {
                        document.getElementById('subitem-category').value = btn.dataset.categoryId;
                        document.getElementById('subitem-name').value = btn.dataset.name;
                        document.getElementById('subitem-file').value = btn.dataset.file;
                        document.getElementById('subitem-id').value = btn.dataset.id;
                        document.getElementById('menu-form').classList.remove('hidden');
                        console.log(`Editing subitem ${btn.dataset.id} in category ${btn.dataset.categoryId}`);
                    });
                });
                document.querySelectorAll('.delete-subitem').forEach(btn => {
                    btn.addEventListener('click', async () => {
                        if (confirm(`Deseja excluir o subitem "${btn.dataset.name}"?`)) {
                            try {
                                const categoryId = btn.dataset.categoryId;
                                const index = parseInt(btn.dataset.id);
                                const doc = await db.collection('menu').doc(categoryId).get();
                                const items = doc.data().items || [];
                                items.splice(index, 1);
                                items.forEach((item, i) => item.order = i);
                                await db.collection('menu').doc(categoryId).update({ items });
                                console.log(`Deleted subitem ${index} in category ${categoryId}`);
                                renderMenu();
                            } catch (error) {
                                console.error('Error deleting subitem:', error);
                                alert('Erro ao excluir subitem: ' + error.message);
                            }
                        }
                    });
                });
            } catch (error) {
                console.error('Error fetching menu:', error);
                document.getElementById('menu').innerHTML = `<p class="text-red-400">Erro ao carregar o menu: ${error.message}</p>`;
            }
        }

        // Reorder categories in Firestore
        async function reorderCategories(draggedId, targetId) {
            try {
                const snapshot = await db.collection('menu').orderBy('order').get();
                const categories = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const draggedIndex = categories.findIndex(cat => cat.id === draggedId);
                const targetIndex = categories.findIndex(cat => cat.id === targetId);
                const [dragged] = categories.splice(draggedIndex, 1);
                categories.splice(targetIndex, 0, dragged);
                const batch = db.batch();
                categories.forEach((cat, index) => {
                    batch.update(db.collection('menu').doc(cat.id), { order: index });
                });
                await batch.commit();
                console.log(`Reordered categories: ${draggedId} to position ${targetIndex}`);
                renderMenu();
            } catch (error) {
                console.error('Error reordering categories:', error);
                alert('Erro ao reordenar categorias: ' + error.message);
            }
        }

        // Reorder subitems in a category
        async function reorderSubitems(categoryId, draggedIndex, targetIndex) {
            try {
                const doc = await db.collection('menu').doc(categoryId).get();
                const items = doc.data().items || [];
                const [dragged] = items.splice(draggedIndex, 1);
                items.splice(targetIndex, 0, dragged);
                items.forEach((item, index) => item.order = index);
                await db.collection('menu').doc(categoryId).update({ items });
                console.log(`Reordered subitems in category ${categoryId}: ${draggedIndex} to ${targetIndex}`);
                renderMenu();
            } catch (error) {
                console.error('Error reordering subitems:', error);
                alert('Erro ao reordenar subitens: ' + error.message);
            }
        }

        // Toggle sidebar
        document.getElementById('toggle-sidebar').addEventListener('click', () => {
            const sidebar = document.getElementById('sidebar');
            const sidebarTitle = document.getElementById('sidebar-title');
            const manageMenuBtn = document.getElementById('manage-menu-btn');
            sidebar.classList.toggle('sidebar-expanded');
            sidebar.classList.toggle('sidebar-collapsed');
            sidebarTitle.classList.toggle('hidden', sidebar.classList.contains('sidebar-collapsed'));
            manageMenuBtn.classList.toggle('hidden', sidebar.classList.contains('sidebar-collapsed'));
            document.querySelectorAll('.menu-text').forEach(el => {
                el.classList.toggle('hidden', sidebar.classList.contains('sidebar-collapsed'));
            });
            console.log(`Sidebar toggled: ${sidebar.classList.contains('sidebar-collapsed') ? 'collapsed' : 'expanded'}`);
        });

        // Show/hide menu management form
        document.getElementById('manage-menu-btn').addEventListener('click', () => {
            const form = document.getElementById('menu-form');
            form.classList.toggle('hidden');
            console.log(`Menu form toggled: ${form.classList.contains('hidden') ? 'hidden' : 'visible'}`);
        });

        // Save category
        document.getElementById('save-category').addEventListener('click', async () => {
            try {
                const name = document.getElementById('category-name').value.trim();
                const id = document.getElementById('category-id').value;
                if (!name) return alert('Nome da categoria é obrigatório');
                const snapshot = await db.collection('menu').get();
                const order = snapshot.size;
                if (id) {
                    await db.collection('menu').doc(id).update({ name });
                    console.log(`Updated category ${id} to name ${name}`);
                } else {
                    await db.collection('menu').doc().set({ name, order, items: [] });
                    console.log(`Created category ${name}`);
                }
                document.getElementById('category-name').value = '';
                document.getElementById('category-id').value = '';
                renderMenu();
            } catch (error) {
                console.error('Error saving category:', error);
                alert('Erro ao salvar categoria: ' + error.message);
            }
        });

        // Clear category form
        document.getElementById('clear-category').addEventListener('click', () => {
            document.getElementById('category-name').value = '';
            document.getElementById('category-id').value = '';
            console.log('Cleared category form');
        });

        // Save subitem
        document.getElementById('save-subitem').addEventListener('click', async () => {
            try {
                const categoryId = document.getElementById('subitem-category').value;
                const name = document.getElementById('subitem-name').value.trim();
                const file = document.getElementById('subitem-file').value.trim();
                const subitemId = document.getElementById('subitem-id').value;
                if (!categoryId || !name || !file) return alert('Todos os campos são obrigatórios');
                const doc = await db.collection('menu').doc(categoryId).get();
                const items = doc.data().items || [];
                if (subitemId) {
                    items[parseInt(subitemId)] = { name, file, order: parseInt(subitemId) };
                    console.log(`Updated subitem ${subitemId} in category ${categoryId}`);
                } else {
                    items.push({ name, file, order: items.length });
                    console.log(`Created subitem ${name} in category ${categoryId}`);
                }
                await db.collection('menu').doc(categoryId).update({ items });
                document.getElementById('subitem-name').value = '';
                document.getElementById('subitem-file').value = '';
                document.getElementById('subitem-id').value = '';
                renderMenu();
            } catch (error) {
                console.error('Error saving subitem:', error);
                alert('Erro ao salvar subitem: ' + error.message);
            }
        });

        // Clear subitem form
        document.getElementById('clear-subitem').addEventListener('click', () => {
            document.getElementById('subitem-name').value = '';
            document.getElementById('subitem-file').value = '';
            document.getElementById('subitem-id').value = '';
            console.log('Cleared subitem form');
        });

        // Load menu on page load
        renderMenu();
    </script>
</body>
</html>