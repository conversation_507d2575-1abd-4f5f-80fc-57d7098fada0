<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verificar Estruturas Duplicadas</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f7f7f7;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 900px;
      margin: 30px auto;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      padding: 30px;
    }
    h1 {
      color: #0854a0;
      font-size: 26px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .btn {
      background: #0854a0;
      color: #fff;
      border: none;
      border-radius: 6px;
      padding: 10px 18px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 20px;
      transition: background 0.2s;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    .btn:hover {
      background: #0a4d8c;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    th, td {
      padding: 12px;
      border: 1px solid #d4d4d4;
      text-align: left;
    }
    th {
      background: #f0f3f6;
      color: #0854a0;
      font-weight: 600;
    }
    tr:nth-child(even) {
      background: #f8f9fa;
    }
    .empty-state {
      text-align: center;
      color: #888;
      margin: 40px 0;
    }
    .loading {
      display: none;
      position: fixed;
      top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(255,255,255,0.7);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    .loading.active {
      display: flex;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #0854a0;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><i class="fas fa-search"></i> Verificar Estruturas Duplicadas</h1>
    <button class="btn" onclick="verificarDuplicadas()"><i class="fas fa-search"></i> Verificar Duplicadas</button>
    <div id="resultado"></div>
  </div>
  <div class="loading" id="loading">
    <div>
      <div class="spinner"></div>
      <div>Processando...</div>
    </div>
  </div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, deleteDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];

    async function carregarProdutos() {
      if (produtos.length > 0) return;
      const snap = await getDocs(collection(db, "produtos"));
      produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }

    window.verificarDuplicadas = async function() {
      document.getElementById('loading').classList.add('active');
      document.getElementById('resultado').innerHTML = '';
      await carregarProdutos();
      try {
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        // Agrupar por produtoPaiId
        const map = new Map();
        estruturas.forEach(e => {
          const key = e.produtoPaiId || e.produto_pai || e.id_produto_pai || e.codigo_pai || '';
          if (!key) return;
          if (!map.has(key)) map.set(key, []);
          map.get(key).push(e);
        });
        // Filtrar duplicados
        const duplicadas = Array.from(map.values()).filter(arr => arr.length > 1);
        if (duplicadas.length === 0) {
          document.getElementById('resultado').innerHTML = '<div class="empty-state">Nenhuma estrutura duplicada encontrada.</div>';
        } else {
          let html = `<table><thead><tr><th>Código Produto Pai</th><th>Descrição</th><th>Qtd Duplicadas</th><th>IDs das Estruturas</th></tr></thead><tbody>`;
          duplicadas.forEach(arr => {
            const prod = produtos.find(p => p.id === (arr[0].produtoPaiId || arr[0].produto_pai || arr[0].id_produto_pai || arr[0].codigo_pai));
            html += `<tr>`;
            html += `<td>${prod ? prod.codigo : '-'}</td>`;
            html += `<td>${prod ? prod.descricao : '-'}</td>`;
            html += `<td>${arr.length}</td>`;
            html += `<td>`;
            arr.forEach(e => {
              html += `<div style='margin-bottom:6px;'>
                <span style='font-family:monospace;'>${e.id}</span>
                <button class='btn btn-sm' style='background:#2196f3;margin-left:6px;' onclick='verOndeUsado("${e.id}")'>Ver onde é usado</button>
                <button class='btn btn-sm' style='background:#dc3545;margin-left:6px;' onclick='excluirEstrutura("${e.id}")'>Excluir</button>
                <div id='ondeUsado_${e.id}' style='margin-top:4px;'></div>
              </div>`;
            });
            html += `</td>`;
            html += `</tr>`;
          });
          html += `</tbody></table>`;
          document.getElementById('resultado').innerHTML = html;
        }
      } catch (err) {
        document.getElementById('resultado').innerHTML = '<div class="empty-state">Erro ao buscar estruturas.</div>';
        console.error(err);
      } finally {
        document.getElementById('loading').classList.remove('active');
      }
    }

    window.verOndeUsado = async function(estruturaId) {
      document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#888;">Buscando...</span>';
      try {
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const estrutura = estruturas.find(e => e.id === estruturaId);
        if (!estrutura) {
          document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#dc3545;">Estrutura não encontrada.</span>';
          return;
        }
        const produtoPaiId = estrutura.produtoPaiId || estrutura.produto_pai || estrutura.id_produto_pai || estrutura.codigo_pai;
        // Procurar onde o produto pai é usado como componente
        const usados = estruturas.filter(e =>
          (e.componentes || []).some(c => c.componentId === produtoPaiId)
        );
        if (usados.length === 0) {
          document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#107e3e;">Não está em uso. Pode excluir.</span>';
        } else {
          let html = '<span style="color:#e9730c;">Usado em:</span> ';
          html += usados.map(e => `<span style='font-family:monospace;'>${e.id}</span>`).join(', ');
          document.getElementById('ondeUsado_' + estruturaId).innerHTML = html;
        }
      } catch (err) {
        document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#dc3545;">Erro ao buscar uso.</span>';
      }
    }

    window.excluirEstrutura = async function(estruturaId) {
      document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#888;">Verificando uso antes de excluir...</span>';
      try {
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const estrutura = estruturas.find(e => e.id === estruturaId);
        if (!estrutura) {
          document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#dc3545;">Estrutura não encontrada.</span>';
          return;
        }
        const produtoPaiId = estrutura.produtoPaiId || estrutura.produto_pai || estrutura.id_produto_pai || estrutura.codigo_pai;
        const usados = estruturas.filter(e =>
          (e.componentes || []).some(c => c.componentId === produtoPaiId)
        );
        let confirmMsg = 'Tem certeza que deseja excluir a estrutura ' + estruturaId + '? Esta ação não pode ser desfeita.';
        if (usados.length > 0) {
          confirmMsg = 'ATENÇÃO: Esta estrutura está em uso em outras estruturas!\nExcluir assim mesmo? Esta ação pode causar inconsistências!';
        }
        if (!confirm(confirmMsg)) return;
        await deleteDoc(doc(db, "estruturas", estruturaId));
        document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#107e3e;">Excluída com sucesso!</span>';
        setTimeout(() => window.verificarDuplicadas(), 1000);
      } catch (err) {
        document.getElementById('ondeUsado_' + estruturaId).innerHTML = '<span style="color:#dc3545;">Erro ao excluir.</span>';
      }
    }
  </script>
</body>
</html> 