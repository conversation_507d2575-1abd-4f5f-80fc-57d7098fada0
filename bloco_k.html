<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bloco K - SPED Fiscal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --warning-color: #ff8c00;
            --danger-color: #dc3545;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, var(--header-bg), var(--primary-color));
            color: white;
            padding: 20px 25px;
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 25px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 26px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #0d6934;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .section {
            background-color: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .section-header {
            background-color: var(--secondary-color);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-content {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
        }

        .info-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .table-responsive {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid var(--border-color);
        }

        th {
            background-color: var(--secondary-color);
            font-weight: 600;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .loading-content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
        }

        .notification-success {
            background-color: var(--success-color);
        }

        .notification-error {
            background-color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-file-invoice"></i>
                Bloco K - SPED Fiscal
            </h1>
            <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                <i class="fas fa-arrow-left"></i> Voltar
            </button>
        </div>

        <div class="section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-calendar"></i> Período do Relatório
                </div>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label>Data Inicial</label>
                        <input type="date" id="dataInicial" required>
                    </div>
                    <div class="form-group">
                        <label>Data Final</label>
                        <input type="date" id="dataFinal" required>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-cog"></i> Configurações
                </div>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label>Tipo de Registro</label>
                        <select id="tipoRegistro">
                            <option value="K200">K200 - Controle de Produção e Estoque</option>
                            <option value="K220">K220 - Outras Informações de Produção</option>
                            <option value="K230">K230 - Itens Produzidos</option>
                            <option value="K235">K235 - Insumos Consumidos</option>
                            <option value="K250">K250 - Industrialização de Terceiros</option>
                            <option value="K255">K255 - Industrialização de Terceiros - Insumos</option>
                            <option value="K260">K260 - Reprocessamento/Reparo de Produtos</option>
                            <option value="K265">K265 - Reprocessamento/Reparo de Produtos - Insumos</option>
                            <option value="K270">K270 - Consumo de Matérias-Primas</option>
                            <option value="K275">K275 - Consumo de Matérias-Primas - Detalhamento</option>
                            <option value="K280">K280 - Correção de Apontamento</option>
                            <option value="K290">K290 - Correção de Apontamento - Detalhamento</option>
                            <option value="K300">K300 - Produção Conjunta</option>
                            <option value="K310">K310 - Produção Conjunta - Detalhamento</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Formato de Saída</label>
                        <select id="formatoSaida">
                            <option value="txt">TXT (SPED Fiscal)</option>
                            <option value="xlsx">Excel (XLSX)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-table"></i> Dados do Relatório
                </div>
            </div>
            <div class="section-content">
                <div class="table-responsive">
                    <table id="dadosRelatorio">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Unidade</th>
                                <th>Valor Unitário</th>
                                <th>Valor Total</th>
                                <th>Origem</th>
                                <th>Tipo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Dados serão preenchidos dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-content">
                <button class="btn btn-primary" onclick="gerarRelatorio()">
                    <i class="fas fa-file-export"></i> Gerar Relatório
                </button>
                <button class="btn btn-success" onclick="exportarK200()" style="margin-left: 10px;">
                    <i class="fas fa-file-export"></i> Exportar K200 (TXT SPED)
                </button>
                <div id="errosValidacao" style="color:#dc3545; margin-top:10px;"></div>
            </div>
        </div>
    </div>

    <div class="loading">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Processando...</p>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            query, 
            where, 
            getDocs,
            orderBy 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let movimentacoes = [];

        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            await loadData();
            setupDateInputs();
        };

        async function loadData() {
            try {
                document.querySelector('.loading').classList.add('active');

                const [produtosSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "movimentacoes"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                showNotification("Erro ao carregar dados do sistema.", "error");
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        }

        function setupDateInputs() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            document.getElementById('dataInicial').value = firstDay.toISOString().split('T')[0];
            document.getElementById('dataFinal').value = lastDay.toISOString().split('T')[0];
        }

        window.gerarRelatorio = async function() {
            const dataInicial = new Date(document.getElementById('dataInicial').value);
            const dataFinal = new Date(document.getElementById('dataFinal').value);
            const tipoRegistro = document.getElementById('tipoRegistro').value;
            const formatoSaida = document.getElementById('formatoSaida').value;

            if (!dataInicial || !dataFinal) {
                showNotification("Selecione o período do relatório.", "error");
                return;
            }

            try {
                document.querySelector('.loading').classList.add('active');

                // Filtrar movimentações pelo período
                const movimentacoesFiltradas = movimentacoes.filter(mov => {
                    const dataMov = mov.data.toDate();
                    return dataMov >= dataInicial && dataMov <= dataFinal;
                });

                // Processar dados para o relatório
                const dadosRelatorio = processarDadosRelatorio(movimentacoesFiltradas, tipoRegistro);

                // Atualizar tabela
                atualizarTabela(dadosRelatorio);

                // Exportar no formato selecionado
                if (formatoSaida === 'txt') {
                    exportarTXT(dadosRelatorio, tipoRegistro);
                } else {
                    exportarExcel(dadosRelatorio);
                }

                showNotification("Relatório gerado com sucesso!", "success");

            } catch (error) {
                console.error("Erro ao gerar relatório:", error);
                showNotification("Erro ao gerar relatório: " + error.message, "error");
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        function processarDadosRelatorio(movimentacoes, tipoRegistro) {
            return movimentacoes.map(mov => {
                const produto = produtos.find(p => p.id === mov.produtoId);
                return {
                    data: mov.data.toDate(),
                    produto: produto ? produto.descricao : 'Produto não encontrado',
                    quantidade: mov.quantidade,
                    unidade: produto ? produto.unidade : '-',
                    valorUnitario: mov.valorUnitario || 0,
                    valorTotal: (mov.quantidade * (mov.valorUnitario || 0)).toFixed(2),
                    origem: produto ? produto.origem : '-',
                    tipo: mov.tipo
                };
            });
        }

        function atualizarTabela(dados) {
            const tbody = document.querySelector('#dadosRelatorio tbody');
            tbody.innerHTML = dados.map(item => `
                <tr>
                    <td>${item.data.toLocaleDateString()}</td>
                    <td>${item.produto}</td>
                    <td>${item.quantidade}</td>
                    <td>${item.unidade}</td>
                    <td>R$ ${item.valorUnitario.toFixed(2)}</td>
                    <td>R$ ${item.valorTotal}</td>
                    <td>${item.origem}</td>
                    <td>${item.tipo}</td>
                </tr>
            `).join('');
        }

        function exportarTXT(dados, tipoRegistro) {
            let conteudo = '';

            // Cabeçalho do registro
            conteudo += `|${tipoRegistro}|${new Date().toISOString().split('T')[0]}|\n`;

            // Dados
            dados.forEach(item => {
                conteudo += `|${tipoRegistro}|`;
                conteudo += `${item.data.toISOString().split('T')[0]}|`;
                conteudo += `${item.produto}|`;
                conteudo += `${item.quantidade}|`;
                conteudo += `${item.unidade}|`;
                conteudo += `${item.valorUnitario}|`;
                conteudo += `${item.valorTotal}|`;
                conteudo += `${item.origem}|`;
                conteudo += `${item.tipo}|\n`;
            });

            // Criar e baixar arquivo
            const blob = new Blob([conteudo], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `BlocoK_${tipoRegistro}_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function exportarExcel(dados) {
            // Implementar exportação para Excel usando a biblioteca XLSX
            // Similar ao que já temos em outras partes do sistema
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        window.exportarK200 = async function() {
            document.getElementById('errosValidacao').innerHTML = '';
            try {
                document.querySelector('.loading').classList.add('active');
                // Carregar produtos e estoques
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                // Validar produtos obrigatórios
                const erros = [];
                produtos.forEach(prod => {
                    if (!prod.ncm || !/^\d{8}$/.test(prod.ncm)) {
                        erros.push(`Produto ${prod.codigo}: NCM inválido ou ausente.`);
                    }
                    if (!prod.unidade) {
                        erros.push(`Produto ${prod.codigo}: Unidade ausente.`);
                    }
                    if (!prod.tipoItem) {
                        erros.push(`Produto ${prod.codigo}: Tipo de item ausente.`);
                    }
                });
                if (erros.length > 0) {
                    document.getElementById('errosValidacao').innerHTML = erros.join('<br>');
                    document.querySelector('.loading').classList.remove('active');
                    return;
                }
                // Gerar registros K200
                let conteudo = '';
                conteudo += '|K200|DataEstoque|CodigoItem|Unidade|Qtd|TipoItem|\n';
                estoques.forEach(est => {
                    const prod = produtos.find(p => p.id === est.produtoId);
                    if (!prod) return;
                    conteudo += `|K200|${new Date().toISOString().split('T')[0]}|${prod.codigo}|${prod.unidade}|${est.saldoFinal || 0}|${prod.tipoItem}|\n`;
                });
                // Baixar arquivo
                const blob = new Blob([conteudo], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `BlocoK_K200_${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                document.querySelector('.loading').classList.remove('active');
            } catch (err) {
                document.getElementById('errosValidacao').innerHTML = 'Erro ao exportar K200.';
                document.querySelector('.loading').classList.remove('active');
            }
        }
    </script>
</body>
</html> 