<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resposta de Cotação</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
            color: white;
            padding: 0;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        .header-content {
            position: relative;
            z-index: 1;
            padding: 30px;
        }
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 20px;
        }
        .company-info {
            flex: 1;
            min-width: 300px;
        }
        .supplier-info {
            flex: 1;
            min-width: 300px;
            text-align: right;
        }
        .company-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .company-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .company-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }
        .supplier-badge {
            background: rgba(255,255,255,0.15);
            padding: 15px 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }
        .supplier-label {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .supplier-name {
            font-size: 18px;
            font-weight: 600;
        }
        .quotation-title {
            text-align: center;
            margin: 0;
            font-size: 28px;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .main-content {
            padding: 30px;
        }
        .info-block {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #1a237e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .info-label {
            font-weight: 600;
            color: #1a237e;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        .info-value {
            color: #333;
            font-size: 1.1em;
            font-weight: 500;
        }
        .table-container {
            overflow-x: auto;
            margin: 25px 0;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            position: relative;
        }
        .table-container::after {
            content: '← Deslize para ver mais colunas →';
            position: absolute;
            bottom: 10px;
            right: 20px;
            background: rgba(26, 35, 126, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7em;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        @media (max-width: 1250px) {
            .table-container::after {
                opacity: 1;
            }
        }
        table {
            width: 100%;
            min-width: 1150px;
            border-collapse: collapse;
            background: white;
            table-layout: fixed;
        }
        th, td {
            padding: 8px 6px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        th {
            background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
            color: white;
            font-weight: 600;
            font-size: 0.75em;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
        }
        tbody tr {
            transition: all 0.2s ease;
        }
        tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .quantity-input {
            width: 80px;
            padding: 6px 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 0.8em;
            transition: all 0.2s ease;
            background: white;
            text-align: center;
        }
        .price-input {
            width: 90px;
            padding: 6px 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 0.8em;
            transition: all 0.2s ease;
            background: white;
            text-align: center;
        }
        .price-input:focus, .quantity-input:focus {
            outline: none;
            border-color: #1a237e;
            box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
            transform: translateY(-1px);
        }
        .price-input:disabled, .quantity-input:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-col {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"],
        input[type="number"],
        select,
        textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 0.95em;
            transition: all 0.2s ease;
            background: white;
        }
        input[type="text"]:focus,
        input[type="number"]:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: #1a237e;
            box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
        }
        .submit-button {
            background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
        }
        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 35, 126, 0.4);
        }
        .submit-button:active {
            transform: translateY(0);
        }
        .total-row {
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .success-message {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            text-align: center;
            z-index: 1000;
            border: 1px solid #e9ecef;
            max-width: 400px;
            width: 90%;
        }
        .success-icon {
            color: #28a745;
            font-size: 64px;
            margin-bottom: 20px;
            animation: bounce 0.6s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }
        .error-message {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #f5c6cb;
            border-left: 4px solid #dc3545;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .invalid-link {
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-radius: 16px;
            margin: 40px auto;
            max-width: 600px;
            border: 1px solid #f5c6cb;
            box-shadow: 0 8px 32px rgba(220, 53, 69, 0.1);
        }
        .invalid-link h2 {
            color: #dc3545;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .shipping-options {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .payment-terms {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .item-checkbox {
            transform: scale(1.2);
            cursor: pointer;
        }
        .item-row {
            transition: all 0.3s ease;
        }
        .item-row.selected {
            background-color: #f0f8ff !important;
            border-left: 4px solid #2196f3 !important;
        }
        .item-row input:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }
        .item-row input:enabled {
            background-color: white;
            color: #333;
        }
        #selectedItemsInfo {
            font-weight: 500;
        }
        .unit-select {
            width: 85px;
            padding: 8px 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.85em;
            font-weight: 500;
            background: white;
            transition: all 0.2s ease;
        }
        .unit-select:focus {
            outline: none;
            border-color: #1a237e;
            box-shadow: 0 0 0 2px rgba(26, 35, 126, 0.1);
        }
        .unit-select:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        .price-unit-display {
            font-style: italic;
            margin-top: 4px;
            font-size: 0.75em;
            color: #6c757d;
        }
        .conversion-highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
            border-left: 4px solid #f39c12 !important;
            position: relative;
        }
        .conversion-highlight td:first-child {
            padding-left: 8px;
        }
        th {
            font-size: 0.8em;
            white-space: nowrap;
            text-align: center;
        }
        td {
            font-size: 0.9em;
            text-align: center;
        }
        .ipi-input, .icms-input {
            width: 40px;
            padding: 6px 4px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 0.75em;
            text-align: center;
        }
        .unit-select {
            width: 75px;
            font-size: 0.75em;
            padding: 6px 4px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            text-align: center;
        }

        /* Melhorar alinhamento das células */
        .item-row td {
            vertical-align: middle;
        }

        /* Estilo para o total */
        .item-total {
            font-weight: 600;
            color: #1a237e;
        }
        .item-checkbox {
            transform: scale(1.3);
            cursor: pointer;
            accent-color: #1a237e;
        }
        .shipping-options, .payment-terms {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            margin-top: 25px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .shipping-options h3, .payment-terms h3 {
            color: #1a237e;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div id="mainContainer" class="container" style="display: none;">
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo" id="companyLogo">N</div>
                        <div class="company-name" id="companyName">Naliteck Sistemas</div>
                        <div class="company-details" id="companyDetails">
                            <div>📧 <EMAIL></div>
                            <div>📞 (11) 99999-9999</div>
                            <div>🏢 São Paulo, SP</div>
                        </div>
                    </div>
                    <div class="supplier-info">
                        <div class="supplier-badge">
                            <div class="supplier-label">Fornecedor Destinatário</div>
                            <div class="supplier-name" id="supplierName">Carregando...</div>
                        </div>
                    </div>
                </div>
                <h1 class="quotation-title" id="quotationTitle">Carregando...</h1>
            </div>
        </div>

        <div class="main-content">
            <div class="info-block">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Data da Cotação</div>
                    <div class="info-value" id="quotationDate">Carregando...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Prazo para Resposta</div>
                    <div class="info-value" id="responseDeadline">7 dias</div>
                </div>
                <div class="info-item" style="display: flex; align-items: center; gap: 15px;">
                    <div style="flex-shrink: 0;">
                        <svg width="60" height="30" viewBox="0 0 400 140" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="textGradientSmall" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="20" y="50" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#textGradientSmall)">Naliteck</text>
                            <text x="20" y="75" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">Sistemas</text>
                            <text x="20" y="95" font-family="Arial, sans-serif" font-size="10" fill="#9ca3af">MRP • ERP • Gestão</text>
                        </svg>
                    </div>
                    <div style="flex: 1;">
                        <div class="info-label">Empresa Solicitante</div>
                        <div class="info-value" id="requestingCompany">Naliteck Sistemas</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="errorMessage" class="error-message"></div>

        <form id="quotationForm" onsubmit="handleSubmit(event)">
            <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 25px; border-radius: 12px; margin-bottom: 25px; border: 1px solid #90caf9; box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);">
                <h3 style="margin: 0 0 15px 0; color: #1976d2; display: flex; align-items: center; gap: 10px;">
                    📋 Instruções para Resposta
                </h3>
                <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #2196f3;">
                    <p style="margin: 0; color: #1565c0; line-height: 1.6;">
                        ✅ <strong>Selecione apenas os itens que você pode fornecer</strong> marcando a caixa de seleção<br>
                        📊 <strong>Ajuste as quantidades</strong> conforme sua capacidade ou lote mínimo<br>
                        💰 <strong>Informe preços unitários</strong> na unidade de sua preferência<br>
                        📝 <strong>Não precisa cotar todos os itens</strong> - responda apenas os de seu interesse
                    </p>
                </div>
                <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; color: #856404; font-size: 0.95em; line-height: 1.5;">
                        <strong>🔄 Conversão de Unidades:</strong> Itens com fundo amarelo possuem <strong>duas unidades</strong>.<br>
                        Você pode escolher cotar na <strong>Unidade Interna</strong> (controle) ou <strong>Unidade Compra</strong> (fornecimento).<br>
                        O sistema fará a conversão automaticamente para manter a consistência dos cálculos.
                    </p>
                </div>
            </div>

            <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th rowspan="2" style="width: 40px; text-align: center; padding: 8px 4px;">
                            <input type="checkbox" id="selectAllItems" onchange="toggleAllItems()" title="Selecionar/Desmarcar todos">
                        </th>
                        <th rowspan="2" style="width: 80px; text-align: center; padding: 8px 4px;">CÓDIGO</th>
                        <th rowspan="2" style="width: 200px; text-align: center; padding: 8px 4px;">DESCRIÇÃO</th>
                        <th colspan="2" style="text-align: center; padding: 8px 4px;">UNIDADES</th>
                        <th colspan="2" style="text-align: center; padding: 8px 4px;">QUANTIDADES</th>
                        <th colspan="2" style="text-align: center; padding: 8px 4px;">COTAÇÃO</th>
                        <th colspan="2" style="text-align: center; padding: 8px 4px;">IMPOSTOS</th>
                        <th rowspan="2" style="width: 90px; text-align: center; padding: 8px 4px;">TOTAL</th>
                    </tr>
                    <tr>
                        <!-- Células correspondentes aos colspan acima -->
                        <th style="width: 56px; text-align: center; padding: 8px 4px;">INTERNA</th>
                        <th style="width: 56px; text-align: center; padding: 8px 4px;">COMPRA</th>
                        <th style="width: 90px; text-align: center; padding: 8px 4px;">SOLICITADA</th>
                        <th style="width: 90px; text-align: center; padding: 8px 4px;">COTADA</th>
                        <th style="width: 80px; text-align: center; padding: 8px 4px;">UNIDADE</th>
                        <th style="width: 100px; text-align: center; padding: 8px 4px;">PREÇO UNIT.</th>
                        <th style="width: 48px; text-align: center; padding: 8px 4px;">IPI %</th>
                        <th style="width: 48px; text-align: center; padding: 8px 4px;">ICMS %</th>
                    </tr>
                </thead>
                <tbody id="itemsTable">
                </tbody>
                <tfoot>
                    <tr style="background: #f0f8ff;">
                        <td colspan="12" style="text-align: center; padding: 10px; font-style: italic; color: #666;">
                            <span id="selectedItemsInfo">Nenhum item selecionado</span>
                        </td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="11" style="text-align: right; padding: 8px;">Subtotal (itens selecionados):</td>
                        <td id="subtotal" style="padding: 8px;">R$ 0,00</td>
                    </tr>
                    <tr>
                        <td colspan="11" style="text-align: right; padding: 8px;">Total IPI:</td>
                        <td id="totalIPI" style="padding: 8px;">R$ 0,00</td>
                    </tr>
                    <tr>
                        <td colspan="11" style="text-align: right; padding: 8px;">Total ICMS:</td>
                        <td id="totalICMS" style="padding: 8px;">R$ 0,00</td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="11" style="text-align: right; padding: 8px;">Total Geral:</td>
                        <td id="grandTotal" style="padding: 8px;">R$ 0,00</td>
                    </tr>
                </tfoot>
            </table>
            </div> <!-- Fecha table-container -->

            <!-- Bloco de frete e condições comerciais -->
            <div class="shipping-options">
                <h3>Informações de Frete e Entrega</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="shippingType">Tipo de Frete</label>
                        <select id="shippingType" required>
                            <option value="">Selecione...</option>
                            <option value="CIF">CIF (Por conta do fornecedor)</option>
                            <option value="FOB">FOB (Por conta do cliente)</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label for="shippingCost">Valor do Frete</label>
                        <input type="number" id="shippingCost" step="0.01" min="0">
                    </div>
                    <div class="form-col">
                        <label for="deliveryTime">Prazo de Entrega (dias)</label>
                        <input type="number" id="deliveryTime" min="1" required>
                    </div>
                </div>
            </div>

            <div class="payment-terms">
                <h3>Condições Comerciais</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="paymentTerms">Condição de Pagamento</label>
                        <select id="paymentTerms" required>
                            <option value="">Selecione...</option>
                            <option value="AVISTA">À Vista</option>
                            <option value="7DIAS">7 Dias</option>
                            <option value="15DIAS">15 Dias</option>
                            <option value="28DIAS">28 Dias</option>
                            <option value="30DIAS">30 Dias</option>
                            <option value="45DIAS">45 Dias</option>
                            <option value="60DIAS">60 Dias</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label for="minimumOrder">Pedido Mínimo</label>
                        <input type="number" id="minimumOrder" step="0.01" min="0">
                    </div>
                    <div class="form-col">
                        <label for="validity">Validade da Proposta (dias)</label>
                        <input type="number" id="validity" min="1" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label for="warranty">Garantia (meses)</label>
                        <input type="number" id="warranty" min="0">
                    </div>
                    <div class="form-col">
                        <label for="discount">Desconto Adicional (%)</label>
                        <input type="number" id="discount" step="0.01" min="0" max="100">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="observations">Observações</label>
                <textarea id="observations" rows="4"></textarea>
            </div>

            <button type="submit" class="submit-button">📤 Enviar Resposta</button>
        </form>
        </div> <!-- Fecha main-content -->
    </div>

    <div id="invalidLink" class="invalid-link" style="display: none;">
        <h2>Link Inválido</h2>
        <p>O link desta cotação é inválido ou expirou.</p>
        <p>Por favor, entre em contato com a empresa para obter um novo link.</p>
    </div>

    <div id="overlay" class="overlay"></div>
    <div id="successMessage" class="success-message">
        <div class="success-icon">✅</div>
        <h2 style="color: #28a745; margin-bottom: 15px;">Resposta Enviada com Sucesso!</h2>
        <p style="margin-bottom: 10px; color: #333;">Sua cotação foi recebida e está sendo processada.</p>
        <p style="margin-bottom: 20px; color: #666; font-size: 0.9em;">Em breve você receberá um e-mail de confirmação com os detalhes.</p>
        <div style="background: #d4edda; padding: 10px; border-radius: 6px; color: #155724; font-size: 0.85em;">
            📧 Acompanhe sua caixa de entrada para atualizações
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            doc,
            getDoc,
            updateDoc,
            collection,
            getDocs,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentQuotation = null;
        let currentSupplier = null;
        let produtos = []; // Cache dos produtos para conversão

        // Função para carregar produtos do banco
        async function loadProducts() {
            try {
                const produtosSnapshot = await getDocs(collection(db, "produtos"));
                produtos = produtosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                console.log('Produtos carregados:', produtos.length);
            } catch (error) {
                console.error("Erro ao carregar produtos:", error);
            }
        }

        // Função para carregar dados da empresa
        async function loadCompanyData() {
            try {
                const docRef = doc(db, "empresa", "config");
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();
                    // Atualizar nome da empresa
                    const companyName = data.nomeFantasia || "Naliteck Sistemas";
                    document.getElementById('requestingCompany').textContent = companyName;

                    console.log('Dados da empresa carregados:', companyName);
                } else {
                    console.warn("Dados da empresa não encontrados. Usando valores padrão.");
                }
            } catch (error) {
                console.error("Erro ao carregar dados da empresa:", error);
            }
        }

        window.onload = async function() {
  const params = new URLSearchParams(window.location.search);
  const quotationId = params.get('cotacao');
  const supplierId = params.get('fornecedor');

  if (!quotationId || !supplierId) {
    showInvalidLink();
    return;
  }

  try {
    // Carregar produtos e dados da empresa primeiro
    await loadProducts();
    await loadCompanyData();

    const quotationDoc = await getDoc(doc(db, "cotacoes", quotationId));
    if (!quotationDoc.exists()) {
      showInvalidLink();
      return;
    }

    currentQuotation = { id: quotationDoc.id, ...quotationDoc.data() };

    console.log('DEBUG - Status da cotação:', currentQuotation.status);

    if (!currentQuotation.fornecedores?.includes(supplierId)) {
      showInvalidLink();
      return;
    }

    const supplierDoc = await getDoc(doc(db, "fornecedores", supplierId));
    if (!supplierDoc.exists()) {
      showInvalidLink();
      return;
    }

    currentSupplier = { id: supplierDoc.id, ...supplierDoc.data() };

    // VERIFICAÇÃO MODIFICADA AQUI
    if (!['ABERTA', 'ENVIADA', 'PENDENTE'].includes(currentQuotation.status)) {
      showInvalidLink(`Status atual (${currentQuotation.status}) não permite respostas.`);
      return;
    }

    if (currentQuotation.respostas?.[currentSupplier.id]) {
      showInvalidLink("Você já respondeu esta cotação.");
      return;
    }

    loadQuotationData();

      // Carregar rascunho se existir
      setTimeout(loadDraft, 1000);
    } catch (error) {
      console.error("Erro:", error);
      showInvalidLink("Erro ao carregar cotação.");
    }
  };

        function showInvalidLink(message = "O link desta cotação é inválido ou expirou.") {
            document.getElementById('mainContainer').style.display = 'none';
            const invalidLink = document.getElementById('invalidLink');
            invalidLink.querySelector('p').textContent = message;
            invalidLink.style.display = 'block';
        }

        function loadQuotationData() {
  // Carregar dados do cabeçalho
  document.getElementById('quotationTitle').textContent = `Solicitação de Cotação #${currentQuotation.numero}`;
  document.getElementById('supplierName').textContent = currentSupplier.nome || currentSupplier.razaoSocial || 'Fornecedor';

  // Atualizar logo da empresa com primeira letra
  const companyName = 'Naliteck Sistemas';
  document.getElementById('companyLogo').textContent = companyName.charAt(0);

  // Carregar dados da cotação
  document.getElementById('quotationDate').textContent =
    new Date(currentQuotation.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR');

  const tableBody = document.getElementById('itemsTable');
  tableBody.innerHTML = '';

  currentQuotation.itens.forEach((item, index) => {
    // Buscar produto no cache para obter dados de conversão
    const produto = produtos.find(p => p.codigo === item.codigo);

    // Determinar se tem conversão - primeiro do item, depois do produto
    let hasConversion = false;
    let unidadeSecundaria = null;
    let fatorConversao = null;

    if (item.unidadeSecundaria && item.fatorConversao) {
      hasConversion = true;
      unidadeSecundaria = item.unidadeSecundaria;
      fatorConversao = item.fatorConversao;
    } else if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
      hasConversion = true;
      unidadeSecundaria = produto.unidadeSecundaria;
      fatorConversao = produto.fatorConversao;
    }

    const unidadeInterna = item.unidade || 'UN';
    const unidadeCompra = hasConversion ? unidadeSecundaria : unidadeInterna;
    const quantidadeInterna = item.quantidade || 0;
    const quantidadeCompra = hasConversion ? (quantidadeInterna * fatorConversao) : quantidadeInterna;

    console.log('Item da cotação (resposta):', {
      codigo: item.codigo,
      hasConversion,
      unidadeInterna,
      unidadeCompra,
      quantidadeInterna,
      quantidadeCompra,
      fatorConversao,
      produto: produto ? 'encontrado' : 'não encontrado',
      itemOriginal: item
    });

    const row = document.createElement('tr');
    row.className = `item-row ${hasConversion ? 'conversion-highlight' : ''}`;
    row.innerHTML = `
      <td style="width: 40px; text-align: center; padding: 8px 4px;">
        <input type="checkbox" class="item-checkbox" data-index="${index}" onchange="toggleItemInputs(${index})" title="Selecionar este item para cotação">
      </td>
      <td style="width: 80px; text-align: center; font-weight: 500; font-size: 0.85em; padding: 8px 4px;">${item.codigo || '-'}</td>
      <td style="width: 200px; text-align: left; font-size: 0.8em; word-wrap: break-word; overflow: hidden; padding: 8px 6px;">${item.descricao || '-'}</td>
      <td style="width: 56px; text-align: center; font-weight: bold; font-size: 0.85em; padding: 8px 4px;">${unidadeInterna}</td>
      <td style="width: 56px; text-align: center; font-weight: bold; font-size: 0.85em; padding: 8px 4px; ${hasConversion ? 'background-color: #fff3cd;' : ''}">${unidadeCompra}</td>
      <td style="width: 90px; text-align: center; font-size: 0.8em; padding: 8px 4px;">
        <div>${quantidadeInterna.toFixed(3)}</div>
        ${hasConversion ? `<div style="color: #666; font-style: italic; font-size: 0.7em;">(${quantidadeCompra.toFixed(3)} ${unidadeCompra})</div>` : ''}
      </td>
      <td style="width: 90px; text-align: center; padding: 8px 4px;">
        <input type="number" class="quantity-input" data-index="${index}" step="0.001" min="0.001"
               value="${quantidadeCompra.toFixed(3)}" onchange="updateTotals(); updateConversion(${index})" disabled
               data-has-conversion="${hasConversion}" data-factor="${fatorConversao || 1}"
               data-unit-interna="${unidadeInterna}" data-unit-compra="${unidadeCompra}">
      </td>
      <td style="width: 80px; text-align: center; padding: 8px 4px;">
        <select class="unit-select" data-index="${index}" onchange="updateUnitChoice(${index})" disabled>
          <option value="compra" selected>${unidadeCompra}</option>
          ${hasConversion ? `<option value="interna">${unidadeInterna}</option>` : ''}
        </select>
      </td>
      <td style="width: 100px; text-align: center; padding: 8px 4px;">
        <input type="number" class="price-input" data-index="${index}" step="0.01" min="0"
               onchange="updateTotals()" disabled placeholder="0,00"
               data-unit-compra="${unidadeCompra}">
        <small class="price-unit-display" style="display: block; color: #666; font-size: 0.7em; margin-top: 1px;">por ${unidadeCompra}</small>
      </td>
      <td style="width: 48px; text-align: center; padding: 8px 4px;"><input type="number" class="ipi-input" data-index="${index}" step="0.01" min="0" max="100" onchange="updateTotals()" disabled placeholder="0"></td>
      <td style="width: 48px; text-align: center; padding: 8px 4px;"><input type="number" class="icms-input" data-index="${index}" step="0.01" min="0" max="100" onchange="updateTotals()" disabled placeholder="0"></td>
      <td style="width: 90px; text-align: center; padding: 8px 4px;" class="item-total">R$ 0,00</td>
    `;
    tableBody.appendChild(row);
  });

  document.getElementById('mainContainer').style.display = 'block';

  // Inicializar informações dos itens selecionados
  updateSelectedItemsInfo();
}

        // Função para alternar seleção de todos os itens
        window.toggleAllItems = function() {
            const selectAll = document.getElementById('selectAllItems');
            const checkboxes = document.querySelectorAll('.item-checkbox');

            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = selectAll.checked;
                toggleItemInputs(index);
            });
        };

        // Função para habilitar/desabilitar inputs de um item específico
        window.toggleItemInputs = function(index) {
            const checkbox = document.querySelector(`.item-checkbox[data-index="${index}"]`);
            const quantityInput = document.querySelector(`.quantity-input[data-index="${index}"]`);
            const unitSelect = document.querySelector(`.unit-select[data-index="${index}"]`);
            const priceInput = document.querySelector(`.price-input[data-index="${index}"]`);
            const ipiInput = document.querySelector(`.ipi-input[data-index="${index}"]`);
            const icmsInput = document.querySelector(`.icms-input[data-index="${index}"]`);
            const row = checkbox.closest('tr');

            if (checkbox.checked) {
                // Habilitar inputs
                quantityInput.disabled = false;
                unitSelect.disabled = false;
                priceInput.disabled = false;
                ipiInput.disabled = false;
                icmsInput.disabled = false;
                priceInput.required = true;
                quantityInput.required = true;
                row.style.backgroundColor = '#f0f8ff';
                row.style.borderLeft = '4px solid #2196f3';
            } else {
                // Desabilitar inputs e limpar valores
                quantityInput.disabled = true;
                unitSelect.disabled = true;
                priceInput.disabled = true;
                ipiInput.disabled = true;
                icmsInput.disabled = true;
                priceInput.required = false;
                quantityInput.required = false;
                priceInput.value = '';
                ipiInput.value = '';
                icmsInput.value = '';
                // Resetar para unidade de compra
                unitSelect.value = 'compra';
                updateUnitChoice(index);
                row.style.backgroundColor = '';
                row.style.borderLeft = '';
            }

            updateTotals();
            updateSelectedItemsInfo();
        };

        // Função para atualizar escolha de unidade
        window.updateUnitChoice = function(index) {
            const unitSelect = document.querySelector(`.unit-select[data-index="${index}"]`);
            const quantityInput = document.querySelector(`.quantity-input[data-index="${index}"]`);
            const priceInput = document.querySelector(`.price-input[data-index="${index}"]`);
            const priceUnitDisplay = quantityInput.parentElement.parentElement.querySelector('.price-unit-display');

            // Obter dados dos atributos do input
            const hasConversion = quantityInput.dataset.hasConversion === 'true';
            const fatorConversao = parseFloat(quantityInput.dataset.factor) || 1;
            const unidadeInterna = quantityInput.dataset.unitInterna;
            const unidadeCompra = quantityInput.dataset.unitCompra;

            // Calcular quantidades baseadas no item original
            const item = currentQuotation.itens[index];
            const quantidadeInterna = item.quantidade || 0;
            const quantidadeCompra = hasConversion ? (quantidadeInterna * fatorConversao) : quantidadeInterna;

            if (unitSelect.value === 'interna' && hasConversion) {
                // Cotando na unidade interna
                quantityInput.value = quantidadeInterna.toFixed(3);
            } else {
                // Cotando na unidade de compra
                quantityInput.value = quantidadeCompra.toFixed(3);
            }

            // IMPORTANTE: O preço SEMPRE é pela unidade de compra
            priceUnitDisplay.textContent = `por ${unidadeCompra}`;
            priceInput.setAttribute('data-unit-compra', unidadeCompra);

            updateTotals();
        };

        // Função para atualizar conversão quando quantidade muda
        window.updateConversion = function(index) {
            // Esta função pode ser expandida se necessário para validações adicionais
            updateTotals();
        };

        // Função para atualizar informações dos itens selecionados
        function updateSelectedItemsInfo() {
            const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
            const totalItems = document.querySelectorAll('.item-checkbox').length;
            const selectedCount = selectedCheckboxes.length;

            const infoElement = document.getElementById('selectedItemsInfo');
            if (selectedCount === 0) {
                infoElement.textContent = 'Nenhum item selecionado para cotação';
                infoElement.style.color = '#d32f2f';
            } else {
                infoElement.textContent = `${selectedCount} de ${totalItems} itens selecionados para cotação`;
                infoElement.style.color = '#1976d2';
            }
        }

        window.updateTotals = function() {
            let subtotal = 0;
            let totalIPI = 0;
            let totalICMS = 0;

            // Calcular apenas itens selecionados
            document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
                const index = checkbox.dataset.index;
                const row = checkbox.closest('tr');
                const quantityInput = document.querySelector(`.quantity-input[data-index="${index}"]`);
                const unitSelect = document.querySelector(`.unit-select[data-index="${index}"]`);
                const priceInput = document.querySelector(`.price-input[data-index="${index}"]`);
                const ipiInput = document.querySelector(`.ipi-input[data-index="${index}"]`);
                const icmsInput = document.querySelector(`.icms-input[data-index="${index}"]`);

                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const ipi = parseFloat(ipiInput.value) || 0;
                const icms = parseFloat(icmsInput.value) || 0;

                // Converter quantidade para unidade de compra se necessário
                const hasConversion = quantityInput.dataset.hasConversion === 'true';
                const fatorConversao = parseFloat(quantityInput.dataset.factor) || 1;
                let quantityForCalculation = quantity;

                if (hasConversion && unitSelect.value === 'interna') {
                    // Se cotando na unidade interna, converter para unidade de compra para cálculo
                    quantityForCalculation = quantity * fatorConversao;
                }

                // IMPORTANTE: O preço é SEMPRE pela unidade de compra
                // Então o cálculo é: quantityForCalculation * price

                const itemSubtotal = quantityForCalculation * price;
                const itemIPI = itemSubtotal * (ipi / 100);
                const itemICMS = itemSubtotal * (icms / 100);

                subtotal += itemSubtotal;
                totalIPI += itemIPI;
                totalICMS += itemICMS;

                // Atualizar total do item
                const totalCell = row.querySelector('.item-total');
                totalCell.textContent = `R$ ${(itemSubtotal + itemIPI + itemICMS).toFixed(2).replace('.', ',')}`;
            });

            // Limpar totais de itens não selecionados
            document.querySelectorAll('.item-checkbox:not(:checked)').forEach(checkbox => {
                const row = checkbox.closest('tr');
                const totalCell = row.querySelector('.item-total');
                totalCell.textContent = 'R$ 0,00';
            });

            const shippingCost = parseFloat(document.getElementById('shippingCost').value) || 0;
            const discount = parseFloat(document.getElementById('discount').value) || 0;
            const discountValue = subtotal * (discount / 100);

            document.getElementById('subtotal').textContent = `R$ ${subtotal.toFixed(2).replace('.', ',')}`;
            document.getElementById('totalIPI').textContent = `R$ ${totalIPI.toFixed(2).replace('.', ',')}`;
            document.getElementById('totalICMS').textContent = `R$ ${totalICMS.toFixed(2).replace('.', ',')}`;
            document.getElementById('grandTotal').textContent =
                `R$ ${(subtotal + totalIPI + totalICMS + shippingCost - discountValue).toFixed(2).replace('.', ',')}`;
        };

        // Função para validar formulário em tempo real
        function validateForm() {
            const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
            const deliveryTime = document.getElementById('deliveryTime').value;
            const paymentTerms = document.getElementById('paymentTerms').value;
            const validity = document.getElementById('validity').value;

            let isValid = true;
            let errors = [];

            // Validar se pelo menos um item foi selecionado
            if (selectedCheckboxes.length === 0) {
                errors.push('Selecione pelo menos um item para cotar.');
                isValid = false;
            }

            // Validar preços dos itens selecionados
            const selectedPrices = [];
            selectedCheckboxes.forEach(checkbox => {
                const index = checkbox.dataset.index;
                const priceInput = document.querySelector(`.price-input[data-index="${index}"]`);
                const price = parseFloat(priceInput.value) || 0;
                selectedPrices.push(price);

                if (price <= 0) {
                    const row = checkbox.closest('tr');
                    const codigo = row.cells[1].textContent;
                    errors.push(`Item ${codigo}: preço deve ser maior que zero.`);
                    isValid = false;
                }
            });

            // Validar prazo de entrega
            if (!deliveryTime || parseInt(deliveryTime) <= 0) {
                errors.push('Prazo de entrega deve ser maior que zero.');
                isValid = false;
            }

            // Validar condição de pagamento
            if (!paymentTerms) {
                errors.push('Selecione uma condição de pagamento.');
                isValid = false;
            }

            // Validar validade da proposta
            if (!validity || parseInt(validity) <= 0) {
                errors.push('Validade da proposta deve ser maior que zero.');
                isValid = false;
            }

            // Validar valores extremos (apenas para itens selecionados)
            if (selectedPrices.length > 1) {
                const maxPrice = Math.max(...selectedPrices);
                const minPrice = Math.min(...selectedPrices.filter(p => p > 0));
                if (minPrice > 0 && maxPrice / minPrice > 100) {
                    errors.push('Atenção: Há uma grande variação entre os preços dos itens selecionados. Verifique se estão corretos.');
                }
            }

            const errorMessage = document.getElementById('errorMessage');
            if (errors.length > 0) {
                errorMessage.innerHTML = errors.join('<br>');
                errorMessage.style.display = 'block';
            } else {
                errorMessage.style.display = 'none';
            }

            return isValid;
        }

        // Função para salvar rascunho
        window.saveDraft = function() {
            const draftData = {
                prices: Array.from(document.getElementsByClassName('price-input')).map(input => input.value),
                ipi: Array.from(document.getElementsByClassName('ipi-input')).map(input => input.value),
                icms: Array.from(document.getElementsByClassName('icms-input')).map(input => input.value),
                shippingType: document.getElementById('shippingType').value,
                shippingCost: document.getElementById('shippingCost').value,
                deliveryTime: document.getElementById('deliveryTime').value,
                paymentTerms: document.getElementById('paymentTerms').value,
                minimumOrder: document.getElementById('minimumOrder').value,
                validity: document.getElementById('validity').value,
                warranty: document.getElementById('warranty').value,
                discount: document.getElementById('discount').value,
                observations: document.getElementById('observations').value,
                timestamp: new Date().toISOString()
            };

            const params = new URLSearchParams(window.location.search);
            const quotationId = params.get('cotacao');
            const supplierId = params.get('fornecedor');

            localStorage.setItem(`draft_${quotationId}_${supplierId}`, JSON.stringify(draftData));

            // Mostrar confirmação
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                z-index: 1000;
            `;
            notification.textContent = 'Rascunho salvo!';
            document.body.appendChild(notification);

            setTimeout(() => notification.remove(), 2000);
        };

        // Função para carregar rascunho
        function loadDraft() {
            const params = new URLSearchParams(window.location.search);
            const quotationId = params.get('cotacao');
            const supplierId = params.get('fornecedor');

            const draftKey = `draft_${quotationId}_${supplierId}`;
            const draftData = localStorage.getItem(draftKey);

            if (draftData) {
                const data = JSON.parse(draftData);

                // Verificar se o rascunho não está muito antigo (7 dias)
                const draftDate = new Date(data.timestamp);
                const now = new Date();
                const daysDiff = (now - draftDate) / (1000 * 60 * 60 * 24);

                if (daysDiff > 7) {
                    localStorage.removeItem(draftKey);
                    return;
                }

                // Mostrar opção de carregar rascunho
                const loadDraftDiv = document.createElement('div');
                loadDraftDiv.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    background-color: #fff3cd;
                    color: #856404;
                    padding: 10px;
                    text-align: center;
                    border-bottom: 1px solid #ffeaa7;
                    z-index: 1000;
                `;
                loadDraftDiv.innerHTML = `
                    Encontramos um rascunho salvo em ${draftDate.toLocaleString()}. 
                    <button onclick="restoreDraft()" style="margin-left: 10px; padding: 5px 10px; background: #856404; color: white; border: none; border-radius: 3px;">Carregar Rascunho</button>
                    <button onclick="this.parentElement.remove()" style="margin-left: 5px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px;">Ignorar</button>
                `;
                document.body.insertBefore(loadDraftDiv, document.body.firstChild);

                window.restoreDraft = function() {
                    // Restaurar dados do rascunho
                    data.prices.forEach((price, index) => {
                        const input = document.getElementsByClassName('price-input')[index];
                        if (input) input.value = price;
                    });

                    data.ipi.forEach((ipi, index) => {
                        const input = document.getElementsByClassName('ipi-input')[index];
                        if (input) input.value = ipi;
                    });

                    data.icms.forEach((icms, index) => {
                        const input = document.getElementsByClassName('icms-input')[index];
                        if (input) input.value = icms;
                    });

                    document.getElementById('shippingType').value = data.shippingType || '';
                    document.getElementById('shippingCost').value = data.shippingCost || '';
                    document.getElementById('deliveryTime').value = data.deliveryTime || '';
                    document.getElementById('paymentTerms').value = data.paymentTerms || '';
                    document.getElementById('minimumOrder').value = data.minimumOrder || '';
                    document.getElementById('validity').value = data.validity || '';
                    document.getElementById('warranty').value = data.warranty || '';
                    document.getElementById('discount').value = data.discount || '';
                    document.getElementById('observations').value = data.observations || '';

                    updateTotals();
                    loadDraftDiv.remove();

                    // Remover rascunho após carregar
                    localStorage.removeItem(draftKey);
                };
            }
        }

        window.handleSubmit = async function(event) {
            event.preventDefault();

            const errorMessage = document.getElementById('errorMessage');
            errorMessage.style.display = 'none';

            // Validar formulário
            if (!validateForm()) {
                return;
            }

            // Confirmar envio
            const confirmSubmit = confirm('Tem certeza que deseja enviar esta resposta? Após o envio não será possível alterar.');
            if (!confirmSubmit) {
                return;
            }

            try {
                // Helper function to safely parse numbers and avoid NaN/undefined
                const safeParseInt = (value, defaultValue = 0) => {
                    const parsed = parseInt(value);
                    return isNaN(parsed) ? defaultValue : parsed;
                };

                const safeParseFloat = (value, defaultValue = 0) => {
                    const parsed = parseFloat(value);
                    return isNaN(parsed) ? defaultValue : parsed;
                };

                // Function to validate object for undefined values
                const validateObject = (obj, path = '') => {
                    for (const [key, value] of Object.entries(obj)) {
                        const currentPath = path ? `${path}.${key}` : key;
                        if (value === undefined) {
                            console.error(`❌ Undefined value found at: ${currentPath}`);
                            return false;
                        } else if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
                            if (!validateObject(value, currentPath)) {
                                return false;
                            }
                        } else if (Array.isArray(value)) {
                            for (let i = 0; i < value.length; i++) {
                                if (value[i] === undefined) {
                                    console.error(`❌ Undefined value found at: ${currentPath}[${i}]`);
                                    return false;
                                } else if (value[i] !== null && typeof value[i] === 'object') {
                                    if (!validateObject(value[i], `${currentPath}[${i}]`)) {
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                    return true;
                };

                // Coletar dados apenas dos itens selecionados
                const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
                const itensResposta = [];

                selectedCheckboxes.forEach(checkbox => {
                    const index = safeParseInt(checkbox.dataset.index, 0);
                    const quantityInput = document.querySelector(`.quantity-input[data-index="${index}"]`);
                    const unitSelect = document.querySelector(`.unit-select[data-index="${index}"]`);
                    const priceInput = document.querySelector(`.price-input[data-index="${index}"]`);
                    const ipiInput = document.querySelector(`.ipi-input[data-index="${index}"]`);
                    const icmsInput = document.querySelector(`.icms-input[data-index="${index}"]`);

                    const itemOriginal = currentQuotation.itens[index];
                    const quantidadeCotada = safeParseFloat(quantityInput.value, 0);
                    // IMPORTANTE: O preço é SEMPRE pela unidade de compra
                    const precoUnitario = safeParseFloat(priceInput.value, 0);
                    const unidadeCotacao = unitSelect.value || '';

                    // Determinar unidades e quantidades
                    const hasConversion = Boolean(itemOriginal.unidadeSecundaria && itemOriginal.fatorConversao);
                    const unidadeInterna = itemOriginal.unidade || 'UN';
                    const unidadeCompra = hasConversion ? itemOriginal.unidadeSecundaria : unidadeInterna;
                    const quantidadeSolicitadaCompra = hasConversion ?
                        (itemOriginal.quantidade * itemOriginal.fatorConversao) : itemOriginal.quantidade;

                    // Converter quantidade cotada para unidade de compra se necessário
                    let quantidadeFornecidaCompra = quantidadeCotada;
                    let unidadeResposta = unidadeCompra;

                    if (hasConversion && unidadeCotacao === 'interna') {
                        quantidadeFornecidaCompra = quantidadeCotada * itemOriginal.fatorConversao;
                        unidadeResposta = unidadeInterna;
                    }

                    itensResposta.push({
                        indiceItem: index,
                        codigo: itemOriginal.codigo || '',
                        descricao: itemOriginal.descricao || '',
                        // Dados da solicitação
                        quantidadeSolicitada: quantidadeSolicitadaCompra || 0,
                        unidadeSolicitada: unidadeCompra || 'UN',
                        // Dados da resposta
                        quantidadeFornecida: quantidadeCotada,
                        unidadeFornecida: unidadeResposta || 'UN',
                        quantidadeFornecidaCompra: quantidadeFornecidaCompra || 0, // Para cálculos
                        // Preço e impostos
                        precoUnitario: precoUnitario,
                        unidadePreco: unidadeResposta || 'UN',
                        ipi: safeParseFloat(ipiInput.value, 0),
                        icms: safeParseFloat(icmsInput.value, 0),
                        // Cálculo do valor total (sempre na base da unidade de compra)
                        valorTotal: (quantidadeFornecidaCompra || 0) * precoUnitario,
                        // Observações
                        temConversao: hasConversion,
                        fatorConversao: safeParseFloat(itemOriginal.fatorConversao, 1),
                        observacaoItem: quantidadeCotada !== quantidadeSolicitadaCompra ?
                            `Quantidade ajustada: solicitado ${quantidadeSolicitadaCompra.toFixed(3)} ${unidadeCompra}, fornecido ${quantidadeCotada.toFixed(3)} ${unidadeResposta}` : ''
                    });
                });

                const resposta = {
                    itens: itensResposta,
                    totalItensRespondidos: itensResposta.length,
                    totalItensDisponiveis: currentQuotation.itens.length,
                    frete: {
                        tipo: document.getElementById('shippingType').value || '',
                        valor: safeParseFloat(document.getElementById('shippingCost').value, 0)
                    },
                    condicaoPagamento: document.getElementById('paymentTerms').value || '',
                    prazoEntrega: safeParseInt(document.getElementById('deliveryTime').value, 1),
                    pedidoMinimo: safeParseFloat(document.getElementById('minimumOrder').value, 0),
                    validadeProposta: safeParseInt(document.getElementById('validity').value, 30),
                    garantia: safeParseInt(document.getElementById('warranty').value, 0),
                    desconto: safeParseFloat(document.getElementById('discount').value, 0),
                    observacoes: document.getElementById('observations').value || '',
                    dataResposta: Timestamp.now(),
                    fornecedorId: currentSupplier.id,
                    fornecedorNome: currentSupplier.nome || currentSupplier.razaoSocial || ''
                };

                const respostas = { ...(currentQuotation.respostas || {}) };
                respostas[currentSupplier.id] = resposta;

                const todosResponderam = currentQuotation.fornecedores.every(
                    fornecedorId => respostas[fornecedorId]
                );

                // Validate data before sending to Firestore
                const dataToUpdate = {
                    respostas,
                    status: todosResponderam ? 'RESPONDIDA' : 'ENVIADA'
                };

                console.log('🔍 Validating data before Firestore update:', dataToUpdate);

                if (!validateObject(dataToUpdate)) {
                    throw new Error('Dados contêm valores indefinidos que não podem ser salvos no Firestore');
                }

                console.log('✅ Data validation passed, updating Firestore...');
                await updateDoc(doc(db, "cotacoes", currentQuotation.id), dataToUpdate);

                document.getElementById('overlay').style.display = 'block';
                document.getElementById('successMessage').style.display = 'block';

                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            } catch (error) {
                console.error("Erro ao enviar resposta:", error);
                errorMessage.textContent = "Erro ao enviar resposta. Por favor, tente novamente.";
                errorMessage.style.display = 'block';
            }
        };
    </script>
</body>
</html>