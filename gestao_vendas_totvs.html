<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão de Vendas - Sistema ERP</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .tabs {
      display: flex;
      border-bottom: 3px solid #ecf0f1;
      margin-bottom: 30px;
      overflow-x: auto;
    }

    .tab {
      padding: 15px 25px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-weight: 600;
      color: #7f8c8d;
      border-radius: 8px 8px 0 0;
      margin-right: 5px;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .tab.active {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      transform: translateY(-2px);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .filters {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .form-control {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
    }

    .table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
    }

    .table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .status {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-align: center;
      min-width: 100px;
    }

    .status-orcamento {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status-aprovado {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-producao {
      background: #e2e3e5;
      color: #383d41;
      border: 1px solid #d6d8db;
    }

    .status-faturado {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }

    .status-entregue {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status-cancelado {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 6px;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 0;
      border-radius: 15px;
      width: 95%;
      max-width: 1000px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.2);
      animation: modalSlideIn 0.3s ease;
      max-height: 90vh;
      overflow-y: auto;
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .modal-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 20px 25px;
      border-radius: 15px 15px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-body {
      padding: 25px;
    }

    .close {
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .close:hover {
      transform: scale(1.1);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border-left: 5px solid;
      transition: all 0.3s ease;
      text-align: center;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card.orcamentos {
      border-left-color: #f39c12;
    }

    .stat-card.pedidos {
      border-left-color: #27ae60;
    }

    .stat-card.valor {
      border-left-color: #3498db;
    }

    .stat-card.clientes {
      border-left-color: #17a2b8;
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .stat-label {
      color: #7f8c8d;
      font-weight: 600;
      font-size: 14px;
    }

    .stat-icon {
      font-size: 32px;
      margin-bottom: 15px;
      opacity: 0.7;
    }

    /* Responsividade */
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        border-radius: 10px;
      }

      .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header h1 {
        font-size: 24px;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .main-content {
        padding: 20px;
      }

      .tabs {
        flex-wrap: wrap;
      }

      .tab {
        flex: 1;
        min-width: 120px;
      }

      .filter-row {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .table-container {
        overflow-x: auto;
      }

      .actions {
        flex-direction: column;
        gap: 5px;
      }

      .modal-content {
        width: 98%;
        margin: 5% auto;
      }

      .modal-body {
        padding: 15px;
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }

      .btn {
        padding: 10px 15px;
        font-size: 13px;
      }

      .header h1 {
        font-size: 20px;
      }

      .stat-number {
        font-size: 24px;
      }
    }

    /* Tabela TOTVS */
    .table-container {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .table-header {
      background: var(--secondary-color);
      padding: 15px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .table-actions {
      display: flex;
      gap: 10px;
    }

    .totvs-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 13px;
    }

    .totvs-table th {
      background: var(--secondary-color);
      padding: 12px 15px;
      text-align: left;
      font-weight: 600;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .totvs-table td {
      padding: 12px 15px;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .totvs-table tbody tr:hover {
      background: rgba(8, 84, 160, 0.05);
    }

    .totvs-table tbody tr:nth-child(even) {
      background: rgba(0,0,0,0.02);
    }

    /* Status Badges */
    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-orcamento { background: #e3f2fd; color: #1976d2; }
    .status-aprovado { background: #e8f5e8; color: #2e7d32; }
    .status-producao { background: #fff3e0; color: #f57c00; }
    .status-faturado { background: #e8f5e8; color: #388e3c; }
    .status-entregue { background: #e1f5fe; color: #0277bd; }
    .status-cancelado { background: #ffebee; color: #d32f2f; }

    /* Responsividade */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 10px;
      }

      .toolbar-content {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-content {
        grid-template-columns: 1fr;
      }

      .metrics-section {
        grid-template-columns: repeat(2, 1fr);
      }

      .totvs-table {
        font-size: 12px;
      }

      .totvs-table th,
      .totvs-table td {
        padding: 8px 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1>
        <i class="fas fa-chart-line"></i>
        Gestão de Vendas
      </h1>
      <div class="header-actions">
        <button class="btn btn-success" onclick="novoOrcamento()">
          <i class="fas fa-plus"></i>
          Novo Orçamento
        </button>
        <button class="btn btn-primary" onclick="novoPedido()">
          <i class="fas fa-shopping-cart"></i>
          Novo Pedido
        </button>
        <button class="btn btn-info" onclick="exportarDados()">
          <i class="fas fa-download"></i>
          Exportar
        </button>
        <button class="btn btn-warning" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i>
          Voltar
        </button>
      </div>
    </div>

    <!-- Conteúdo Principal -->
    <div class="main-content">
      <!-- Métricas -->
      <div class="stats-grid">
        <div class="stat-card orcamentos">
          <div class="stat-icon" style="color: #f39c12;">
            <i class="fas fa-file-invoice"></i>
          </div>
          <div class="stat-number" id="totalOrcamentos">0</div>
          <div class="stat-label">Orçamentos Ativos</div>
        </div>
        <div class="stat-card pedidos">
          <div class="stat-icon" style="color: #27ae60;">
            <i class="fas fa-shopping-cart"></i>
          </div>
          <div class="stat-number" id="totalPedidos">0</div>
          <div class="stat-label">Pedidos em Andamento</div>
        </div>
        <div class="stat-card valor">
          <div class="stat-icon" style="color: #3498db;">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="stat-number" id="valorTotal">R$ 0</div>
          <div class="stat-label">Valor Total</div>
        </div>
        <div class="stat-card clientes">
          <div class="stat-icon" style="color: #17a2b8;">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-number" id="totalClientes">0</div>
          <div class="stat-label">Clientes Ativos</div>
        </div>
      </div>

      <!-- Filtros -->
      <div class="filters">
        <div class="filter-row">
          <div class="form-group">
            <label>Período</label>
            <select class="form-control" id="periodoFilter">
              <option value="">Todos os períodos</option>
              <option value="hoje">Hoje</option>
              <option value="semana">Esta semana</option>
              <option value="mes">Este mês</option>
              <option value="trimestre">Este trimestre</option>
              <option value="ano">Este ano</option>
            </select>
          </div>
          <div class="form-group">
            <label>Status</label>
            <select class="form-control" id="statusFilter">
              <option value="">Todos os status</option>
              <option value="Orçamento">Orçamento</option>
              <option value="Aprovado">Aprovado</option>
              <option value="Em Produção">Em Produção</option>
              <option value="Faturado">Faturado</option>
              <option value="Entregue">Entregue</option>
              <option value="Cancelado">Cancelado</option>
            </select>
          </div>
          <div class="form-group">
            <label>Cliente</label>
            <select class="form-control" id="clienteFilter">
              <option value="">Todos os clientes</option>
            </select>
          </div>
          <div class="form-group">
            <label>Vendedor</label>
            <select class="form-control" id="vendedorFilter">
              <option value="">Todos os vendedores</option>
            </select>
          </div>
          <div class="form-group">
            <label>Valor Mínimo</label>
            <input type="number" class="form-control" id="valorMinFilter" placeholder="R$ 0,00" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor Máximo</label>
            <input type="number" class="form-control" id="valorMaxFilter" placeholder="R$ 999.999,99" step="0.01">
          </div>
        </div>
        <div style="text-align: center; margin-top: 15px;">
          <button class="btn btn-primary" onclick="aplicarFiltros()">
            <i class="fas fa-search"></i>
            Aplicar Filtros
          </button>
          <button class="btn btn-warning" onclick="limparFiltros()">
            <i class="fas fa-eraser"></i>
            Limpar
          </button>
        </div>
      </div>

      <!-- Tabela Principal -->
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>Número</th>
              <th>Tipo</th>
              <th>Cliente</th>
              <th>Data</th>
              <th>Valor Total</th>
              <th>Status</th>
              <th>Vendedor</th>
              <th>Prazo Entrega</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="vendasTableBody">
            <!-- Dados serão carregados via JavaScript -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Modal de Orçamento/Pedido -->
  <div id="vendaModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">Novo Orçamento</h2>
        <span class="close" onclick="fecharModal()">&times;</span>
      </div>

      <div class="modal-body">
        <form id="vendaForm">
          <input type="hidden" id="editingId">
          <input type="hidden" id="tipoDocumento" value="orcamento">

          <!-- Dados Gerais -->
          <div class="form-row">
            <div class="form-group">
              <label>Número</label>
              <input type="text" class="form-control" id="numeroVenda" readonly>
            </div>
            <div class="form-group">
              <label>Data Criação</label>
              <input type="date" class="form-control" id="dataCriacao" required>
            </div>
            <div class="form-group">
              <label>Data Validade</label>
              <input type="date" class="form-control" id="dataValidade" required>
            </div>
            <div class="form-group">
              <label>Status</label>
              <select class="form-control" id="statusVenda">
                <option value="Aberto">Aberto</option>
                <option value="Aprovado">Aprovado</option>
                <option value="Convertido em Pedido">Convertido em Pedido</option>
                <option value="Cancelado">Cancelado</option>
              </select>
            </div>
          </div>

          <!-- Cliente e Dados Comerciais -->
          <div class="form-row">
            <div class="form-group">
              <label>Cliente *</label>
              <select class="form-control" id="clienteSelect" required onchange="selecionarCliente()">
                <option value="">Selecione o cliente...</option>
              </select>
            </div>
            <div class="form-group">
              <label>CFOP *</label>
              <select class="form-control" id="cfopSelect" required>
                <option value="">Selecione o CFOP...</option>
              </select>
            </div>
            <div class="form-group">
              <label>Condição de Pagamento *</label>
              <select class="form-control" id="condicaoPagamento" required>
                <option value="">Selecione...</option>
              </select>
            </div>
            <div class="form-group">
              <label>Prazo Entrega (dias)</label>
              <input type="number" class="form-control" id="prazoEntrega" min="1" value="30">
            </div>
          </div>

          <!-- Frete e Transportadora -->
          <div class="form-row">
            <div class="form-group">
              <label>Tipo de Frete</label>
              <select class="form-control" id="tipoFrete">
                <option value="FOB">FOB - Por conta do destinatário</option>
                <option value="CIF">CIF - Por conta do remetente</option>
              </select>
            </div>
            <div class="form-group">
              <label>Transportadora</label>
              <select class="form-control" id="transportadoraSelect">
                <option value="">Selecione a transportadora...</option>
              </select>
            </div>
            <div class="form-group">
              <label>Nº Pedido Cliente</label>
              <input type="text" class="form-control" id="numeroPedidoCliente" placeholder="Número do pedido do cliente">
            </div>
            <div class="form-group">
              <label>Vendedor</label>
              <select class="form-control" id="vendedorSelect">
                <option value="">Selecione o vendedor...</option>
              </select>
            </div>
          </div>

          <!-- Itens do Orçamento/Pedido -->
          <div style="margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
              <h3>Itens</h3>
              <button type="button" class="btn btn-success btn-sm" onclick="adicionarItem()">
                <i class="fas fa-plus"></i>
                Adicionar Item
              </button>
            </div>

            <div class="table-container">
              <table class="table" id="itensTable">
                <thead>
                  <tr>
                    <th>Produto</th>
                    <th>Quantidade</th>
                    <th>Valor Unitário</th>
                    <th>Valor Total</th>
                    <th>Ações</th>
                  </tr>
                </thead>
                <tbody id="itensTableBody">
                  <!-- Itens serão adicionados dinamicamente -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Totais e Impostos -->
          <div class="form-row">
            <div class="form-group">
              <label>Subtotal</label>
              <input type="text" class="form-control" id="subtotal" readonly>
            </div>
            <div class="form-group">
              <label>Desconto (%)</label>
              <input type="number" class="form-control" id="desconto" step="0.01" min="0" max="100" onchange="calcularTotais()">
            </div>
            <div class="form-group">
              <label>Valor Desconto</label>
              <input type="text" class="form-control" id="valorDesconto" readonly>
            </div>
            <div class="form-group">
              <label>Total Geral</label>
              <input type="text" class="form-control" id="valorTotal" readonly style="font-weight: bold; font-size: 16px;">
            </div>
          </div>

          <!-- Impostos -->
          <div class="form-row">
            <div class="form-group">
              <label>ICMS</label>
              <input type="text" class="form-control" id="totalICMS" readonly>
            </div>
            <div class="form-group">
              <label>IPI</label>
              <input type="text" class="form-control" id="totalIPI" readonly>
            </div>
            <div class="form-group">
              <label>PIS</label>
              <input type="text" class="form-control" id="totalPIS" readonly>
            </div>
            <div class="form-group">
              <label>COFINS</label>
              <input type="text" class="form-control" id="totalCOFINS" readonly>
            </div>
          </div>

          <!-- Observações -->
          <div class="form-row">
            <div class="form-group" style="grid-column: 1 / -1;">
              <label>Observações</label>
              <textarea class="form-control" id="observacoes" rows="3" placeholder="Observações gerais do orçamento/pedido..."></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="fecharModal()">
          <i class="fas fa-times"></i>
          Cancelar
        </button>
        <button type="button" class="btn btn-primary" onclick="salvarVenda()">
          <i class="fas fa-save"></i>
          Salvar
        </button>
      </div>
    </div>
  </div>

  <!-- Modal para Adicionar Item -->
  <div id="itemModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h2>Adicionar Item</h2>
        <span class="close" onclick="fecharModalItem()">&times;</span>
      </div>

      <div class="modal-body">
        <form id="itemForm">
          <div class="form-row">
            <div class="form-group">
              <label>Produto *</label>
              <select class="form-control" id="produtoSelect" required onchange="selecionarProduto()">
                <option value="">Selecione o produto...</option>
              </select>
            </div>
            <div class="form-group">
              <label>Código</label>
              <input type="text" class="form-control" id="codigoProduto" readonly>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Descrição</label>
              <input type="text" class="form-control" id="descricaoProduto" readonly>
            </div>
            <div class="form-group">
              <label>Unidade</label>
              <input type="text" class="form-control" id="unidadeProduto" readonly>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Quantidade *</label>
              <input type="number" class="form-control" id="quantidadeItem" min="0.01" step="0.01" required onchange="calcularTotalItem()">
            </div>
            <div class="form-group">
              <label>Valor Unitário *</label>
              <input type="number" class="form-control" id="valorUnitarioItem" min="0.01" step="0.01" required onchange="calcularTotalItem()">
            </div>
            <div class="form-group">
              <label>Valor Total</label>
              <input type="text" class="form-control" id="valorTotalItem" readonly>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Estoque Disponível</label>
              <input type="text" class="form-control" id="estoqueDisponivel" readonly>
            </div>
            <div class="form-group">
              <label>Preço Sugerido</label>
              <input type="text" class="form-control" id="precoSugerido" readonly>
            </div>
          </div>

          <!-- Impostos do Item -->
          <h4 style="margin: 20px 0 10px 0; color: #2c3e50;">Impostos</h4>
          <div class="form-row">
            <div class="form-group">
              <label>ICMS (%)</label>
              <input type="number" class="form-control" id="aliquotaICMS" step="0.01" onchange="calcularImpostosItem()">
            </div>
            <div class="form-group">
              <label>IPI (%)</label>
              <input type="number" class="form-control" id="aliquotaIPI" step="0.01" onchange="calcularImpostosItem()">
            </div>
            <div class="form-group">
              <label>PIS (%)</label>
              <input type="number" class="form-control" id="aliquotaPIS" step="0.01" onchange="calcularImpostosItem()">
            </div>
            <div class="form-group">
              <label>COFINS (%)</label>
              <input type="number" class="form-control" id="aliquotaCOFINS" step="0.01" onchange="calcularImpostosItem()">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Valor ICMS</label>
              <input type="text" class="form-control" id="valorICMSItem" readonly>
            </div>
            <div class="form-group">
              <label>Valor IPI</label>
              <input type="text" class="form-control" id="valorIPIItem" readonly>
            </div>
            <div class="form-group">
              <label>Valor PIS</label>
              <input type="text" class="form-control" id="valorPISItem" readonly>
            </div>
            <div class="form-group">
              <label>Valor COFINS</label>
              <input type="text" class="form-control" id="valorCOFINSItem" readonly>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="fecharModalItem()">
          <i class="fas fa-times"></i>
          Cancelar
        </button>
        <button type="button" class="btn btn-primary" onclick="confirmarItem()">
          <i class="fas fa-check"></i>
          Adicionar Item
        </button>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script>
    // Funções globais que precisam estar disponíveis no HTML
    window.novoOrcamento = function() {
      if (typeof editandoId !== 'undefined') {
        editandoId = null;
      }
      document.getElementById('modalTitle').textContent = 'Novo Orçamento';
      document.getElementById('tipoDocumento').value = 'orcamento';
      document.getElementById('numeroVenda').value = gerarNumero('ORC');
      limparFormulario();
      document.getElementById('vendaModal').style.display = 'block';
    };

    window.novoPedido = function() {
      if (typeof editandoId !== 'undefined') {
        editandoId = null;
      }
      document.getElementById('modalTitle').textContent = 'Novo Pedido de Venda';
      document.getElementById('tipoDocumento').value = 'pedido';
      document.getElementById('numeroVenda').value = gerarNumero('PV');
      limparFormulario();

      // Para pedidos, status padrão é "Aprovado"
      if (document.getElementById('statusVenda')) {
        document.getElementById('statusVenda').value = 'Aprovado';
      }

      document.getElementById('vendaModal').style.display = 'block';
    };

    window.fecharModal = function() {
      document.getElementById('vendaModal').style.display = 'none';
      if (typeof limparFormulario === 'function') {
        limparFormulario();
      }
    };

    window.toggleFilters = function() {
      const content = document.getElementById('filtersContent');
      const icon = document.getElementById('filterIcon');

      if (content.style.display === 'none') {
        content.style.display = 'grid';
        icon.className = 'fas fa-chevron-up';
        document.querySelector('.filters-toggle span').textContent = 'Recolher';
      } else {
        content.style.display = 'none';
        icon.className = 'fas fa-chevron-down';
        document.querySelector('.filters-toggle span').textContent = 'Expandir';
      }
    };

    function gerarNumero(prefixo) {
      const timestamp = Date.now().toString().slice(-6);
      return `${prefixo}${timestamp}`;
    }

    // Outras funções globais
    window.adicionarItem = function() {
      if (typeof editandoItemIndex !== 'undefined') {
        editandoItemIndex = -1;
      }
      limparFormularioItem();
      document.getElementById('itemModal').style.display = 'block';
    };

    window.fecharModalItem = function() {
      document.getElementById('itemModal').style.display = 'none';
      limparFormularioItem();
    };

    window.selecionarCliente = function() {
      // Implementação será feita no módulo
      console.log('Cliente selecionado');
    };

    window.selecionarProduto = function() {
      // Implementação será feita no módulo
      console.log('Produto selecionado');
    };

    window.calcularTotalItem = function() {
      // Implementação será feita no módulo
      console.log('Calculando total do item');
    };

    window.calcularImpostosItem = function() {
      // Implementação será feita no módulo
      console.log('Calculando impostos do item');
    };

    window.confirmarItem = function() {
      // Implementação será feita no módulo
      console.log('Confirmando item');
    };

    window.editarItem = function(index) {
      // Implementação será feita no módulo
      console.log('Editando item:', index);
    };

    window.removerItem = function(index) {
      // Implementação será feita no módulo
      console.log('Removendo item:', index);
    };

    window.salvarVenda = function() {
      // Implementação será feita no módulo
      console.log('Salvando venda');
    };

    window.editarVenda = function(id, tipo) {
      // Implementação será feita no módulo
      console.log('Editando venda:', id, tipo);
    };

    window.excluirVenda = function(id, tipo) {
      // Implementação será feita no módulo
      console.log('Excluindo venda:', id, tipo);
    };

    window.limparFiltros = function() {
      // Implementação será feita no módulo
      console.log('Limpando filtros');
    };

    window.atualizarDados = function() {
      // Implementação será feita no módulo
      console.log('Atualizando dados');
    };

    // Funções auxiliares globais
    function limparFormulario() {
      console.log('Limpando formulário');
    }

    function limparFormularioItem() {
      console.log('Limpando formulário de item');
    }
  </script>

  <script type="module">
    // Usar configuração do firebase-config.js
    import { db } from './firebase-config.js';
    import {
      collection,
      addDoc,
      getDocs,
      doc,
      updateDoc,
      deleteDoc,
      query,
      where,
      orderBy,
      Timestamp
    } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

    // Variáveis globais
    let clientes = [];
    let produtos = [];
    let vendedores = [];
    let condicoesPagamento = [];
    let cfops = [];
    let orcamentos = [];
    let pedidos = [];
    let transportadoras = [];
    let tabelaPrecos = [];
    let configImpostos = [];
    let estoques = [];
    let itemCount = 0;
    let editandoId = null;
    let itensTemporarios = [];
    let editandoItemIndex = -1;

    // Alíquotas padrão de impostos
    const ALIQUOTAS_PADRAO = {
      icms: 18,
      ipi: 5,
      pis: 1.65,
      cofins: 7.6
    };

    // Inicialização
    window.addEventListener('DOMContentLoaded', async () => {
      try {
        console.log('Iniciando sistema de vendas...');
        await verificarAutenticacao();
        await carregarDadosIniciais();
        await carregarVendas();
        atualizarMetricas();
        configurarEventos();
        console.log('Sistema de vendas inicializado com sucesso!');
      } catch (error) {
        console.error('Erro na inicialização:', error);
        Swal.fire('Erro', 'Erro ao inicializar o sistema. Verifique a conexão.', 'error');
      }
    });

    // Verificar autenticação
    async function verificarAutenticacao() {
      const userData = localStorage.getItem('currentUser');
      if (!userData) {
        window.location.href = 'login.html';
        return;
      }

      const user = JSON.parse(userData);
      document.getElementById('userName').textContent = user.nome || 'Usuário';

      // Carregar dados da empresa
      try {
        const empresaSnap = await getDocs(collection(db, "empresa"));
        if (!empresaSnap.empty) {
          const empresa = empresaSnap.docs[0].data();
          document.getElementById('companyName').textContent = empresa.razaoSocial || 'Empresa';
        }
      } catch (error) {
        console.error('Erro ao carregar dados da empresa:', error);
      }
    }

    // Carregar dados iniciais
    async function carregarDadosIniciais() {
      try {
        console.log('Carregando dados do Firebase...');

        // Carregar dados básicos primeiro
        const [
          fornecedoresSnap,
          produtosSnap,
          usuariosSnap,
          cfopsSnap
        ] = await Promise.all([
          getDocs(collection(db, "fornecedores")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "usuarios")),
          getDocs(collection(db, "cfops"))
        ]);

        // Processar fornecedores (clientes e transportadoras)
        const todosFornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        clientes = todosFornecedores.filter(f =>
          (f.tipo === "Cliente" || f.tipo === "Ambos") && f.ativo !== false
        );
        transportadoras = todosFornecedores.filter(f =>
          (f.tipo === "Fornecedor" || f.tipo === "Ambos") && f.ativo !== false
        );

        // Processar produtos
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
          .filter(p => p.ativo !== false);

        // Processar usuários (vendedores)
        vendedores = usuariosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Processar CFOPs
        cfops = cfopsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
          .filter(c => c.ativo !== false);

        console.log('Dados carregados:', {
          clientes: clientes.length,
          produtos: produtos.length,
          vendedores: vendedores.length,
          cfops: cfops.length
        });

        // Carregar dados opcionais
        try {
          const [
            condicoesSnap,
            orcamentosSnap,
            pedidosSnap,
            estoquesSnap
          ] = await Promise.all([
            getDocs(collection(db, "condicoesEspeciais")).catch(() => ({ docs: [] })),
            getDocs(collection(db, "orcamentos")).catch(() => ({ docs: [] })),
            getDocs(collection(db, "pedidosVenda")).catch(() => ({ docs: [] })),
            getDocs(collection(db, "estoques")).catch(() => ({ docs: [] }))
          ]);

          condicoesPagamento = condicoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          orcamentos = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          // Se não há condições de pagamento, criar algumas padrão
          if (condicoesPagamento.length === 0) {
            condicoesPagamento = [
              { id: 'avista', descricao: 'À Vista' },
              { id: '30dias', descricao: '30 dias' },
              { id: '30-60', descricao: '30/60 dias' }
            ];
          }

        } catch (error) {
          console.warn('Algumas coleções opcionais não foram encontradas:', error);
        }

        preencherSelects();

      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        Swal.fire('Erro', 'Erro ao carregar dados do sistema. Verifique a conexão com o Firebase.', 'error');

        // Usar dados mínimos para não quebrar o sistema
        clientes = [];
        produtos = [];
        vendedores = [];
        cfops = [];
        condicoesPagamento = [
          { id: 'avista', descricao: 'À Vista' },
          { id: '30dias', descricao: '30 dias' }
        ];
      }
    }

    // Preencher selects
    function preencherSelects() {
      console.log('Preenchendo selects com dados:', {
        clientes: clientes.length,
        produtos: produtos.length,
        vendedores: vendedores.length,
        cfops: cfops.length
      });

      // Clientes
      const clienteSelect = document.getElementById('clienteSelect');
      const clienteFilter = document.getElementById('clienteFilter');

      if (clienteSelect) {
        clienteSelect.innerHTML = '<option value="">Selecione o cliente...</option>';
        clientes.forEach(cliente => {
          const nome = cliente.nomeFantasia || cliente.razaoSocial || cliente.nome || 'Cliente sem nome';
          const codigo = cliente.codigo ? `[${cliente.codigo}] ` : '';
          clienteSelect.innerHTML += `<option value="${cliente.id}">${codigo}${nome}</option>`;
        });
        console.log(`Clientes carregados no select: ${clientes.length}`);
      }

      if (clienteFilter) {
        clienteFilter.innerHTML = '<option value="">Todos os clientes</option>';
        clientes.forEach(cliente => {
          const nome = cliente.nomeFantasia || cliente.razaoSocial || cliente.nome || 'Cliente sem nome';
          clienteFilter.innerHTML += `<option value="${cliente.id}">${nome}</option>`;
        });
      }

      // Produtos
      const produtoSelect = document.getElementById('produtoSelect');
      if (produtoSelect) {
        produtoSelect.innerHTML = '<option value="">Selecione o produto...</option>';
        produtos.forEach(produto => {
          const codigo = produto.codigo || 'SEM CÓDIGO';
          const descricao = produto.descricao || 'Produto sem descrição';
          produtoSelect.innerHTML += `<option value="${produto.id}">${codigo} - ${descricao}</option>`;
        });
        console.log(`Produtos carregados no select: ${produtos.length}`);
      }

      // Vendedores
      const vendedorSelect = document.getElementById('vendedorSelect');
      const vendedorFilter = document.getElementById('vendedorFilter');

      if (vendedorSelect) {
        vendedorSelect.innerHTML = '<option value="">Selecione o vendedor...</option>';
        vendedores.forEach(vendedor => {
          const nome = vendedor.nome || vendedor.nomeCompleto || 'Vendedor sem nome';
          vendedorSelect.innerHTML += `<option value="${vendedor.id}">${nome}</option>`;
        });
        console.log(`Vendedores carregados no select: ${vendedores.length}`);
      }

      if (vendedorFilter) {
        vendedorFilter.innerHTML = '<option value="">Todos os vendedores</option>';
        vendedores.forEach(vendedor => {
          const nome = vendedor.nome || vendedor.nomeCompleto || 'Vendedor sem nome';
          vendedorFilter.innerHTML += `<option value="${vendedor.id}">${nome}</option>`;
        });
      }

      // Condições de Pagamento
      const condicaoSelect = document.getElementById('condicaoPagamento');
      if (condicaoSelect) {
        condicaoSelect.innerHTML = '<option value="">Selecione...</option>';
        condicoesPagamento.forEach(condicao => {
          const descricao = condicao.descricao || condicao.nome || 'Condição sem descrição';
          condicaoSelect.innerHTML += `<option value="${condicao.id}">${descricao}</option>`;
        });
        console.log(`Condições de pagamento carregadas: ${condicoesPagamento.length}`);
      }

      // CFOPs
      const cfopSelect = document.getElementById('cfopSelect');
      if (cfopSelect) {
        cfopSelect.innerHTML = '<option value="">Selecione o CFOP...</option>';
        cfops.forEach(cfop => {
          const codigo = cfop.codigo || cfop.cfop || 'SEM CÓDIGO';
          const descricao = cfop.descricao || 'CFOP sem descrição';
          cfopSelect.innerHTML += `<option value="${codigo}">${codigo} - ${descricao}</option>`;
        });
        console.log(`CFOPs carregados no select: ${cfops.length}`);
      }

      // Transportadoras
      const transportadoraSelect = document.getElementById('transportadoraSelect');
      if (transportadoraSelect) {
        transportadoraSelect.innerHTML = '<option value="">Selecione a transportadora...</option>';
        transportadoras.forEach(transportadora => {
          const nome = transportadora.nomeFantasia || transportadora.razaoSocial || transportadora.nome || 'Transportadora';
          const codigo = transportadora.codigo ? `[${transportadora.codigo}] ` : '';
          transportadoraSelect.innerHTML += `<option value="${transportadora.id}">${codigo}${nome}</option>`;
        });
        console.log(`Transportadoras carregadas: ${transportadoras.length}`);
      }
    }

    // Sobrescrever funções globais com implementações reais
    window.novoOrcamento = function() {
      editandoId = null;
      document.getElementById('modalTitle').textContent = 'Novo Orçamento';
      document.getElementById('tipoDocumento').value = 'orcamento';
      document.getElementById('numeroVenda').value = gerarNumero('ORC');
      limparFormulario();
      document.getElementById('vendaModal').style.display = 'block';
    };

    window.novoPedido = function() {
      editandoId = null;
      document.getElementById('modalTitle').textContent = 'Novo Pedido de Venda';
      document.getElementById('tipoDocumento').value = 'pedido';
      document.getElementById('numeroVenda').value = gerarNumero('PV');
      limparFormulario();

      // Para pedidos, status padrão é "Aprovado"
      if (document.getElementById('statusVenda')) {
        document.getElementById('statusVenda').value = 'Aprovado';
      }

      document.getElementById('vendaModal').style.display = 'block';
    };

    window.fecharModal = function() {
      document.getElementById('vendaModal').style.display = 'none';
      limparFormulario();
    };

    window.logout = function() {
      localStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    };

    window.exportarDados = function() {
      Swal.fire('Info', 'Funcionalidade de exportação em desenvolvimento', 'info');
    };

    window.aplicarFiltros = function() {
      carregarVendas();
    };

    // Sobrescrever outras funções globais
    window.limparFiltros = function() {
      document.getElementById('periodoFilter').value = '';
      document.getElementById('statusFilter').value = '';
      document.getElementById('clienteFilter').value = '';
      document.getElementById('vendedorFilter').value = '';
      document.getElementById('valorMinFilter').value = '';
      document.getElementById('valorMaxFilter').value = '';
      carregarVendas();
    };

    window.selecionarCliente = function() {
      const clienteId = document.getElementById('clienteSelect').value;
      if (!clienteId) return;

      const cliente = clientes.find(c => c.id === clienteId);
      if (cliente) {
        // Aqui você pode preencher campos automáticos baseados no cliente
        // Por exemplo, condição de pagamento padrão, vendedor responsável, etc.
        console.log('Cliente selecionado:', cliente);
      }
    };

    window.atualizarDados = function() {
      carregarDadosIniciais().then(() => {
        carregarVendas();
        atualizarMetricas();
        Swal.fire('Sucesso', 'Dados atualizados com sucesso!', 'success');
      }).catch(error => {
        console.error('Erro ao atualizar dados:', error);
        Swal.fire('Erro', 'Erro ao atualizar dados', 'error');
      });
    };

    // Implementações reais das funções auxiliares
    function limparFormulario() {
      if (document.getElementById('vendaForm')) {
        document.getElementById('vendaForm').reset();
      }
      itensTemporarios = [];
      if (typeof atualizarTabelaItens === 'function') {
        atualizarTabelaItens();
      }
      editandoId = null;
      editandoItemIndex = -1;

      // Definir valores padrão
      if (document.getElementById('dataCriacao')) {
        document.getElementById('dataCriacao').value = new Date().toISOString().split('T')[0];
      }
      if (document.getElementById('dataValidade')) {
        const dataValidade = new Date();
        dataValidade.setDate(dataValidade.getDate() + 30);
        document.getElementById('dataValidade').value = dataValidade.toISOString().split('T')[0];
      }
      if (document.getElementById('statusVenda')) {
        document.getElementById('statusVenda').value = 'Aberto';
      }
      if (document.getElementById('tipoFrete')) {
        document.getElementById('tipoFrete').value = 'FOB';
      }
      if (document.getElementById('prazoEntrega')) {
        document.getElementById('prazoEntrega').value = '30';
      }

      if (typeof calcularTotais === 'function') {
        calcularTotais();
      }
    }

    // Sobrescrever função global
    window.limparFormulario = limparFormulario;

    window.salvarVenda = function() {
      const clienteId = document.getElementById('clienteSelect').value;
      const cfop = document.getElementById('cfopSelect').value;
      const condicaoPagamentoId = document.getElementById('condicaoPagamento').value;

      if (!clienteId || !cfop || !condicaoPagamentoId) {
        Swal.fire('Atenção', 'Preencha todos os campos obrigatórios', 'warning');
        return;
      }

      if (itensTemporarios.length === 0) {
        Swal.fire('Atenção', 'Adicione pelo menos um item ao orçamento', 'warning');
        return;
      }

      const tipoDoc = document.getElementById('tipoDocumento').value;
      const isOrcamento = tipoDoc === 'orcamento';

      // Calcular totais
      let subtotal = 0;
      let totalICMS = 0;
      let totalIPI = 0;
      let totalPIS = 0;
      let totalCOFINS = 0;

      itensTemporarios.forEach(item => {
        subtotal += item.valorTotal;
        totalICMS += item.valorICMS;
        totalIPI += item.valorIPI;
        totalPIS += item.valorPIS;
        totalCOFINS += item.valorCOFINS;
      });

      const desconto = parseFloat(document.getElementById('desconto')?.value || 0);
      const valorDesconto = subtotal * (desconto / 100);
      const valorTotal = subtotal - valorDesconto;

      const documento = {
        numero: document.getElementById('numeroVenda').value,
        clienteId: clienteId,
        dataCriacao: Timestamp.fromDate(new Date(document.getElementById('dataCriacao').value)),
        dataValidade: Timestamp.fromDate(new Date(document.getElementById('dataValidade').value)),
        status: document.getElementById('statusVenda').value,
        cfop: cfop,
        condicaoPagamentoId: condicaoPagamentoId,
        vendedorId: document.getElementById('vendedorSelect').value || null,
        transportadoraId: document.getElementById('transportadoraSelect').value || null,
        tipoFrete: document.getElementById('tipoFrete').value,
        numeroPedidoCliente: document.getElementById('numeroPedidoCliente').value || null,
        prazoEntrega: parseInt(document.getElementById('prazoEntrega').value),
        itens: itensTemporarios,
        subtotal: subtotal,
        percentualDesconto: desconto,
        valorDesconto: valorDesconto,
        valorTotal: valorTotal,
        totalICMS: totalICMS,
        totalIPI: totalIPI,
        totalPIS: totalPIS,
        totalCOFINS: totalCOFINS,
        observacoes: document.getElementById('observacoes').value || null,
        usuarioCriacao: localStorage.getItem('currentUser') ? JSON.parse(localStorage.getItem('currentUser')).id : null,
        dataUltimaAtualizacao: Timestamp.now()
      };

      if (!editandoId) {
        documento.dataCriacao = Timestamp.now();
      }

      salvarDocumento(documento, isOrcamento);
    };

    async function salvarDocumento(documento, isOrcamento) {
      try {
        const colecao = isOrcamento ? 'orcamentos' : 'pedidosVenda';

        if (editandoId) {
          await updateDoc(doc(db, colecao, editandoId), documento);
          Swal.fire('Sucesso', `${isOrcamento ? 'Orçamento' : 'Pedido'} atualizado com sucesso!`, 'success');
        } else {
          await addDoc(collection(db, colecao), documento);
          Swal.fire('Sucesso', `${isOrcamento ? 'Orçamento' : 'Pedido'} criado com sucesso!`, 'success');
        }

        fecharModal();
        await carregarDadosIniciais();
        carregarVendas();
        atualizarMetricas();

      } catch (error) {
        console.error('Erro ao salvar:', error);
        Swal.fire('Erro', 'Erro ao salvar documento', 'error');
      }
    }

    // Implementar funções de gerenciamento de itens
    window.adicionarItem = function() {
      editandoItemIndex = -1;
      limparFormularioItem();
      document.getElementById('itemModal').style.display = 'block';
    };

    window.fecharModalItem = function() {
      document.getElementById('itemModal').style.display = 'none';
      limparFormularioItem();
    };

    function limparFormularioItem() {
      if (document.getElementById('itemForm')) {
        document.getElementById('itemForm').reset();
      }
      document.getElementById('codigoProduto').value = '';
      document.getElementById('descricaoProduto').value = '';
      document.getElementById('unidadeProduto').value = '';
      document.getElementById('estoqueDisponivel').value = '';
      document.getElementById('precoSugerido').value = '';
      document.getElementById('valorTotalItem').value = '';
      document.getElementById('valorICMSItem').value = '';
      document.getElementById('valorIPIItem').value = '';
      document.getElementById('valorPISItem').value = '';
      document.getElementById('valorCOFINSItem').value = '';
    }

    // Sobrescrever função global
    window.limparFormularioItem = limparFormularioItem;

    window.selecionarProduto = function() {
      const produtoId = document.getElementById('produtoSelect').value;
      if (!produtoId) {
        limparCamposProduto();
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      if (produto) {
        console.log('Produto selecionado:', produto);

        document.getElementById('codigoProduto').value = produto.codigo || 'SEM CÓDIGO';
        document.getElementById('descricaoProduto').value = produto.descricao || 'Produto sem descrição';
        document.getElementById('unidadeProduto').value = produto.unidade || produto.unidadeMedida || 'UN';

        // Buscar estoque
        const estoque = estoques.find(e => e.produtoId === produtoId);
        const saldoEstoque = estoque ? (estoque.saldo || 0) : 0;
        document.getElementById('estoqueDisponivel').value = saldoEstoque.toLocaleString('pt-BR', { minimumFractionDigits: 2 });

        // Definir preço sugerido
        let precoSugerido = 0;

        // Tentar buscar na tabela de preços primeiro
        const preco = tabelaPrecos.find(p => p.produtoId === produtoId);
        if (preco && preco.preco) {
          precoSugerido = preco.preco;
        } else if (produto.precoVenda) {
          precoSugerido = produto.precoVenda;
        } else if (produto.preco) {
          precoSugerido = produto.preco;
        } else {
          precoSugerido = 0;
        }

        document.getElementById('precoSugerido').value = `R$ ${precoSugerido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        document.getElementById('valorUnitarioItem').value = precoSugerido;

        // Configurar alíquotas padrão
        document.getElementById('aliquotaICMS').value = ALIQUOTAS_PADRAO.icms;
        document.getElementById('aliquotaIPI').value = ALIQUOTAS_PADRAO.ipi;
        document.getElementById('aliquotaPIS').value = ALIQUOTAS_PADRAO.pis;
        document.getElementById('aliquotaCOFINS').value = ALIQUOTAS_PADRAO.cofins;

        calcularImpostosItem();
      } else {
        console.warn('Produto não encontrado:', produtoId);
        limparCamposProduto();
      }
    };

    function limparCamposProduto() {
      document.getElementById('codigoProduto').value = '';
      document.getElementById('descricaoProduto').value = '';
      document.getElementById('unidadeProduto').value = '';
      document.getElementById('estoqueDisponivel').value = '';
      document.getElementById('precoSugerido').value = '';
      document.getElementById('valorUnitarioItem').value = '';
    }

    window.calcularTotalItem = function() {
      const quantidade = parseFloat(document.getElementById('quantidadeItem').value || 0);
      const valorUnitario = parseFloat(document.getElementById('valorUnitarioItem').value || 0);
      const total = quantidade * valorUnitario;

      document.getElementById('valorTotalItem').value = `R$ ${total.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      if (typeof calcularImpostosItem === 'function') {
        calcularImpostosItem();
      }
    };

    window.calcularImpostosItem = function() {
      const valorTotal = parseFloat(document.getElementById('valorUnitarioItem').value || 0) *
                        parseFloat(document.getElementById('quantidadeItem').value || 0);

      const icms = valorTotal * (parseFloat(document.getElementById('aliquotaICMS').value || 0) / 100);
      const ipi = valorTotal * (parseFloat(document.getElementById('aliquotaIPI').value || 0) / 100);
      const pis = valorTotal * (parseFloat(document.getElementById('aliquotaPIS').value || 0) / 100);
      const cofins = valorTotal * (parseFloat(document.getElementById('aliquotaCOFINS').value || 0) / 100);

      document.getElementById('valorICMSItem').value = `R$ ${icms.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      document.getElementById('valorIPIItem').value = `R$ ${ipi.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      document.getElementById('valorPISItem').value = `R$ ${pis.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      document.getElementById('valorCOFINSItem').value = `R$ ${cofins.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
    };

    window.confirmarItem = function() {
      const produtoId = document.getElementById('produtoSelect').value;
      const quantidade = parseFloat(document.getElementById('quantidadeItem').value);
      const valorUnitario = parseFloat(document.getElementById('valorUnitarioItem').value);

      if (!produtoId || !quantidade || !valorUnitario) {
        Swal.fire('Atenção', 'Preencha todos os campos obrigatórios', 'warning');
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      if (!produto) {
        Swal.fire('Erro', 'Produto não encontrado', 'error');
        return;
      }

      const item = {
        produtoId: produtoId,
        codigo: produto.codigo || 'SEM CÓDIGO',
        descricao: produto.descricao || 'Produto sem descrição',
        unidade: produto.unidade || produto.unidadeMedida || 'UN',
        quantidade: quantidade,
        valorUnitario: valorUnitario,
        valorTotal: quantidade * valorUnitario,
        aliquotaICMS: parseFloat(document.getElementById('aliquotaICMS').value || 0),
        aliquotaIPI: parseFloat(document.getElementById('aliquotaIPI').value || 0),
        aliquotaPIS: parseFloat(document.getElementById('aliquotaPIS').value || 0),
        aliquotaCOFINS: parseFloat(document.getElementById('aliquotaCOFINS').value || 0),
        valorICMS: quantidade * valorUnitario * (parseFloat(document.getElementById('aliquotaICMS').value || 0) / 100),
        valorIPI: quantidade * valorUnitario * (parseFloat(document.getElementById('aliquotaIPI').value || 0) / 100),
        valorPIS: quantidade * valorUnitario * (parseFloat(document.getElementById('aliquotaPIS').value || 0) / 100),
        valorCOFINS: quantidade * valorUnitario * (parseFloat(document.getElementById('aliquotaCOFINS').value || 0) / 100)
      };

      if (editandoItemIndex >= 0) {
        itensTemporarios[editandoItemIndex] = item;
      } else {
        itensTemporarios.push(item);
      }

      atualizarTabelaItens();
      window.fecharModalItem();
      calcularTotais();
    };

    function limparFormularioItem() {
      document.getElementById('itemForm').reset();
      document.getElementById('codigoProduto').value = '';
      document.getElementById('descricaoProduto').value = '';
      document.getElementById('unidadeProduto').value = '';
      document.getElementById('estoqueDisponivel').value = '';
      document.getElementById('precoSugerido').value = '';
      document.getElementById('valorTotalItem').value = '';
      document.getElementById('valorICMSItem').value = '';
      document.getElementById('valorIPIItem').value = '';
      document.getElementById('valorPISItem').value = '';
      document.getElementById('valorCOFINSItem').value = '';
    }

    function atualizarTabelaItens() {
      const tbody = document.getElementById('itensTableBody');
      tbody.innerHTML = '';

      itensTemporarios.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.codigo} - ${item.descricao}</td>
          <td>${item.quantidade} ${item.unidade}</td>
          <td>R$ ${item.valorUnitario.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</td>
          <td>R$ ${item.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</td>
          <td>
            <div class="actions">
              <button class="btn btn-warning btn-sm" onclick="editarItem(${index})" title="Editar">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-danger btn-sm" onclick="removerItem(${index})" title="Remover">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    window.editarItem = function(index) {
      editandoItemIndex = index;
      const item = itensTemporarios[index];

      document.getElementById('produtoSelect').value = item.produtoId;
      window.selecionarProduto();
      document.getElementById('quantidadeItem').value = item.quantidade;
      document.getElementById('valorUnitarioItem').value = item.valorUnitario;
      document.getElementById('aliquotaICMS').value = item.aliquotaICMS;
      document.getElementById('aliquotaIPI').value = item.aliquotaIPI;
      document.getElementById('aliquotaPIS').value = item.aliquotaPIS;
      document.getElementById('aliquotaCOFINS').value = item.aliquotaCOFINS;

      window.calcularTotalItem();
      document.getElementById('itemModal').style.display = 'block';
    };

    window.removerItem = function(index) {
      Swal.fire({
        title: 'Confirmar Remoção',
        text: 'Deseja remover este item?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, remover',
        cancelButtonText: 'Cancelar'
      }).then((result) => {
        if (result.isConfirmed) {
          itensTemporarios.splice(index, 1);
          atualizarTabelaItens();
          calcularTotais();
        }
      });
    };

    function calcularTotais() {
      let subtotal = 0;
      let totalICMS = 0;
      let totalIPI = 0;
      let totalPIS = 0;
      let totalCOFINS = 0;

      itensTemporarios.forEach(item => {
        subtotal += item.valorTotal;
        totalICMS += item.valorICMS;
        totalIPI += item.valorIPI;
        totalPIS += item.valorPIS;
        totalCOFINS += item.valorCOFINS;
      });

      const desconto = parseFloat(document.getElementById('desconto')?.value || 0);
      const valorDesconto = subtotal * (desconto / 100);
      const total = subtotal - valorDesconto;

      if (document.getElementById('subtotal')) {
        document.getElementById('subtotal').value = `R$ ${subtotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('valorDesconto')) {
        document.getElementById('valorDesconto').value = `R$ ${valorDesconto.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('valorTotal')) {
        document.getElementById('valorTotal').value = `R$ ${total.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('totalICMS')) {
        document.getElementById('totalICMS').value = `R$ ${totalICMS.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('totalIPI')) {
        document.getElementById('totalIPI').value = `R$ ${totalIPI.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('totalPIS')) {
        document.getElementById('totalPIS').value = `R$ ${totalPIS.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
      if (document.getElementById('totalCOFINS')) {
        document.getElementById('totalCOFINS').value = `R$ ${totalCOFINS.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
      }
    }

    function atualizarMetricas() {
      document.getElementById('totalOrcamentos').textContent = orcamentos.length;
      document.getElementById('totalPedidos').textContent = pedidos.length;
      document.getElementById('totalClientes').textContent = clientes.length;

      const valorTotal = [...orcamentos, ...pedidos].reduce((sum, item) => sum + (item.valorTotal || 0), 0);
      document.getElementById('valorTotal').textContent = `R$ ${valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
    }

    async function carregarVendas() {
      const tbody = document.getElementById('vendasTableBody');
      if (!tbody) return;

      tbody.innerHTML = '';

      const todasVendas = [
        ...orcamentos.map(o => ({ ...o, tipo: 'Orçamento' })),
        ...pedidos.map(p => ({ ...p, tipo: 'Pedido' }))
      ];

      console.log('Carregando vendas:', {
        orcamentos: orcamentos.length,
        pedidos: pedidos.length,
        total: todasVendas.length
      });

      if (todasVendas.length === 0) {
        tbody.innerHTML = `
          <tr>
            <td colspan="9" style="text-align: center; padding: 40px; color: #666;">
              <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px; display: block; opacity: 0.5;"></i>
              <h3 style="margin: 0 0 10px 0; color: #999;">Nenhum orçamento ou pedido encontrado</h3>
              <p style="margin: 0; font-size: 14px;">Clique em "Novo Orçamento" ou "Novo Pedido" para começar.</p>
            </td>
          </tr>
        `;
        return;
      }

      todasVendas.sort((a, b) => {
        const dateA = a.dataCriacao?.seconds || a.dataCriacao?.toDate?.()?.getTime() || 0;
        const dateB = b.dataCriacao?.seconds || b.dataCriacao?.toDate?.()?.getTime() || 0;
        return dateB - dateA;
      });

      todasVendas.forEach(venda => {
        const cliente = clientes.find(c => c.id === venda.clienteId);
        const vendedor = vendedores.find(v => v.id === venda.vendedorId);

        // Determinar classe do status
        let statusClass = 'status-orcamento';
        const status = venda.status || 'Orçamento';

        switch(status.toLowerCase()) {
          case 'aprovado':
            statusClass = 'status-aprovado';
            break;
          case 'em produção':
          case 'producao':
            statusClass = 'status-producao';
            break;
          case 'faturado':
            statusClass = 'status-faturado';
            break;
          case 'entregue':
            statusClass = 'status-entregue';
            break;
          case 'cancelado':
            statusClass = 'status-cancelado';
            break;
        }

        // Formatar data
        let dataFormatada = 'N/A';
        if (venda.dataCriacao) {
          if (venda.dataCriacao.seconds) {
            dataFormatada = new Date(venda.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR');
          } else if (venda.dataCriacao.toDate) {
            dataFormatada = venda.dataCriacao.toDate().toLocaleDateString('pt-BR');
          }
        }

        const nomeCliente = cliente ?
          (cliente.nomeFantasia || cliente.razaoSocial || cliente.nome || 'Cliente sem nome') :
          'Cliente não encontrado';

        const nomeVendedor = vendedor ?
          (vendedor.nome || vendedor.nomeCompleto || 'Vendedor sem nome') :
          'N/A';

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${venda.numero || 'N/A'}</td>
          <td><span class="status ${venda.tipo === 'Orçamento' ? 'status-orcamento' : 'status-aprovado'}">${venda.tipo}</span></td>
          <td>${nomeCliente}</td>
          <td>${dataFormatada}</td>
          <td>R$ ${(venda.valorTotal || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</td>
          <td><span class="status ${statusClass}">${status}</span></td>
          <td>${nomeVendedor}</td>
          <td>${venda.prazoEntrega || 'N/A'} dias</td>
          <td>
            <div class="actions">
              <button class="btn btn-warning btn-sm" onclick="editarVenda('${venda.id}', '${venda.tipo}')">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-danger btn-sm" onclick="excluirVenda('${venda.id}', '${venda.tipo}')">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    window.editarVenda = function(id, tipo) {
      Swal.fire('Info', 'Funcionalidade de edição em desenvolvimento', 'info');
      console.log('Editar:', id, tipo);
    };

    window.excluirVenda = function(id, tipo) {
      Swal.fire({
        title: 'Confirmar Exclusão',
        text: `Deseja realmente excluir este ${tipo.toLowerCase()}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar'
      }).then(async (result) => {
        if (result.isConfirmed) {
          try {
            const colecao = tipo === 'Orçamento' ? 'orcamentos' : 'pedidosVenda';
            await deleteDoc(doc(db, colecao, id));

            Swal.fire('Excluído!', `${tipo} excluído com sucesso.`, 'success');

            // Recarregar dados
            await carregarDadosIniciais();
            carregarVendas();
            atualizarMetricas();
          } catch (error) {
            console.error('Erro ao excluir:', error);
            Swal.fire('Erro', 'Erro ao excluir documento', 'error');
          }
        }
      });
    };



    function configurarEventos() {
      // Configurar eventos de filtros
      const filtros = ['periodoFilter', 'statusFilter', 'clienteFilter', 'vendedorFilter', 'valorMinFilter', 'valorMaxFilter'];
      filtros.forEach(filtroId => {
        const elemento = document.getElementById(filtroId);
        if (elemento) {
          elemento.addEventListener('change', aplicarFiltros);
        }
      });
    }


  </script>

  <!-- Estilos adicionais para modal e formulário -->
  <style>
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 2000;
      overflow-y: auto;
    }

    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 1200px;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      max-height: 95vh;
      overflow-y: auto;
    }

    .modal-header {
      background: var(--primary-color);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h2 {
      margin: 0;
      font-size: 18px;
    }

    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background 0.2s;
    }

    .close-button:hover {
      background: rgba(255,255,255,0.2);
    }

    .modal-body {
      padding: 20px;
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .form-section {
      margin-bottom: 25px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      overflow: hidden;
    }

    .form-section h3 {
      background: var(--secondary-color);
      margin: 0;
      padding: 12px 15px;
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-color);
      border-bottom: 1px solid var(--border-color);
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      padding: 15px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-size: 12px;
      font-weight: 600;
      color: var(--text-muted);
      margin-bottom: 5px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 13px;
      background: white;
      transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .field-with-button {
      display: flex;
      gap: 5px;
    }

    .field-with-button select {
      flex: 1;
    }

    .field-with-button button {
      padding: 8px 12px;
      background: var(--secondary-color);
      border: 1px solid var(--border-color);
      border-radius: 4px;
      cursor: pointer;
      color: var(--primary-color);
    }

    .field-with-button button:hover {
      background: var(--primary-color);
      color: white;
    }

    .items-header {
      padding: 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .totals-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      padding: 15px;
    }

    .total-item {
      display: flex;
      flex-direction: column;
    }

    .total-item label {
      font-size: 12px;
      font-weight: 600;
      color: var(--text-muted);
      margin-bottom: 5px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .total-item input {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 13px;
      background: white;
    }

    textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 13px;
      font-family: inherit;
      resize: vertical;
      margin: 15px;
      box-sizing: border-box;
    }

    /* Tabela */
    .table-container {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .table-header {
      background: var(--secondary-color);
      padding: 15px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .table-actions {
      display: flex;
      gap: 10px;
    }

    .totvs-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 13px;
    }

    .totvs-table th {
      background: var(--secondary-color);
      padding: 12px 15px;
      text-align: left;
      font-weight: 600;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .totvs-table td {
      padding: 12px 15px;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .totvs-table tbody tr:hover {
      background: rgba(8, 84, 160, 0.05);
    }

    .totvs-table tbody tr:nth-child(even) {
      background: rgba(0,0,0,0.02);
    }

    /* Status Badges */
    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-orcamento { background: #e3f2fd; color: #1976d2; }
    .status-pedido { background: #f3e5f5; color: #7b1fa2; }
    .status-aprovado { background: #e8f5e8; color: #2e7d32; }
    .status-em-producao { background: #fff3e0; color: #f57c00; }
    .status-faturado { background: #e8f5e8; color: #388e3c; }
    .status-entregue { background: #e1f5fe; color: #0277bd; }
    .status-cancelado { background: #ffebee; color: #d32f2f; }
  </style>
</body>
</html>
