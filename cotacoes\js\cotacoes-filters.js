// ===== COTAÇÕES - FILTROS E PESQUISA =====

// ===== PESQUISA =====
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (searchTerm === '') {
        renderQuotations();
        return;
    }

    const filtered = cotacoes.filter(quotation => {
        const numero = (quotation.numero || '').toLowerCase();
        const solicitacao = solicitacoes.find(s => s.id === quotation.solicitacaoId);
        const solicitacaoNumero = solicitacao ? (solicitacao.numero || '').toLowerCase() : '';
        const observacoes = (quotation.observacoes || '').toLowerCase();
        
        // Pesquisar em fornecedores
        let fornecedorMatch = false;
        if (quotation.fornecedores) {
            fornecedorMatch = quotation.fornecedores.some(fornecedorId => {
                const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                if (fornecedor) {
                    const nome = (fornecedor.nome || '').toLowerCase();
                    const razaoSocial = (fornecedor.razaoSocial || fornecedor.razao_social || '').toLowerCase();
                    const codigo = (fornecedor.codigo || '').toLowerCase();
                    return nome.includes(searchTerm) || razaoSocial.includes(searchTerm) || codigo.includes(searchTerm);
                }
                return false;
            });
        }

        return numero.includes(searchTerm) || 
               solicitacaoNumero.includes(searchTerm) || 
               observacoes.includes(searchTerm) ||
               fornecedorMatch;
    });

    renderQuotations(filtered);
    showNotification(`${filtered.length} cotações encontradas`, 'info', 2000);
}

// ===== FILTROS AVANÇADOS =====
function applyFilters() {
    const dataInicio = document.getElementById('dataInicio').value;
    const dataFim = document.getElementById('dataFim').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const solicitacaoFilter = document.getElementById('solicitacaoFilter').value;
    const fornecedorFilter = document.getElementById('fornecedorFilter').value;

    let filtered = [...cotacoes];

    // Filtro por data
    if (dataInicio) {
        const startDate = new Date(dataInicio);
        filtered = filtered.filter(c => {
            const cotacaoDate = c.dataCriacao?.toDate() || new Date(0);
            return cotacaoDate >= startDate;
        });
    }

    if (dataFim) {
        const endDate = new Date(dataFim);
        endDate.setHours(23, 59, 59, 999); // Incluir o dia todo
        filtered = filtered.filter(c => {
            const cotacaoDate = c.dataCriacao?.toDate() || new Date(0);
            return cotacaoDate <= endDate;
        });
    }

    // Filtro por status
    if (statusFilter) {
        filtered = filtered.filter(c => c.status === statusFilter);
    }

    // Filtro por solicitação
    if (solicitacaoFilter) {
        filtered = filtered.filter(c => c.solicitacaoId === solicitacaoFilter);
    }

    // Filtro por fornecedor
    if (fornecedorFilter) {
        filtered = filtered.filter(c => 
            c.fornecedores && c.fornecedores.includes(fornecedorFilter)
        );
    }

    renderQuotations(filtered);
    
    const activeFilters = [dataInicio, dataFim, statusFilter, solicitacaoFilter, fornecedorFilter]
        .filter(f => f).length;
    
    if (activeFilters > 0) {
        showNotification(`${filtered.length} cotações encontradas com ${activeFilters} filtro(s) ativo(s)`, 'info', 3000);
    }
}

function clearFilters() {
    document.getElementById('dataInicio').value = '';
    document.getElementById('dataFim').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('solicitacaoFilter').value = '';
    document.getElementById('fornecedorFilter').value = '';
    document.getElementById('searchInput').value = '';

    // Manter comportamento padrão: aglutinadas ocultas, fechadas ocultas
    showAglutinadas = false;
    showFechadas = false;
    updateToggleButtons();

    renderQuotations();
    showNotification('Filtros limpos', 'info', 2000);
}

// ===== AÇÕES RÁPIDAS =====
function showAllQuotations() {
    clearFilters();
    showFechadas = true;
    showAglutinadas = true;
    updateToggleButtons();
    renderQuotations();
    showNotification('Mostrando todas as cotações', 'info');
}

function showOpenQuotations() {
    clearFilters();
    document.getElementById('statusFilter').value = 'ABERTA';
    // Manter aglutinadas ocultas por padrão
    showAglutinadas = false;
    updateToggleButtons();
    applyFilters();
}

function showSentQuotations() {
    clearFilters();
    document.getElementById('statusFilter').value = 'ENVIADA';
    // Manter aglutinadas ocultas por padrão
    showAglutinadas = false;
    updateToggleButtons();
    applyFilters();
}

function showRespondedQuotations() {
    clearFilters();
    document.getElementById('statusFilter').value = 'RESPONDIDA';
    // Manter aglutinadas ocultas por padrão
    showAglutinadas = false;
    updateToggleButtons();
    applyFilters();
}

function showPendingApproval() {
    clearFilters();
    // Manter aglutinadas ocultas por padrão
    showAglutinadas = false;
    updateToggleButtons();
    const filtered = cotacoes.filter(c =>
        c.status === 'RESPONDIDA' &&
        c.respostas &&
        Object.keys(c.respostas).length > 0
    );
    renderQuotations(filtered);
    showNotification(`${filtered.length} cotações aguardando aprovação`, 'info');
}

// ===== CONTROLES DE VISUALIZAÇÃO =====
function toggleAglutinatedView() {
    showAglutinadas = !showAglutinadas;
    updateToggleButtons();
    renderQuotations();
}

function toggleClosedView() {
    showFechadas = !showFechadas;
    updateToggleButtons();
    renderQuotations();
}

function updateToggleButtons() {
    // Botão aglutinadas
    const aglutIcon = document.getElementById('viewToggleIcon');
    const aglutText = document.getElementById('viewToggleText');
    
    if (aglutIcon && aglutText) {
        if (showAglutinadas) {
            aglutIcon.className = 'fas fa-eye-slash';
            aglutText.textContent = 'Ocultar Aglutinadas';
        } else {
            aglutIcon.className = 'fas fa-eye';
            aglutText.textContent = 'Mostrar Aglutinadas';
        }
    }

    // Botão fechadas
    const fechIcon = document.getElementById('closedToggleIcon');
    const fechText = document.getElementById('closedToggleText');
    
    if (fechIcon && fechText) {
        if (showFechadas) {
            fechIcon.className = 'fas fa-eye';
            fechText.textContent = 'Ocultar Fechadas';
        } else {
            fechIcon.className = 'fas fa-eye-slash';
            fechText.textContent = 'Mostrar Fechadas';
        }
    }
}

// ===== CARREGAMENTO DE FILTROS =====
function loadFilters() {
    loadSolicitacaoFilter();
    loadFornecedorFilter();
}

function loadSolicitacaoFilter() {
    const select = document.getElementById('solicitacaoFilter');
    if (!select) return;

    select.innerHTML = '<option value="">Todas</option>';
    
    // Filtrar apenas solicitações que têm cotações
    const solicitacoesComCotacao = solicitacoes.filter(s => 
        cotacoes.some(c => c.solicitacaoId === s.id)
    );

    solicitacoesComCotacao
        .sort((a, b) => (b.numero || 0) - (a.numero || 0))
        .forEach(solicitacao => {
            const option = document.createElement('option');
            option.value = solicitacao.id;
            option.textContent = `SC-${solicitacao.numero || 'N/A'}`;
            select.appendChild(option);
        });
}

function loadFornecedorFilter() {
    const select = document.getElementById('fornecedorFilter');
    if (!select) return;

    select.innerHTML = '<option value="">Todos</option>';
    
    // Filtrar apenas fornecedores que têm cotações
    const fornecedoresComCotacao = fornecedores.filter(f => 
        cotacoes.some(c => c.fornecedores && c.fornecedores.includes(f.id))
    );

    fornecedoresComCotacao
        .sort((a, b) => (a.nome || '').localeCompare(b.nome || ''))
        .forEach(fornecedor => {
            const option = document.createElement('option');
            option.value = fornecedor.id;
            option.textContent = fornecedor.nome || 'Nome não informado';
            select.appendChild(option);
        });
}

// ===== ESTATÍSTICAS =====
function updateStats() {
    const stats = {
        abertas: cotacoes.filter(c => c.status === 'ABERTA').length,
        enviadas: cotacoes.filter(c => c.status === 'ENVIADA').length,
        respondidas: cotacoes.filter(c => c.status === 'RESPONDIDA').length,
        aprovadas: cotacoes.filter(c => c.status === 'APROVADA').length
    };

    // Atualizar elementos do DOM
    const elements = {
        'statAbertas': stats.abertas,
        'statEnviadas': stats.enviadas,
        'statRespondidas': stats.respondidas,
        'statAprovadas': stats.aprovadas
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });

    // Atualizar estatísticas de integração
    updateIntegrationStats();
}

function updateIntegrationStats() {
    const solicitacoesAprovadas = solicitacoes.filter(s => s.status === 'APROVADA').length;
    const cotacoesGeradas = cotacoes.length;
    const pedidosCriados = 0; // Implementar quando tiver integração com pedidos
    
    // Calcular tempo médio (exemplo)
    const tempoMedio = cotacoes.length > 0 ? 
        Math.round(cotacoes.reduce((acc, c) => {
            const created = c.dataCriacao?.toDate() || new Date();
            const now = new Date();
            return acc + (now - created) / (1000 * 60 * 60 * 24);
        }, 0) / cotacoes.length) : 0;

    const integrationElements = {
        'integrationSolicitacoes': solicitacoesAprovadas,
        'integrationCotacoes': cotacoesGeradas,
        'integrationPedidos': pedidosCriados,
        'integrationTempo': tempoMedio
    };

    Object.entries(integrationElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// ===== PAGINAÇÃO =====
function updatePaginationInfo() {
    const start = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(currentPage * itemsPerPage, totalItems);

    const paginationInfo = document.getElementById('paginationInfo');
    if (paginationInfo) {
        paginationInfo.textContent = `Mostrando ${start} a ${end} de ${totalItems} cotações`;
    }

    // Atualizar informações sobre cotações ocultas
    const hiddenInfo = document.getElementById('hiddenQuotationsInfo');
    if (hiddenInfo) {
        const totalCotacoes = cotacoes.length;
        const cotacoesFechadas = cotacoes.filter(c => c.status === 'FECHADA').length;
        const cotacoesAglutinadas = cotacoes.filter(c =>
            (c.status === 'AGLUTINADO' || c.status === 'AGLUTINADA') ||
            (c.aglutinada && c.aglutinada.tipo === 'filha')
        ).length;
        
        let hiddenMessages = [];
        
        if (!showFechadas && cotacoesFechadas > 0) {
            hiddenMessages.push(`${cotacoesFechadas} fechadas ocultas`);
        }
        
        if (!showAglutinadas && cotacoesAglutinadas > 0) {
            hiddenMessages.push(`${cotacoesAglutinadas} aglutinadas ocultas`);
        }
        
        if (hiddenMessages.length > 0) {
            hiddenInfo.innerHTML = `
                <i class="fas fa-eye-slash"></i> 
                ${hiddenMessages.join(' • ')} 
                (Total: ${totalCotacoes} cotações)
            `;
            hiddenInfo.style.display = 'block';
        } else {
            hiddenInfo.style.display = 'none';
        }
    }
}

function updatePaginationControls() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(totalItems / itemsPerPage);
    pagination.innerHTML = '';

    // Botão anterior
    const prevBtn = document.createElement('button');
    prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => changePage(currentPage - 1);
    pagination.appendChild(prevBtn);

    // Páginas
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i;
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.onclick = () => changePage(i);
        pagination.appendChild(pageBtn);
    }

    // Botão próximo
    const nextBtn = document.createElement('button');
    nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => changePage(currentPage + 1);
    pagination.appendChild(nextBtn);
}

function changePage(page) {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderQuotations(filteredCotacoes);
}

function changeItemsPerPage() {
    itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
    currentPage = 1; // Reset para primeira página
    renderQuotations(filteredCotacoes);
}

// ===== ABAS =====
function showTab(tabName, event) {
    // Ocultar todas as abas
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remover classe active de todos os botões
    document.querySelectorAll('.tab').forEach(btn => {
        btn.classList.remove('active');
    });

    // Mostrar aba selecionada
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Ativar botão correspondente
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // Fallback: encontrar o botão pela aba
        const tabButton = document.querySelector(`[onclick*="showTab('${tabName}')"]`);
        if (tabButton) {
            tabButton.classList.add('active');
        }
    }

    // Carregar conteúdo específico da aba se necessário
    if (tabName === 'comparacao') {
        loadComparisonSelect();
    }
}

function loadComparisonSelect() {
    const select = document.getElementById('quotationCompareSelect');
    if (!select) return;

    select.innerHTML = '<option value="">Selecione...</option>';
    
    cotacoes
        .filter(c => c.respostas && Object.keys(c.respostas).length > 0)
        .forEach(quotation => {
            const option = document.createElement('option');
            option.value = quotation.id;
            option.textContent = `${quotation.numero} - ${quotation.fornecedores?.length || 0} fornecedores`;
            select.appendChild(option);
        });
}
