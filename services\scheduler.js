// Serviço para agendar e executar tarefas automáticas
import { AutomationService } from './automation-service.js';
import { NotificationScheduler } from './notification-scheduler.js';

export class Scheduler {
  static async startScheduledTasks() {
    try {
      // Executa tarefas imediatamente ao iniciar
      await this.runTasks();

      // Agenda execução periódica
      setInterval(async () => {
        await this.runTasks();
      }, 5 * 60 * 1000); // A cada 5 minutos
    } catch (error) {
      console.error('Erro ao iniciar agendador:', error);
      // Não propaga o erro para não interromper a aplicação
    }
  }

  static async runTasks() {
    try {
      const now = new Date();
      const hour = now.getHours();

      // Executa tarefas independentes de horário
      await Promise.allSettled([
        AutomationService.autoConvertApprovedQuotes(),
        NotificationScheduler.checkNewInvoices()
      ]);

      // Executa tarefas em horários específicos
      if (hour === 8 || hour === 14 || hour === 18) {
        await Promise.allSettled([
          NotificationScheduler.checkInvoiceDueDates(),
          NotificationScheduler.checkOverdueInvoices()
        ]);
      }
    } catch (error) {
      console.error('Erro ao executar tarefas agendadas:', error);
      // Não propaga o erro para não interromper outras tarefas
    }
  }
} 