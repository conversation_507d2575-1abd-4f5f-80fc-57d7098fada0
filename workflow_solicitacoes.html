
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Workflow de Solicitações - Sistema ERP</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      margin: 0;
    }

    .workflow-tabs {
      display: flex;
      background: var(--secondary-color);
      border-bottom: 2px solid var(--border-color);
    }

    .tab {
      padding: 12px 20px;
      cursor: pointer;
      background: var(--secondary-color);
      border: none;
      color: var(--text-color);
      font-weight: 500;
      transition: all 0.3s;
    }

    .tab.active {
      background: var(--primary-color);
      color: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
    }

    .tab-content.active {
      display: block;
    }

    .workflow-stage {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 20px;
      overflow: hidden;
    }

    .stage-header {
      background: var(--primary-color);
      color: white;
      padding: 15px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .stage-content {
      padding: 20px;
    }

    .solicitation-form {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: var(--text-color);
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-danger {
      background: var(--danger-color);
      color: white;
    }

    .btn-warning {
      background: var(--warning-color);
      color: white;
    }

    .approval-flow {
      display: flex;
      align-items: center;
      margin: 20px 0;
    }

    .approval-step {
      flex: 1;
      text-align: center;
      position: relative;
    }

    .approval-step:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 15px;
      right: -50%;
      width: 100%;
      height: 2px;
      background: var(--border-color);
      z-index: 1;
    }

    .approval-step.completed::after {
      background: var(--success-color);
    }

    .step-circle {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: var(--border-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 10px;
      font-weight: bold;
      position: relative;
      z-index: 2;
    }

    .step-circle.completed {
      background: var(--success-color);
    }

    .step-circle.active {
      background: var(--primary-color);
    }

    .items-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .items-table th,
    .items-table td {
      padding: 10px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .items-table th {
      background: var(--secondary-color);
      font-weight: 600;
    }

    .priority-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: bold;
    }

    .priority-alta {
      background: #ffebee;
      color: #c62828;
    }

    .priority-media {
      background: #fff3e0;
      color: #ef6c00;
    }

    .priority-baixa {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }

    .status-pending {
      background: var(--warning-color);
    }

    .status-approved {
      background: var(--success-color);
    }

    .status-rejected {
      background: var(--danger-color);
    }

    .comments-section {
      background: var(--secondary-color);
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
    }

    .comment-item {
      background: white;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
      border-left: 3px solid var(--primary-color);
    }

    .comment-header {
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 5px;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
    }

    .modal-content {
      background: white;
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 800px;
      border-radius: 8px;
      position: relative;
    }

    .close {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 24px;
      cursor: pointer;
    }

    .alert {
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .alert-info {
      background: #d1ecf1;
      border-left: 4px solid #17a2b8;
      color: #0c5460;
    }

    .alert-warning {
      background: #fff3cd;
      border-left: 4px solid #ffc107;
      color: #856404;
    }

    .alert-success {
      background: #d4edda;
      border-left: 4px solid #28a745;
      color: #155724;
    }

    .budget-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      background: var(--secondary-color);
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .budget-item {
      text-align: center;
    }

    .budget-value {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .budget-label {
      font-size: 12px;
      color: var(--text-color);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Workflow de Solicitações de Compra</h1>
      <div>
        <button class="btn btn-primary" onclick="openNewSolicitation()">Nova Solicitação</button>
        <button class="btn btn-warning" onclick="generateReport()">Relatórios</button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
      </div>
    </div>

    <div class="workflow-tabs">
      <button class="tab active" onclick="showTab('minhas-solicitacoes')">Minhas Solicitações</button>
      <button class="tab" onclick="showTab('aprovacoes-pendentes')">Aprovações Pendentes</button>
      <button class="tab" onclick="showTab('dashboard')">Dashboard</button>
      <button class="tab" onclick="showTab('configuracoes')">Configurações</button>
    </div>

    <!-- Aba Minhas Solicitações -->
    <div id="minhas-solicitacoes" class="tab-content active">
      <div class="workflow-stage">
        <div class="stage-header">
          <span>Fluxo de Aprovação</span>
          <span id="workflow-status">Em Análise</span>
        </div>
        <div class="stage-content">
          <div class="approval-flow" id="approval-flow">
            <div class="approval-step completed">
              <div class="step-circle completed">1</div>
              <div>Solicitação</div>
            </div>
            <div class="approval-step active">
              <div class="step-circle active">2</div>
              <div>Supervisor</div>
            </div>
            <div class="approval-step">
              <div class="step-circle">3</div>
              <div>Gerente</div>
            </div>
            <div class="approval-step">
              <div class="step-circle">4</div>
              <div>Diretor</div>
            </div>
            <div class="approval-step">
              <div class="step-circle">5</div>
              <div>Cotação</div>
            </div>
          </div>
        </div>
      </div>

      <div class="workflow-stage">
        <div class="stage-header">
          <span>Solicitações em Andamento</span>
          <span id="total-solicitacoes">0</span>
        </div>
        <div class="stage-content">
          <table class="items-table">
            <thead>
              <tr>
                <th>Número</th>
                <th>Data</th>
                <th>Descrição</th>
                <th>Valor Total</th>
                <th>Status</th>
                <th>Prioridade</th>
                <th>Próxima Aprovação</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="solicitacoes-table"></tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Aba Aprovações Pendentes -->
    <div id="aprovacoes-pendentes" class="tab-content">
      <div class="alert alert-warning">
        <strong>Atenção:</strong> Você possui <span id="pending-count">0</span> solicitações aguardando sua aprovação.
      </div>
      
      <div class="workflow-stage">
        <div class="stage-header">
          <span>Aguardando Minha Aprovação</span>
        </div>
        <div class="stage-content">
          <table class="items-table">
            <thead>
              <tr>
                <th>Número</th>
                <th>Solicitante</th>
                <th>Data</th>
                <th>Valor</th>
                <th>Justificativa</th>
                <th>Urgência</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="aprovacoes-table"></tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Aba Dashboard -->
    <div id="dashboard" class="tab-content">
      <div class="budget-info">
        <div class="budget-item">
          <div class="budget-value" id="orcamento-utilizado">R$ 0,00</div>
          <div class="budget-label">Orçamento Utilizado</div>
        </div>
        <div class="budget-item">
          <div class="budget-value" id="orcamento-disponivel">R$ 0,00</div>
          <div class="budget-label">Orçamento Disponível</div>
        </div>
        <div class="budget-item">
          <div class="budget-value" id="solicitacoes-mes">0</div>
          <div class="budget-label">Solicitações do Mês</div>
        </div>
        <div class="budget-item">
          <div class="budget-value" id="prazo-medio">0</div>
          <div class="budget-label">Prazo Médio (dias)</div>
        </div>
      </div>

      <div class="workflow-stage">
        <div class="stage-header">
          <span>Indicadores de Performance</span>
        </div>
        <div class="stage-content">
          <canvas id="performance-chart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>

    <!-- Aba Configurações -->
    <div id="configuracoes" class="tab-content">
      <div class="workflow-stage">
        <div class="stage-header">
          <span>Configurações de Workflow</span>
        </div>
        <div class="stage-content">
          <form id="workflow-config-form">
            <div class="solicitation-form">
              <div class="form-group">
                <label>Limite para Aprovação Supervisor</label>
                <input type="number" class="form-control" id="limite-supervisor" step="0.01">
              </div>
              <div class="form-group">
                <label>Limite para Aprovação Gerente</label>
                <input type="number" class="form-control" id="limite-gerente" step="0.01">
              </div>
              <div class="form-group">
                <label>Limite para Aprovação Diretor</label>
                <input type="number" class="form-control" id="limite-diretor" step="0.01">
              </div>
              <div class="form-group">
                <label>Prazo Padrão para Aprovação (dias)</label>
                <input type="number" class="form-control" id="prazo-aprovacao">
              </div>
            </div>
            <button type="submit" class="btn btn-success">Salvar Configurações</button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Nova Solicitação -->
  <div id="modal-nova-solicitacao" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeModal('modal-nova-solicitacao')">&times;</span>
      <h2>Nova Solicitação de Compra</h2>
      
      <form id="nova-solicitacao-form">
        <div class="solicitation-form">
          <div class="form-group">
            <label>Centro de Custo *</label>
            <select class="form-control" id="centro-custo" required>
              <option value="">Selecione...</option>
              <option value="PROD">Produção</option>
              <option value="ADM">Administrativo</option>
              <option value="VEND">Vendas</option>
              <option value="MAN">Manutenção</option>
            </select>
          </div>
          <div class="form-group">
            <label>Prioridade *</label>
            <select class="form-control" id="prioridade" required>
              <option value="">Selecione...</option>
              <option value="ALTA">Alta</option>
              <option value="MEDIA">Média</option>
              <option value="BAIXA">Baixa</option>
            </select>
          </div>
          <div class="form-group">
            <label>Data Necessária *</label>
            <input type="date" class="form-control" id="data-necessaria" required>
          </div>
          <div class="form-group">
            <label>Categoria de Compra</label>
            <select class="form-control" id="categoria-compra">
              <option value="">Selecione...</option>
              <option value="MATERIA_PRIMA">Matéria Prima</option>
              <option value="SERVICOS">Serviços</option>
              <option value="EQUIPAMENTOS">Equipamentos</option>
              <option value="CONSUMIVEL">Consumível</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Justificativa *</label>
          <textarea class="form-control" id="justificativa" rows="3" required></textarea>
        </div>

        <h3>Itens da Solicitação</h3>
        <div id="itens-container">
          <table class="items-table">
            <thead>
              <tr>
                <th>Código</th>
                <th>Descrição</th>
                <th>Quantidade</th>
                <th>Unidade</th>
                <th>Valor Estimado</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="itens-solicitacao"></tbody>
          </table>
          <button type="button" class="btn btn-primary" onclick="addItem()">Adicionar Item</button>
        </div>

        <div style="margin-top: 20px;">
          <button type="submit" class="btn btn-success">Enviar Solicitação</button>
          <button type="button" class="btn btn-danger" onclick="closeModal('modal-nova-solicitacao')">Cancelar</button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      updateDoc, 
      Timestamp,
      query,
      where,
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let currentUser = null;
    let solicitacoes = [];
    let workflowConfig = {};

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }
      
      currentUser = JSON.parse(userSession);
      await loadSolicitacoes();
      await loadWorkflowConfig();
      updateDashboard();
    };

    async function loadSolicitacoes() {
      try {
        const solicitacoesSnap = await getDocs(
          query(
            collection(db, "solicitacoesCompra"),
            where("solicitanteId", "==", currentUser.id),
            orderBy("dataCriacao", "desc")
          )
        );
        
        solicitacoes = solicitacoesSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        updateSolicitacoesTable();
        updateAprovacoesTable();
      } catch (error) {
        console.error("Erro ao carregar solicitações:", error);
      }
    }

    async function loadWorkflowConfig() {
      try {
        const configSnap = await getDocs(collection(db, "workflowConfig"));
        if (!configSnap.empty) {
          workflowConfig = configSnap.docs[0].data();
        }
      } catch (error) {
        console.error("Erro ao carregar configurações:", error);
      }
    }

    function updateSolicitacoesTable() {
      const tableBody = document.getElementById('solicitacoes-table');
      tableBody.innerHTML = '';
      
      solicitacoes.forEach(solicitacao => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${solicitacao.numero}</td>
          <td>${formatDate(solicitacao.dataCriacao)}</td>
          <td>${solicitacao.descricao || 'N/A'}</td>
          <td>R$ ${(solicitacao.valorTotal || 0).toFixed(2)}</td>
          <td>
            <span class="status-indicator status-${getStatusClass(solicitacao.status)}"></span>
            ${solicitacao.status}
          </td>
          <td>
            <span class="priority-badge priority-${solicitacao.prioridade?.toLowerCase() || 'baixa'}">
              ${solicitacao.prioridade || 'Baixa'}
            </span>
          </td>
          <td>${getProximaAprovacao(solicitacao)}</td>
          <td>
            <button class="btn btn-primary" onclick="viewSolicitacao('${solicitacao.id}')">Ver</button>
            ${solicitacao.status === 'RASCUNHO' ? `
              <button class="btn btn-warning" onclick="editSolicitacao('${solicitacao.id}')">Editar</button>
            ` : ''}
          </td>
        `;
        tableBody.appendChild(row);
      });
      
      document.getElementById('total-solicitacoes').textContent = solicitacoes.length;
    }

    function updateAprovacoesTable() {
      // Implementar lógica para carregar aprovações pendentes baseada no nível do usuário
    }

    function updateDashboard() {
      // Calcular indicadores
      const valorTotal = solicitacoes.reduce((sum, s) => sum + (s.valorTotal || 0), 0);
      const solicitacoesMes = solicitacoes.filter(s => {
        const data = new Date(s.dataCriacao.seconds * 1000);
        const hoje = new Date();
        return data.getMonth() === hoje.getMonth() && data.getFullYear() === hoje.getFullYear();
      }).length;

      document.getElementById('orcamento-utilizado').textContent = `R$ ${valorTotal.toFixed(2)}`;
      document.getElementById('solicitacoes-mes').textContent = solicitacoesMes;
    }

    window.showTab = function(tabName) {
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
      document.getElementById(tabName).classList.add('active');
    };

    window.openNewSolicitation = function() {
      document.getElementById('modal-nova-solicitacao').style.display = 'block';
      addItem(); // Adicionar primeiro item automaticamente
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
    };

    window.addItem = function() {
      const tbody = document.getElementById('itens-solicitacao');
      const row = document.createElement('tr');
      row.innerHTML = `
        <td><input type="text" class="form-control" placeholder="Código" required></td>
        <td><input type="text" class="form-control" placeholder="Descrição" required></td>
        <td><input type="number" class="form-control" placeholder="Qtd" min="1" required></td>
        <td>
          <select class="form-control">
            <option value="UN">UN</option>
            <option value="KG">KG</option>
            <option value="MT">MT</option>
            <option value="LT">LT</option>
          </select>
        </td>
        <td><input type="number" class="form-control" placeholder="0,00" step="0.01"></td>
        <td><button type="button" class="btn btn-danger" onclick="removeItem(this)">Remover</button></td>
      `;
      tbody.appendChild(row);
    };

    window.removeItem = function(button) {
      button.closest('tr').remove();
    };

    document.getElementById('nova-solicitacao-form').addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const itens = [];
      const rows = document.querySelectorAll('#itens-solicitacao tr');
      let valorTotal = 0;
      
      rows.forEach(row => {
        const inputs = row.querySelectorAll('input, select');
        const item = {
          codigo: inputs[0].value,
          descricao: inputs[1].value,
          quantidade: parseFloat(inputs[2].value),
          unidade: inputs[3].value,
          valorEstimado: parseFloat(inputs[4].value) || 0
        };
        itens.push(item);
        valorTotal += item.quantidade * item.valorEstimado;
      });

      const solicitacao = {
        numero: await generateSolicitationNumber(),
        solicitanteId: currentUser.id,
        solicitanteNome: currentUser.nome,
        centroCusto: document.getElementById('centro-custo').value,
        prioridade: document.getElementById('prioridade').value,
        dataNecessaria: Timestamp.fromDate(new Date(document.getElementById('data-necessaria').value)),
        categoriaCompra: document.getElementById('categoria-compra').value,
        justificativa: document.getElementById('justificativa').value,
        itens: itens,
        valorTotal: valorTotal,
        status: 'AGUARDANDO_APROVACAO',
        dataCriacao: Timestamp.now(),
        workflowAtual: determineWorkflowLevel(valorTotal),
        historicoAprovacoes: []
      };

      try {
        await addDoc(collection(db, "solicitacoesCompra"), solicitacao);
        alert('Solicitação enviada com sucesso!');
        closeModal('modal-nova-solicitacao');
        await loadSolicitacoes();
      } catch (error) {
        console.error("Erro ao criar solicitação:", error);
        alert("Erro ao criar solicitação.");
      }
    });

    async function generateSolicitationNumber() {
      const year = new Date().getFullYear();
      const month = String(new Date().getMonth() + 1).padStart(2, '0');
      const count = solicitacoes.length + 1;
      return `SC${year}${month}${String(count).padStart(4, '0')}`;
    }

    function determineWorkflowLevel(valor) {
      if (valor <= 1000) return 1; // Supervisor
      if (valor <= 10000) return 2; // Gerente
      return 3; // Diretor
    }

    function formatDate(timestamp) {
      if (!timestamp) return '';
      return new Date(timestamp.seconds * 1000).toLocaleDateString('pt-BR');
    }

    function getStatusClass(status) {
      switch (status) {
        case 'APROVADO': return 'approved';
        case 'REJEITADO': return 'rejected';
        default: return 'pending';
      }
    }

    function getProximaAprovacao(solicitacao) {
      const nivel = solicitacao.workflowAtual;
      switch (nivel) {
        case 1: return 'Supervisor';
        case 2: return 'Gerente';
        case 3: return 'Diretor';
        default: return 'Finalizado';
      }
    }

    window.viewSolicitacao = function(id) {
      // Implementar visualização detalhada
      console.log('Visualizar solicitação:', id);
    };

    window.editSolicitacao = function(id) {
      // Implementar edição
      console.log('Editar solicitação:', id);
    };

    window.generateReport = function() {
      window.open('relatorio_solicitacoes.html', '_blank');
    };
  </script>
</body>
</html>
