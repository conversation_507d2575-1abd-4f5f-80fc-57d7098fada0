export const StockPolicy = {
    // Configurações gerais de estoque
    generalSettings: {
        // Permitir estoque negativo
        allowNegativeStock: false,
        
        // Método de valorização padrão
        valuationMethod: 'MEDIO', // FIFO, LIFO, MEDIO
        
        // Alertas de estoque
        stockAlertThresholds: {
            critical: 0,
            low: 10,
            reorder: 20
        }
    },

    // Regras de movimentação
    movementRules: {
        // Tipos de movimentação permitidos
        allowedTypes: [
            'ENTRADA', 
            'SAIDA', 
            'TRANSFERENCIA', 
            'AJUSTE', 
            'PRODUCAO', 
            'CONSUMO'
        ],

        // Restrições de movimentação
        restrictions: {
            // Impedir saídas que zeram estoque
            preventZeroStock: true,
            
            // Exigir justificativa para ajustes
            requireReasonForAdjustments: true,
            
            // Limite de ajuste percentual
            maxAdjustmentPercentage: 10
        }
    },

    // Políticas de reserva
    reservationPolicies: {
        // Tempo máximo de reserva
        maxReservationTime: 24 * 60 * 60 * 1000, // 24 horas
        
        // Ação após expiração de reserva
        reservationExpirationAction: 'LIBERAR'
    },

    // Auditoria e rastreabilidade
    auditSettings: {
        // Manter log de todas as movimentações
        keepFullMovementLog: true,
        
        // Período de retenção de logs
        logRetentionPeriod: 365 * 24 * 60 * 60 * 1000, // 1 ano
        
        // Gerar relatório de inconsistências
        generateInconsistencyReports: true
    },

    // Métodos de validação
    validateMovement(movementData) {
        const errors = [];

        // Validações básicas
        if (!movementData.produtoId) {
            errors.push('Produto não identificado');
        }

        if (movementData.quantidade <= 0) {
            errors.push('Quantidade inválida');
        }

        // Validar tipo de movimentação
        if (!this.movementRules.allowedTypes.includes(movementData.tipo)) {
            errors.push(`Tipo de movimentação não permitido: ${movementData.tipo}`);
        }

        // Validações específicas por tipo de movimentação
        switch (movementData.tipo) {
            case 'AJUSTE':
                if (!movementData.observacoes) {
                    errors.push('Ajuste requer justificativa');
                }
                break;
            case 'SAIDA':
                if (this.movementRules.restrictions.preventZeroStock && 
                    movementData.saldoFinal <= 0) {
                    errors.push('Saída não permitida - estoque zeraria');
                }
                break;
        }

        return errors;
    },

    // Método para gerar relatório de auditoria
    async generateAuditReport() {
        // Lógica de geração de relatório de auditoria
        const inconsistencies = await InventoryService.gerarRelatorioInconsistencias();
        
        return {
            timestamp: new Date(),
            totalInconsistencies: inconsistencies.length,
            details: inconsistencies
        };
    }
}; 