# FYRON MRP - <PERSON><PERSON><PERSON> do Logo

## 📋 Arquivos Disponíveis

### Logos SVG
- `fyron_logo.svg` - <PERSON><PERSON> completo (400x200px)
- `fyron_logo_compact.svg` - Logo compacto (200x80px)  
- `favicon.svg` - F<PERSON><PERSON> (32x32px)

### Estilos CSS
- `fyron-logo.css` - Estilos e classes para o logo

## 🎨 Cores do Logo

### Gradient<PERSON> (FYRON)
- **Azul <PERSON>**: `#1e40af`
- **Azul <PERSON>**: `#2563eb` 
- **Azul <PERSON>**: `#3b82f6`

### Gradient<PERSON>in<PERSON> (MRP)
- **Cinza Escuro**: `#6b7280`
- **<PERSON><PERSON><PERSON> Médio**: `#9ca3af`
- **Cinza Claro**: `#d1d5db`

### Formas Dinâmicas
- **Verde**: `#10b981` → `#059669`
- **Azu<PERSON>**: `#3b82f6` → `#1d4ed8`

## 📐 Tamanhos Disponíveis

### Classes CSS
```css
.fyron-logo-large    /* 400x200px */
.fyron-logo-medium   /* 280x120px */
.fyron-logo-small    /* 200x80px */
.fyron-logo-compact  /* 150x60px */
.fyron-logo-mini     /* 100x40px */
```

## 🚀 Como Usar

### 1. HTML Básico
```html
<!-- Logo completo -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium">

<!-- Logo compacto -->
<img src="assets/fyron_logo_compact.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-small">
```

### 2. Com Animações
```html
<!-- Logo com pulso -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium fyron-logo-pulse">

<!-- Logo com brilho -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium fyron-logo-glow">

<!-- Logo flutuante -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium fyron-logo-float">
```

### 3. Posicionamento
```html
<!-- Logo centralizado -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium fyron-logo-center">

<!-- Logo à esquerda -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-small fyron-logo-left">

<!-- Logo à direita -->
<img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-small fyron-logo-right">
```

### 4. Em Containers
```html
<!-- Container de cabeçalho -->
<div class="fyron-logo-header">
    <img src="assets/fyron_logo_compact.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-small">
    <h1>Sistema de Gestão</h1>
</div>

<!-- Container de sidebar -->
<div class="fyron-logo-sidebar">
    <img src="assets/fyron_logo.svg" alt="FYRON MRP" class="fyron-logo fyron-logo-medium">
</div>
```

## 📱 Responsividade

O logo se adapta automaticamente aos tamanhos de tela:

- **Desktop**: Tamanhos completos
- **Tablet** (≤768px): Redução de 30%
- **Mobile** (≤480px): Redução de 50%

## ♿ Acessibilidade

### Texto Alternativo
Sempre use `alt="FYRON MRP"` para leitores de tela.

### Alto Contraste
O logo se ajusta automaticamente em modo de alto contraste.

### Movimento Reduzido
Animações são desabilitadas para usuários que preferem movimento reduzido.

## 🖨️ Impressão

Em modo de impressão, o logo é convertido automaticamente para escala de cinza.

## 🌙 Tema Escuro

O logo se ajusta automaticamente em temas escuros com maior brilho e contraste.

## 📋 Favicon

Para usar o favicon:

```html
<link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
```

## ✅ Boas Práticas

1. **Sempre use o arquivo SVG** para melhor qualidade
2. **Mantenha proporções** - não distorça o logo
3. **Use classes CSS** em vez de estilos inline
4. **Teste em diferentes tamanhos** de tela
5. **Verifique contraste** em fundos diferentes
6. **Inclua texto alternativo** para acessibilidade

## 🚫 O que NÃO fazer

- ❌ Não altere as cores originais
- ❌ Não distorça as proporções
- ❌ Não use em fundos que comprometam a legibilidade
- ❌ Não remova elementos do logo
- ❌ Não use versões pixelizadas quando SVG está disponível

## 🔧 Personalização

Para criar variações do logo, edite os arquivos SVG mantendo:
- Estrutura de gradientes
- Proporções dos elementos
- Hierarquia visual (FYRON > MRP)
- Formas dinâmicas características
