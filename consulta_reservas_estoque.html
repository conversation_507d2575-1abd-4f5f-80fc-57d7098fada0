<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Consulta de Reservas de Estoque</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
        }

        .table td {
            padding: 10px 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-ok { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 2px;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .font-bold { font-weight: bold; }

        .color-red { color: #dc3545; }
        .color-orange { color: #fd7e14; }
        .color-green { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>🔒 Consulta de Reservas de Estoque</h1>
                <p>Visualize e gerencie todas as reservas de materiais para ordens de produção</p>
            </div>
            <button class="btn btn-secondary" onclick="window.location.href='index.html'">← Voltar</button>
        </div>

        <!-- Estatísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value color-red" id="totalReservado">0</div>
                <div class="stat-label">Total Reservado (R$)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value color-orange" id="itensComReserva">0</div>
                <div class="stat-label">Itens com Reserva</div>
            </div>
            <div class="stat-card">
                <div class="stat-value color-green" id="opsAtivas">0</div>
                <div class="stat-label">OPs Ativas</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="percentualReservado">0%</div>
                <div class="stat-label">% do Estoque Reservado</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <h3>🔍 Filtros</h3>
            <div class="filter-row">
                <div>
                    <label>Buscar:</label>
                    <input type="text" id="searchInput" placeholder="Código ou descrição..." onkeyup="aplicarFiltros()">
                </div>
                <div>
                    <label>Armazém:</label>
                    <select id="armazemFilter" onchange="aplicarFiltros()">
                        <option value="">Todos os armazéns</option>
                    </select>
                </div>
                <div>
                    <label>Tipo:</label>
                    <select id="tipoFilter" onchange="aplicarFiltros()">
                        <option value="">Todos os tipos</option>
                        <option value="MP">Matéria Prima</option>
                        <option value="SP">Semi-Produto</option>
                        <option value="PA">Produto Acabado</option>
                    </select>
                </div>
                <div>
                    <label>Status:</label>
                    <select id="statusFilter" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                        <option value="ok">Disponível</option>
                        <option value="warning">Baixo</option>
                        <option value="danger">Crítico</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <button class="btn btn-primary" onclick="aplicarFiltros()">🔍 Buscar</button>
                <button class="btn btn-secondary" onclick="limparFiltros()">🗑️ Limpar</button>
                <button class="btn btn-success" onclick="exportarExcel()">📊 Exportar Excel</button>
                <button class="btn btn-warning" onclick="liberarReservasOrfas()">🔓 Liberar Órfãs</button>
            </div>
        </div>

        <!-- Tabela -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Tipo</th>
                        <th>Armazém</th>
                        <th class="text-right">Saldo Total</th>
                        <th class="text-right">Saldo Reservado</th>
                        <th class="text-right">Saldo Disponível</th>
                        <th class="text-right">% Reservado</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">OPs Vinculadas</th>
                        <th class="text-center">Ações</th>
                    </tr>
                </thead>
                <tbody id="tabelaReservas">
                    <tr>
                        <td colspan="11" class="loading">Carregando dados...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal de Detalhes -->
    <div id="modalDetalhes" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <span class="close-button" onclick="fecharModal()">×</span>
            <h2 id="modalTitulo">Detalhes das Reservas</h2>
            <div id="modalConteudo"></div>
        </div>
    </div>

    <!-- Scripts Firebase -->
    <script type="module">
        // ===================================================================
        // CONSULTA RESERVAS ESTOQUE - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { collection, getDocs, doc, updateDoc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase já inicializado centralmente

        // Variáveis globais
        let estoques = [];
        let produtos = [];
        let armazens = [];
        let ordensProducao = [];
        let dadosFiltrados = [];

        // Carregar dados iniciais
        window.onload = async function() {
            await carregarDados();
            atualizarEstatisticas();
            renderizarTabela();
        };

        async function carregarDados() {
            try {
                const tbody = document.getElementById('tabelaReservas');
                tbody.innerHTML = '<tr><td colspan="11" class="loading">Carregando dados...</td></tr>';

                // Carregar dados em paralelo
                const [estoquesSnap, produtosSnap, armazensSnap, opsSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar apenas estoques com reserva
                estoques = estoques.filter(estoque => (estoque.saldoReservado || 0) > 0);

                // Preencher filtros
                preencherFiltros();

                dadosFiltrados = [...estoques];

            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                document.getElementById('tabelaReservas').innerHTML = 
                    '<tr><td colspan="11" class="no-data">Erro ao carregar dados</td></tr>';
            }
        }

        function preencherFiltros() {
            // Preencher armazéns
            const armazemSelect = document.getElementById('armazemFilter');
            armazemSelect.innerHTML = '<option value="">Todos os armazéns</option>';
            armazens.forEach(armazem => {
                armazemSelect.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
            });
        }

        window.aplicarFiltros = function() {
            const busca = document.getElementById('searchInput').value.toLowerCase();
            const armazemId = document.getElementById('armazemFilter').value;
            const tipo = document.getElementById('tipoFilter').value;
            const status = document.getElementById('statusFilter').value;

            dadosFiltrados = estoques.filter(estoque => {
                const produto = produtos.find(p => p.id === estoque.produtoId);
                const armazem = armazens.find(a => a.id === estoque.armazemId);

                if (!produto) return false;

                // Filtro de busca
                const matchBusca = !busca || 
                    produto.codigo.toLowerCase().includes(busca) ||
                    produto.descricao.toLowerCase().includes(busca);

                // Filtro de armazém
                const matchArmazem = !armazemId || estoque.armazemId === armazemId;

                // Filtro de tipo
                const matchTipo = !tipo || produto.tipo === tipo;

                // Filtro de status
                let matchStatus = true;
                if (status) {
                    const saldoTotal = estoque.saldo || 0;
                    const saldoReservado = estoque.saldoReservado || 0;
                    const saldoDisponivel = saldoTotal - saldoReservado;

                    if (status === 'ok') matchStatus = saldoDisponivel > 0;
                    else if (status === 'warning') matchStatus = saldoDisponivel <= 0 && saldoTotal > 0;
                    else if (status === 'danger') matchStatus = saldoTotal <= 0;
                }

                return matchBusca && matchArmazem && matchTipo && matchStatus;
            });

            atualizarEstatisticas();
            renderizarTabela();
        };

        window.limparFiltros = function() {
            document.getElementById('searchInput').value = '';
            document.getElementById('armazemFilter').value = '';
            document.getElementById('tipoFilter').value = '';
            document.getElementById('statusFilter').value = '';
            
            dadosFiltrados = [...estoques];
            atualizarEstatisticas();
            renderizarTabela();
        };

        function atualizarEstatisticas() {
            const totalReservado = dadosFiltrados.reduce((sum, estoque) => {
                const produto = produtos.find(p => p.id === estoque.produtoId);
                const valorUnitario = produto?.valorUnitario || produto?.custo || 0;
                return sum + ((estoque.saldoReservado || 0) * valorUnitario);
            }, 0);

            const itensComReserva = dadosFiltrados.length;

            const opsAtivas = ordensProducao.filter(op => 
                op.status === 'ABERTA' || op.status === 'EM_PRODUCAO'
            ).length;

            const totalEstoque = estoques.reduce((sum, estoque) => sum + (estoque.saldo || 0), 0);
            const totalReservadoQtd = dadosFiltrados.reduce((sum, estoque) => sum + (estoque.saldoReservado || 0), 0);
            const percentualReservado = totalEstoque > 0 ? (totalReservadoQtd / totalEstoque * 100) : 0;

            document.getElementById('totalReservado').textContent = 
                new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(totalReservado);
            document.getElementById('itensComReserva').textContent = itensComReserva;
            document.getElementById('opsAtivas').textContent = opsAtivas;
            document.getElementById('percentualReservado').textContent = percentualReservado.toFixed(1) + '%';
        }

        function renderizarTabela() {
            const tbody = document.getElementById('tabelaReservas');

            if (dadosFiltrados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" class="no-data">Nenhuma reserva encontrada</td></tr>';
                return;
            }

            tbody.innerHTML = '';

            dadosFiltrados.forEach(estoque => {
                const produto = produtos.find(p => p.id === estoque.produtoId);
                const armazem = armazens.find(a => a.id === estoque.armazemId);

                if (!produto) return;

                const saldoTotal = estoque.saldo || 0;
                const saldoReservado = estoque.saldoReservado || 0;
                const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado);
                const percentualReservado = saldoTotal > 0 ? (saldoReservado / saldoTotal * 100) : 0;

                // Determinar status
                let statusClass = 'status-ok';
                let statusText = 'OK';
                if (saldoDisponivel <= 0 && saldoTotal > 0) {
                    statusClass = 'status-warning';
                    statusText = 'Baixo';
                } else if (saldoTotal <= 0) {
                    statusClass = 'status-danger';
                    statusText = 'Crítico';
                }

                // Contar OPs vinculadas
                const opsVinculadas = ordensProducao.filter(op => 
                    op.materiaisNecessarios?.some(m => m.produtoId === produto.id) &&
                    (op.status === 'ABERTA' || op.status === 'EM_PRODUCAO')
                ).length;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="font-bold">${produto.codigo}</td>
                    <td>${produto.descricao}</td>
                    <td>${produto.tipo || 'N/A'}</td>
                    <td>${armazem?.codigo || 'N/A'}</td>
                    <td class="text-right font-bold">${saldoTotal.toLocaleString()}</td>
                    <td class="text-right color-red font-bold">${saldoReservado.toLocaleString()}</td>
                    <td class="text-right ${saldoDisponivel <= 0 ? 'color-red' : 'color-green'} font-bold">${saldoDisponivel.toLocaleString()}</td>
                    <td class="text-right">${percentualReservado.toFixed(1)}%</td>
                    <td class="text-center"><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td class="text-center">${opsVinculadas}</td>
                    <td class="text-center">
                        <button class="btn btn-primary" onclick="verDetalhes('${estoque.id}')" title="Ver Detalhes">👁️</button>
                        <button class="btn btn-warning" onclick="liberarReserva('${estoque.id}')" title="Liberar Reserva">🔓</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Funções globais
        window.verDetalhes = function(estoqueId) {
            // Implementar modal de detalhes
            alert('Funcionalidade de detalhes será implementada');
        };

        window.liberarReserva = function(estoqueId) {
            if (confirm('Tem certeza que deseja liberar esta reserva?')) {
                // Implementar liberação de reserva
                alert('Funcionalidade de liberação será implementada');
            }
        };

        window.liberarReservasOrfas = function() {
            alert('Funcionalidade de liberação de reservas órfãs será implementada');
        };

        window.exportarExcel = function() {
            alert('Funcionalidade de exportação será implementada');
        };

        window.fecharModal = function() {
            document.getElementById('modalDetalhes').style.display = 'none';
        };
    </script>
</body>
</html>
