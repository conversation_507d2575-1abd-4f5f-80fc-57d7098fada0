<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor <PERSON> <PERSON><PERSON><PERSON><PERSON> - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .search-bar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }

        .search-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .table th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            padding: 15px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
        }

        .status-pendente { background: #fff3cd; color: #856404; }
        .status-aprovada { background: #d4edda; color: #155724; }
        .status-rejeitada { background: #f8d7da; color: #721c24; }
        .status-em_cotacao { background: #d1ecf1; color: #0c5460; }
        .status-excluida { background: #6c757d; color: white; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-control {
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .items-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
        }

        .item-row {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            display: grid;
            grid-template-columns: 1fr 1fr 100px 80px 120px 120px auto;
            gap: 10px;
            align-items: center;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success { background: #28a745; }
        .notification-error { background: #dc3545; }
        .notification-warning { background: #ffc107; color: #212529; }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .text-truncate {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .field-readonly {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .danger-zone {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .danger-zone h4 {
            color: #e53e3e;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> Editor Administrativo - Solicitações de Compra</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>ATENÇÃO:</strong> Esta é uma ferramenta administrativa. Use com cuidado. Alterações aqui afetam diretamente o banco de dados.
            </div>

            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" placeholder="Buscar por número, solicitante, departamento..." oninput="filterSolicitacoes()">
                <button class="btn btn-primary" onclick="loadAllSolicitacoes()">
                    <i class="fas fa-sync"></i> Recarregar
                </button>
                <div id="totalCount" style="font-weight: 600; color: #495057;"></div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Solicitante</th>
                            <th>Departamento</th>
                            <th>Status</th>
                            <th>Prioridade</th>
                            <th>Itens</th>
                            <th>Valor</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="solicitacoesTableBody">
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-spinner fa-spin fa-2x"></i><br><br>
                                Carregando solicitações...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Solicitação</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="solicitacaoId" name="id">
                    
                    <!-- Seção: Identificação -->
                    <h4 style="margin-bottom: 15px; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-id-card"></i> Identificação
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="numero">Número da Solicitação</label>
                            <input type="text" id="numero" class="form-control" placeholder="Ex: SC-2024-0001">
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" class="form-control" required>
                                <option value="PENDENTE">Pendente</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="REJEITADA">Rejeitada</option>
                                <option value="EM_COTACAO">Em Cotação</option>
                                <option value="EXCLUIDA">Excluída</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tipo">Tipo</label>
                            <select id="tipo" class="form-control" required>
                                <option value="NORMAL">Normal</option>
                                <option value="URGENTE">Urgente</option>
                                <option value="EMERGENCIAL">Emergencial</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="origem">Origem</label>
                            <select id="origem" class="form-control" required>
                                <option value="MANUAL">Manual</option>
                                <option value="SISTEMA">Sistema</option>
                                <option value="IMPORTACAO">Importação</option>
                            </select>
                        </div>
                    </div>

                    <!-- Seção: Solicitante -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-user"></i> Solicitante
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="solicitante">Nome do Solicitante</label>
                            <input type="text" id="solicitante" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="solicitanteId">ID do Solicitante</label>
                            <input type="text" id="solicitanteId" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="departamento">Departamento (ID)</label>
                            <input type="text" id="departamento" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="departamentoNome">Nome do Departamento</label>
                            <input type="text" id="departamentoNome" class="form-control">
                        </div>
                    </div>

                    <!-- Seção: Datas -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-calendar"></i> Datas
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="dataCriacao">Data de Criação</label>
                            <input type="datetime-local" id="dataCriacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="ultimaAtualizacao">Última Atualização</label>
                            <input type="datetime-local" id="ultimaAtualizacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="dataNecessidade">Data Necessidade</label>
                            <input type="date" id="dataNecessidade" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="dataLimiteAprovacao">Data Limite Aprovação</label>
                            <input type="date" id="dataLimiteAprovacao" class="form-control">
                        </div>
                    </div>

                    <!-- Seção: Detalhes -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-info-circle"></i> Detalhes
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="prioridade">Prioridade</label>
                            <select id="prioridade" class="form-control" required>
                                <option value="BAIXA">Baixa</option>
                                <option value="MEDIA">Média</option>
                                <option value="ALTA">Alta</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="centroCustoId">Centro de Custo ID</label>
                            <input type="text" id="centroCustoId" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="justificativa">Justificativa</label>
                        <textarea id="justificativa" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="observacoes">Observações</label>
                        <textarea id="observacoes" class="form-control" rows="2"></textarea>
                    </div>

                    <!-- Seção: Itens -->
                    <div class="items-section">
                        <h4 style="margin-bottom: 15px; color: #495057;">
                            <i class="fas fa-list"></i> Itens da Solicitação
                            <button type="button" class="btn btn-success btn-sm" style="float: right;" onclick="addNewItem()">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </button>
                        </h4>
                        <div id="itemsContainer">
                            <!-- Itens serão adicionados dinamicamente -->
                        </div>
                    </div>

                    <!-- Seção: Campos de Sistema -->
                    <div id="systemFields" style="display: none;">
                        <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                            <i class="fas fa-cog"></i> Campos do Sistema
                            <button type="button" class="btn btn-secondary btn-sm" style="float: right;" onclick="toggleSystemFields()">
                                <i class="fas fa-eye"></i> Mostrar/Ocultar
                            </button>
                        </h4>
                        <div id="systemFieldsContent" style="display: none;">
                            <!-- Campos de aprovação, cotação, etc. -->
                        </div>
                    </div>

                    <!-- Zona de Perigo -->
                    <div class="danger-zone">
                        <h4><i class="fas fa-exclamation-triangle"></i> Zona de Perigo</h4>
                        <p style="margin-bottom: 15px;">Ações irreversíveis que afetam permanentemente os dados.</p>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteForever()">
                            <i class="fas fa-trash"></i> Excluir Permanentemente
                        </button>
                    </div>

                    <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal()" style="margin-right: 10px;">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            Timestamp,
            orderBy,
            query
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let allSolicitacoes = [];
        let filteredSolicitacoes = [];
        let currentEditId = null;

        // Carregar todas as solicitações
        window.loadAllSolicitacoes = async function() {
            try {
                showNotification('Carregando solicitações...', 'info');
                
                const q = query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"));
                const snapshot = await getDocs(q);
                
                allSolicitacoes = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                
                filteredSolicitacoes = [...allSolicitacoes];
                renderSolicitacoes();
                updateTotalCount();
                
                showNotification(`${allSolicitacoes.length} solicitações carregadas com sucesso!`, 'success');
            } catch (error) {
                console.error('Erro ao carregar solicitações:', error);
                showNotification('Erro ao carregar solicitações: ' + error.message, 'error');
            }
        };

        // Renderizar tabela
        function renderSolicitacoes() {
            const tbody = document.getElementById('solicitacoesTableBody');
            
            if (filteredSolicitacoes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-search fa-2x"></i><br><br>
                            Nenhuma solicitação encontrada
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredSolicitacoes.map(sol => {
                const dataFormatada = formatDate(sol.dataCriacao);
                const valorTotal = calculateTotal(sol.itens || []);
                const numItens = (sol.itens || []).length;

                return `
                    <tr>
                        <td><strong>${sol.numero || 'N/A'}</strong></td>
                        <td>${dataFormatada}</td>
                        <td class="text-truncate">${sol.solicitante || 'N/A'}</td>
                        <td class="text-truncate">${sol.departamentoNome || sol.departamento || 'N/A'}</td>
                        <td><span class="status status-${(sol.status || 'pendente').toLowerCase()}">${getStatusText(sol.status)}</span></td>
                        <td><span class="priority priority-${(sol.prioridade || 'media').toLowerCase()}">${sol.prioridade || 'MEDIA'}</span></td>
                        <td style="text-align: center;">${numItens}</td>
                        <td>R$ ${formatCurrency(valorTotal)}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="editSolicitacao('${sol.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Filtrar solicitações
        window.filterSolicitacoes = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            if (!searchTerm) {
                filteredSolicitacoes = [...allSolicitacoes];
            } else {
                filteredSolicitacoes = allSolicitacoes.filter(sol => 
                    (sol.numero || '').toLowerCase().includes(searchTerm) ||
                    (sol.solicitante || '').toLowerCase().includes(searchTerm) ||
                    (sol.departamento || '').toLowerCase().includes(searchTerm) ||
                    (sol.departamentoNome || '').toLowerCase().includes(searchTerm) ||
                    (sol.justificativa || '').toLowerCase().includes(searchTerm)
                );
            }
            
            renderSolicitacoes();
            updateTotalCount();
        };

        // Atualizar contador
        function updateTotalCount() {
            document.getElementById('totalCount').textContent = 
                `${filteredSolicitacoes.length} de ${allSolicitacoes.length} solicitações`;
        }

        // Editar solicitação
        window.editSolicitacao = function(id) {
            const solicitacao = allSolicitacoes.find(s => s.id === id);
            if (!solicitacao) {
                showNotification('Solicitação não encontrada!', 'error');
                return;
            }

            currentEditId = id;
            populateEditForm(solicitacao);
            document.getElementById('editModal').style.display = 'block';
        };

        // Preencher formulário de edição
        function populateEditForm(sol) {
            document.getElementById('solicitacaoId').value = sol.id;
            document.getElementById('numero').value = sol.numero || '';
            document.getElementById('status').value = sol.status || 'PENDENTE';
            document.getElementById('tipo').value = sol.tipo || 'NORMAL';
            document.getElementById('origem').value = sol.origem || 'MANUAL';
            document.getElementById('solicitante').value = sol.solicitante || '';
            document.getElementById('solicitanteId').value = sol.solicitanteId || '';
            document.getElementById('departamento').value = sol.departamento || '';
            document.getElementById('departamentoNome').value = sol.departamentoNome || '';
            document.getElementById('prioridade').value = sol.prioridade || 'MEDIA';
            document.getElementById('centroCustoId').value = sol.centroCustoId || '';
            document.getElementById('justificativa').value = sol.justificativa || '';
            document.getElementById('observacoes').value = sol.observacoes || '';

            // Datas
            if (sol.dataCriacao) {
                document.getElementById('dataCriacao').value = timestampToDatetime(sol.dataCriacao);
            }
            if (sol.ultimaAtualizacao) {
                document.getElementById('ultimaAtualizacao').value = timestampToDatetime(sol.ultimaAtualizacao);
            }
            if (sol.dataNecessidade) {
                document.getElementById('dataNecessidade').value = timestampToDate(sol.dataNecessidade);
            }
            if (sol.dataLimiteAprovacao) {
                document.getElementById('dataLimiteAprovacao').value = timestampToDate(sol.dataLimiteAprovacao);
            }

            // Renderizar itens
            renderItems(sol.itens || []);
        }

        // Renderizar itens
        function renderItems(itens) {
            const container = document.getElementById('itemsContainer');
            
            if (itens.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #6c757d;">
                        <i class="fas fa-box-open fa-2x"></i><br><br>
                        Nenhum item adicionado
                    </div>
                `;
                return;
            }

            container.innerHTML = itens.map((item, index) => `
                <div class="item-row" data-index="${index}">
                    <input type="text" placeholder="Código" value="${item.codigo || ''}" onchange="updateItem(${index}, 'codigo', this.value)">
                    <input type="text" placeholder="Descrição" value="${item.descricao || ''}" onchange="updateItem(${index}, 'descricao', this.value)">
                    <input type="number" placeholder="Qtd" value="${item.quantidade || 1}" step="0.001" onchange="updateItem(${index}, 'quantidade', parseFloat(this.value))">
                    <input type="text" placeholder="Un" value="${item.unidade || 'UN'}" onchange="updateItem(${index}, 'unidade', this.value)">
                    <input type="number" placeholder="Valor Unit." value="${item.valorUnitario || 0}" step="0.01" onchange="updateItem(${index}, 'valorUnitario', parseFloat(this.value))">
                    <div style="font-weight: 600;">R$ ${formatCurrency((item.quantidade || 0) * (item.valorUnitario || 0))}</div>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        // Funções de itens
        let currentItems = [];

        window.addNewItem = function() {
            const newItem = {
                produtoId: '',
                codigo: '',
                descricao: '',
                quantidade: 1,
                unidade: 'UN',
                valorUnitario: 0,
                observacoes: ''
            };
            currentItems.push(newItem);
            renderItems(currentItems);
        };

        window.updateItem = function(index, field, value) {
            if (currentItems[index]) {
                currentItems[index][field] = value;
                renderItems(currentItems);
            }
        };

        window.removeItem = function(index) {
            if (confirm('Remover este item?')) {
                currentItems.splice(index, 1);
                renderItems(currentItems);
            }
        };

        // Salvar alterações
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!currentEditId) {
                showNotification('Erro: ID da solicitação não encontrado!', 'error');
                return;
            }

            try {
                const formData = {
                    numero: document.getElementById('numero').value,
                    status: document.getElementById('status').value,
                    tipo: document.getElementById('tipo').value,
                    origem: document.getElementById('origem').value,
                    solicitante: document.getElementById('solicitante').value,
                    solicitanteId: document.getElementById('solicitanteId').value,
                    departamento: document.getElementById('departamento').value,
                    departamentoNome: document.getElementById('departamentoNome').value,
                    prioridade: document.getElementById('prioridade').value,
                    centroCustoId: document.getElementById('centroCustoId').value,
                    justificativa: document.getElementById('justificativa').value,
                    observacoes: document.getElementById('observacoes').value,
                    itens: currentItems,
                    ultimaAtualizacao: Timestamp.now()
                };

                // Processar datas
                if (document.getElementById('dataNecessidade').value) {
                    formData.dataNecessidade = Timestamp.fromDate(new Date(document.getElementById('dataNecessidade').value));
                }
                if (document.getElementById('dataLimiteAprovacao').value) {
                    formData.dataLimiteAprovacao = Timestamp.fromDate(new Date(document.getElementById('dataLimiteAprovacao').value));
                }

                // Remover campos vazios
                Object.keys(formData).forEach(key => {
                    if (formData[key] === '' || formData[key] === null || formData[key] === undefined) {
                        delete formData[key];
                    }
                });

                await updateDoc(doc(db, "solicitacoesCompra", currentEditId), formData);
                
                showNotification('Solicitação atualizada com sucesso!', 'success');
                closeModal();
                loadAllSolicitacoes();

            } catch (error) {
                console.error('Erro ao salvar:', error);
                showNotification('Erro ao salvar: ' + error.message, 'error');
            }
        });

        // Excluir permanentemente
        window.deleteForever = async function() {
            if (!currentEditId) return;

            const confirmText = prompt('ATENÇÃO! Esta ação é IRREVERSÍVEL.\n\nDigite "EXCLUIR PERMANENTEMENTE" para confirmar:');
            
            if (confirmText !== 'EXCLUIR PERMANENTEMENTE') {
                showNotification('Operação cancelada.', 'warning');
                return;
            }

            try {
                await deleteDoc(doc(db, "solicitacoesCompra", currentEditId));
                showNotification('Solicitação excluída permanentemente!', 'success');
                closeModal();
                loadAllSolicitacoes();
            } catch (error) {
                console.error('Erro ao excluir:', error);
                showNotification('Erro ao excluir: ' + error.message, 'error');
            }
        };

        // Fechar modal
        window.closeModal = function() {
            document.getElementById('editModal').style.display = 'none';
            currentEditId = null;
            currentItems = [];
        };

        // Funções utilitárias
        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toLocaleString('pt-BR');
            } catch {
                return 'Data inválida';
            }
        }

        function timestampToDatetime(timestamp) {
            if (!timestamp) return '';
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toISOString().slice(0, 16);
            } catch {
                return '';
            }
        }

        function timestampToDate(timestamp) {
            if (!timestamp) return '';
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toISOString().slice(0, 10);
            } catch {
                return '';
            }
        }

        function formatCurrency(value) {
            return (value || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }

        function calculateTotal(itens) {
            return (itens || []).reduce((total, item) => total + ((item.quantidade || 0) * (item.valorUnitario || 0)), 0);
        }

        function getStatusText(status) {
            const statusMap = {
                'PENDENTE': 'Pendente',
                'APROVADA': 'Aprovada',
                'REJEITADA': 'Rejeitada',
                'EM_COTACAO': 'Em Cotação',
                'EXCLUIDA': 'Excluída'
            };
            return statusMap[status] || status || 'N/A';
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Toggle system fields
        window.toggleSystemFields = function() {
            const content = document.getElementById('systemFieldsContent');
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        };

        // Inicializar ao carregar a página
        window.addEventListener('load', function() {
            loadAllSolicitacoes();
        });

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeModal();
            }
        };

        // Tornar currentItems disponível globalmente
        window.getCurrentItems = function() {
            return currentItems;
        };

        window.setCurrentItems = function(items) {
            currentItems = items;
        };

        // Inicializar currentItems quando editar
        window.editSolicitacao = function(id) {
            const solicitacao = allSolicitacoes.find(s => s.id === id);
            if (!solicitacao) {
                showNotification('Solicitação não encontrada!', 'error');
                return;
            }

            currentEditId = id;
            currentItems = JSON.parse(JSON.stringify(solicitacao.itens || [])); // Deep copy
            populateEditForm(solicitacao);
            document.getElementById('editModal').style.display = 'block';
        };
    </script>
</body>
</html> 