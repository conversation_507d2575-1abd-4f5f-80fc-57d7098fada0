
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Relatório de Explosão de Estruturas</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* ========================================
           🎨 CSS PADRONIZADO - EXPLOSÃO ESTRUTURAS
           Baseado em: gestao_compras_integrada.html
           ======================================== */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .form-container {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .form-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
            display: block;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .filters-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }

        .explosion-tree {
            margin-top: 25px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .tree-node {
            margin: 8px 0;
            padding: 15px;
            border-left: 4px solid #3498db;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tree-node:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .tree-node.level-0 { 
            margin-left: 0px; 
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left-color: #2196F3;
        }
        .tree-node.level-1 { 
            margin-left: 30px; 
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-left-color: #9c27b0;
        }
        .tree-node.level-2 { 
            margin-left: 60px; 
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left-color: #4caf50;
        }
        .tree-node.level-3 { 
            margin-left: 90px; 
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left-color: #ff9800;
        }
        .tree-node.level-4 { 
            margin-left: 120px; 
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            border-left-color: #e91e63;
        }

        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }

        .node-info {
            flex: 1;
        }

        .node-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .node-details {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .node-quantity {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .type-badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .type-PA { 
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); 
            color: white; 
        }
        .type-SP { 
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); 
            color: white; 
        }
        .type-MP { 
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%); 
            color: white; 
        }

        .summary-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #3498db;
            transition: all 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .summary-card:nth-child(1) { border-left-color: #3498db; }
        .summary-card:nth-child(2) { border-left-color: #27ae60; }
        .summary-card:nth-child(3) { border-left-color: #f39c12; }
        .summary-card:nth-child(4) { border-left-color: #e74c3c; }

        .summary-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            margin-top: 25px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
            margin: 25px 0;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filters-section {
                grid-template-columns: 1fr;
            }

            .summary-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .table-container {
                overflow-x: auto;
            }

            .tree-node {
                margin-left: 0 !important;
                padding: 15px;
            }

            .tree-node.level-1 { border-left-color: #f39c12; }
            .tree-node.level-2 { border-left-color: #27ae60; }
            .tree-node.level-3 { border-left-color: #e74c3c; }
        }

        @media print {
            .no-print {
                display: none;
            }
            .container {
                margin: 0;
                padding: 10px;
                box-shadow: none;
                border-radius: 0;
            }
            .header {
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-sitemap"></i>
                Relatório de Explosão de Estruturas
            </h1>
            <div class="header-actions">
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Alerta informativo -->
            <div class="alert alert-info no-print">
                <i class="fas fa-info-circle"></i>
                <strong>Explosão de Estruturas:</strong>
                Gere uma lista detalhada de todos os componentes necessários para produzir um produto acabado ou semi-acabado, 
                incluindo quantidades e níveis hierárquicos.
            </div>

            <!-- Filtros -->
            <div class="filters-section no-print">
                <div class="form-group">
                    <label for="productSelect">
                        <i class="fas fa-box"></i> Selecione o Produto
                    </label>
                    <select id="productSelect" class="form-control" onchange="updateQuantityField()">
                        <option value="">Selecione um produto...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="quantityInput">
                        <i class="fas fa-calculator"></i> Quantidade
                    </label>
                    <input type="number" id="quantityInput" class="form-control" min="1" value="1" step="0.001">
                </div>

                <div class="form-group">
                    <label for="maxLevels">
                        <i class="fas fa-layer-group"></i> Níveis Máximos
                    </label>
                    <select id="maxLevels" class="form-control">
                        <option value="1">🔸 1 Nível</option>
                        <option value="2">🔸🔸 2 Níveis</option>
                        <option value="3">🔸🔸🔸 3 Níveis</option>
                        <option value="4">🔸🔸🔸🔸 4 Níveis</option>
                        <option value="5" selected>🔸🔸🔸🔸🔸 5 Níveis</option>
                        <option value="999">♾️ Todos os Níveis</option>
                    </select>
                </div>

                <div class="form-group" style="display: flex; align-items: end; gap: 15px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="generateExplosion()">
                        <i class="fas fa-rocket"></i> Gerar Explosão
                    </button>
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Exportar Excel
                    </button>
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                </div>
            </div>

            <div class="loading">
                <div class="spinner"></div>
                <p><strong>Processando explosão de estrutura...</strong></p>
            </div>

            <div id="explosionResults"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let estruturas = [];
        let currentExplosion = null;

        // Inicialização
        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            await loadData();
            setupProductSelect();
        };

        async function loadData() {
            try {
                const [produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ Carregados ${produtos.length} produtos e ${estruturas.length} estruturas`);
                
                // Exibir alerta de sucesso no carregamento
                if (produtos.length === 0) {
                    document.getElementById('explosionResults').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Atenção:</strong> Nenhum produto encontrado no sistema. 
                            Cadastre produtos antes de gerar explosões de estrutura.
                        </div>
                    `;
                }
            } catch (error) {
                console.error("❌ Erro ao carregar dados:", error);
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Erro ao carregar dados:</strong> ${error.message}
                        <br><small>Verifique sua conexão e tente novamente.</small>
                    </div>
                `;
            }
        }

        function setupProductSelect() {
            const select = document.getElementById('productSelect');
            select.innerHTML = '<option value="">Selecione um produto...</option>';

            produtos
                .filter(p => p.tipo === 'PA' || p.tipo === 'SP')
                .sort((a, b) => a.codigo.localeCompare(b.codigo))
                .forEach(produto => {
                    const option = document.createElement('option');
                    option.value = produto.id;
                    option.textContent = `${produto.codigo} - ${produto.descricao} (${produto.tipo})`;
                    select.appendChild(option);
                });
        }

        window.updateQuantityField = function() {
            const productId = document.getElementById('productSelect').value;
            if (productId) {
                const produto = produtos.find(p => p.id === productId);
                if (produto) {
                    document.getElementById('quantityInput').placeholder = `Quantidade em ${produto.unidade}`;
                }
            }
        };

        window.generateExplosion = async function() {
            const productId = document.getElementById('productSelect').value;
            const quantity = parseFloat(document.getElementById('quantityInput').value) || 1;
            const maxLevels = parseInt(document.getElementById('maxLevels').value);

            // Validações
            if (!productId) {
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Seleção Obrigatória:</strong> Por favor, selecione um produto para gerar a explosão.
                    </div>
                `;
                return;
            }

            if (quantity <= 0) {
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Quantidade Inválida:</strong> A quantidade deve ser maior que zero.
                    </div>
                `;
                return;
            }

            document.querySelector('.loading').classList.add('active');
            document.getElementById('explosionResults').innerHTML = '';

            try {
                const produto = produtos.find(p => p.id === productId);
                
                if (!produto) {
                    throw new Error('Produto não encontrado no sistema.');
                }

                console.log(`🚀 Iniciando explosão para: ${produto.codigo} (Qty: ${quantity}, Níveis: ${maxLevels})`);
                
                currentExplosion = await explodeStructure(productId, quantity, 0, maxLevels);
                
                if (!currentExplosion || currentExplosion.length === 0) {
                    document.getElementById('explosionResults').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i>
                            <strong>Estrutura Vazia:</strong> O produto selecionado não possui estrutura definida ou não foi possível processar.
                        </div>
                    `;
                    return;
                }
                
                renderExplosionResults(produto, quantity, currentExplosion);
                console.log(`✅ Explosão concluída: ${currentExplosion.length} itens processados`);
                
            } catch (error) {
                console.error("❌ Erro ao gerar explosão:", error);
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Erro no Processamento:</strong> ${error.message}
                        <br><small>Verifique os dados e tente novamente.</small>
                    </div>
                `;
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        async function explodeStructure(productId, quantity, level = 0, maxLevels = 999, processedIds = new Set()) {
            // Prevenir loops infinitos
            if (processedIds.has(productId) || level > maxLevels) {
                return [];
            }

            processedIds.add(productId);

            const produto = produtos.find(p => p.id === productId);
            if (!produto) return [];

            const estrutura = estruturas.find(e => e.produtoPaiId === productId);
            
            const explosionData = [{
                level,
                produto,
                quantidade: quantity,
                quantidadeAcumulada: quantity,
                temEstrutura: !!estrutura,
                componentes: []
            }];

            // Se tem estrutura, explode os componentes
            if (estrutura && estrutura.componentes) {
                for (const componente of estrutura.componentes) {
                    const componenteProduto = produtos.find(p => p.id === componente.componentId);
                    if (componenteProduto) {
                        const quantidadeComponente = quantity * componente.quantidade;
                        
                        // Adiciona o componente atual
                        explosionData.push({
                            level: level + 1,
                            produto: componenteProduto,
                            quantidade: componente.quantidade,
                            quantidadeAcumulada: quantidadeComponente,
                            temEstrutura: estruturas.some(e => e.produtoPaiId === componente.componentId),
                            componentes: []
                        });

                        // Se o componente é SP e tem estrutura, explode recursivamente
                        if (componenteProduto.tipo === 'SP' && level < maxLevels) {
                            const subExplosion = await explodeStructure(
                                componente.componentId, 
                                quantidadeComponente, 
                                level + 1, 
                                maxLevels,
                                new Set(processedIds)
                            );
                            explosionData.push(...subExplosion.slice(1)); // Remove o primeiro item para evitar duplicação
                        }
                    }
                }
            }

            return explosionData;
        }

        function renderExplosionResults(produto, quantity, explosionData) {
            const resultsDiv = document.getElementById('explosionResults');
            
            // Calcular estatísticas
            const stats = calculateStats(explosionData);
            
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Explosão Gerada com Sucesso!</strong>
                    Estrutura processada para: <strong>${produto.codigo} - ${produto.descricao}</strong> 
                    (Quantidade Base: <strong>${quantity} ${produto.unidade}</strong>)
                </div>

                <div class="summary-section">
                    <h2 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-chart-pie"></i>
                        Resumo da Explosão
                    </h2>
                    
                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="summary-number">${stats.totalItens}</div>
                            <div class="summary-label"><i class="fas fa-list"></i> Total de Itens</div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-number">${stats.materiaPrima}</div>
                            <div class="summary-label"><i class="fas fa-cubes"></i> Matérias-Primas</div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-number">${stats.subProdutos}</div>
                            <div class="summary-label"><i class="fas fa-cogs"></i> Sub-Produtos</div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-number">${stats.maxLevel + 1}</div>
                            <div class="summary-label"><i class="fas fa-layer-group"></i> Níveis de Profundidade</div>
                        </div>
                    </div>
                </div>

                <div class="explosion-tree">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-sitemap"></i>
                        Estrutura Hierárquica
                    </h3>
                    ${renderTreeNodes(explosionData)}
                </div>

                <div class="table-container">
                    <h3 style="color: white; margin: 0; padding: 20px; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-table"></i>
                        Lista Consolidada de Materiais
                    </h3>
                    ${renderMaterialsTable(explosionData)}
                </div>
            `;
        }

        function renderTreeNodes(explosionData) {
            const levelIcons = ['🔷', '🔸', '🔹', '🔺', '🔻'];
            const typeIcons = {
                'PA': '🏭',
                'SP': '⚙️',
                'MP': '📦'
            };

            return explosionData.map(item => `
                <div class="tree-node level-${item.level}">
                    <div class="node-header">
                        <div class="node-info">
                            <div class="node-title">
                                ${levelIcons[item.level] || '🔸'} 
                                <strong>${item.produto.codigo}</strong> - ${item.produto.descricao}
                                <span class="type-badge type-${item.produto.tipo}">
                                    ${typeIcons[item.produto.tipo] || '📋'} ${item.produto.tipo}
                                </span>
                            </div>
                            <div class="node-details">
                                <i class="fas fa-calculator"></i> Quantidade unitária: <strong>${item.quantidade}</strong> ${item.produto.unidade} | 
                                <i class="fas fa-plus-circle"></i> Total acumulado: <strong>${item.quantidadeAcumulada.toFixed(3)}</strong> ${item.produto.unidade}
                                ${item.temEstrutura ? ' | <i class="fas fa-sitemap" style="color: #27ae60;" title="Possui estrutura"></i> <span style="color: #27ae60; font-weight: 600;">Estruturado</span>' : ' | <i class="fas fa-circle" style="color: #95a5a6;" title="Item final"></i> <span style="color: #95a5a6;">Item Final</span>'}
                            </div>
                        </div>
                        <div class="node-quantity">
                            ${item.quantidadeAcumulada.toFixed(3)} ${item.produto.unidade}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function renderMaterialsTable(explosionData) {
            // Agrupar materiais por código
            const materialsMap = new Map();
            
            explosionData.forEach(item => {
                if (item.produto.tipo === 'MP' || item.produto.tipo === 'SP') {
                    const key = item.produto.codigo;
                    if (materialsMap.has(key)) {
                        materialsMap.get(key).quantidadeTotal += item.quantidadeAcumulada;
                    } else {
                        materialsMap.set(key, {
                            produto: item.produto,
                            quantidadeTotal: item.quantidadeAcumulada,
                            nivel: item.level
                        });
                    }
                }
            });

            const materialsArray = Array.from(materialsMap.values())
                .sort((a, b) => a.produto.codigo.localeCompare(b.produto.codigo));

            return `
                <table>
                    <thead>
                        <tr>
                            <th><i class="fas fa-code"></i> Código</th>
                            <th><i class="fas fa-tag"></i> Descrição</th>
                            <th><i class="fas fa-layer-group"></i> Tipo</th>
                            <th><i class="fas fa-calculator"></i> Quantidade Total</th>
                            <th><i class="fas fa-balance-scale"></i> Unidade</th>
                            <th><i class="fas fa-sitemap"></i> Menor Nível</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${materialsArray.map(item => {
                            const typeIcons = { 'PA': '🏭', 'SP': '⚙️', 'MP': '📦' };
                            return `
                                <tr>
                                    <td><strong>${item.produto.codigo}</strong></td>
                                    <td>${item.produto.descricao}</td>
                                    <td>
                                        <span class="type-badge type-${item.produto.tipo}">
                                            ${typeIcons[item.produto.tipo] || '📋'} ${item.produto.tipo}
                                        </span>
                                    </td>
                                    <td style="text-align: right; font-weight: 600; color: #2c3e50;">
                                        ${item.quantidadeTotal.toFixed(3)}
                                    </td>
                                    <td style="font-weight: 500;">${item.produto.unidade}</td>
                                    <td style="text-align: center;">
                                        <span style="background: #3498db; color: white; padding: 4px 8px; border-radius: 10px; font-size: 11px; font-weight: 600;">
                                            Nível ${item.nivel}
                                        </span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;
        }

        function calculateStats(explosionData) {
            const stats = {
                totalItens: explosionData.length,
                materiaPrima: explosionData.filter(item => item.produto.tipo === 'MP').length,
                subProdutos: explosionData.filter(item => item.produto.tipo === 'SP').length,
                maxLevel: Math.max(...explosionData.map(item => item.level))
            };
            return stats;
        }

        window.exportToExcel = function() {
            if (!currentExplosion) {
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Explosão Necessária:</strong> Gere uma explosão de estrutura primeiro antes de exportar.
                    </div>
                `;
                return;
            }

            try {
                const workbook = XLSX.utils.book_new();
                
                // Aba 1: Estrutura Hierárquica
                const hierarchyData = currentExplosion.map(item => ({
                    'Nível': item.level,
                    'Código': item.produto.codigo,
                    'Descrição': item.produto.descricao,
                    'Tipo': item.produto.tipo,
                    'Quantidade Unitária': item.quantidade,
                    'Quantidade Total': item.quantidadeAcumulada,
                    'Unidade': item.produto.unidade,
                    'Tem Estrutura': item.temEstrutura ? 'Sim' : 'Não'
                }));

                const wsHierarchy = XLSX.utils.json_to_sheet(hierarchyData);
                XLSX.utils.book_append_sheet(workbook, wsHierarchy, "Estrutura Hierárquica");

                // Aba 2: Materiais Consolidados
                const materialsMap = new Map();
                currentExplosion.forEach(item => {
                    if (item.produto.tipo === 'MP' || item.produto.tipo === 'SP') {
                        const key = item.produto.codigo;
                        if (materialsMap.has(key)) {
                            materialsMap.get(key)['Quantidade Total'] += item.quantidadeAcumulada;
                        } else {
                            materialsMap.set(key, {
                                'Código': item.produto.codigo,
                                'Descrição': item.produto.descricao,
                                'Tipo': item.produto.tipo,
                                'Quantidade Total': item.quantidadeAcumulada,
                                'Unidade': item.produto.unidade,
                                'Menor Nível': item.level
                            });
                        }
                    }
                });

                const materialsData = Array.from(materialsMap.values());
                const wsMaterials = XLSX.utils.json_to_sheet(materialsData);
                XLSX.utils.book_append_sheet(workbook, wsMaterials, "Materiais Consolidados");

                // Salvar arquivo
                const produto = produtos.find(p => p.id === document.getElementById('productSelect').value);
                const fileName = `Explosao_${produto.codigo}_${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(workbook, fileName);

                // Exibir alerta de sucesso
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success';
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; animation: slideIn 0.3s ease;';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <strong>Exportado com Sucesso!</strong> 
                    Arquivo: <strong>${fileName}</strong>
                `;
                document.body.appendChild(alertDiv);
                
                setTimeout(() => {
                    document.body.removeChild(alertDiv);
                }, 4000);

                console.log(`📊 Relatório exportado: ${fileName}`);
            } catch (error) {
                console.error("❌ Erro ao exportar:", error);
                document.getElementById('explosionResults').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Erro na Exportação:</strong> ${error.message}
                        <br><small>Verifique os dados e tente novamente.</small>
                    </div>
                `;
            }
        };

        // Expor funções globalmente
        window.generateExplosion = generateExplosion;
        window.updateQuantityField = updateQuantityField;
        window.exportToExcel = exportToExcel;
    </script>
</body>
</html>
