/* ===== COTAÇÕES - ESTILOS PRINCIPAIS ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* ===== HEADER ===== */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 25px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* ===== BOTÕES ===== */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-sm {
    padding: 5px 12px;
    font-size: 0.8rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== CONTEÚDO PRINCIPAL ===== */
.main-content {
    padding: 30px;
}

/* ===== WORKFLOW ===== */
.workflow-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    position: relative;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.workflow-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 60px;
    width: 100px;
    height: 3px;
    background: #e9ecef;
    z-index: 1;
}

.workflow-step.completed::after {
    background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
}

.workflow-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.workflow-step.completed .workflow-icon {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.workflow-step.active .workflow-icon {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    animation: pulse 2s infinite;
}

.workflow-step .workflow-icon {
    background: #e9ecef;
    color: #6c757d;
}

.workflow-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
    text-align: center;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* ===== ESTATÍSTICAS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 5px solid;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.abertas {
    border-left-color: #3498db;
}

.stat-card.enviadas {
    border-left-color: #f39c12;
}

.stat-card.respondidas {
    border-left-color: #27ae60;
}

.stat-card.aprovadas {
    border-left-color: #9b59b6;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    color: #7f8c8d;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* ===== ABAS ===== */
.tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 25px;
}

.tab {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab:hover {
    color: #3498db;
    background: #f8f9fa;
}

.tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ===== BARRA DE PESQUISA ===== */
.search-bar {
    position: relative;
    margin-bottom: 25px;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.2rem;
}

/* ===== AÇÕES RÁPIDAS ===== */
.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

/* ===== FILTROS ===== */
.filters {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.filters h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-control {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* ===== TABELA ===== */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== STATUS E BADGES ===== */
.status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status.aberta {
    background: #e3f2fd;
    color: #1976d2;
}

.status.enviada {
    background: #fff3e0;
    color: #f57c00;
}

.status.respondida {
    background: #e8f5e8;
    color: #388e3c;
}

.status.aprovada {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status.fechada {
    background: #f5f5f5;
    color: #616161;
}

.status.aglutinada {
    background: #fff3e0;
    color: #e65100;
}

.integration-badge {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 5px;
}

/* ===== COTAÇÕES ESPECIAIS ===== */
.quotation-row.aglutinada {
    background: linear-gradient(90deg, #e3f2fd 0%, #f8f9fa 100%);
    border-left: 4px solid #2196f3;
}

.quotation-row.aglutinada-filha {
    background: linear-gradient(90deg, #fff3e0 0%, #f8f9fa 100%);
    border-left: 4px solid #ff9800;
    opacity: 0.8;
}

.quotation-row.fechada {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    opacity: 0.7;
    border-left: 4px solid #6c757d;
}

.quotation-row.fechada td {
    color: #6c757d !important;
}

.quotation-row.hidden {
    display: none;
}

.aglutinacao-badge {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 5px;
}

.aglutinacao-badge.filha {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.fechada-badge {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 5px;
}

/* ===== AÇÕES ===== */
.actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.quotation-checkbox {
    transform: scale(1.2);
    cursor: pointer;
}

/* ===== PAGINAÇÃO ===== */
.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
}

.items-per-page label {
    font-weight: 500;
    color: #2c3e50;
}

.items-per-page select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pagination {
    display: flex;
    justify-content: center;
    padding: 20px;
    gap: 5px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination button:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.pagination button.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== MODAIS ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close:hover {
    color: #f39c12;
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
}

/* ===== ALERTAS ===== */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.alert-info {
    background: #e3f2fd;
    color: #1976d2;
    border-left: 4px solid #2196f3;
}

.alert-success {
    background: #e8f5e8;
    color: #388e3c;
    border-left: 4px solid #4caf50;
}

.alert-warning {
    background: #fff3e0;
    color: #f57c00;
    border-left: 4px solid #ff9800;
}

.alert-danger {
    background: #ffebee;
    color: #d32f2f;
    border-left: 4px solid #f44336;
}

/* ===== NOTIFICAÇÕES ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transform: translateX(400px);
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.notification-error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.notification-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.notification-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

/* ===== MODAL DE EDIÇÃO DE COTAÇÃO ===== */
.quotation-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col-md-3 {
    flex: 0 0 25%;
    padding: 10px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 10px;
}

.edit-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 25px;
    gap: 5px;
}

.edit-tab {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px 8px 0 0;
}

.edit-tab:hover {
    color: #3498db;
    background: #f8f9fa;
}

.edit-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: #f8f9fa;
    font-weight: 600;
}

.edit-tab-content {
    display: none;
}

.edit-tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.section-header h4 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* ===== TABELA DE ITENS ===== */
.items-section .table th {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: white;
    font-size: 0.85rem;
    padding: 12px 8px;
}

.items-section .table td {
    padding: 8px;
    vertical-align: middle;
}

.item-input {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 0.9rem;
}

.item-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.item-total {
    font-weight: bold;
    color: #27ae60;
}

/* ===== FORNECEDORES ===== */
.supplier-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.supplier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.supplier-name {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1rem;
}

.supplier-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.supplier-status.active {
    background: #d4edda;
    color: #155724;
}

/* ===== CONDIÇÕES COMERCIAIS ===== */
.commercial-conditions {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.financial-summary {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 25px;
    border: 1px solid #e9ecef;
}

.financial-summary h5 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.summary-item.total {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.summary-item label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: #6c757d;
}

.summary-item.total label {
    color: white;
}

.summary-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.summary-item.total .summary-value {
    color: white;
    font-size: 1.4rem;
}

/* ===== OBSERVAÇÕES ===== */
.observations-section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid #f39c12;
}

/* ===== AÇÕES DO MODAL ===== */
.modal-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 150px;
}

/* ===== LINKS DE RESPOSTA ===== */
.response-links-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.response-link-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 10px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.response-link-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.response-link-item:last-child {
    margin-bottom: 0;
}

.supplier-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.supplier-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.supplier-details h6 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.supplier-details small {
    color: #6c757d;
    font-size: 12px;
}

.link-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.link-url {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.copy-link-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.copy-link-btn:hover {
    background: #0056b3;
}

.copy-link-btn.copied {
    background: #28a745;
}

.no-suppliers-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* ===== ANÁLISE DE COTAÇÕES ===== */
.analysis-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    gap: 5px;
}

.analysis-tab {
    padding: 12px 20px;
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    font-weight: 500;
}

.analysis-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.analysis-tab.active {
    background: #007bff;
    color: white;
    border-bottom: 2px solid #007bff;
}

.analysis-tab-content {
    display: none;
}

.analysis-tab-content.active {
    display: block;
}

.quotation-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 12px;
    text-transform: uppercase;
}

.comparison-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.section-header h4 {
    margin: 0;
    color: #495057;
}

.comparison-controls {
    display: flex;
    gap: 10px;
}

.table-container {
    max-height: 600px;
    overflow-y: auto;
}

.comparison-row {
    border-bottom: 1px solid #e9ecef;
}

.supplier-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.supplier-option:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.supplier-option.selected {
    border-color: #28a745;
    background: #d4edda;
}

.supplier-option.best-price {
    border-color: #ffc107;
    background: #fff3cd;
}

.supplier-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.supplier-name {
    font-weight: 600;
    color: #495057;
}

.supplier-details {
    font-size: 12px;
    color: #6c757d;
}

.price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.unit-price {
    font-weight: 600;
    color: #28a745;
    font-size: 16px;
}

.total-price {
    font-size: 12px;
    color: #6c757d;
}

.best-price-badge {
    background: #ffc107;
    color: #856404;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.selected-badge {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.suppliers-analysis {
    display: grid;
    gap: 20px;
}

.supplier-analysis-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.supplier-analysis-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.supplier-analysis-body {
    padding: 20px;
}

.analysis-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric-card {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.metric-value {
    font-size: 24px;
    font-weight: 600;
    color: #007bff;
}

.metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 5px;
}

.winners-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.selected-items-table {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.selection-totals {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
}

.total-card {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.total-item:last-child {
    border-bottom: none;
}

.total-item.total {
    font-weight: 600;
    font-size: 16px;
    color: #28a745;
    border-top: 2px solid #28a745;
    margin-top: 10px;
    padding-top: 15px;
}

.selection-summary {
    background: #e3f2fd;
    color: #1976d2;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

/* ===== ANÁLISE POR FORNECEDOR ===== */
.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.complete {
    background: #d4edda;
    color: #155724;
}

.status-badge.partial {
    background: #fff3cd;
    color: #856404;
}

.supplier-details-section {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.detail-item {
    margin-bottom: 8px;
    padding: 5px 0;
}

.observations-text {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    min-height: 60px;
    font-style: italic;
    color: #6c757d;
}

.supplier-items-section {
    margin-top: 20px;
}

.items-table-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.best-price-row {
    background: #fff3cd !important;
}

/* ===== SELEÇÃO FINAL ===== */
.no-selection-message {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.no-selection-message h5 {
    margin: 15px 0 10px 0;
    color: #495057;
}

.supplier-selection-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.supplier-selection-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.supplier-selection-header h5 {
    margin: 0;
    color: #495057;
}

.supplier-selection-items {
    padding: 15px;
}

/* ===== RESUMO FINAL ===== */
.summary-overview {
    margin-bottom: 30px;
}

.summary-stat-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.stat-icon {
    font-size: 32px;
    color: #007bff;
    width: 50px;
    text-align: center;
}

.stat-content {
    flex: 1;
    text-align: left;
}

.stat-value {
    font-size: 28px;
    font-weight: 600;
    color: #495057;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 5px;
}

.summary-financial {
    margin-bottom: 30px;
}

.financial-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
}

.financial-card.savings {
    border-color: #28a745;
    background: #f8fff9;
}

.financial-card h5 {
    margin: 0 0 10px 0;
    color: #6c757d;
    font-size: 14px;
    text-transform: uppercase;
}

.financial-value {
    font-size: 24px;
    font-weight: 600;
    color: #495057;
}

.financial-card.savings .financial-value {
    color: #28a745;
}

.financial-percent {
    font-size: 12px;
    color: #28a745;
    margin-top: 5px;
}

.summary-suppliers {
    margin-bottom: 30px;
}

.suppliers-distribution {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.supplier-distribution-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
}

.supplier-distribution-item:last-child {
    border-bottom: none;
}

.supplier-info {
    flex: 1;
}

.supplier-info strong {
    display: block;
    color: #495057;
}

.supplier-info small {
    color: #6c757d;
}

.supplier-value {
    font-weight: 600;
    color: #28a745;
    margin: 0 20px;
}

.progress-bar {
    width: 100px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    transition: width 0.3s ease;
}

.summary-recommendations {
    margin-bottom: 20px;
}

.recommendations-list {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
}

.recommendation-item:last-child {
    margin-bottom: 0;
}

.recommendation-item.success {
    background: #d4edda;
    color: #155724;
}

.recommendation-item.warning {
    background: #fff3cd;
    color: #856404;
}

.recommendation-item.info {
    background: #d1ecf1;
    color: #0c5460;
}

.no-selection-summary {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.no-selection-summary h4 {
    margin: 15px 0 10px 0;
    color: #495057;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
    .col-md-3, .col-md-6 {
        flex: 0 0 100%;
    }

    .edit-tabs, .analysis-tabs {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-buttons .btn {
        width: 100%;
    }

    .response-link-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .link-actions {
        width: 100%;
        justify-content: space-between;
    }

    .link-url {
        max-width: 100%;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .comparison-controls {
        width: 100%;
        justify-content: space-between;
    }

    .analysis-metrics {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Responsividade para conversão de unidades */
    .unit-column,
    .quantity-column {
        min-width: auto;
        padding: 4px;
    }

    .unit-value {
        font-size: 1rem;
    }

    .converted-quantity,
    .same-quantity {
        min-width: 80px;
        padding: 4px 8px;
    }

    .converted-qty,
    .same-qty {
        font-size: 0.9rem;
    }

    .quantity-input {
        max-width: 80px;
        padding: 4px;
    }
}

/* ===== CONVERSÃO DE UNIDADES - LAYOUT EM COLUNAS ===== */

/* Colunas de Unidade */
.unit-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    min-width: 80px;
    text-align: center;
}

.unit-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Colunas de Quantidade */
.quantity-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    min-width: 120px;
}

.quantity-input {
    width: 100%;
    max-width: 100px;
    text-align: center;
    margin-bottom: 4px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    padding: 6px;
    font-weight: 500;
}

.quantity-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.quantity-unit {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* Quantidade Convertida */
.converted-quantity {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 6px;
    border: 2px solid #bbdefb;
    min-width: 100px;
}

.converted-qty {
    font-weight: bold;
    color: #1976d2;
    font-size: 1rem;
    margin-bottom: 2px;
}

/* Quantidade Igual (sem conversão) */
.same-quantity {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 2px solid #dee2e6;
    min-width: 100px;
}

.same-qty {
    font-weight: bold;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 2px;
}

.price-unit-info {
    display: block;
    color: #6c757d;
    font-size: 0.75rem;
    margin-top: 4px;
    font-style: italic;
    text-align: center;
}

/* Destaque para valor unitário baseado em conversão */
.price-conversion-highlight {
    border-left: 4px solid #f39c12;
}

/* Indicador visual para produtos com conversão */
.item-input[data-has-conversion="true"] {
    border-left: 4px solid #17a2b8;
}

/* Tooltip para conversão */
.conversion-tooltip {
    position: relative;
    cursor: help;
}

.conversion-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.conversion-tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    margin-bottom: -5px;
}
