<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizador de PDF em Tabela</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        td {
            padding: 10px;
            vertical-align: top;
        }
        .file-column {
            width: 50%;
            text-align: right;
        }
        .preview-column {
            width: 50%;
            position: relative;
        }
        .file-item {
            cursor: pointer;
            padding: 5px;
        }
        .file-item:hover + .preview-column .preview {
            display: block;
        }
        .preview {
            display: none;
            position: absolute;
            left: 10px;
            background: white;
            border: 1px solid #ccc;
            padding: 5px;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h1>Visualizador de PDF em Tabela</h1>
    <input type="file" id="pdfInput" multiple accept=".pdf">
    <table id="pdfTable">
        <tr>
            <th>Pré-visualização</th>
            <th>Arquivos</th>
        </tr>
    </table>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.min.js"></script>
    <script>
        const pdfInput = document.getElementById('pdfInput');
        const pdfTable = document.getElementById('pdfTable');

        pdfInput.addEventListener('change', async () => {
            // Limpa a tabela, exceto o cabeçalho
            while (pdfTable.rows.length > 1) pdfTable.deleteRow(1);

            const files = pdfInput.files;

            for (let file of files) {
                const row = pdfTable.insertRow();

                // Coluna da pré-visualização (esquerda)
                const previewCell = row.insertCell(0);
                previewCell.className = 'preview-column';
                const previewDiv = document.createElement('div');
                previewDiv.className = 'preview';
                previewCell.appendChild(previewDiv);

                // Coluna dos arquivos (direita)
                const fileCell = row.insertCell(1);
                fileCell.className = 'file-column';
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';
                fileDiv.textContent = file.name;
                fileCell.appendChild(fileDiv);

                // Carrega o PDF e gera a prévia
                const arrayBuffer = await file.arrayBuffer();
                const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
                const page = await pdf.getPage(1); // Primeira página
                const scale = 0.75; // Reduzido de 1.5 para 0.75 (50% menor)
                const viewport = page.getViewport({ scale });
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                await page.render({ canvasContext: context, viewport }).promise;
                previewDiv.appendChild(canvas);
            }
        });

        // Adiciona o evento de hover manualmente
        document.addEventListener('mouseover', (e) => {
            if (e.target.classList.contains('file-item')) {
                const preview = e.target.parentElement.previousElementSibling.querySelector('.preview');
                preview.style.display = 'block';
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.classList.contains('file-item')) {
                const preview = e.target.parentElement.previousElementSibling.querySelector('.preview');
                preview.style.display = 'none';
            }
        });
    </script>
</body>
</html>