// Serviço para gerenciar automatizações do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  updateDoc, 
  addDoc, 
  Timestamp,
  writeBatch,
  query,
  where,
  getDocs,
  getDoc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { NotificationService } from './notification-service.js';
import { WorkflowService } from './workflow-service.js';

export class AutomationService {
  // Converte automaticamente orçamentos aprovados em pedidos
  static async autoConvertApprovedQuotes() {
    try {
      // Busca orçamentos aprovados que ainda não foram convertidos
      const quotesQuery = query(
        collection(db, "orcamentos"),
        where("status", "==", "Aprovado pelo Cliente"),
        where("convertidoEmPedido", "==", false)
      );

      const quotesSnapshot = await getDocs(quotesQuery);
      const batch = writeBatch(db);

      for (const quoteDoc of quotesSnapshot.docs) {
        const quote = quoteDoc.data();

        // Cria o pedido
        const pedidoData = {
          orcamentoOrigem: quoteDoc.id,
          clienteId: quote.clienteId,
          cfop: quote.cfop,
          itens: quote.itens,
          valorTotal: quote.valorTotal,
          impostosTotais: quote.impostosTotais,
          condicaoPagamento: quote.condicaoPagamento,
          condicaoPagamentoDetalhes: quote.condicaoPagamentoDetalhes,
          prazoEntrega: quote.prazoEntrega,
          tipoFrete: quote.tipoFrete,
          transportadora: quote.transportadora,
          numeroPedidoCliente: quote.numeroPedidoCliente,
          observacoes: quote.observacoes,
          status: 'Aguardando Aprovação',
          dataCriacao: Timestamp.now(),
          criadoPor: 'Sistema (Automático)',
          workflowAprovacao: {
            nivelAtual: 1,
            niveisNecessarios: this.determineWorkflow(quote.valorTotal).niveisNecessarios,
            aprovacoes: []
          }
        };

        // Adiciona o pedido
        const pedidoRef = await addDoc(collection(db, "pedidosVenda"), pedidoData);

        // Atualiza o orçamento
        batch.update(quoteDoc.ref, {
          status: 'Convertido em Pedido',
          convertidoEmPedido: true,
          pedidoId: pedidoRef.id,
          dataConversao: Timestamp.now()
        });

        // Envia notificações
        await NotificationService.createNotification({
          tipo: 'PEDIDO_CRIADO',
          titulo: `Pedido criado automaticamente a partir do orçamento ${quote.numero}`,
          mensagem: `O orçamento ${quote.numero} foi convertido no pedido ${pedidoRef.id}`,
          destinatarios: ['VENDAS', 'FINANCEIRO'],
          prioridade: 'NORMAL',
          dados: {
            orcamentoId: quoteDoc.id,
            pedidoId: pedidoRef.id,
            clienteId: quote.clienteId
          }
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('Erro na conversão automática de orçamentos:', error);
      throw error;
    }
  }

  // Integração com gateway de pagamentos
  static async processPayment(pedidoId, metodoPagamento) {
    try {
      const pedidoDoc = await getDoc(doc(db, "pedidosVenda", pedidoId));
      const pedido = pedidoDoc.data();

      let paymentData;
      switch (metodoPagamento.toLowerCase()) {
        case 'pix':
          paymentData = await this.generatePixPayment(pedido);
          break;
        case 'boleto':
          paymentData = await this.generateBoletoPayment(pedido);
          break;
        default:
          throw new Error('Método de pagamento não suportado');
      }

      // Atualiza o pedido com os dados do pagamento
      await updateDoc(doc(db, "pedidosVenda", pedidoId), {
        dadosPagamento: {
          ...paymentData,
          status: 'PENDENTE',
          dataGeracao: Timestamp.now()
        }
      });

      // Envia notificação ao cliente
      await this.sendPaymentNotification(pedido, paymentData);

      return paymentData;
    } catch (error) {
      console.error('Erro ao processar pagamento:', error);
      throw error;
    }
  }

  // Gera pagamento PIX
  static async generatePixPayment(pedido) {
    try {
      // Integração com API de PIX (exemplo)
      const pixData = {
        valor: pedido.valorTotal,
        chave: 'CHAVE_PIX_EMPRESA', // Configurar
        beneficiario: 'NOME_EMPRESA', // Configurar
        cidade: 'CIDADE_EMPRESA', // Configurar
        identificador: `PED${pedido.numero}`,
        vencimento: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24h
      };

      // Aqui você integraria com seu provedor de PIX
      // const pixResponse = await pixProvider.generateCharge(pixData);

      return {
        tipo: 'PIX',
        // qrCode: pixResponse.qrCode,
        // chaveCopia: pixResponse.chaveCopia,
        valor: pedido.valorTotal,
        vencimento: pixData.vencimento
      };
    } catch (error) {
      console.error('Erro ao gerar PIX:', error);
      throw error;
    }
  }

  // Gera boleto bancário
  static async generateBoletoPayment(pedido) {
    try {
      // Integração com API de boleto (exemplo)
      const boletoData = {
        valor: pedido.valorTotal,
        vencimento: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 dias
        sacado: {
          nome: pedido.cliente.nome,
          documento: pedido.cliente.cpfCnpj,
          endereco: pedido.cliente.endereco
        },
        numeroDocumento: pedido.numero
      };

      // Aqui você integraria com seu provedor de boletos
      // const boletoResponse = await boletoProvider.generateBoleto(boletoData);

      return {
        tipo: 'BOLETO',
        // numeroBoletoBancario: boletoResponse.nossoNumero,
        // linhaDigitavel: boletoResponse.linhaDigitavel,
        // codigoBarras: boletoResponse.codigoBarras,
        valor: pedido.valorTotal,
        vencimento: boletoData.vencimento
      };
    } catch (error) {
      console.error('Erro ao gerar boleto:', error);
      throw error;
    }
  }

  // Envia notificações de pagamento
  static async sendPaymentNotification(pedido, paymentData) {
    try {
      // Busca dados do cliente
      const clienteDoc = await getDoc(doc(db, "fornecedores", pedido.clienteId));
      const cliente = clienteDoc.data();

      // Cria notificação no sistema
      await NotificationService.createNotification({
        tipo: 'PAGAMENTO_GERADO',
        titulo: `Pagamento gerado para o pedido ${pedido.numero}`,
        mensagem: `Foi gerado um ${paymentData.tipo} no valor de R$ ${paymentData.valor.toFixed(2)}`,
        destinatarios: ['FINANCEIRO', pedido.clienteId],
        prioridade: 'ALTA',
        dados: {
          pedidoId: pedido.id,
          clienteId: pedido.clienteId,
          pagamento: paymentData
        }
      });

      // Envia e-mail se disponível
      if (cliente.email) {
        await addDoc(collection(db, "filaEmails"), {
          para: cliente.email,
          assunto: `Pagamento - Pedido ${pedido.numero}`,
          corpo: this.generatePaymentEmailBody(pedido, paymentData),
          anexos: [], // Adicionar boleto/QRCode como anexo se necessário
          status: 'PENDENTE',
          dataEnvio: Timestamp.now()
        });
      }

      // Envia SMS se disponível
      if (cliente.celular) {
        await addDoc(collection(db, "filaSMS"), {
          numero: cliente.celular,
          mensagem: `Pagamento gerado para pedido ${pedido.numero}. Acesse o portal para mais detalhes.`,
          status: 'PENDENTE',
          dataEnvio: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Erro ao enviar notificação de pagamento:', error);
      // Não lança erro para não interromper o fluxo principal
    }
  }

  // Gera corpo do e-mail de pagamento
  static generatePaymentEmailBody(pedido, paymentData) {
    const template = `
      <h2>Pagamento Gerado - Pedido ${pedido.numero}</h2>
      <p>Prezado cliente,</p>
      <p>O pagamento do seu pedido foi gerado com os seguintes detalhes:</p>
      <ul>
        <li>Método: ${paymentData.tipo}</li>
        <li>Valor: R$ ${paymentData.valor.toFixed(2)}</li>
        <li>Vencimento: ${paymentData.vencimento.toLocaleDateString()}</li>
      </ul>
      ${paymentData.tipo === 'PIX' ? `
        <p>QR Code PIX:</p>
        <img src="${paymentData.qrCode}" alt="QR Code PIX"/>
        <p>Chave de cópia: ${paymentData.chaveCopia}</p>
      ` : `
        <p>Linha Digitável: ${paymentData.linhaDigitavel}</p>
        <p>Código de Barras: ${paymentData.codigoBarras}</p>
      `}
      <p>Em caso de dúvidas, entre em contato conosco.</p>
    `;

    return template;
  }

  // Determina workflow baseado no valor
  static determineWorkflow(valor) {
    if (valor <= 10000) {
      return { niveisNecessarios: 1, limiteValor: 10000 };
    } else if (valor <= 50000) {
      return { niveisNecessarios: 2, limiteValor: 50000 };
    } else if (valor <= 100000) {
      return { niveisNecessarios: 3, limiteValor: 100000 };
    } else {
      return { niveisNecessarios: 4, limiteValor: null };
    }
  }
} 