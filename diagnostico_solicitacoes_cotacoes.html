<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico: Solicitações vs Cotações</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .alert {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            color: white;
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            color: white;
        }

        .section {
            background: var(--light-bg);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid var(--secondary-color);
        }

        .section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, var(--primary-color) 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: var(--light-bg);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.danger {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid var(--secondary-color);
        }

        .stat-card.warning {
            border-left-color: var(--warning-color);
        }

        .stat-card.danger {
            border-left-color: var(--danger-color);
        }

        .stat-card.success {
            border-left-color: var(--success-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transform: translateX(400px);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            transition: width 0.3s ease;
        }

        .details-section {
            display: none;
            margin-top: 20px;
        }

        .details-section.active {
            display: block;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-search"></i> Diagnóstico: Solicitações vs Cotações</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Alerta Principal -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>INCONSISTÊNCIA DETECTADA:</strong> O número de cotações não corresponde ao esperado baseado nas solicitações.
                    Esta ferramenta irá investigar as possíveis causas.
                </div>
            </div>

            <!-- Estatísticas Gerais -->
            <div class="section">
                <h3><i class="fas fa-chart-bar"></i> Estatísticas Gerais</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalSolicitacoes">-</div>
                        <div class="stat-label">Total de Solicitações</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalCotacoes">-</div>
                        <div class="stat-label">Total de Cotações</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number" id="solicitacoesSemCotacao">-</div>
                        <div class="stat-label">Solicitações sem Cotação</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number" id="cotacoesOrfas">-</div>
                        <div class="stat-label">Cotações Órfãs</div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="executarDiagnostico()">
                        <i class="fas fa-play"></i> Executar Diagnóstico Completo
                    </button>
                </div>
            </div>

            <!-- Progresso do Diagnóstico -->
            <div id="progressoSection" style="display: none;">
                <div class="section">
                    <h3><i class="fas fa-cog fa-spin"></i> Executando Diagnóstico...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                    </div>
                    <div id="progressText">Iniciando análise...</div>
                </div>
            </div>

            <!-- Resultados do Diagnóstico -->
            <div id="resultadosSection" style="display: none;">
                <!-- Solicitações sem Cotação -->
                <div class="section">
                    <h3><i class="fas fa-exclamation-triangle"></i> Solicitações sem Cotação</h3>
                    <div id="solicitacoesSemCotacaoContainer">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Número</th>
                                        <th>Data Criação</th>
                                        <th>Status</th>
                                        <th>Solicitante</th>
                                        <th>Valor Estimado</th>
                                        <th>Motivo</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="solicitacoesSemCotacaoTable">
                                    <!-- Dados carregados dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Cotações Órfãs -->
                <div class="section">
                    <h3><i class="fas fa-unlink"></i> Cotações Órfãs (sem Solicitação)</h3>
                    <div id="cotacoesOrfasContainer">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Número</th>
                                        <th>Data Criação</th>
                                        <th>Status</th>
                                        <th>Criado Por</th>
                                        <th>Solicitação ID</th>
                                        <th>Tipo</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="cotacoesOrfasTable">
                                    <!-- Dados carregados dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Análise de Relacionamentos -->
                <div class="section">
                    <h3><i class="fas fa-link"></i> Análise de Relacionamentos</h3>
                    <div id="relacionamentosContainer">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Solicitação</th>
                                        <th>Status Solicitação</th>
                                        <th>Cotações Relacionadas</th>
                                        <th>Status das Cotações</th>
                                        <th>Observações</th>
                                    </tr>
                                </thead>
                                <tbody id="relacionamentosTable">
                                    <!-- Dados carregados dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Ações Corretivas -->
                <div class="section">
                    <h3><i class="fas fa-tools"></i> Ações Corretivas Sugeridas</h3>
                    <div id="acoesCorretivas">
                        <!-- Ações serão geradas dinamicamente -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <!-- Firebase -->
    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            updateDoc,
            query,
            orderBy,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let solicitacoes = [];
        let cotacoes = [];
        let diagnosticoCompleto = {};

        // Executar diagnóstico
        window.executarDiagnostico = async function() {
            try {
                document.getElementById('progressoSection').style.display = 'block';
                document.getElementById('resultadosSection').style.display = 'none';

                await carregarDados();
                await analisarRelacionamentos();
                await gerarRelatorios();

                document.getElementById('progressoSection').style.display = 'none';
                document.getElementById('resultadosSection').style.display = 'block';

                showNotification('Diagnóstico concluído!', 'success');

            } catch (error) {
                console.error('Erro no diagnóstico:', error);
                showNotification('Erro no diagnóstico: ' + error.message, 'error');
            }
        };

        async function carregarDados() {
            updateProgress(10, 'Carregando solicitações...');

            const [solicitacoesSnap, cotacoesSnap] = await Promise.all([
                getDocs(query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))),
                getDocs(query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc")))
            ]);

            solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            updateProgress(30, 'Dados carregados. Analisando...');

            // Atualizar estatísticas
            document.getElementById('totalSolicitacoes').textContent = solicitacoes.length;
            document.getElementById('totalCotacoes').textContent = cotacoes.length;
        }

        async function analisarRelacionamentos() {
            updateProgress(50, 'Analisando relacionamentos...');

            // Encontrar solicitações sem cotação
            const solicitacoesSemCotacao = solicitacoes.filter(sol => {
                if (sol.status !== 'APROVADA' && sol.status !== 'EM_COTACAO' && sol.status !== 'COTADO') {
                    return false; // Só solicitações que deveriam ter cotação
                }

                return !cotacoes.some(cot => cot.solicitacaoId === sol.id);
            });

            // Encontrar cotações órfãs
            const cotacoesOrfas = cotacoes.filter(cot => {
                if (!cot.solicitacaoId) return true; // Sem referência
                return !solicitacoes.some(sol => sol.id === cot.solicitacaoId);
            });

            diagnosticoCompleto = {
                solicitacoesSemCotacao,
                cotacoesOrfas,
                totalSolicitacoes: solicitacoes.length,
                totalCotacoes: cotacoes.length
            };

            // Atualizar estatísticas
            document.getElementById('solicitacoesSemCotacao').textContent = solicitacoesSemCotacao.length;
            document.getElementById('cotacoesOrfas').textContent = cotacoesOrfas.length;

            updateProgress(80, 'Gerando relatórios...');
        }

        async function gerarRelatorios() {
            // Gerar tabela de solicitações sem cotação
            const solicitacoesTable = document.getElementById('solicitacoesSemCotacaoTable');
            solicitacoesTable.innerHTML = '';

            diagnosticoCompleto.solicitacoesSemCotacao.forEach(sol => {
                const row = document.createElement('tr');
                const motivo = determinarMotivo(sol);

                row.innerHTML = `
                    <td><strong>${sol.numero || 'S/N'}</strong></td>
                    <td>${formatDate(sol.dataCriacao)}</td>
                    <td><span class="status-badge ${getStatusClass(sol.status)}">${sol.status}</span></td>
                    <td>${sol.solicitante || 'N/A'}</td>
                    <td>R$ ${formatCurrency(sol.valorEstimado || 0)}</td>
                    <td>${motivo}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="criarCotacao('${sol.id}')">
                            <i class="fas fa-plus"></i> Criar Cotação
                        </button>
                    </td>
                `;
                solicitacoesTable.appendChild(row);
            });

            // Gerar tabela de cotações órfãs
            const cotacoesTable = document.getElementById('cotacoesOrfasTable');
            cotacoesTable.innerHTML = '';

            diagnosticoCompleto.cotacoesOrfas.forEach(cot => {
                const row = document.createElement('tr');
                const tipo = determinarTipoCotacao(cot);

                row.innerHTML = `
                    <td><strong>${cot.numero || 'S/N'}</strong></td>
                    <td>${formatDate(cot.dataCriacao)}</td>
                    <td><span class="status-badge ${getStatusClass(cot.status)}">${cot.status}</span></td>
                    <td>${cot.criadoPor || 'N/A'}</td>
                    <td>${cot.solicitacaoId || 'AUSENTE'}</td>
                    <td>${tipo}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="vincularSolicitacao('${cot.id}')">
                            <i class="fas fa-link"></i> Vincular
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="excluirCotacao('${cot.id}')">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </td>
                `;
                cotacoesTable.appendChild(row);
            });

            // Gerar ações corretivas
            gerarAcoesCorretivas();

            updateProgress(100, 'Diagnóstico concluído!');
        }

        function determinarMotivo(solicitacao) {
            if (solicitacao.status === 'PENDENTE') return 'Solicitação ainda pendente';
            if (solicitacao.status === 'REJEITADA') return 'Solicitação rejeitada';
            if (solicitacao.status === 'APROVADA') return 'Cotação não foi criada';
            if (solicitacao.status === 'EM_COTACAO') return 'Em processo de cotação';
            return 'Status inconsistente';
        }

        function determinarTipoCotacao(cotacao) {
            if (!cotacao.solicitacaoId) return 'Cotação manual';
            if (cotacao.aglutinada) return 'Cotação aglutinada';
            if (cotacao.numero && cotacao.numero.startsWith('CTA-')) return 'Cotação aglutinada';
            return 'Referência inválida';
        }

        function gerarAcoesCorretivas() {
            const container = document.getElementById('acoesCorretivas');
            let html = '';

            if (diagnosticoCompleto.solicitacoesSemCotacao.length > 0) {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <strong>${diagnosticoCompleto.solicitacoesSemCotacao.length} solicitações aprovadas sem cotação</strong>
                            <br>Recomendação: Criar cotações para estas solicitações ou revisar o status.
                            <br><button class="btn btn-warning" onclick="criarCotacoesEmLote()" style="margin-top: 10px;">
                                <i class="fas fa-magic"></i> Criar Cotações em Lote
                            </button>
                        </div>
                    </div>
                `;
            }

            if (diagnosticoCompleto.cotacoesOrfas.length > 0) {
                html += `
                    <div class="alert alert-danger">
                        <i class="fas fa-unlink"></i>
                        <div>
                            <strong>${diagnosticoCompleto.cotacoesOrfas.length} cotações órfãs encontradas</strong>
                            <br>Recomendação: Vincular a solicitações existentes ou excluir se desnecessárias.
                            <br><button class="btn btn-danger" onclick="limparCotacoesOrfas()" style="margin-top: 10px;">
                                <i class="fas fa-broom"></i> Limpar Cotações Órfãs
                            </button>
                        </div>
                    </div>
                `;
            }

            if (diagnosticoCompleto.solicitacoesSemCotacao.length === 0 && diagnosticoCompleto.cotacoesOrfas.length === 0) {
                html += `
                    <div class="alert alert-info">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <strong>Nenhuma inconsistência encontrada!</strong>
                            <br>Todos os relacionamentos entre solicitações e cotações estão corretos.
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Funções de ação
        window.criarCotacao = async function(solicitacaoId) {
            if (confirm('Criar cotação para esta solicitação?')) {
                try {
                    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
                    // Redirecionar para tela de criação de cotação com parâmetros
                    window.location.href = `cotacoes_melhorada.html?solicitacao=${solicitacaoId}`;
                } catch (error) {
                    showNotification('Erro ao criar cotação: ' + error.message, 'error');
                }
            }
        };

        window.vincularSolicitacao = async function(cotacaoId) {
            const solicitacaoId = prompt('Digite o ID da solicitação para vincular:');
            if (solicitacaoId) {
                try {
                    await updateDoc(doc(db, "cotacoes", cotacaoId), {
                        solicitacaoId: solicitacaoId,
                        ultimaAtualizacao: Timestamp.now()
                    });
                    showNotification('Cotação vinculada com sucesso!', 'success');
                    executarDiagnostico(); // Reexecutar diagnóstico
                } catch (error) {
                    showNotification('Erro ao vincular: ' + error.message, 'error');
                }
            }
        };

        window.excluirCotacao = async function(cotacaoId) {
            if (confirm('ATENÇÃO: Excluir esta cotação órfã? Esta ação não pode ser desfeita!')) {
                try {
                    await deleteDoc(doc(db, "cotacoes", cotacaoId));
                    showNotification('Cotação excluída com sucesso!', 'success');
                    executarDiagnostico(); // Reexecutar diagnóstico
                } catch (error) {
                    showNotification('Erro ao excluir: ' + error.message, 'error');
                }
            }
        };

        window.criarCotacoesEmLote = function() {
            if (confirm(`Criar cotações para ${diagnosticoCompleto.solicitacoesSemCotacao.length} solicitações?`)) {
                showNotification('Funcionalidade em desenvolvimento', 'info');
            }
        };

        window.limparCotacoesOrfas = function() {
            if (confirm(`ATENÇÃO: Excluir ${diagnosticoCompleto.cotacoesOrfas.length} cotações órfãs? Esta ação não pode ser desfeita!`)) {
                showNotification('Funcionalidade em desenvolvimento', 'info');
            }
        };

        // Funções auxiliares
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString('pt-BR');
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        function getStatusClass(status) {
            switch (status) {
                case 'APROVADA': case 'ABERTA': return 'success';
                case 'PENDENTE': case 'EM_COTACAO': return 'warning';
                case 'REJEITADA': case 'CANCELADO': return 'danger';
                default: return 'info';
            }
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>
