<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico de Reservas Automáticas</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .diagnostic-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .diagnostic-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-box {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .status-ok {
            border-left-color: #27ae60;
            background: #d4edda;
        }

        .status-warning {
            border-left-color: #f39c12;
            background: #fff3cd;
        }

        .status-error {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .config-status.active {
            background: #d4edda;
            color: #155724;
        }

        .config-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .op-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }

        .material-item {
            background: white;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 3px solid #28a745;
        }

        .material-item.no-reserve {
            border-left-color: #dc3545;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-search"></i>
                Diagnóstico de Reservas Automáticas
            </h1>
            <div class="header-actions">
                <button class="btn btn-warning" onclick="window.location.href='ordens_producao.html'">
                    <i class="fas fa-arrow-left"></i>
                    Voltar para OPs
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Alerta informativo -->
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Diagnóstico de Reservas:</strong>
                Este diagnóstico verifica se as reservas automáticas estão funcionando corretamente e identifica possíveis problemas.
            </div>

            <!-- Controles -->
            <div class="diagnostic-section">
                <div class="diagnostic-title">
                    <i class="fas fa-play"></i>
                    Controles de Diagnóstico
                </div>
                
                <button class="btn btn-primary" onclick="executarDiagnosticoCompleto()">
                    <i class="fas fa-search"></i>
                    🔍 Diagnóstico Completo
                </button>
                
                <button class="btn btn-success" onclick="verificarConfiguracoes()">
                    <i class="fas fa-cog"></i>
                    ⚙️ Verificar Configurações
                </button>
                
                <button class="btn btn-warning" onclick="analisarOPsRecentes()">
                    <i class="fas fa-clock"></i>
                    🕐 Analisar OPs Recentes
                </button>
                
                <button class="btn btn-danger" onclick="corrigirReservas()">
                    <i class="fas fa-wrench"></i>
                    🔧 Corrigir Reservas
                </button>

                <button class="btn btn-warning" onclick="forcarReconexao()">
                    <i class="fas fa-sync"></i>
                    🔄 Forçar Reconexão
                </button>
            </div>

            <!-- Resultados -->
            <div id="resultados">
                <!-- Resultados serão inseridos aqui -->
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>

    <script>
        // Carregar configuração do arquivo centralizado
        let firebaseConfig = {};
        let db = null;

        // Função para detectar automaticamente a configuração do firebase-config.js
        async function initializeFirebaseFromConfig() {
            try {
                // Tentar carregar configuração do firebase-config.js via fetch
                let configDetectada = null;

                try {
                    // Tentar ler o arquivo firebase-config.js
                    const response = await fetch('./firebase-config.js');
                    const configText = await response.text();

                    // Extrair configuração usando regex
                    const configMatch = configText.match(/const firebaseConfig = \{([^}]+)\}/s);
                    if (configMatch) {
                        // Extrair valores individuais
                        const apiKeyMatch = configText.match(/apiKey:\s*["']([^"']+)["']/);
                        const authDomainMatch = configText.match(/authDomain:\s*["']([^"']+)["']/);
                        const projectIdMatch = configText.match(/projectId:\s*["']([^"']+)["']/);
                        const storageBucketMatch = configText.match(/storageBucket:\s*["']([^"']+)["']/);
                        const messagingSenderIdMatch = configText.match(/messagingSenderId:\s*["']([^"']+)["']/);
                        const appIdMatch = configText.match(/appId:\s*["']([^"']+)["']/);
                        const measurementIdMatch = configText.match(/measurementId:\s*["']([^"']+)["']/);

                        if (projectIdMatch) {
                            configDetectada = {
                                apiKey: apiKeyMatch ? apiKeyMatch[1] : "",
                                authDomain: authDomainMatch ? authDomainMatch[1] : "",
                                projectId: projectIdMatch[1],
                                storageBucket: storageBucketMatch ? storageBucketMatch[1] : "",
                                messagingSenderId: messagingSenderIdMatch ? messagingSenderIdMatch[1] : "",
                                appId: appIdMatch ? appIdMatch[1] : "",
                                measurementId: measurementIdMatch ? measurementIdMatch[1] : ""
                            };

                            console.log('🔍 Configuração detectada do firebase-config.js:', configDetectada.projectId);
                        }
                    }
                } catch (fetchError) {
                    console.warn('⚠️ Não foi possível ler firebase-config.js:', fetchError.message);
                }

                // Se não conseguiu detectar, usar configurações conhecidas
                if (!configDetectada) {
                    // Tentar configurações conhecidas
                    const configsConhecidas = [
                        // Banco original
                        {
                            apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
                            authDomain: "banco-mrp.firebaseapp.com",
                            projectId: "banco-mrp",
                            storageBucket: "banco-mrp.firebasestorage.app",
                            messagingSenderId: "740147152218",
                            appId: "1:740147152218:web:2d301340bf314e68d75f63",
                            measurementId: "G-YNNQ1VX1EH"
                        },
                        // Banco de teste
                        {
                            apiKey: "AIzaSyCG-VW7vhGZf2EvXzVkssajO10x0krfHCM",
                            authDomain: "naliteck-mrp.firebaseapp.com",
                            projectId: "naliteck-mrp",
                            storageBucket: "naliteck-mrp.firebasestorage.app",
                            messagingSenderId: "755022520906",
                            appId: "1:755022520906:web:efc8b69186289325c6fcb3",
                            measurementId: "G-C7W8FG17KG"
                        }
                    ];

                    // Testar cada configuração
                    for (const config of configsConhecidas) {
                        try {
                            if (firebase.apps.length > 0) {
                                await firebase.app().delete();
                            }

                            firebase.initializeApp(config);
                            const testDb = firebase.firestore();

                            // Testar conexão
                            await testDb.collection('produtos').limit(1).get();

                            configDetectada = config;
                            db = testDb;
                            console.log('✅ Conectado automaticamente ao:', config.projectId);
                            break;

                        } catch (testError) {
                            console.warn(`❌ Falha ao conectar em ${config.projectId}:`, testError.message);
                            continue;
                        }
                    }
                }

                if (!configDetectada) {
                    throw new Error('Nenhuma configuração Firebase funcionou');
                }

                // Se detectou mas ainda não inicializou
                if (!db) {
                    if (!firebase.apps.length) {
                        firebase.initializeApp(configDetectada);
                    }
                    db = firebase.firestore();

                    // Testar conexão final
                    await db.collection('produtos').limit(1).get();
                }

                firebaseConfig = configDetectada;
                console.log('🔥 Firebase inicializado com sucesso:', firebaseConfig.projectId);
                return true;

            } catch (error) {
                console.error('❌ Erro ao inicializar Firebase:', error);
                throw error;
            }
        }

        // Variáveis globais
        let ordensProducao = [];
        let produtos = [];
        let estruturas = [];
        let estoques = [];
        let configuracoes = {};

        // Carregar dados iniciais
        async function carregarDados() {
            try {
                console.log('🔄 Carregando dados para diagnóstico...');

                // Garantir que o Firebase está inicializado
                if (!db) {
                    await initializeFirebaseFromConfig();
                }

                // Carregar produtos
                const produtosSnap = await db.collection("produtos").get();
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar estruturas
                const estruturasSnap = await db.collection("estruturas").get();
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar estoques
                const estoquesSnap = await db.collection("estoques").get();
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar OPs recentes
                const opsSnap = await db.collection("ordensProducao")
                    .orderBy("dataCriacao", "desc")
                    .limit(50)
                    .get();
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar configurações - CORRIGIDO para fonte única
                try {
                    const configDoc = await db.collection("parametros").doc("sistema").get();
                    configuracoes = configDoc.exists ? configDoc.data() : {};
                    console.log('✅ Configurações carregadas da fonte única:', configuracoes);
                } catch (error) {
                    console.warn('Configurações de sistema não encontradas');
                    configuracoes = {};
                }

                console.log(`✅ Dados carregados: ${produtos.length} produtos, ${estruturas.length} estruturas, ${estoques.length} estoques, ${ordensProducao.length} OPs`);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                throw error;
            }
        }

        // Função principal de diagnóstico
        async function executarDiagnosticoCompleto() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<div class="loading"><div class="spinner"></div>Executando diagnóstico completo...</div>';

            try {
                await carregarDados();

                let html = '';

                // 1. Verificar configurações
                html += await gerarRelatorioConfiguracoes();

                // 2. Analisar OPs recentes
                html += await gerarRelatorioOPsRecentes();

                // 3. Verificar consistência de reservas
                html += await gerarRelatorioConsistenciaReservas();

                // 4. Identificar problemas
                html += await gerarRelatorioProblemas();

                resultados.innerHTML = html;

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                resultados.innerHTML = `
                    <div class="result-box status-error">
                        <h3>❌ Erro no Diagnóstico</h3>
                        <p>Erro: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function verificarConfiguracoes() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<div class="loading"><div class="spinner"></div>Verificando configurações...</div>';

            try {
                await carregarDados();
                const html = await gerarRelatorioConfiguracoes();
                resultados.innerHTML = html;
            } catch (error) {
                console.error('❌ Erro ao verificar configurações:', error);
                resultados.innerHTML = `
                    <div class="result-box status-error">
                        <h3>❌ Erro ao Verificar Configurações</h3>
                        <p>Erro: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function analisarOPsRecentes() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<div class="loading"><div class="spinner"></div>Analisando OPs recentes...</div>';

            try {
                await carregarDados();
                const html = await gerarRelatorioOPsRecentes();
                resultados.innerHTML = html;
            } catch (error) {
                console.error('❌ Erro ao analisar OPs:', error);
                resultados.innerHTML = `
                    <div class="result-box status-error">
                        <h3>❌ Erro ao Analisar OPs</h3>
                        <p>Erro: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function corrigirReservas() {
            if (!confirm('⚠️ ATENÇÃO!\n\nEsta função irá corrigir reservas inconsistentes.\nDeseja continuar?')) {
                return;
            }

            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<div class="loading"><div class="spinner"></div>Corrigindo reservas...</div>';

            try {
                await carregarDados();
                const html = await executarCorrecaoReservas();
                resultados.innerHTML = html;
            } catch (error) {
                console.error('❌ Erro ao corrigir reservas:', error);
                resultados.innerHTML = `
                    <div class="result-box status-error">
                        <h3>❌ Erro ao Corrigir Reservas</h3>
                        <p>Erro: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function gerarRelatorioConfiguracoes() {
            const configPadrao = {
                aglutinarOps: false,
                usarSaldoEstoque: true,
                opsFirmes: false,
                reservarEstoque: true
            };

            const configAtual = {
                aglutinarOps: configuracoes.aglutinarOps ?? configPadrao.aglutinarOps,
                usarSaldoEstoque: configuracoes.usarSaldoEstoque ?? configPadrao.usarSaldoEstoque,
                opsFirmes: configuracoes.opsFirmes ?? configPadrao.opsFirmes,
                reservarEstoque: configuracoes.reservarEstoque ?? configPadrao.reservarEstoque
            };

            const statusClass = configAtual.reservarEstoque ? 'status-ok' : 'status-error';

            return `
                <div class="diagnostic-section">
                    <div class="diagnostic-title">
                        <i class="fas fa-cog"></i>
                        Configurações de Produção
                    </div>

                    <div class="result-box ${statusClass}">
                        <h3>${configAtual.reservarEstoque ? '✅' : '❌'} Configuração de Reserva Automática</h3>

                        <div class="config-item">
                            <span><strong>Aglutinação de OPs:</strong></span>
                            <span class="config-status ${configAtual.aglutinarOps ? 'active' : 'inactive'}">
                                ${configAtual.aglutinarOps ? 'ATIVO' : 'INATIVO'}
                            </span>
                        </div>

                        <div class="config-item">
                            <span><strong>Uso de Saldo de Estoque:</strong></span>
                            <span class="config-status ${configAtual.usarSaldoEstoque ? 'active' : 'inactive'}">
                                ${configAtual.usarSaldoEstoque ? 'ATIVO' : 'INATIVO'}
                            </span>
                        </div>

                        <div class="config-item">
                            <span><strong>OPs Firmes por Padrão:</strong></span>
                            <span class="config-status ${configAtual.opsFirmes ? 'active' : 'inactive'}">
                                ${configAtual.opsFirmes ? 'ATIVO' : 'INATIVO'}
                            </span>
                        </div>

                        <div class="config-item">
                            <span><strong>Reserva Automática de Estoque:</strong></span>
                            <span class="config-status ${configAtual.reservarEstoque ? 'active' : 'inactive'}">
                                ${configAtual.reservarEstoque ? 'ATIVO' : 'INATIVO'}
                            </span>
                        </div>

                        ${!configAtual.reservarEstoque ? `
                            <div class="alert alert-danger" style="margin-top: 15px;">
                                <strong>⚠️ PROBLEMA IDENTIFICADO:</strong><br>
                                A configuração "Reserva Automática de Estoque" está INATIVA.<br>
                                Isso explica por que não estão sendo gerados empenhos/reservas.<br><br>
                                <strong>Solução:</strong> Ative esta configuração em config_parametros.html
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // Função para forçar reconexão
        async function forcarReconexao() {
            const resultados = document.getElementById('resultados');
            resultados.innerHTML = '<div class="loading"><div class="spinner"></div>Forçando reconexão...</div>';

            try {
                // Limpar conexão atual
                if (firebase.apps.length > 0) {
                    await firebase.app().delete();
                }

                // Resetar variáveis
                db = null;
                firebaseConfig = {};

                // Forçar nova detecção
                await initializeFirebaseFromConfig();

                const projectInfo = `
                    <div class="alert alert-success">
                        <i class="fas fa-database"></i>
                        <strong>Reconectado ao Firebase:</strong> ${firebaseConfig.projectId}
                        <br><small>Reconexão forçada: ${new Date().toLocaleString()}</small>
                    </div>
                `;
                resultados.innerHTML = projectInfo;

            } catch (error) {
                console.error('❌ Erro na reconexão:', error);
                resultados.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ Erro na Reconexão:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Tornar funções globais
        window.executarDiagnosticoCompleto = executarDiagnosticoCompleto;
        window.verificarConfiguracoes = verificarConfiguracoes;
        window.analisarOPsRecentes = analisarOPsRecentes;
        window.corrigirReservas = corrigirReservas;
        window.forcarReconexao = forcarReconexao;

        async function gerarRelatorioOPsRecentes() {
            const opsComMateriais = ordensProducao.filter(op => op.materiaisNecessarios && op.materiaisNecessarios.length > 0);
            const opsComReservas = opsComMateriais.filter(op =>
                op.materiaisNecessarios.some(mat => mat.quantidadeReservada > 0)
            );
            const opsSemReservas = opsComMateriais.filter(op =>
                !op.materiaisNecessarios.some(mat => mat.quantidadeReservada > 0)
            );

            let html = `
                <div class="diagnostic-section">
                    <div class="diagnostic-title">
                        <i class="fas fa-clock"></i>
                        Análise de OPs Recentes (últimas 50)
                    </div>

                    <div class="result-box ${opsSemReservas.length > 0 ? 'status-warning' : 'status-ok'}">
                        <h3>📊 Estatísticas de Reservas</h3>
                        <p><strong>Total de OPs analisadas:</strong> ${ordensProducao.length}</p>
                        <p><strong>OPs com materiais necessários:</strong> ${opsComMateriais.length}</p>
                        <p><strong>OPs com reservas:</strong> ${opsComReservas.length}</p>
                        <p><strong>OPs sem reservas:</strong> ${opsSemReservas.length}</p>
                    </div>
            `;

            if (opsSemReservas.length > 0) {
                html += `
                    <div class="result-box status-warning">
                        <h3>⚠️ OPs sem Reservas Detectadas</h3>
                        <p>As seguintes OPs possuem materiais necessários mas não têm reservas:</p>
                `;

                opsSemReservas.slice(0, 10).forEach(op => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    const dataOp = op.dataCriacao ? new Date(op.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A';

                    html += `
                        <div class="op-details">
                            <strong>OP ${op.numero || op.id}</strong> - ${produto?.codigo || 'N/A'}<br>
                            <small>Status: ${op.status} | Data: ${dataOp} | Materiais: ${op.materiaisNecessarios.length}</small>
                        </div>
                    `;
                });

                if (opsSemReservas.length > 10) {
                    html += `<p><em>... e mais ${opsSemReservas.length - 10} OPs</em></p>`;
                }

                html += `</div>`;
            }

            html += `</div>`;
            return html;
        }

        async function gerarRelatorioConsistenciaReservas() {
            const estoquesComReserva = estoques.filter(e => e.saldoReservado > 0);
            let inconsistencias = 0;
            let reservasOrfas = 0;

            let html = `
                <div class="diagnostic-section">
                    <div class="diagnostic-title">
                        <i class="fas fa-balance-scale"></i>
                        Consistência de Reservas
                    </div>

                    <div class="result-box status-ok">
                        <h3>📋 Análise de Estoques com Reserva</h3>
                        <p><strong>Total de estoques:</strong> ${estoques.length}</p>
                        <p><strong>Estoques com reserva:</strong> ${estoquesComReserva.length}</p>
                    </div>
            `;

            if (estoquesComReserva.length > 0) {
                html += `<div class="result-box status-info"><h3>🔍 Detalhes das Reservas</h3>`;

                for (const estoque of estoquesComReserva.slice(0, 10)) {
                    const produto = produtos.find(p => p.id === estoque.produtoId);

                    // Verificar se existe OP que justifique a reserva
                    const opsComEsteItem = ordensProducao.filter(op =>
                        op.materiaisNecessarios?.some(mat =>
                            mat.produtoId === estoque.produtoId && mat.quantidadeReservada > 0
                        )
                    );

                    if (opsComEsteItem.length === 0) {
                        reservasOrfas++;
                    }

                    html += `
                        <div class="material-item ${opsComEsteItem.length === 0 ? 'no-reserve' : ''}">
                            <strong>${produto?.codigo || 'N/A'}</strong><br>
                            <small>Reservado: ${estoque.saldoReservado} | Saldo: ${estoque.saldo} |
                            OPs relacionadas: ${opsComEsteItem.length}
                            ${opsComEsteItem.length === 0 ? ' ⚠️ RESERVA ÓRFÃ' : ''}</small>
                        </div>
                    `;
                }

                html += `</div>`;

                if (reservasOrfas > 0) {
                    html += `
                        <div class="result-box status-warning">
                            <h3>⚠️ Reservas Órfãs Detectadas</h3>
                            <p>Encontradas <strong>${reservasOrfas}</strong> reservas sem OP correspondente.</p>
                            <p>Essas reservas podem ser liberadas para disponibilizar estoque.</p>
                        </div>
                    `;
                }
            }

            html += `</div>`;
            return html;
        }

        async function gerarRelatorioProblemas() {
            const problemas = [];

            // Verificar configuração
            if (!configuracoes.reservarEstoque) {
                problemas.push({
                    tipo: 'CRÍTICO',
                    titulo: 'Reserva Automática Desabilitada',
                    descricao: 'A configuração "reservarEstoque" está desabilitada',
                    solucao: 'Ativar em config_parametros.html'
                });
            }

            // Verificar OPs sem reservas
            const opsComMateriais = ordensProducao.filter(op => op.materiaisNecessarios && op.materiaisNecessarios.length > 0);
            const opsSemReservas = opsComMateriais.filter(op =>
                !op.materiaisNecessarios.some(mat => mat.quantidadeReservada > 0)
            );

            if (opsSemReservas.length > 0) {
                problemas.push({
                    tipo: 'ALERTA',
                    titulo: `${opsSemReservas.length} OPs sem Reservas`,
                    descricao: 'OPs com materiais necessários mas sem reservas automáticas',
                    solucao: 'Verificar se foram criadas antes da ativação da configuração'
                });
            }

            // Verificar reservas órfãs
            const estoquesComReserva = estoques.filter(e => e.saldoReservado > 0);
            let reservasOrfas = 0;
            for (const estoque of estoquesComReserva) {
                const temOPRelacionada = ordensProducao.some(op =>
                    op.materiaisNecessarios?.some(mat =>
                        mat.produtoId === estoque.produtoId && mat.quantidadeReservada > 0
                    )
                );
                if (!temOPRelacionada) reservasOrfas++;
            }

            if (reservasOrfas > 0) {
                problemas.push({
                    tipo: 'ALERTA',
                    titulo: `${reservasOrfas} Reservas Órfãs`,
                    descricao: 'Reservas de estoque sem OP correspondente',
                    solucao: 'Usar função de limpeza de reservas órfãs'
                });
            }

            let html = `
                <div class="diagnostic-section">
                    <div class="diagnostic-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Problemas Identificados
                    </div>
            `;

            if (problemas.length === 0) {
                html += `
                    <div class="result-box status-ok">
                        <h3>✅ Nenhum Problema Detectado</h3>
                        <p>O sistema de reservas automáticas está funcionando corretamente!</p>
                    </div>
                `;
            } else {
                problemas.forEach(problema => {
                    const statusClass = problema.tipo === 'CRÍTICO' ? 'status-error' : 'status-warning';
                    const icon = problema.tipo === 'CRÍTICO' ? '🚨' : '⚠️';

                    html += `
                        <div class="result-box ${statusClass}">
                            <h3>${icon} ${problema.titulo}</h3>
                            <p><strong>Descrição:</strong> ${problema.descricao}</p>
                            <p><strong>Solução:</strong> ${problema.solucao}</p>
                        </div>
                    `;
                });
            }

            html += `</div>`;
            return html;
        }

        async function executarCorrecaoReservas() {
            let correcoes = 0;
            let html = `
                <div class="diagnostic-section">
                    <div class="diagnostic-title">
                        <i class="fas fa-wrench"></i>
                        Correção de Reservas
                    </div>
            `;

            try {
                // Liberar reservas órfãs
                const estoquesComReserva = estoques.filter(e => e.saldoReservado > 0);

                for (const estoque of estoquesComReserva) {
                    const temOPRelacionada = ordensProducao.some(op =>
                        op.materiaisNecessarios?.some(mat =>
                            mat.produtoId === estoque.produtoId && mat.quantidadeReservada > 0
                        )
                    );

                    if (!temOPRelacionada) {
                        await db.collection("estoques").doc(estoque.id).update({
                            saldoReservado: 0,
                            ultimaMovimentacao: firebase.firestore.Timestamp.now(),
                            observacao: `Reserva órfã liberada: ${estoque.saldoReservado} unidades em ${new Date().toLocaleString()}`
                        });
                        correcoes++;
                    }
                }

                html += `
                    <div class="result-box status-success">
                        <h3>✅ Correção Concluída</h3>
                        <p><strong>Reservas órfãs liberadas:</strong> ${correcoes}</p>
                        <p>As reservas sem OP correspondente foram liberadas automaticamente.</p>
                    </div>
                `;

            } catch (error) {
                html += `
                    <div class="result-box status-error">
                        <h3>❌ Erro na Correção</h3>
                        <p>Erro: ${error.message}</p>
                    </div>
                `;
            }

            html += `</div>`;
            return html;
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔍 Diagnóstico de Reservas carregado!');

            // Inicializar Firebase automaticamente
            try {
                await initializeFirebaseFromConfig();
                console.log('✅ Firebase inicializado com sucesso!');

                // Mostrar informações do projeto conectado
                const projectInfo = `
                    <div class="alert alert-success">
                        <i class="fas fa-database"></i>
                        <strong>Conectado ao Firebase:</strong> ${firebaseConfig.projectId}
                        <br><small>Última detecção: ${new Date().toLocaleString()}</small>
                    </div>
                `;
                document.getElementById('resultados').innerHTML = projectInfo;

            } catch (error) {
                console.error('❌ Erro ao inicializar Firebase:', error);
                document.getElementById('resultados').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Erro de Conexão:</strong> Não foi possível conectar ao Firebase.<br>
                        Erro: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
