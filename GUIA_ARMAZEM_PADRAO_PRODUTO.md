# 🏭 GUIA - ARMAZÉM PADRÃO POR PRODUTO

## 🎯 **MUDANÇA IMPORTANTE NO SISTEMA**

### **📋 CONCEITO ATUALIZADO:**
> **O armazém padrão agora é definido individualmente no cadastro de cada produto, não mais nos parâmetros gerais do sistema. Isso oferece maior flexibilidade e controle granular.**

---

## 🔄 **MUDANÇAS IMPLEMENTADAS**

### **❌ REMOVIDO DOS PARÂMETROS GERAIS:**
```
┌─────────────────────────────────────────┐
│ ❌ CONFIG_PARAMETROS.HTML               │
├─────────────────────────────────────────┤
│ • Campo "Armazém Padrão" removido      │
│ • Não há mais configuração global     │
│ • Sistema mais flexível               │
└─────────────────────────────────────────┘
```

### **✅ ADICIONADO NO CADASTRO DE PRODUTOS:**
```
┌─────────────────────────────────────────┐
│ ✅ CADASTRO_PRODUTO.HTML                │
├─────────────────────────────────────────┤
│ • Campo "Armazém Padrão" obrigatório   │
│ • Configuração individual por produto │
│ • Maior controle e flexibilidade      │
└─────────────────────────────────────────┘
```

---

## 🏗️ **ESTRUTURA ATUAL DO SISTEMA**

### **📊 CADASTRO DE PRODUTOS:**
```javascript
// Estrutura no Firebase
{
    id: "produto123",
    codigo: "PAR001",
    descricao: "Parafuso M6x20",
    unidade: "UN",
    armazemPadraoId: "armazem001", // ← CAMPO OBRIGATÓRIO
    // ... outros campos
}
```

### **🏭 CADASTRO DE ARMAZÉNS:**
```javascript
// Estrutura no Firebase
{
    id: "armazem001",
    codigo: "EST01",
    nome: "Estoque Principal",
    descricao: "Armazém principal de materiais",
    tipo: "ESTOQUE"
}
```

---

## 🎯 **BENEFÍCIOS DA MUDANÇA**

### **✅ FLEXIBILIDADE:**
```
┌─────────────────────────────────────────┐
│ 🎯 CONTROLE GRANULAR                    │
├─────────────────────────────────────────┤
│ • Cada produto tem seu armazém padrão  │
│ • Materiais críticos → Armazém especial│
│ • Produtos comuns → Estoque principal  │
│ • Químicos → Armazém controlado        │
└─────────────────────────────────────────┘
```

### **✅ ORGANIZAÇÃO:**
```
Exemplo de Configuração:
┌─────────────────┬─────────────────────┐
│ Produto         │ Armazém Padrão      │
├─────────────────┼─────────────────────┤
│ Parafusos       │ EST01 - Principal   │
│ Produtos Químicos│ QUI01 - Controlado │
│ Ferramentas     │ FER01 - Ferramental│
│ Matéria Prima   │ MP01 - Mat. Prima   │
└─────────────────┴─────────────────────┘
```

### **✅ CONTROLE:**
- **Rastreabilidade** melhorada por tipo de material
- **Segregação** automática de produtos especiais
- **Compliance** com normas de armazenagem
- **Otimização** de espaço e logística

---

## 🔧 **COMO CONFIGURAR**

### **1️⃣ CADASTRO DE PRODUTOS:**
```
1. Acesse: cadastro_produto.html
2. Vá para aba "Estoque"
3. Seção "Endereçamento"
4. Campo "Armazém Padrão" é OBRIGATÓRIO
5. Selecione o armazém apropriado
6. Salve o produto
```

### **2️⃣ VALIDAÇÃO AUTOMÁTICA:**
```javascript
// Sistema valida automaticamente
if (!productData.armazemPadraoId) {
    errorMessages.push('O armazém padrão é obrigatório.');
    isValid = false;
}
```

---

## 🔄 **IMPACTO NOS MÓDULOS**

### **📦 RECEBIMENTO DE MATERIAIS:**
```
┌─────────────────────────────────────────┐
│ 📦 RECEBIMENTO_MATERIAIS_MELHORADO.HTML │
├─────────────────────────────────────────┤
│ • Usa armazém padrão do produto        │
│ • Pré-seleciona automaticamente       │
│ • Mostra código do armazém na tabela   │
│ • Entrada direta no armazém correto    │
└─────────────────────────────────────────┘
```

#### **🎯 FUNCIONAMENTO:**
```javascript
// Sistema busca armazém padrão do produto
function getItemDestination(produto) {
    // Se tem controle de qualidade
    if (needsInspection) {
        return { text: 'QUALIDADE', class: 'quality' };
    }
    
    // Usa armazém padrão do produto
    const armazemPadrao = armazens.find(a => a.id === produto?.armazemPadraoId);
    const armazemTexto = armazemPadrao ? armazemPadrao.codigo : 'ESTOQUE';
    
    return { text: armazemTexto, class: 'stock' };
}
```

### **📋 SOLICITAÇÕES DE COMPRA:**
```
┌─────────────────────────────────────────┐
│ 📋 SOLICITACAO_COMPRAS_MELHORADA.HTML   │
├─────────────────────────────────────────┤
│ • Considera armazém padrão do produto  │
│ • Agrupa por armazém de destino        │
│ • Otimiza logística de recebimento     │
└─────────────────────────────────────────┘
```

### **📊 MOVIMENTAÇÕES DE ESTOQUE:**
```
┌─────────────────────────────────────────┐
│ 📊 MOVIMENTACOES_ESTOQUE.HTML           │
├─────────────────────────────────────────┤
│ • Entrada automática no armazém padrão │
│ • Transferências entre armazéns        │
│ • Controle de saldos por armazém       │
└─────────────────────────────────────────┘
```

---

## 📊 **EXEMPLOS PRÁTICOS**

### **🔧 CONFIGURAÇÃO POR CATEGORIA:**

#### **📦 MATERIAIS COMUNS:**
```
Produto: Parafuso M6x20
Armazém Padrão: EST01 - Estoque Principal
Motivo: Material comum, alta rotatividade
```

#### **⚗️ PRODUTOS QUÍMICOS:**
```
Produto: Solvente Industrial
Armazém Padrão: QUI01 - Armazém Controlado
Motivo: Produto perigoso, controle especial
```

#### **🔩 FERRAMENTAS:**
```
Produto: Chave Phillips 1/4"
Armazém Padrão: FER01 - Ferramental
Motivo: Ferramenta, controle de empréstimo
```

#### **🏭 MATÉRIA PRIMA:**
```
Produto: Chapa de Aço 1020
Armazém Padrão: MP01 - Matéria Prima
Motivo: Insumo produtivo, controle de lote
```

---

## ⚠️ **MIGRAÇÃO DE DADOS EXISTENTES**

### **🔄 PRODUTOS SEM ARMAZÉM PADRÃO:**
```javascript
// Script de migração (se necessário)
produtos.forEach(produto => {
    if (!produto.armazemPadraoId) {
        // Definir armazém padrão baseado em regras
        if (produto.tipo === 'QUIMICO') {
            produto.armazemPadraoId = 'armazem_quimico';
        } else if (produto.categoria === 'FERRAMENTA') {
            produto.armazemPadraoId = 'armazem_ferramental';
        } else {
            produto.armazemPadraoId = 'armazem_principal';
        }
    }
});
```

### **📋 VALIDAÇÃO DE INTEGRIDADE:**
```javascript
// Verificar se todos os produtos têm armazém padrão
const produtosSemArmazem = produtos.filter(p => !p.armazemPadraoId);
if (produtosSemArmazem.length > 0) {
    console.warn('Produtos sem armazém padrão:', produtosSemArmazem);
}
```

---

## 🎯 **MELHORES PRÁTICAS**

### **📋 DEFINIÇÃO DE ARMAZÉNS:**
```
1. Crie armazéns específicos por tipo:
   • EST01 - Estoque Principal
   • QUI01 - Produtos Químicos
   • FER01 - Ferramental
   • MP01 - Matéria Prima
   • QUA01 - Quarentena/Qualidade

2. Configure produtos conforme necessidade:
   • Materiais comuns → Estoque Principal
   • Produtos perigosos → Armazém Controlado
   • Ferramentas → Ferramental
   • Insumos → Matéria Prima
```

### **🔧 CONFIGURAÇÃO RECOMENDADA:**
```
┌─────────────────────────────────────────┐
│ 🎯 ESTRATÉGIA DE ARMAZENAGEM            │
├─────────────────────────────────────────┤
│ • Segregar por tipo de material       │
│ • Considerar normas de segurança      │
│ • Otimizar fluxo de movimentação      │
│ • Facilitar controle de estoque       │
└─────────────────────────────────────────┘
```

---

## ✅ **RESULTADO FINAL**

### **🎯 SISTEMA MAIS FLEXÍVEL:**
- ✅ **Armazém padrão** definido por produto
- ✅ **Controle granular** de armazenagem
- ✅ **Flexibilidade** para diferentes tipos de material
- ✅ **Organização** melhorada do estoque
- ✅ **Compliance** com normas específicas
- ✅ **Otimização** de processos logísticos

### **📈 BENEFÍCIOS ALCANÇADOS:**
- 🎯 **Maior controle** sobre armazenagem
- 📊 **Melhor organização** do estoque
- 🔒 **Segurança** para produtos especiais
- ⚡ **Eficiência** nos processos
- 📋 **Rastreabilidade** aprimorada
- 🏭 **Adequação** às necessidades específicas

**Agora cada produto tem seu armazém padrão específico, oferecendo maior flexibilidade e controle sobre a armazenagem de materiais!** 🎉✅🏭📦🚀
