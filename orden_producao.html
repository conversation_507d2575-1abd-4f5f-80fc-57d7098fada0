<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Nova Ordem de Produção Avançada</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --header-bg: #354a5f;
    }
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
      margin: 0;
      padding: 0;
    }
    .container {
      width: 90%;
      max-width: 1000px;
      margin: 40px auto;
      padding: 24px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    h1 {
      color: var(--primary-color);
      font-size: 26px;
      margin-bottom: 20px;
    }
    label { font-weight: 500; color: var(--text-secondary); }
    .options { margin: 20px 0; }
    .result { margin-top: 30px; }
    .erro { color: var(--danger-color); font-weight: bold; }
    .success { color: var(--success-color); font-weight: bold; }
    .material-list { margin: 10px 0 20px 30px; }
    .material-list li { margin-bottom: 4px; font-size: 15px; }
    .op-block {
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 24px;
      padding: 18px 18px 10px 18px;
      background: var(--secondary-color);
      box-shadow: 0 1px 4px rgba(8,84,160,0.04);
    }
    .op-title {
      font-weight: bold;
      color: var(--primary-color);
      font-size: 18px;
      margin-bottom: 8px;
    }
    input[type="text"], input[type="number"] {
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 15px;
      margin-right: 10px;
      margin-bottom: 8px;
    }
    input[type="checkbox"] {
      margin-right: 6px;
    }
    button {
      background-color: var(--success-color);
      color: #fff;
      padding: 10px 22px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-size: 15px;
      transition: background 0.2s;
    }
    button:hover {
      background-color: var(--success-hover);
    }
    @media (max-width: 700px) {
      .container { width: 98%; padding: 10px; }
      h1 { font-size: 20px; }
      .op-title { font-size: 15px; }
      .op-block { padding: 10px 6px 6px 10px; }
    }
  </style>
</head>
<body>
  <h1>Nova Ordem de Produção Avançada</h1>
  <form id="formOP">
    <label>Produto (código): <input type="text" id="codigoProduto" required></label>
    <label style="margin-left:20px;">Quantidade: <input type="number" id="quantidade" min="0.001" step="0.001" required></label>
    <div class="options">
      <label><input type="checkbox" id="considerarEstoque" checked> Considerar saldo de estoque</label>
      <label style="margin-left:20px;"><input type="checkbox" id="aglutinarSP"> Aglutinar OPs de SP iguais</label>
    </div>
    <button type="submit">Gerar OPs</button>
  </form>
  <div class="result" id="resultado"></div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function buscarProdutoPorCodigo(codigo) {
      const snap = await getDocs(query(collection(db, 'produtos'), where('codigo', '==', codigo)));
      if (snap.empty) return null;
      return { id: snap.docs[0].id, ...snap.docs[0].data() };
    }
    async function buscarEstrutura(produtoId) {
      const snap = await getDocs(query(collection(db, 'estruturas'), where('produtoPaiId', '==', produtoId)));
      if (snap.empty) return null;
      return { id: snap.docs[0].id, ...snap.docs[0].data() };
    }
    async function buscarEstoque(produtoId) {
      // Soma saldo de todos os armazéns
      let saldo = 0;
      const snap = await getDocs(query(collection(db, 'estoque'), where('codigo', '==', produtoId)));
      snap.forEach(doc => {
        const d = doc.data();
        saldo += Number(d.quantidade || 0);
      });
      return saldo;
    }

    document.getElementById('formOP').onsubmit = async function(e) {
      e.preventDefault();
      const resultado = document.getElementById('resultado');
      resultado.innerHTML = 'Processando...';
      const codigo = document.getElementById('codigoProduto').value.trim();
      const quantidade = Number(document.getElementById('quantidade').value);
      const considerarEstoque = document.getElementById('considerarEstoque').checked;
      const aglutinarSP = document.getElementById('aglutinarSP').checked;
      try {
        const produto = await buscarProdutoPorCodigo(codigo);
        if (!produto) throw new Error('Produto não encontrado!');
        const estrutura = await buscarEstrutura(produto.id);
        if (!estrutura) throw new Error('Estrutura do produto não encontrada!');
        const opTree = await gerarOPs(produto, quantidade, estrutura, considerarEstoque, aglutinarSP);
        resultado.innerHTML = renderOPTree(opTree);
      } catch (e) {
        resultado.innerHTML = `<span class="erro">${e.message}</span>`;
      }
    };

    async function gerarOPs(produto, quantidade, estrutura, considerarEstoque, aglutinarSP, nivel=0, visited=new Set(), aglutinaMap={}) {
      if (nivel > 10) throw new Error('Estrutura muito profunda (possível ciclo)');
      if (visited.has(produto.id)) throw new Error('Ciclo detectado na estrutura: ' + produto.codigo);
      visited.add(produto.id);
      let materiais = [];
      let filhos = [];
      for (const comp of estrutura.componentes) {
        const compProdSnap = await getDocs(query(collection(db, 'produtos'), where('id', '==', comp.componentId)));
        if (compProdSnap.empty) continue;
        const compProd = { id: comp.componentId, ...compProdSnap.docs[0].data() };
        const qtdNecessaria = quantidade * comp.quantidade;
        if (compProd.tipo === 'SP') {
          // Aglutinação de SP
          if (aglutinarSP) {
            if (!aglutinaMap[compProd.id]) {
              aglutinaMap[compProd.id] = { produto: compProd, quantidade: 0, filhos: [], materiais: [] };
            }
            aglutinaMap[compProd.id].quantidade += qtdNecessaria;
          } else {
            // Gera OP filha normalmente
            const subEstrutura = await buscarEstrutura(compProd.id);
            if (!subEstrutura) throw new Error('Estrutura não encontrada para SP: ' + compProd.codigo);
            const filho = await gerarOPs(compProd, qtdNecessaria, subEstrutura, considerarEstoque, aglutinarSP, nivel+1, new Set(visited), aglutinaMap);
            filhos.push(filho);
          }
          // Sempre exibe SP na lista de materiais
          materiais.push({ ...compProd, quantidade: qtdNecessaria, tipo: 'SP' });
        } else {
          // MP
          let saldo = 0;
          if (considerarEstoque) saldo = await buscarEstoque(compProd.codigo);
          const necessidade = Math.max(0, qtdNecessaria - saldo);
          materiais.push({ ...compProd, quantidade: qtdNecessaria, saldo, necessidade, tipo: 'MP' });
        }
      }
      // Se aglutinarSP, gera filhos depois
      if (nivel === 0 && aglutinarSP) {
        for (const spId in aglutinaMap) {
          const sp = aglutinaMap[spId];
          const subEstrutura = await buscarEstrutura(sp.produto.id);
          if (!subEstrutura) throw new Error('Estrutura não encontrada para SP: ' + sp.produto.codigo);
          const filho = await gerarOPs(sp.produto, sp.quantidade, subEstrutura, considerarEstoque, aglutinarSP, nivel+1, new Set(visited), aglutinaMap);
          filhos.push(filho);
        }
      }
      return { produto, quantidade, materiais, filhos, nivel };
    }

    function renderOPTree(op, pai=true) {
      let html = `<div class="op-block"><div class="op-title">${pai ? 'OP Pai' : 'OP SP'}: ${op.produto.codigo} - ${op.produto.descricao} (Qtd: ${op.quantidade})</div>`;
      html += '<ul class="material-list">';
      for (const mat of op.materiais) {
        if (mat.tipo === 'MP') {
          html += `<li>MP: ${mat.codigo} - ${mat.descricao} | Qtd: ${mat.quantidade}`;
          if ('saldo' in mat) html += ` | Saldo: ${mat.saldo}`;
          if ('necessidade' in mat) html += ` | Necessidade: ${mat.necessidade}`;
          html += '</li>';
        } else if (mat.tipo === 'SP') {
          html += `<li>SP: ${mat.codigo} - ${mat.descricao} | Qtd: ${mat.quantidade}</li>`;
        }
      }
      html += '</ul>';
      for (const filho of op.filhos) {
        html += renderOPTree(filho, false);
      }
      html += '</div>';
      return html;
    }
  </script>
</body>
</html>
