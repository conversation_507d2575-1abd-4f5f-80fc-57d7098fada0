/* ===== SISTEMA CSS PADRONIZADO - BASEADO EM COTAÇÕES ===== */

/* ===== VARIÁVEIS CSS GLOBAIS ===== */
:root {
    /* Cores Principais - Baseadas na tela de cotações */
    --primary-color: #3498db;
    --primary-hover: #2980b9;
    --primary-light: #e3f2fd;
    
    --secondary-color: #34495e;
    --secondary-hover: #2c3e50;
    --secondary-light: #f8f9fa;
    
    /* Cores de Status */
    --success-color: #27ae60;
    --success-hover: #2ecc71;
    --success-light: #e8f5e8;
    
    --warning-color: #f39c12;
    --warning-hover: #e67e22;
    --warning-light: #fff3e0;
    
    --danger-color: #e74c3c;
    --danger-hover: #c0392b;
    --danger-light: #ffebee;
    
    --info-color: #17a2b8;
    --info-hover: #138496;
    --info-light: #d1ecf1;
    
    /* Cores Neutras */
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    /* Cores de Texto */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --text-white: #ffffff;
    
    /* Cores de Fundo */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-header: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    /* Bordas */
    --border-color: #e9ecef;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 15px;
    --border-radius-xl: 20px;
    
    /* Sombras */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 5px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 20px 40px rgba(0,0,0,0.1);
    --shadow-xl: 0 25px 50px rgba(0,0,0,0.2);
    
    /* Espaçamentos */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 15px;
    --spacing-lg: 25px;
    --spacing-xl: 40px;
    
    /* Tipografia */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.8rem;
    --font-size-sm: 0.9rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.2rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;
    
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Transições */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== RESET E BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-gradient);
    min-height: 100vh;
    padding: 20px;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-size-base);
}

/* ===== CONTAINERS ===== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.container-fluid {
    width: 100%;
    max-width: none;
}

.container-sm {
    max-width: 800px;
}

.container-lg {
    max-width: 1600px;
}

/* ===== HEADERS ===== */
.header {
    background: var(--bg-header);
    color: var(--text-white);
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.header h1 {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* ===== CONTEÚDO PRINCIPAL ===== */
.main-content {
    padding: var(--spacing-xl);
}

.content-section {
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

/* ===== BOTÕES ===== */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--text-white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: var(--text-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
    color: var(--text-white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
    color: var(--text-white);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
    color: var(--text-white);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-700) 100%);
    color: var(--text-white);
}

.btn-sm {
    padding: 5px 12px;
    font-size: var(--font-size-xs);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== CARDS E ESTATÍSTICAS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    border-left: 5px solid;
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 10px;
}

.stat-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.5px;
}

/* ===== ABAS ===== */
.tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.tab {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
    border-bottom: 3px solid transparent;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tab:hover {
    color: var(--primary-color);
    background: var(--gray-100);
}

.tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--gray-100);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ===== FORMULÁRIOS ===== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-col {
    display: flex;
    flex-direction: column;
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
}

input, select, textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-bar {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) 50px var(--spacing-md) 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

/* ===== FILTROS ===== */
.filters {
    background: var(--gray-100);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
}

.filters h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* ===== TABELAS ===== */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: var(--bg-header);
    color: var(--text-white);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--gray-100);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== STATUS E BADGES ===== */
.status {
    padding: 5px 12px;
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status.aberta {
    background: var(--primary-light);
    color: var(--primary-color);
}

.status.enviada {
    background: var(--warning-light);
    color: var(--warning-color);
}

.status.respondida {
    background: var(--success-light);
    color: var(--success-color);
}

.status.aprovada {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status.fechada {
    background: #f5f5f5;
    color: #616161;
}

/* ===== MODAIS ===== */
.modal {
    display: none;
    position: fixed;
    z-index: var(--z-modal);
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--bg-primary);
    margin: 2% auto;
    padding: 0;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn var(--transition-normal);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: var(--bg-header);
    color: var(--text-white);
    padding: 20px 30px;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.close {
    color: var(--text-white);
    font-size: 28px;
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.close:hover {
    color: var(--warning-color);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--spacing-xl);
}

/* ===== ALERTAS ===== */
.alert {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
}

.alert-info {
    background: var(--info-light);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

.alert-success {
    background: var(--success-light);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background: var(--warning-light);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background: var(--danger-light);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

/* ===== NOTIFICAÇÕES ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    color: var(--text-white);
    font-weight: var(--font-weight-bold);
    z-index: var(--z-tooltip);
    transform: translateX(400px);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
}

.notification-error {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
}

.notification-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
}

.notification-info {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

/* ===== PAGINAÇÃO ===== */
.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--gray-100);
    border-top: 1px solid var(--border-color);
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.items-per-page label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.items-per-page select {
    padding: 5px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.pagination {
    display: flex;
    justify-content: center;
    padding: var(--spacing-md);
    gap: 5px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
}

.pagination button:hover {
    background: var(--gray-100);
    border-color: var(--primary-color);
}

.pagination button.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== WORKFLOW STEPS ===== */
.workflow-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.workflow-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 60px;
    width: 100px;
    height: 3px;
    background: var(--border-color);
    z-index: 1;
}

.workflow-step.completed::after {
    background: linear-gradient(90deg, var(--success-color) 0%, var(--success-hover) 100%);
}

.workflow-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.workflow-step.completed .workflow-icon {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: var(--text-white);
}

.workflow-step.active .workflow-icon {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--text-white);
    animation: pulse 2s infinite;
}

.workflow-step .workflow-icon {
    background: var(--border-color);
    color: var(--gray-600);
}

.workflow-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: center;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* ===== CLASSES UTILITÁRIAS ===== */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-white { color: var(--text-white) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-white { background-color: var(--bg-primary) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }

.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }

    .main-content {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 992px) {
    .header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: var(--spacing-md);
    }

    .header-actions {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .workflow-steps {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .workflow-step:not(:last-child)::after {
        display: none;
    }
}

@media (max-width: 768px) {
    body {
        padding: var(--spacing-sm);
    }

    .container {
        border-radius: 0;
        margin: 0;
    }

    .header {
        margin: 0;
        border-radius: 0;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .header-actions {
        width: 100%;
    }

    .header-actions .btn {
        flex: 1;
        min-width: 0;
    }

    .tabs {
        flex-direction: column;
    }

    .tab {
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }

    .tab.active {
        border-bottom-color: var(--border-color);
        background: var(--primary-light);
    }

    .table-container {
        overflow-x: auto;
    }

    .table {
        min-width: 600px;
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
    }

    .modal-body {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-number {
        font-size: 2rem;
    }

    .pagination {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .pagination button {
        padding: 6px 10px;
        font-size: var(--font-size-sm);
    }

    .pagination-info {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: var(--font-size-xl);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-sm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .search-input {
        padding: var(--spacing-sm) 40px var(--spacing-sm) var(--spacing-md);
    }

    .search-icon {
        right: var(--spacing-md);
        font-size: var(--font-size-base);
    }

    .workflow-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .workflow-label {
        font-size: var(--font-size-xs);
    }
}

/* ===== ANIMAÇÕES ADICIONAIS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

/* ===== ESTADOS DE HOVER E FOCUS MELHORADOS ===== */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
}

.table tbody tr:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tab:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px var(--primary-color);
}

/* ===== MELHORIAS DE ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
