<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HM-SYSTEMS - Gerenciar CFOPs</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      font-size: 13px;
    }

    .sap-header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sap-logo {
      font-weight: bold;
      font-size: 24px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .sap-title {
      color: var(--primary-color);
      font-size: 20px;
      margin: 0 0 20px 0;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .totvs-form {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .totvs-form h2 {
      color: var(--primary-color);
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-group {
      margin-bottom: 10px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }

    .totvs-input, .totvs-select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
      font-size: 13px;
      background-color: white;
    }

    .totvs-input:focus, .totvs-select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.2);
    }

    .btn-totvs {
      padding: 5px 10px;
      border: 1px solid var(--primary-color);
      border-radius: 3px;
      background-color: white;
      color: var(--primary-color);
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-totvs:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-totvs i {
      margin-right: 5px;
    }

    .btn-totvs-primary {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
    }

    .btn-totvs-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-totvs-secondary {
      background-color: white;
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
    }

    .btn-totvs-secondary:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-totvs-danger {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
    }

    .btn-totvs-danger:hover {
      background-color: #a30000;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      justify-content: flex-end;
    }

    .totvs-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-top: 15px;
      font-size: 12px;
    }

    .totvs-table th {
      background-color: var(--secondary-color);
      color: var(--primary-color);
      font-weight: 600;
      padding: 8px 5px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .totvs-table td {
      padding: 6px 5px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .totvs-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .totvs-table tr:hover {
      background-color: #e6f2ff;
    }

    .totvs-status {
      font-size: 11px;
      padding: 3px 6px;
      border-radius: 3px;
      font-weight: 500;
    }

    .status-active {
      background-color: #d4edda;
      color: #155724;
    }

    .status-inactive {
      background-color: #f8d7da;
      color: #721c24;
    }

    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      display: none;
    }

    .notification-success {
      background-color: #d4edda;
      color: #155724;
      display: block;
    }

    .notification-error {
      background-color: #f8d7da;
      color: #721c24;
      display: block;
    }

    .sap-status {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--secondary-color);
      padding: 5px 10px;
      font-size: 12px;
      margin-top: 20px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">HM-SYSTEMS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

  <div class="container">
    <div class="sap-title"><i class="fas fa-list-alt"></i> Gerenciar CFOPs</div>

    <!-- Formulário para Adicionar/Editar CFOP -->
    <div class="totvs-form">
      <h2>Adicionar Novo CFOP</h2>
      <form id="cfopForm" onsubmit="handleCFOP(event)">
        <div class="form-row">
          <div class="form-group">
            <label>ID do CFOP</label>
            <input type="text" id="cfopId" class="totvs-input" placeholder="Ex.: cfop_5102" required readonly>
          </div>
          <div class="form-group">
            <label>Código</label>
            <input type="text" id="cfopCodigo" class="totvs-input" placeholder="Ex.: 5102" required maxlength="4">
          </div>
          <div class="form-group">
            <label>Descrição</label>
            <input type="text" id="cfopDescricao" class="totvs-input" placeholder="Ex.: Venda de mercadoria dentro do estado" required>
          </div>
          <div class="form-group">
            <label>Tipo</label>
            <select id="cfopTipo" class="totvs-select" required>
              <option value="">Selecione...</option>
              <option value="Entrada">Entrada</option>
              <option value="Saída">Saída</option>
            </select>
          </div>
          <div class="form-group">
            <label>Ativo</label>
            <select id="cfopAtivo" class="totvs-select" required>
              <option value="true">Sim</option>
              <option value="false">Não</option>
            </select>
          </div>
        </div>
        <div class="form-actions">
          <button type="submit" class="btn-totvs-primary"><i class="fas fa-save"></i> Salvar</button>
          <button type="button" class="btn-totvs-secondary" onclick="resetForm()"><i class="fas fa-times"></i> Cancelar</button>
        </div>
      </form>
    </div>

    <!-- Tabela de CFOPs -->
    <table class="totvs-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Tipo</th>
          <th>Ativo</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="cfopTableBody"></tbody>
    </table>

    <div class="form-actions">
      <button class="btn-totvs-secondary" onclick="window.location.href='orcamentos.html'">
        <i class="fas fa-arrow-left"></i> Voltar
      </button>
      <button class="btn-totvs-danger" id="logoutButton" style="display: none;" onclick="logout()">
        <i class="fas fa-sign-out-alt"></i> Sair
      </button>
    </div>
  </div>

  <div class="sap-status">
    <div>Transação: ZCFOP - Gerenciar CFOPs</div>
    <div>Sistema: PRD | Cliente: 800</div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      doc,
      updateDoc,
      deleteDoc,
      onSnapshot
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    import * as Swal from 'https://cdn.jsdelivr.net/npm/sweetalert2@11/+esm';

    let cfops = [];
    let usuarioAtual = null;
    let editMode = false;
    let currentEditId = null;

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      document.getElementById('logoutButton').style.display = 'inline';

      await loadCFOPs();

      onSnapshot(collection(db, "cfops"), (snapshot) => {
        cfops = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
      });
    };

    async function loadCFOPs() {
      try {
        const cfopsSnap = await getDocs(collection(db, "cfops"));
        cfops = cfopsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
      } catch (error) {
        console.error("Erro ao carregar CFOPs:", error);
        showNotification("Erro ao carregar CFOPs", "error");
      }
    }

    function loadCFOPTable() {
      const tableBody = document.getElementById('cfopTableBody');
      tableBody.innerHTML = '';

      cfops.forEach(cfop => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${cfop.id}</td>
          <td>${cfop.codigo}</td>
          <td>${cfop.descricao}</td>
          <td>${cfop.tipo}</td>
          <td><span class="totvs-status ${cfop.ativo ? 'status-active' : 'status-inactive'}">${cfop.ativo ? 'Ativo' : 'Inativo'}</span></td>
          <td>
            <button class="btn-totvs" onclick="editCFOP('${cfop.id}')"><i class="fas fa-edit"></i> Editar</button>
            <button class="btn-totvs" onclick="deleteCFOP('${cfop.id}')"><i class="fas fa-trash"></i> Excluir</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.handleCFOP = async function(event) {
      event.preventDefault();

      if (!usuarioAtual) {
        showNotification('Por favor, faça login para gerenciar CFOPs.', 'error');
        window.location.href = 'login.html';
        return;
      }

      const cfopId = document.getElementById('cfopId').value;
      const codigo = document.getElementById('cfopCodigo').value;
      const descricao = document.getElementById('cfopDescricao').value;
      const tipo = document.getElementById('cfopTipo').value;
      const ativo = document.getElementById('cfopAtivo').value === 'true';

      if (!codigo || !descricao || !tipo) {
        showNotification('Por favor, preencha todos os campos obrigatórios.', 'error');
        return;
      }

      if (!/^\d{4}$/.test(codigo)) {
        showNotification('O código do CFOP deve ter exatamente 4 dígitos.', 'error');
        return;
      }

      const cfopData = {
        id: cfopId,
        codigo: codigo,
        descricao: descricao,
        tipo: tipo,
        ativo: ativo
      };

      try {
        if (editMode) {
          // Atualizar CFOP existente
          await updateDoc(doc(db, "cfops", currentEditId), cfopData);
          showNotification(`CFOP ${codigo} atualizado com sucesso!`, 'success');
          editMode = false;
          currentEditId = null;
        } else {
          // Adicionar novo CFOP
          const docRef = await addDoc(collection(db, "cfops"), cfopData);
          await updateDoc(doc(db, "cfops", docRef.id), { id: `cfop_${codigo}` });
          showNotification(`CFOP ${codigo} criado com sucesso!`, 'success');
        }
        resetForm();
      } catch (error) {
        console.error("Erro ao salvar CFOP:", error);
        showNotification(`Erro ao salvar CFOP: ${error.message}`, 'error');
      }
    };

    window.editCFOP = function(cfopId) {
      const cfop = cfops.find(c => c.id === cfopId);
      if (!cfop) return;

      editMode = true;
      currentEditId = cfopId;

      document.getElementById('cfopId').value = cfop.id;
      document.getElementById('cfopCodigo').value = cfop.codigo;
      document.getElementById('cfopDescricao').value = cfop.descricao;
      document.getElementById('cfopTipo').value = cfop.tipo;
      document.getElementById('cfopAtivo').value = cfop.ativo.toString();

      document.querySelector('.totvs-form h2').textContent = `Editar CFOP ${cfop.codigo}`;
      document.getElementById('cfopId').readOnly = true;
      document.getElementById('cfopCodigo').readOnly = true; // Evitar alterar o código
    };

    window.deleteCFOP = async function(cfopId) {
      if (!usuarioAtual) {
        showNotification('Por favor, faça login para excluir CFOPs.', 'error');
        window.location.href = 'login.html';
        return;
      }

      if (!confirm('Tem certeza que deseja excluir este CFOP?')) return;

      try {
        await deleteDoc(doc(db, "cfops", cfopId));
        showNotification('CFOP excluído com sucesso!', 'success');
      } catch (error) {
        console.error("Erro ao excluir CFOP:", error);
        showNotification('Erro ao excluir CFOP.', 'error');
      }
    };

    window.resetForm = function() {
      document.getElementById('cfopForm').reset();
      document.getElementById('cfopId').value = '';
      document.getElementById('cfopId').readOnly = false;
      document.getElementById('cfopCodigo').readOnly = false;
      document.querySelector('.totvs-form h2').textContent = 'Adicionar Novo CFOP';
      editMode = false;
      currentEditId = null;

      // Gerar ID automático baseado no código ao digitar
      document.getElementById('cfopCodigo').addEventListener('input', function() {
        const codigo = this.value;
        if (codigo) {
          document.getElementById('cfopId').value = `cfop_${codigo}`;
        } else {
          document.getElementById('cfopId').value = '';
        }
      });
    };

    window.logout = function() {
      localStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    };

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.className = `notification notification-${type}`;
      document.querySelector('.container').prepend(notification);
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Inicializar o evento de geração de ID
    document.getElementById('cfopCodigo').addEventListener('input', function() {
      const codigo = this.value;
      if (codigo) {
        document.getElementById('cfopId').value = `cfop_${codigo}`;
      } else {
        document.getElementById('cfopId').value = '';
      }
    });
  </script>
</body>
</html>