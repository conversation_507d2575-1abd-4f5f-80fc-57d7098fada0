<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Relatório de Empenho de Materiais</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --header-bg: #354a5f;
    }
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      font-size: 13px;
    }
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    h1 {
      color: var(--primary-color);
      font-size: 20px;
      margin-bottom: 20px;
    }
    .filtros { margin-bottom: 16px; display: flex; gap: 12px; align-items: center; }
    .filtros label { font-weight: 500; color: #555; }
    .filtros input, .filtros select {
      padding: 6px 8px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
      font-size: 13px;
      background-color: white;
    }
    .filtros input:focus, .filtros select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.2);
    }
    .filtros button {
      padding: 5px 10px;
      border: 1px solid var(--primary-color);
      border-radius: 3px;
      background-color: white;
      color: var(--primary-color);
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;
    }
    .filtros button:hover {
      background-color: var(--primary-color);
      color: white;
    }
    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-top: 15px;
      font-size: 12px;
      background: white;
    }
    th {
      background-color: var(--secondary-color);
      color: var(--primary-color);
      font-weight: 600;
      padding: 8px 5px;
      border: 1px solid var(--border-color);
      text-align: left;
    }
    td {
      padding: 6px 5px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
    }
    tr:nth-child(even) { background-color: #f9f9f9; }
    tr:hover { background-color: #e6f2ff; }
    tr.critico { background: #ffeaea; }
    tr.critico td { color: #b71c1c; font-weight: bold; }
    .paginacao { margin-top: 12px; text-align: right; }
    .paginacao button { margin: 0 2px; padding: 5px 10px; border: 1px solid var(--primary-color); border-radius: 3px; background-color: white; color: var(--primary-color); font-size: 11px; cursor: pointer; transition: all 0.2s; }
    .paginacao button:hover, .paginacao button[style*="font-weight:bold"] { background-color: var(--primary-color); color: white; }
  </style>
</head>
<body>
  <div class="container">
    <h1><i class="fas fa-warehouse"></i> Relatório de Empenho de Materiais</h1>
    <div class="filtros">
      <label>Grupo:
        <select id="grupoFiltro"><option value="">Todos</option></select>
      </label>
      <label>Família:
        <select id="familiaFiltro"><option value="">Todas</option></select>
      </label>
      <input type="text" id="busca" placeholder="Buscar código ou descrição..." style="flex:1;">
      <button onclick="exportarParaExcel()"><i class="fas fa-file-excel"></i> Exportar Excel</button>
    </div>
    <table>
      <thead>
        <tr>
          <th>Código</th>
          <th>Descrição</th>
          <th>Grupo</th>
          <th>Família</th>
          <th>Unidade</th>
          <th>Armazém(s)</th>
          <th>Saldo Disponível</th>
          <th>Empenhado</th>
          <th>Necessidade Total</th>
          <th>Necessidade Pendente</th>
        </tr>
      </thead>
      <tbody id="relatorioBody"></tbody>
    </table>
    <div class="paginacao" id="paginacao"></div>
  </div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [], estoques = [], opsAbertas = [], grupos = [], familias = [], armazens = [];
    let paginaAtual = 1, itensPorPagina = 20, dadosFiltrados = [];

    async function carregarDados() {
      const [estoquesSnap, produtosSnap, opsSnap, gruposSnap, familiasSnap, armazensSnap] = await Promise.all([
        getDocs(collection(db, "estoques")),
        getDocs(collection(db, "produtos")),
        getDocs(collection(db, "ordensProducao")),
        getDocs(collection(db, "grupos")),
        getDocs(collection(db, "familias")),
        getDocs(collection(db, "armazens"))
      ]);
      estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      opsAbertas = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');
      grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      popularFiltros();
      aplicarFiltros();
    }

    function popularFiltros() {
      const grupoFiltro = document.getElementById('grupoFiltro');
      grupos.sort((a, b) => (a.nomeGrupo || '').localeCompare(b.nomeGrupo || ''));
      grupos.forEach(g => {
        grupoFiltro.innerHTML += `<option value="${g.codigoGrupo}">${g.codigoGrupo} - ${g.nomeGrupo}</option>`;
      });
      const familiaFiltro = document.getElementById('familiaFiltro');
      familias.sort((a, b) => (a.nomeFamilia || '').localeCompare(b.nomeFamilia || ''));
      familias.forEach(f => {
        familiaFiltro.innerHTML += `<option value="${f.codigoFamilia}">${f.codigoFamilia} - ${f.nomeFamilia}</option>`;
      });
    }

    function aplicarFiltros() {
      const grupoSel = document.getElementById('grupoFiltro').value;
      const familiaSel = document.getElementById('familiaFiltro').value;
      const busca = document.getElementById('busca').value.toLowerCase();
      // Mapear necessidades por produto
      const necessidades = {};
      for (const op of opsAbertas) {
        if (Array.isArray(op.materiaisNecessarios)) {
          for (const mat of op.materiaisNecessarios) {
            if (!necessidades[mat.produtoId]) necessidades[mat.produtoId] = 0;
            necessidades[mat.produtoId] += Number(mat.quantidade) || 0;
          }
        }
      }
      dadosFiltrados = produtos.filter(produto => {
        const grupoOK = !grupoSel || produto.grupo === grupoSel;
        const familiaOK = !familiaSel || produto.familia === familiaSel;
        const buscaOK = !busca ||
          (produto.codigo && produto.codigo.toLowerCase().includes(busca)) ||
          (produto.descricao && produto.descricao.toLowerCase().includes(busca));
        return grupoOK && familiaOK && buscaOK;
      }).map(produto => {
        const estoquesProduto = estoques.filter(e => e.produtoId === produto.id);
        const saldo = estoquesProduto.reduce((sum, e) => sum + (e.saldo || 0), 0);
        const empenhado = estoquesProduto.reduce((sum, e) => sum + (e.saldoReservado || 0), 0);
        const necessidade = necessidades[produto.id] || 0;
        const pendente = Math.max(0, necessidade - empenhado);
        const grupoObj = grupos.find(g => g.codigoGrupo === produto.grupo);
        const familiaObj = familias.find(f => f.codigoFamilia === produto.familia);
        const armazensProduto = estoquesProduto.map(e => {
          const arm = armazens.find(a => a.id === e.armazemId);
          return arm ? `${arm.codigo} (${e.saldo.toFixed(3)})` : '';
        }).filter(Boolean).join(', ');
        return {
          ...produto,
          saldo,
          empenhado,
          necessidade,
          pendente,
          grupo: grupoObj ? grupoObj.nomeGrupo : produto.grupo,
          familia: familiaObj ? familiaObj.nomeFamilia : produto.familia,
          armazensProduto
        };
      }).filter(p => p.necessidade > 0 || p.empenhado > 0);
      paginaAtual = 1;
      renderizarTabela();
    }

    function renderizarTabela() {
      const body = document.getElementById('relatorioBody');
      body.innerHTML = '';
      const inicio = (paginaAtual - 1) * itensPorPagina;
      const fim = inicio + itensPorPagina;
      const pagina = dadosFiltrados.slice(inicio, fim);
      pagina.forEach(produto => {
        body.innerHTML += `
          <tr class="${produto.pendente > 0 ? 'critico' : ''}">
            <td>${produto.codigo}</td>
            <td>${produto.descricao}</td>
            <td>${produto.grupo || ''}</td>
            <td>${produto.familia || ''}</td>
            <td>${produto.unidade}</td>
            <td>${produto.armazensProduto}</td>
            <td>${produto.saldo.toFixed(3)}</td>
            <td>${produto.empenhado.toFixed(3)}</td>
            <td>${produto.necessidade.toFixed(3)}</td>
            <td>${produto.pendente.toFixed(3)}</td>
          </tr>
        `;
      });
      renderizarPaginacao();
    }

    function renderizarPaginacao() {
      const pagDiv = document.getElementById('paginacao');
      const totalPaginas = Math.ceil(dadosFiltrados.length / itensPorPagina);
      pagDiv.innerHTML = '';
      if (totalPaginas <= 1) return;
      for (let i = 1; i <= totalPaginas; i++) {
        pagDiv.innerHTML += `<button onclick="mudarPagina(${i})" ${i === paginaAtual ? 'style=\"font-weight:bold;\"' : ''}>${i}</button>`;
      }
    }
    window.mudarPagina = function(p) { paginaAtual = p; renderizarTabela(); };

    document.getElementById('grupoFiltro').onchange = aplicarFiltros;
    document.getElementById('familiaFiltro').onchange = aplicarFiltros;
    document.getElementById('busca').oninput = aplicarFiltros;

    window.exportarParaExcel = function() {
      let csv = 'Código,Descrição,Grupo,Família,Unidade,Armazéns,Saldo Disponível,Empenhado,Necessidade Total,Necessidade Pendente\n';
      dadosFiltrados.forEach(produto => {
        csv += `"${produto.codigo}","${produto.descricao}","${produto.grupo}","${produto.familia}","${produto.unidade}","${produto.armazensProduto}",${produto.saldo.toFixed(3)},${produto.empenhado.toFixed(3)},${produto.necessidade.toFixed(3)},${produto.pendente.toFixed(3)}\n`;
      });
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'relatorio_empenho_materiais.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    };

    carregarDados();
  </script>
</body>
</html> 