<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recebimento de Materiais - Sistema Inteligente</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        /* Workflow Steps */
        .workflow-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
        }

        .workflow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .workflow-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 25px;
            left: 60px;
            width: 100px;
            height: 3px;
            background: #e9ecef;
            z-index: 1;
        }

        .workflow-step.completed::after {
            background: linear-gradient(90deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .workflow-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .workflow-step.completed .workflow-icon {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            color: white;
        }

        .workflow-step.active .workflow-icon {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            animation: pulse 2s infinite;
        }

        .workflow-step .workflow-icon {
            background: #e9ecef;
            color: #6c757d;
        }

        .workflow-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--primary-color);
            text-align: center;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Configuration Alert */
        .config-alert {
            background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .config-alert.quality-enabled {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .config-alert.direct-stock {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        /* Form Styles */
        .form-section {
            background: var(--light-bg);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid var(--secondary-color);
        }

        .form-section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-control {
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-control.error {
            border-color: var(--danger-color);
            background: #fff5f5;
        }

        .form-control.warning {
            border-color: var(--warning-color);
            background: #fffbf0;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, var(--primary-color) 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: var(--light-bg);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.quality {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-badge.approved {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.stock {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-badge.delayed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.ok {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transform: translateX(400px);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
        }

        /* Estilos para o overlay de redirecionamento */
        .redirect-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            padding: 30px;
        }

        .redirect-content {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 100%;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .redirect-content h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .redirect-content .alert {
            text-align: left;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .redirect-content .alert i {
            margin-right: 8px;
            font-size: 1.2em;
        }

        .redirect-content .btn {
            padding: 10px 20px;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 180px;
            margin: 5px;
        }

        .redirect-content .btn i {
            margin-right: 8px;
        }

        #countdown {
            font-weight: bold;
            color: #0d6efd;
            font-size: 1.2em;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                flex-direction: column;
                gap: 20px;
            }
            
            .workflow-step::after {
                display: none;
            }
            
            .redirect-overlay {
                padding: 20px;
            }
            
            .redirect-content {
                width: 95%;
                padding: 20px;
            }
            
            .redirect-content h3 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-truck-loading"></i> Recebimento de Materiais</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Workflow de Recebimento -->
            <div class="workflow-steps">
                <div class="workflow-step completed">
                    <div class="workflow-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="workflow-label">Pedido de Compra</div>
                </div>
                <div class="workflow-step active">
                    <div class="workflow-icon">
                        <i class="fas fa-truck-loading"></i>
                    </div>
                    <div class="workflow-label">Recebimento</div>
                </div>
                <div class="workflow-step" id="qualityStep">
                    <div class="workflow-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="workflow-label">Inspeção</div>
                </div>
                <div class="workflow-step">
                    <div class="workflow-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="workflow-label">Estoque</div>
                </div>
            </div>

            <!-- Alerta de Configuração -->
            <div id="configAlert" class="config-alert">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Configuração do Sistema:</strong>
                    <span id="configMessage">Carregando configurações...</span>
                </div>
            </div>

            <!-- Seleção do Pedido -->
            <div class="form-section">
                <h3><i class="fas fa-search"></i> Selecionar Pedido de Compra</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label>Pedido de Compra</label>
                        <select class="form-control" id="orderSelect" onchange="loadOrderDetails()">
                            <option value="">Selecione o pedido...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Fornecedor</label>
                        <input type="text" class="form-control" id="supplierName" readonly>
                    </div>
                </div>
            </div>

            <!-- Informações Detalhadas do Fornecedor -->
            <div id="supplierDetails" class="form-section" style="display: none;">
                <h3><i class="fas fa-building"></i> Informações do Fornecedor e Pedido</h3>

                <!-- Alerta de Status do Pedido -->
                <div id="orderStatusAlert" style="display: none; margin-bottom: 20px;"></div>
                <div class="form-row">
                    <div class="form-group">
                        <label>CNPJ</label>
                        <input type="text" class="form-control" id="supplierCnpj" readonly>
                    </div>
                    <div class="form-group">
                        <label>Contato</label>
                        <input type="text" class="form-control" id="supplierContact" readonly>
                    </div>
                    <div class="form-group">
                        <label>Solicitante</label>
                        <input type="text" class="form-control" id="orderRequester" readonly>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Data do Pedido</label>
                        <input type="text" class="form-control" id="orderDate" readonly>
                    </div>
                    <div class="form-group">
                        <label>Entrega Prevista</label>
                        <input type="text" class="form-control" id="deliveryDate" readonly>
                    </div>
                    <div class="form-group">
                        <label>Status do Prazo</label>
                        <div id="deliveryStatus" style="padding: 8px 0;"></div>
                    </div>
                </div>
            </div>

            <!-- Dados do Recebimento -->
            <div id="receiptSection" class="form-section" style="display: none;">
                <h3><i class="fas fa-clipboard-list"></i> Dados do Recebimento</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label>Número da Nota Fiscal</label>
                        <input type="text" class="form-control" id="invoiceNumber" required>
                    </div>
                    <div class="form-group">
                        <label>Série da NF</label>
                        <input type="text" class="form-control" id="invoiceSeries" placeholder="Ex: 1">
                    </div>
                    <div class="form-group">
                        <label>Data da Nota Fiscal</label>
                        <input type="date" class="form-control" id="invoiceDate" required>
                    </div>
                    <div class="form-group">
                        <label>Valor Total da NF</label>
                        <input type="number" class="form-control" id="invoiceValue" step="0.01" required>
                    </div>
                </div>

                <!-- Dados do Frete -->
                <div class="form-row" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4 style="margin: 0 0 15px 0; color: var(--primary-color);"><i class="fas fa-truck"></i> Dados do Frete</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label>Tipo de Frete</label>
                            <select class="form-control" id="freightType" onchange="toggleFreightFields()">
                                <option value="CIF">CIF (Fornecedor paga)</option>
                                <option value="FOB">FOB (Comprador paga)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Valor do Frete</label>
                            <input type="number" class="form-control" id="freightValue" step="0.01" placeholder="0.00" onchange="calculateFreightDistribution()">
                        </div>
                        <div class="form-group">
                            <label>Transportadora</label>
                            <input type="text" class="form-control" id="carrier" placeholder="Nome da transportadora">
                        </div>
                        <div class="form-group">
                            <label>Chave NFe</label>
                            <input type="text" class="form-control" id="nfeKey" placeholder="44 dígitos" maxlength="44">
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="includeFreightInCost" checked onchange="calculateFreightDistribution()">
                                Incluir frete no custo dos produtos (rateio automático)
                            </label>
                        </div>
                        <div id="freightDistributionInfo" style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 4px; font-size: 0.9em;">
                            <strong>Rateio do Frete:</strong> <span id="freightDistributionText">Configure o valor do frete para ver o rateio</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group" style="width: 100%;">
                        <label>Observações</label>
                        <textarea class="form-control" id="observations" rows="3" placeholder="Observações sobre o recebimento..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Itens do Pedido -->
            <div id="itemsSection" style="display: none;">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Qtd. Pedida</th>
                                <th>Qtd. Recebida</th>
                                <th>Saldo Pendente</th>
                                <th>Qtd. a Receber</th>
                                <th>Preço Pedido</th>
                                <th>Preço NF</th>
                                <th>Destino</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- Itens serão carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <button class="btn btn-success" onclick="processReceipt()">
                        <i class="fas fa-check"></i> Processar Recebimento
                    </button>
                </div>
            </div>

            <!-- Histórico de Recebimentos -->
            <div class="form-container" style="margin-top: 30px;">
                <h2 class="form-title">📋 Histórico de Recebimentos</h2>
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="loadReceiptHistory()" style="margin-right: 10px;">
                        🔄 Carregar Histórico
                    </button>
                    <button class="btn btn-secondary" onclick="clearReceiptHistory()">
                        🗑️ Limpar
                    </button>
                    <span style="margin-left: 15px; color: #666; font-size: 14px;">
                        💡 Últimos 20 itens recebidos e seus destinos
                    </span>
                </div>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Pedido</th>
                                <th>Código</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Armazém Destino</th>
                                <th>Fornecedor</th>
                                <th>Usuário</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <tr>
                                <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                    <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
                                    <div>Clique em "Carregar Histórico" para visualizar os últimos recebimentos</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <!-- Firebase e Scripts -->
    <script type="module">
        // Importações Firebase
        import { db, storage } from './firebase-config.js';
        import { MaterialEntryService } from './services/material-entry-service.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            getDoc,
            Timestamp,
            runTransaction,
            writeBatch,
            arrayUnion
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Variáveis globais
        let systemConfig = {};
        let pedidosCompra = [];
        let produtos = [];
        let armazens = [];
        let fornecedores = [];
        let users = [];
        let currentOrder = null;
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Usuário' };

        // ===== INICIALIZAÇÃO =====
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                showNotification('Carregando configurações do sistema...', 'info');
                await loadSystemConfiguration();
                await loadInitialData();
                setupInterface();
                showNotification('Sistema carregado com sucesso!', 'success');
            } catch (error) {
                console.error('Erro na inicialização:', error);
                showNotification('Erro ao carregar sistema: ' + error.message, 'error');
            }
        });

        // ===== CARREGAMENTO DE CONFIGURAÇÕES =====
        async function loadSystemConfiguration() {
            try {
                const configDoc = await getDoc(doc(db, "parametros", "sistema"));

                if (configDoc.exists()) {
                    const data = configDoc.data();
                    systemConfig = {
                        // ✅ Configurações de qualidade (estrutura corrigida)
                        controleQualidade: data.parametros_qualidade?.controleQualidade ||
                                         data.configuracaoSistema?.controleQualidade || false,
                        inspecaoRecebimento: data.parametros_qualidade?.inspecaoRecebimento ||
                                           data.configuracaoSistema?.inspecaoRecebimento || 'manual',
                        rastreabilidadeLote: data.parametros_qualidade?.rastreabilidadeLote || false,

                        // ✅ Configurações de estoque
                        toleranciaRecebimento: data.parametros_estoque?.toleranciaRecebimento || 10,
                        toleranciaPreco: data.parametros_estoque?.toleranciaPreco || 5,
                        permitirRecebimentoParcial: data.parametros_estoque?.permitirRecebimentoParcial !== false,
                        diasAlerteAtraso: data.parametros_estoque?.diasAlerteAtraso || 3,
                        exigirAutorizacaoExcesso: data.parametros_estoque?.exigirAutorizacaoExcesso || false,
                        bloquearPrecoDivergente: data.parametros_estoque?.bloquearPrecoDivergente || false,

                        // ✅ Configurações de frete
                        controleFreteObrigatorio: data.parametros_frete?.controleFreteObrigatorio || false,
                        ratearFreteNoCusto: data.parametros_frete?.ratearFreteNoCusto !== false,
                        criterioRateioFrete: data.parametros_frete?.criterioRateioFrete || 'valor',

                        // ✅ Configurações de compras
                        exigirAprovacaoTolerancia: data.parametros_compras?.exigirAprovacaoTolerancia || false,
                        valorAprovacaoAutomatica: data.parametros_compras?.valorAprovacaoAutomatica || 1000,

                        // ✅ Configurações de custos
                        centroCustoObrigatorio: data.parametros_custos?.centroCustoObrigatorio || false,

                        // Configurações legadas (manter compatibilidade)
                        armazemQualidade: data.configuracaoSistema?.armazemQualidade || false,
                        controleQualidadeObrigatorio: data.controleQualidadeObrigatorio || false,

                        // Configurações de estoque e recebimento
                        permitirEstoqueNegativo: data.permitirEstoqueNegativo || false,
                        toleranciaRecebimento: data.toleranciaRecebimento || 10,
                        toleranciaPreco: data.toleranciaPreco || 5,
                        bloquearPrecoDivergente: data.bloquearPrecoDivergente || false,
                        permitirRecebimentoParcial: data.permitirRecebimentoParcial !== false,
                        diasAlerteAtraso: data.diasAlerteAtraso || 3,
                        exigirAutorizacaoExcesso: data.exigirAutorizacaoExcesso || false,

                        // Outras configurações
                        homologacaoFornecedor: data.configuracaoSistema?.homologacaoFornecedor || false
                    };
                } else {
                    // Configurações padrão
                    systemConfig = {
                        controleQualidade: false,
                        armazemQualidade: false,
                        inspecaoRecebimento: 'manual',
                        controleQualidadeObrigatorio: false,
                        permitirEstoqueNegativo: false,
                        toleranciaRecebimento: 10,
                        toleranciaPreco: 5,
                        bloquearPrecoDivergente: false,
                        permitirRecebimentoParcial: true,
                        diasAlerteAtraso: 3,
                        exigirAutorizacaoExcesso: false,
                        homologacaoFornecedor: false
                    };
                }

                console.log('Configurações carregadas:', systemConfig);
                updateConfigurationAlert();

            } catch (error) {
                console.error('Erro ao carregar configurações:', error);
                throw error;
            }
        }

        function updateConfigurationAlert() {
            const alert = document.getElementById('configAlert');
            const message = document.getElementById('configMessage');
            const qualityStep = document.getElementById('qualityStep');

            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                // Sistema com controle de qualidade ativo
                alert.className = 'config-alert quality-enabled';
                message.innerHTML = `
                    <strong>Controle de Qualidade ATIVO</strong><br>
                    Materiais serão direcionados para o armazém da qualidade para inspeção antes do estoque final.
                    <br><small>Inspeção: ${getInspectionTypeText(systemConfig.inspecaoRecebimento)}</small>
                `;
                qualityStep.classList.add('active');
            } else {
                // Sistema sem controle de qualidade
                alert.className = 'config-alert direct-stock';
                message.innerHTML = `
                    <strong>Entrada Direta no Estoque</strong><br>
                    Materiais serão direcionados para o armazém padrão de cada produto após o recebimento.
                    <br><small>Armazém padrão definido no cadastro de cada produto</small>
                `;
                qualityStep.style.display = 'none';
            }
        }

        function getInspectionTypeText(type) {
            const types = {
                'todos': 'Todos os materiais',
                'criticos': 'Apenas materiais críticos',
                'manual': 'Definição manual por produto'
            };
            return types[type] || 'Configuração padrão';
        }



        // Função para corrigir pedidos sem produtoId
        async function fixOrdersWithoutProductId() {
            try {
                let pedidosCorrigidos = 0;

                for (const pedido of pedidosCompra) {
                    if (!pedido.itens || pedido.itens.length === 0) continue;

                    let precisaCorrigir = false;
                    const itensCorrigidos = pedido.itens.map(item => {
                        if (!item.produtoId && item.codigo) {
                            const produto = produtos.find(p => p.codigo === item.codigo);
                            if (produto) {
                                precisaCorrigir = true;
                                return { ...item, produtoId: produto.id };
                            }
                        }
                        return item;
                    });

                    if (precisaCorrigir) {
                        console.log(`Corrigindo pedido ${pedido.numero} - adicionando produtoId aos itens`);

                        await updateDoc(doc(db, "pedidosCompra", pedido.id), {
                            itens: itensCorrigidos
                        });

                        // Atualizar o objeto local
                        pedido.itens = itensCorrigidos;
                        pedidosCorrigidos++;
                    }
                }

                if (pedidosCorrigidos > 0) {
                    console.log(`✅ ${pedidosCorrigidos} pedidos corrigidos com produtoId`);
                    showNotification(`${pedidosCorrigidos} pedidos foram corrigidos automaticamente`, 'success');
                }

            } catch (error) {
                console.error('Erro ao corrigir pedidos:', error);
                // Não interromper o carregamento por causa deste erro
            }
        }



        // ===== HISTÓRICO DE RECEBIMENTOS =====
        window.loadReceiptHistory = async function() {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 20px;">🔄 Carregando histórico...</td></tr>';

            try {
                console.log('=== CARREGANDO HISTÓRICO DE RECEBIMENTOS ===');

                // Buscar em múltiplas fontes de dados
                const [movimentacoesSnap, estoqueQualidadeSnap, recebimentosSnap] = await Promise.all([
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "estoqueQualidade")),
                    getDocs(collection(db, "recebimentosDetalhes"))
                ]);

                console.log('Dados encontrados:', {
                    movimentacoes: movimentacoesSnap.docs.length,
                    estoqueQualidade: estoqueQualidadeSnap.docs.length,
                    recebimentosDetalhes: recebimentosSnap.docs.length
                });

                // Processar movimentações de estoque
                let historico = [];
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

                // 1. Movimentações de estoque
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes.forEach(mov => {
                    if (mov.tipo !== 'ENTRADA') return;

                    const tipoDoc = mov.tipoDocumento || '';
                    const isRecebimento = tipoDoc.includes('RECEBIMENTO') ||
                                        tipoDoc === 'RECEBIMENTO' ||
                                        tipoDoc === 'QUALIDADE' ||
                                        tipoDoc === 'APROVACAO_QUALIDADE';

                    if (!isRecebimento) return;

                    const dataMovimentacao = mov.dataHora?.toDate ? mov.dataHora.toDate() : new Date(mov.dataHora);
                    if (dataMovimentacao < thirtyDaysAgo) return;

                    historico.push({
                        ...mov,
                        fonte: 'movimentacoes',
                        dataHora: dataMovimentacao
                    });
                });

                // 2. Estoque de qualidade (recebimentos pendentes de inspeção)
                const estoqueQualidade = estoqueQualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar estoques atuais para verificar se itens ainda estão na qualidade
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoquesAtuais = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                estoqueQualidade.forEach(item => {
                    const dataEntrada = item.dataEntrada?.toDate ? item.dataEntrada.toDate() : new Date(item.dataEntrada);
                    if (dataEntrada < thirtyDaysAgo) return;

                    // VERIFICAR SE O ITEM AINDA ESTÁ NA QUALIDADE
                    // Se o produto já tem saldo em outros armazéns, provavelmente foi transferido
                    const produto = produtos.find(p => p.id === item.produtoId || p.codigo === item.codigo);
                    if (produto) {
                        const estoquesDoItem = estoquesAtuais.filter(e => e.produtoId === produto.id);
                        const temEstoqueForaQualidade = estoquesDoItem.some(e => {
                            const armazem = armazens.find(a => a.id === e.armazemId);
                            return armazem && armazem.tipo !== 'QUALIDADE' && e.saldo > 0;
                        });

                        // Se tem estoque fora da qualidade e a entrada foi há mais de 1 dia,
                        // provavelmente foi transferido - não mostrar como pendente
                        const umDiaAtras = new Date();
                        umDiaAtras.setDate(umDiaAtras.getDate() - 1);

                        if (temEstoqueForaQualidade && dataEntrada < umDiaAtras) {
                            console.log(`Item ${item.codigo} já foi transferido da qualidade - não incluindo no histórico`);
                            return; // Pular este item
                        }
                    }

                    // Tentar encontrar fornecedor através do produto em pedidos recentes
                    let fornecedorInfo = null;
                    let numeroDocumento = 'N/A';

                    if (item.produtoId || item.codigo) {
                        const pedidosComProduto = pedidosCompra.filter(p =>
                            p.itens && p.itens.some(pedidoItem =>
                                pedidoItem.produtoId === item.produtoId ||
                                pedidoItem.codigo === item.codigo
                            )
                        );

                        if (pedidosComProduto.length > 0) {
                            // Pegar o pedido mais recente
                            const pedidoMaisRecente = pedidosComProduto.sort((a, b) => {
                                const dateA = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao);
                                const dateB = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao);
                                return dateB - dateA;
                            })[0];

                            const fornecedor = fornecedores.find(f => f.id === pedidoMaisRecente.fornecedorId);
                            fornecedorInfo = {
                                id: pedidoMaisRecente.fornecedorId,
                                nome: fornecedor?.razaoSocial || pedidoMaisRecente.fornecedorNome
                            };
                            numeroDocumento = pedidoMaisRecente.id;
                        }
                    }

                    historico.push({
                        tipo: 'ENTRADA',
                        tipoDocumento: 'QUALIDADE',
                        numeroDocumento: numeroDocumento,
                        produtoId: item.produtoId,
                        produtoCodigo: item.codigo,
                        produtoDescricao: item.descricao,
                        quantidade: item.quantidade,
                        unidade: item.unidade,
                        dataHora: dataEntrada,
                        usuario: 'Sistema',
                        observacoes: item.origem || 'Entrada para qualidade',
                        fornecedorId: fornecedorInfo?.id,
                        fornecedorNome: fornecedorInfo?.nome,
                        fonte: 'estoqueQualidade'
                    });
                });

                // 3. Detalhes de recebimentos
                const recebimentosDetalhes = recebimentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                recebimentosDetalhes.forEach(rec => {
                    const dataRecebimento = rec.dataRecebimento?.toDate ? rec.dataRecebimento.toDate() : new Date(rec.dataRecebimento);
                    if (dataRecebimento < thirtyDaysAgo) return;

                    historico.push({
                        tipo: 'ENTRADA',
                        tipoDocumento: 'RECEBIMENTO_DETALHADO',
                        numeroDocumento: rec.pedidoId,
                        produtoId: rec.produtoId,
                        produtoCodigo: rec.codigo,
                        produtoDescricao: rec.descricao,
                        quantidade: rec.quantidadeConvertida || rec.quantidadeRecebida,
                        unidade: rec.unidadeConvertida || rec.unidadeRecebida,
                        armazemId: rec.armazemDestino,
                        dataHora: dataRecebimento,
                        usuario: rec.recebidoPor,
                        observacoes: `NF: ${rec.notaFiscal?.numero || 'N/A'}`,
                        fonte: 'recebimentosDetalhes'
                    });
                });

                console.log('Total de registros de histórico encontrados:', historico.length);

                // Ordenar por data (mais recente primeiro) e pegar apenas os últimos 20
                historico.sort((a, b) => b.dataHora - a.dataHora);
                const ultimosRecebimentos = historico.slice(0, 20);

                console.log('Últimos 20 recebimentos:', ultimosRecebimentos);

                if (ultimosRecebimentos.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                <div style="font-size: 18px; margin-bottom: 10px;">📭</div>
                                <div>Nenhum recebimento encontrado nos últimos 30 dias</div>
                                <div style="font-size: 12px; margin-top: 10px; color: #999;">
                                    Verificadas: ${movimentacoes.length} movimentações, ${estoqueQualidade.length} itens de qualidade, ${recebimentosDetalhes.length} detalhes
                                </div>
                            </td>
                        </tr>
                    `;
                    return;
                }

                // Renderizar histórico
                await renderReceiptHistory(ultimosRecebimentos);

                showNotification(`Histórico carregado: ${ultimosRecebimentos.length} recebimentos de ${historico.length} registros`, 'success');

            } catch (error) {
                console.error('Erro ao carregar histórico:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; color: #dc3545; padding: 40px;">
                            ❌ Erro ao carregar histórico: ${error.message}
                        </td>
                    </tr>
                `;
                showNotification('Erro ao carregar histórico: ' + error.message, 'error');
            }
        };

        async function renderReceiptHistory(recebimentos) {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = '';

            // Carregar estoques atuais uma vez só para otimizar performance
            const estoquesSnap = await getDocs(collection(db, "estoques"));
            const estoquesAtuais = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            recebimentos.forEach((mov, index) => {
                console.log(`Renderizando item ${index + 1}:`, mov);

                const produto = produtos.find(p => p.id === mov.produtoId);
                const armazem = armazens.find(a => a.id === mov.armazemId);

                // Extrair número do pedido e fornecedor - pode estar em diferentes campos
                let numeroPedido = 'N/A';
                let fornecedorNome = 'N/A';
                let pedidoRelacionado = null;

                // Tentar diferentes formatos de numeroDocumento
                if (mov.numeroDocumento) {
                    // Se é um ID do pedido (formato UUID)
                    if (mov.numeroDocumento.length > 20) {
                        pedidoRelacionado = pedidosCompra.find(p => p.id === mov.numeroDocumento);
                        if (pedidoRelacionado) {
                            numeroPedido = pedidoRelacionado.numero;
                            const fornecedor = fornecedores.find(f => f.id === pedidoRelacionado.fornecedorId);
                            fornecedorNome = fornecedor?.razaoSocial || pedidoRelacionado.fornecedorNome || 'N/A';
                        }
                    } else {
                        // Se é número da NF ou outro documento
                        numeroPedido = mov.numeroDocumento;
                    }
                }

                // Se tem numeroPedido separado (formato novo)
                if (mov.numeroPedido) {
                    numeroPedido = mov.numeroPedido;
                }

                // Se tem fornecedorNome direto (formato novo)
                if (mov.fornecedorNome) {
                    fornecedorNome = mov.fornecedorNome;
                }

                // Para itens de estoqueQualidade, tentar encontrar fornecedor através do produto
                if (mov.fonte === 'estoqueQualidade' && fornecedorNome === 'N/A') {
                    // Buscar pedidos recentes que contenham este produto
                    const pedidosComProduto = pedidosCompra.filter(p =>
                        p.itens && p.itens.some(item =>
                            item.produtoId === mov.produtoId ||
                            item.codigo === mov.produtoCodigo
                        )
                    );

                    if (pedidosComProduto.length > 0) {
                        // Pegar o pedido mais recente
                        const pedidoMaisRecente = pedidosComProduto.sort((a, b) => {
                            const dateA = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao);
                            const dateB = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao);
                            return dateB - dateA;
                        })[0];

                        const fornecedor = fornecedores.find(f => f.id === pedidoMaisRecente.fornecedorId);
                        fornecedorNome = fornecedor?.razaoSocial || pedidoMaisRecente.fornecedorNome || 'N/A';

                        if (numeroPedido === 'N/A') {
                            numeroPedido = pedidoMaisRecente.numero;
                        }
                    }
                }

                // Para recebimentosDetalhes, buscar fornecedor pelo pedido
                if (mov.fonte === 'recebimentosDetalhes' && fornecedorNome === 'N/A' && mov.numeroDocumento) {
                    const pedido = pedidosCompra.find(p => p.id === mov.numeroDocumento);
                    if (pedido) {
                        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                        fornecedorNome = fornecedor?.razaoSocial || pedido.fornecedorNome || 'N/A';
                    }
                }

                const dataFormatada = mov.dataHora.toLocaleString('pt-BR');

                // Determinar cor do badge baseado no tipo de documento e fonte
                let badgeClass = 'ok';
                let badgeText = numeroPedido;

                // Verificar status atual do item para mostrar informação mais precisa
                let statusAtual = '';
                if (mov.produtoId) {
                    const produto = produtos.find(p => p.id === mov.produtoId);
                    if (produto) {
                        // Buscar onde o produto está atualmente usando os estoques já carregados
                        const estoquesDoItem = estoquesAtuais.filter(e => e.produtoId === produto.id && e.saldo > 0);

                        if (estoquesDoItem.length > 0) {
                            const localizacoes = estoquesDoItem.map(e => {
                                const armazem = armazens.find(a => a.id === e.armazemId);
                                return armazem ? armazem.codigo : 'N/A';
                            }).join(', ');
                            statusAtual = ` → Atual: ${localizacoes}`;
                        }
                    }
                }

                if (mov.tipoDocumento === 'QUALIDADE') {
                    badgeClass = 'warning';
                    badgeText = `${numeroPedido} (QUALIDADE)${statusAtual}`;
                } else if (mov.tipoDocumento === 'APROVACAO_QUALIDADE') {
                    badgeClass = 'approved';
                    badgeText = `${numeroPedido} (APROVADO)${statusAtual}`;
                } else if (mov.fonte === 'estoqueQualidade') {
                    badgeClass = 'warning';
                    badgeText = `${numeroPedido} (INSPEÇÃO)${statusAtual}`;
                } else if (mov.fonte === 'recebimentosDetalhes') {
                    badgeClass = 'ok';
                    badgeText = `${numeroPedido} (RECEBIDO)${statusAtual}`;
                }

                // Código do armazém removido - coluna não será exibida

                // DEBUG: Descobrir de onde vem o armazém destino
                let armazemDestinoInfo = 'N/A';
                if (mov.armazemId) {
                    const armazemEncontrado = armazens.find(a => a.id === mov.armazemId);
                    if (armazemEncontrado) {
                        armazemDestinoInfo = `${armazemEncontrado.codigo || armazemEncontrado.nome} - ${armazemEncontrado.nome || armazemEncontrado.descricao || ''}`;

                        // Debug específico para produto 106142
                        if (produto?.codigo === '106142' || mov.produtoCodigo === '106142') {
                            console.log('🏪 PRODUTO 106142 - Armazém encontrado:', {
                                armazemId: mov.armazemId,
                                armazem: armazemEncontrado,
                                textoFinal: armazemDestinoInfo
                            });
                        }
                    } else {
                        armazemDestinoInfo = `ID: ${mov.armazemId} (não encontrado)`;
                    }
                } else if (mov.armazemDestino) {
                    armazemDestinoInfo = `Destino: ${mov.armazemDestino}`;
                } else if (produto?.armazemPadraoId) {
                    const armazemPadrao = armazens.find(a => a.id === produto.armazemPadraoId);
                    armazemDestinoInfo = armazemPadrao ? `Padrão: ${armazemPadrao.codigo}` : 'Padrão não encontrado';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-size: 12px;">${dataFormatada}</td>
                    <td>
                        <span class="status-badge ${badgeClass}" style="font-size: 11px;">
                            ${badgeText.length > 25 ? badgeText.substring(0, 25) + '...' : badgeText}
                        </span>
                        <div style="font-size: 10px; color: #666; margin-top: 2px;">
                            ${mov.fonte || 'movimentacoes'}
                        </div>
                    </td>
                    <td><strong>${produto?.codigo || mov.produtoCodigo || 'N/A'}</strong></td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${produto?.descricao || mov.produtoDescricao || 'Produto não encontrado'}">
                        ${produto?.descricao || mov.produtoDescricao || 'Produto não encontrado'}
                    </td>
                    <td style="text-align: center;">
                        <strong>${(mov.quantidade || 0).toFixed(3)}</strong> ${mov.unidade || produto?.unidade || 'UN'}
                    </td>
                    <td style="font-size: 11px; max-width: 150px;" title="${armazemDestinoInfo}">
                        ${armazemDestinoInfo.length > 20 ? armazemDestinoInfo.substring(0, 20) + '...' : armazemDestinoInfo}
                    </td>
                    <td style="font-size: 12px;">${fornecedorNome}</td>
                    <td style="font-size: 12px;">${mov.usuario || mov.recebidoPor || 'N/A'}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        window.clearReceiptHistory = function() {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
                        <div>Clique em "Carregar Histórico" para visualizar os últimos recebimentos</div>
                    </td>
                </tr>
            `;
        };

        function populateOrderSelect() {
            const select = document.getElementById('orderSelect');
            select.innerHTML = '<option value="">Selecione o pedido...</option>';

            console.log('=== DEBUG POPULATE ORDER SELECT ===');
            console.log('Total pedidos:', pedidosCompra.length);

            // Analisar cada pedido
            pedidosCompra.forEach((p, index) => {
                console.log(`Pedido ${index + 1}:`, {
                    id: p.id,
                    numero: p.numero,
                    status: p.status,
                    temItens: !!p.itens,
                    quantidadeItens: p.itens?.length || 0,
                    itensComSaldo: p.itens ? p.itens.filter(item => (item.quantidadeRecebida || 0) < item.quantidade).length : 0
                });
            });

            // Filtrar pedidos aprovados ou pendentes que ainda têm itens pendentes
            const availableOrders = pedidosCompra.filter(p => {
                const statusOk = (p.status === 'APROVADO' || p.status === 'PENDENTE');
                const temItens = p.itens && p.itens.length > 0;
                const temItensPendentes = p.itens && p.itens.some(item => (item.quantidadeRecebida || 0) < item.quantidade);

                console.log(`Pedido ${p.numero}:`, {
                    statusOk,
                    temItens,
                    temItensPendentes,
                    incluido: statusOk && temItens && temItensPendentes
                });

                return statusOk && temItens && temItensPendentes;
            });

            // Ordenar por prioridade (atrasados primeiro, depois por número)
            availableOrders.sort((a, b) => {
                const aDelayed = isOrderDelayed(a);
                const bDelayed = isOrderDelayed(b);

                if (aDelayed && !bDelayed) return -1;
                if (!aDelayed && bDelayed) return 1;

                return (b.numero || 0) - (a.numero || 0);
            });

            availableOrders.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const isDelayed = isOrderDelayed(pedido);
                const isPending = pedido.status === 'PENDENTE';

                const option = document.createElement('option');
                option.value = pedido.id;

                let prefix = '';
                if (isDelayed) prefix += '⚠️ ';
                if (isPending) prefix += '📋 ';

                option.textContent = `${prefix}${pedido.numero || 'N/A'} - ${fornecedor?.razaoSocial || pedido.fornecedorNome || 'Fornecedor N/A'}${isPending ? ' (PENDENTE APROVAÇÃO)' : ''}`;

                if (isDelayed) {
                    option.style.color = '#e74c3c';
                    option.style.fontWeight = 'bold';
                } else if (isPending) {
                    option.style.color = '#f39c12';
                    option.style.fontWeight = 'bold';
                }

                select.appendChild(option);
            });

            console.log('=== RESULTADO FINAL ===');
            console.log('Pedidos disponíveis encontrados:', availableOrders.length);
            console.log('Pedidos adicionados ao select:', availableOrders.map(p => `${p.numero} (${p.status})`));
        }

        function isOrderDelayed(order) {
            if (!order.dataEntregaPrevista) return false;

            const today = new Date();
            const deliveryDate = order.dataEntregaPrevista.toDate ?
                order.dataEntregaPrevista.toDate() :
                new Date(order.dataEntregaPrevista);

            const diffDays = Math.ceil((today - deliveryDate) / (1000 * 60 * 60 * 24));
            return diffDays >= systemConfig.diasAlerteAtraso;
        }

        function populateWarehouseSelect() {
            const select = document.getElementById('warehouseSelect');
            const hint = document.getElementById('warehouseHint');

            select.innerHTML = '<option value="">Selecione o armazém...</option>';

            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.descricao}`;
                select.appendChild(option);
            });

            // Definir armazém padrão baseado na configuração
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                // Procurar armazém da qualidade
                const qualityWarehouse = armazens.find(a =>
                    a.tipo === 'QUALIDADE' ||
                    a.codigo?.toUpperCase().includes('QUALIDADE') ||
                    a.descricao?.toUpperCase().includes('QUALIDADE')
                );

                if (qualityWarehouse) {
                    select.value = qualityWarehouse.id;
                    hint.textContent = 'Armazém da qualidade selecionado automaticamente';
                    hint.style.color = '#f39c12';
                } else {
                    hint.textContent = 'Atenção: Armazém da qualidade não encontrado';
                    hint.style.color = '#e74c3c';
                }
            } else if (currentOrder && currentOrder.itens && currentOrder.itens.length > 0) {
                // Usar armazém padrão do primeiro produto do pedido
                const firstProduct = produtos.find(p => p.id === currentOrder.itens[0].produtoId);
                if (firstProduct && firstProduct.armazemPadraoId) {
                    const defaultWarehouse = armazens.find(a => a.id === firstProduct.armazemPadraoId);
                    if (defaultWarehouse) {
                        select.value = firstProduct.armazemPadraoId;
                        hint.textContent = `Armazém padrão do produto selecionado: ${defaultWarehouse.codigo}`;
                        hint.style.color = '#27ae60';
                    }
                }
            }
        }

        // ===== CARREGAMENTO DE DETALHES DO PEDIDO =====
        window.loadOrderDetails = async function() {
            const orderId = document.getElementById('orderSelect').value;

            if (!orderId) {
                document.getElementById('receiptSection').style.display = 'none';
                document.getElementById('itemsSection').style.display = 'none';
                document.getElementById('supplierDetails').style.display = 'none';
                return;
            }

            try {
                currentOrder = pedidosCompra.find(p => p.id === orderId);

                if (!currentOrder) {
                    showNotification('Pedido não encontrado', 'error');
                    return;
                }

                // Carregar informações do fornecedor
                await loadSupplierInfo();

                // Mostrar seções
                document.getElementById('supplierDetails').style.display = 'block';
                document.getElementById('receiptSection').style.display = 'block';
                document.getElementById('itemsSection').style.display = 'block';

                // Carregar itens
                loadOrderItems();

                showNotification('Pedido carregado com sucesso', 'success');

            } catch (error) {
                console.error('Erro ao carregar pedido:', error);
                showNotification('Erro ao carregar pedido: ' + error.message, 'error');
            }
        };

        async function loadSupplierInfo() {
            const fornecedor = fornecedores.find(f => f.id === currentOrder.fornecedorId);
            const isDelayed = isOrderDelayed(currentOrder);
            const isPending = currentOrder.status === 'PENDENTE';

            // Mostrar alerta de status se necessário
            const statusAlert = document.getElementById('orderStatusAlert');
            if (isPending) {
                statusAlert.style.display = 'block';
                statusAlert.innerHTML = `
                    <div class="config-alert" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <strong>Pedido Pendente de Aprovação:</strong>
                            Este pedido ainda não foi aprovado. Você pode aprovar agora para prosseguir com o recebimento.
                            <br>
                            <button class="btn btn-success" onclick="approveOrderForReceipt()" style="margin-top: 10px;">
                                <i class="fas fa-check"></i> Aprovar Pedido
                            </button>
                        </div>
                    </div>
                `;
            } else {
                statusAlert.style.display = 'none';
            }

            // Informações básicas do fornecedor
            document.getElementById('supplierName').value = fornecedor?.razaoSocial || currentOrder.fornecedorNome || 'N/A';
            document.getElementById('supplierCnpj').value = fornecedor?.cnpj || 'N/A';
            document.getElementById('supplierContact').value = fornecedor?.telefone || fornecedor?.email || 'N/A';

            // Informações do pedido
            document.getElementById('orderRequester').value = currentOrder.solicitante || currentOrder.criadoPor || 'N/A';

            // Datas
            const orderDate = currentOrder.dataPedido?.toDate ?
                currentOrder.dataPedido.toDate() :
                new Date(currentOrder.dataPedido || Date.now());
            const deliveryDate = currentOrder.dataEntregaPrevista?.toDate ?
                currentOrder.dataEntregaPrevista.toDate() :
                new Date(currentOrder.dataEntregaPrevista || Date.now());

            document.getElementById('orderDate').value = formatDate(orderDate);
            document.getElementById('deliveryDate').value = formatDate(deliveryDate);

            // Status do prazo
            const statusElement = document.getElementById('deliveryStatus');
            if (isDelayed) {
                const diffDays = Math.ceil((new Date() - deliveryDate) / (1000 * 60 * 60 * 24));
                statusElement.innerHTML = `<span class="status-badge delayed">${diffDays} dias em atraso</span>`;
            } else {
                const diffDays = Math.ceil((deliveryDate - new Date()) / (1000 * 60 * 60 * 24));
                if (diffDays <= systemConfig.diasAlerteAtraso) {
                    statusElement.innerHTML = `<span class="status-badge warning">Vence em ${diffDays} dias</span>`;
                } else {
                    statusElement.innerHTML = `<span class="status-badge ok">No prazo</span>`;
                }
            }
        }

        function formatDate(date) {
            return date.toLocaleDateString('pt-BR');
        }

        function loadOrderItems() {
            const tbody = document.getElementById('itemsTableBody');
            tbody.innerHTML = '';

            if (!currentOrder.itens) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">Nenhum item encontrado</td></tr>';
                return;
            }

            currentOrder.itens.forEach((item, index) => {
                const produto = produtos.find(p => p.id === item.produtoId);
                const quantidadeRecebida = item.quantidadeRecebida || 0;
                const saldoPendente = item.quantidade - quantidadeRecebida;

                // Mostrar todos os itens se permitir recebimento parcial, senão só os pendentes
                if (!systemConfig.permitirRecebimentoParcial && saldoPendente <= 0) return;

                const destinationInfo = getItemDestination(produto);
                const maxQuantity = systemConfig.exigirAutorizacaoExcesso ?
                    saldoPendente :
                    saldoPendente * (1 + systemConfig.toleranciaRecebimento / 100);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${produto?.codigo || 'N/A'}</td>
                    <td>${produto?.descricao || item.descricao || 'N/A'}</td>
                    <td>${item.quantidade}</td>
                    <td>${quantidadeRecebida}</td>
                    <td><strong>${Math.max(0, saldoPendente)}</strong></td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="qty_${index}"
                               max="${maxQuantity}"
                               min="0"
                               step="0.01"
                               value="${Math.max(0, saldoPendente)}"
                               style="width: 100px;"
                               onchange="validateQuantity(${index}, this.value)"
                               onkeyup="validateQuantity(${index}, this.value)">
                    </td>
                    <td>R$ ${(item.valorUnitario || 0).toFixed(2)}</td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="price_${index}"
                               step="0.01"
                               value="${(item.valorUnitario || 0).toFixed(2)}"
                               style="width: 100px;"
                               onchange="validatePrice(${index}, this.value)"
                               onkeyup="validatePrice(${index}, this.value)">
                    </td>
                    <td>
                        <span class="status-badge ${destinationInfo.class}">
                            ${destinationInfo.text}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge pending" id="status_${index}">PENDENTE</span>
                    </td>
                `;
                tbody.appendChild(row);

                // Validar preço inicial
                validatePrice(index, item.valorUnitario || 0);
            });
        }

        function getItemDestination(produto) {
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                // Verificar se o produto precisa de inspeção
                const needsInspection = checkIfNeedsInspection(produto);

                if (needsInspection) {
                    return {
                        text: 'QUALIDADE',
                        class: 'quality'
                    };
                }
            }

            // Usar armazém padrão do produto
            const armazemPadrao = armazens.find(a => a.id === produto?.armazemPadraoId);
            const armazemTexto = armazemPadrao ? `${armazemPadrao.codigo}` : 'ESTOQUE';

            return {
                text: armazemTexto,
                class: 'stock'
            };
        }

        function checkIfNeedsInspection(produto) {
            if (!produto) return false;

            switch (systemConfig.inspecaoRecebimento) {
                case 'todos':
                    return true;
                case 'criticos':
                    return produto.critico === true || produto.tipo === 'CRITICO';
                case 'manual':
                    return produto.inspecaoObrigatoria === true;
                default:
                    return false;
            }
        }

        // ===== VALIDAÇÕES =====
        window.validateQuantity = function(index, quantity) {
            const item = currentOrder.itens[index];
            const quantidadeRecebida = item.quantidadeRecebida || 0;
            const saldoPendente = item.quantidade - quantidadeRecebida;
            const qtyInput = document.getElementById(`qty_${index}`);
            const statusElement = document.getElementById(`status_${index}`);

            const qty = parseFloat(quantity) || 0;
            const tolerance = (saldoPendente * systemConfig.toleranciaRecebimento / 100);

            // Resetar classes
            qtyInput.classList.remove('error', 'warning');

            if (qty > saldoPendente) {
                const excess = qty - saldoPendente;
                const excessPercent = saldoPendente > 0 ? (excess / saldoPendente) * 100 : 0;

                if (excessPercent > systemConfig.toleranciaRecebimento) {
                    if (systemConfig.exigirAutorizacaoExcesso) {
                        qtyInput.classList.add('error');
                        statusElement.innerHTML = '<span class="status-badge delayed">EXCESSO - AUTORIZAÇÃO</span>';
                        return false;
                    } else {
                        qtyInput.classList.add('warning');
                        statusElement.innerHTML = '<span class="status-badge warning">EXCESSO</span>';
                    }
                } else {
                    qtyInput.classList.add('warning');
                    statusElement.innerHTML = '<span class="status-badge warning">TOLERÂNCIA</span>';
                }
            } else if (qty < saldoPendente && !systemConfig.permitirRecebimentoParcial) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge delayed">PARCIAL BLOQUEADO</span>';
                return false;
            } else if (qty > 0) {
                statusElement.innerHTML = '<span class="status-badge ok">OK</span>';
            } else {
                statusElement.innerHTML = '<span class="status-badge pending">PENDENTE</span>';
            }

            return true;
        };

        window.validatePrice = function(index, price) {
            const item = currentOrder.itens[index];
            const priceInput = document.getElementById(`price_${index}`);
            const statusElement = document.getElementById(`status_${index}`);

            const currentPrice = parseFloat(price) || 0;
            const originalPrice = item.valorUnitario || 0;
            const priceDiff = Math.abs(currentPrice - originalPrice);
            const pricePercent = originalPrice > 0 ? (priceDiff / originalPrice) * 100 : 0;

            // Resetar classes
            priceInput.classList.remove('error', 'warning');

            if (pricePercent > systemConfig.toleranciaPreco) {
                if (systemConfig.bloquearPrecoDivergente) {
                    priceInput.classList.add('error');
                    if (!statusElement.innerHTML.includes('EXCESSO')) {
                        statusElement.innerHTML = '<span class="status-badge delayed">PREÇO DIVERGENTE</span>';
                    }
                    return false;
                } else {
                    priceInput.classList.add('warning');
                    if (!statusElement.innerHTML.includes('EXCESSO') && !statusElement.innerHTML.includes('OK')) {
                        statusElement.innerHTML = '<span class="status-badge warning">PREÇO ALTERADO</span>';
                    }
                }
            }

            return true;
        };

        // ===== PROCESSAMENTO DO RECEBIMENTO =====
        window.processReceipt = async function() {
            try {
                // Validar dados obrigatórios
                if (!validateReceiptData()) {
                    return;
                }

                showNotification('Processando recebimento...', 'info');

                // Coletar dados do recebimento
                const receiptData = collectReceiptData();

                // Processar cada item recebido
                await processReceiptItems(receiptData);

                showNotification('Recebimento processado com sucesso!', 'success');

                // Limpar formulário e recarregar
                resetForm();
                await loadInitialData();
                setupInterface();

            } catch (error) {
                console.error('Erro ao processar recebimento:', error);
                showNotification('Erro ao processar recebimento: ' + error.message, 'error');
            }
        };

        async function processReceiptItems(receiptData) {
            const resultados = [];
            const batch = writeBatch(db);
            const itensParaQualidade = [];
            const itensParaEstoque = [];

            try {
                // Calcular rateio do frete se necessário
                const freightDistribution = calculateFreightForItems(receiptData);

                // Processar cada item
                for (let index = 0; index < currentOrder.itens.length; index++) {
                    const item = currentOrder.itens[index];
                    const qtyInput = document.getElementById(`qty_${index}`);
                    const priceInput = document.getElementById(`price_${index}`);
                    const loteFornecedorInput = document.getElementById(`lote_${index}`);

                    if (!qtyInput || parseFloat(qtyInput.value) <= 0) continue;

                    const quantidadeRecebida = parseFloat(qtyInput.value);
                    const valorUnitarioBase = parseFloat(priceInput.value) || 0;
                    const produto = produtos.find(p => p.id === item.produtoId);

                    if (!produto) {
                        console.error(`Produto não encontrado para o item: ${item.descricao || item.codigo}`);
                        continue;
                    }

                    // Calcular custo unitário final (incluindo frete se configurado)
                    const freteUnitario = freightDistribution[item.codigo] || 0;
                    const valorUnitarioFinal = valorUnitarioBase + freteUnitario;

                    // Atualizar quantidade recebida no item do pedido
                    item.quantidadeRecebida = (item.quantidadeRecebida || 0) + quantidadeRecebida;

                    // Preparar dados para o MaterialEntryService
                    const entryData = {
                        produtoId: item.produtoId,
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        quantidade: quantidadeRecebida,
                        unidade: produto.unidade || 'UN',
                        armazemId: produto.armazemPadrao || armazens.find(a => a.ativo !== false)?.id,
                        numeroNF: receiptData.numeroNF,
                        chaveNF: receiptData.chaveNF,
                        loteFornecedor: loteFornecedorInput?.value || `LOTE-${Date.now()}`,
                        loteInterno: `INT-${Date.now()}-${index}`,
                        recebimentoId: `REC-${Date.now()}-${index}`,
                        pedidoId: currentOrder.id,
                        centroCusto: document.getElementById('costCenterSelect')?.value || 'DEFAULT',
                        recebidoPor: currentUser.nome,
                        valorUnitario: valorUnitarioFinal,
                        freteUnitario: freteUnitario
                    };

                    try {
                        // Usar MaterialEntryService para processar a entrada
                        const resultado = await MaterialEntryService.processEntry(entryData);
                        
                        // Registrar o resultado
                        resultados.push({
                            ...resultado,
                            produtoCodigo: produto.codigo,
                            produtoDescricao: produto.descricao,
                            quantidade: quantidadeRecebida,
                            unidade: produto.unidade
                        });

                        // Separar itens por destino
                        if (resultado.type === 'QUALITY_INSPECTION') {
                            itensParaQualidade.push({
                                ...item,
                                quantidadeRecebida,
                                qualidadeId: resultado.qualidadeId,
                                motivoInspecao: resultado.reason
                            });
                        } else {
                            itensParaEstoque.push({
                                ...item,
                                quantidadeRecebida
                            });
                        }
                    } catch (error) {
                        console.error(`Erro ao processar item ${produto.codigo}:`, error);
                        resultados.push({
                            success: false,
                            produtoCodigo: produto.codigo,
                            produtoDescricao: produto.descricao,
                            error: error.message
                        });
                    }
                }

                // Atualizar status do pedido com base nos itens processados
                const todosItensRecebidos = currentOrder.itens.every(item => {
                    return (item.quantidadeRecebida || 0) >= item.quantidade;
                });

                // Usar status compatível com pedidos_compra.html
                const novoStatus = todosItensRecebidos ? 'RECEBIDO' : 'PARCIALMENTE_RECEBIDO';

                // Calcular valor total recebido
                const valorTotalRecebido = receiptData.itens.reduce((total, item) => {
                    return total + (item.precoUnitario * item.quantidade);
                }, 0);

                // Atualizar pedido no banco de dados
                const pedidoRef = doc(db, "pedidosCompra", currentOrder.id);
                batch.update(pedidoRef, {
                    status: novoStatus,
                    dataRecebimento: Timestamp.now(),
                    recebidoPor: currentUser.nome,
                    itens: currentOrder.itens,
                    ultimoRecebimento: {
                        data: Timestamp.now(),
                        numeroNF: receiptData.numeroNF,
                        valorRecebido: valorTotalRecebido,
                        tipo: todosItensRecebidos ? 'COMPLETO' : 'PARCIAL'
                    },
                    historico: [
                        ...(currentOrder.historico || []),
                        {
                            data: Timestamp.now(),
                            acao: todosItensRecebidos ? 'RECEBIMENTO_COMPLETO' : 'RECEBIMENTO_PARCIAL',
                            usuario: currentUser.nome,
                            detalhes: `NF: ${receiptData.numeroNF} - Valor: R$ ${valorTotalRecebido.toFixed(2)}`
                        }
                    ]
                });

                // Executar todas as operações em lote
                await batch.commit();

                // Se houver itens para qualidade, redirecionar para a tela de inspeção
                if (itensParaQualidade.length > 0) {
                    // Armazenar itens para qualidade no sessionStorage para a próxima tela
                    sessionStorage.setItem('itensParaQualidade', JSON.stringify(itensParaQualidade));
                    sessionStorage.setItem('pedidoId', currentOrder.id);
                    
                    // Mostrar mensagem de redirecionamento mais informativa
                    const redirectMessage = `
                        <div class="redirect-overlay">
                            <div class="redirect-content">
                                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                                    <span class="visually-hidden">Processando...</span>
                                </div>
                                <h3>Processamento Concluído com Sucesso!</h3>
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    ${itensParaQualidade.length} item(s) foram enviados para inspeção de qualidade.
                                </div>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Você será redirecionado em <span id="countdown">3</span> segundos...
                                </div>
                                <div class="mt-4">
                                    <a href="inspecao_qualidade.html" class="btn btn-primary me-2">
                                        <i class="fas fa-tasks me-2"></i>Ir para Inspeção Agora
                                    </a>
                                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo me-2"></i>Voltar ao Início
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Adicionar a mensagem ao corpo do documento
                    document.body.insertAdjacentHTML('beforeend', redirectMessage);
                    
                    // Iniciar contagem regressiva
                    let seconds = 3;
                    const countdownElement = document.getElementById('countdown');
                    const countdownInterval = setInterval(() => {
                        seconds--;
                        if (countdownElement) {
                            countdownElement.textContent = seconds;
                        }
                        if (seconds <= 0) {
                            clearInterval(countdownInterval);
                        }
                    }, 1000);
                    
                    // Redirecionar após 3 segundos
                    setTimeout(() => {
                        window.location.href = 'inspecao_qualidade.html';
                    }, 3000);
                    
                    return { redirecionar: true };
                }

                return { 
                    success: true, 
                    resultados,
                    itensParaQualidade,
                    itensParaEstoque
                };
            } catch (error) {
                console.error('Erro ao processar recebimento:', error);
                showNotification('Erro ao processar recebimento: ' + error.message, 'error');
                throw error;
            }
        }

        // A função updatePurchaseOrder foi movida para dentro de processReceiptItems
        // para garantir uma transação atômica com o MaterialEntryService

        function validateReceiptData() {
            // Verificar se o pedido está aprovado
            if (currentOrder && currentOrder.status === 'PENDENTE') {
                showNotification('Este pedido precisa ser aprovado antes do recebimento. Clique em "Aprovar Pedido" acima.', 'warning');
                return false;
            }

            // Validar dados básicos do recebimento
            const numeroNF = document.getElementById('invoiceNumber').value.trim();
            const serieNF = document.getElementById('invoiceSeries').value.trim();
            const dataNF = document.getElementById('invoiceDate').value;
            const valorNF = parseFloat(document.getElementById('invoiceValue').value) || 0;
            
            if (!numeroNF) {
                showNotification('O número da nota fiscal é obrigatório', 'error');
                document.getElementById('invoiceNumber').focus();
                return false;
            }
            
            if (!serieNF) {
                showNotification('A série da nota fiscal é obrigatória', 'error');
                document.getElementById('invoiceSeries').focus();
                return false;
            }
            
            if (!dataNF) {
                showNotification('A data da nota fiscal é obrigatória', 'error');
                document.getElementById('invoiceDate').focus();
                return false;
            }
            
            if (valorNF <= 0) {
                showNotification('O valor da nota fiscal deve ser maior que zero', 'error');
                document.getElementById('invoiceValue').focus();
                return false;
            }
            
            // Validar frete se for CIF
            const freightType = document.getElementById('freightType').value;
            const freightValue = parseFloat(document.getElementById('freightValue').value) || 0;
            
            if (freightType === 'CIF' && freightValue <= 0) {
                showNotification('O valor do frete é obrigatório para frete CIF', 'error');
                document.getElementById('freightValue').focus();
                return false;
            }
            
            // Validar se há pelo menos um item com quantidade maior que zero
            let hasValidItems = false;
            if (currentOrder && currentOrder.itens) {
                currentOrder.itens.forEach((item, index) => {
                    const qtyInput = document.getElementById(`qty_${index}`);
                    if (qtyInput && parseFloat(qtyInput.value) > 0) {
                        hasValidItems = true;
                    }
                });
            }
            
            if (!hasValidItems) {
                showNotification('É necessário informar a quantidade recebida para pelo menos um item', 'error');
                return false;
            }
            
            // Validar se há itens que excedem a quantidade pendente
            if (currentOrder && currentOrder.itens) {
                for (let i = 0; i < currentOrder.itens.length; i++) {
                    const item = currentOrder.itens[i];
                    const qtyInput = document.getElementById(`qty_${i}`);
                    
                    if (qtyInput) {
                        const qty = parseFloat(qtyInput.value) || 0;
                        const quantidadePendente = item.quantidade - (item.quantidadeRecebida || 0);
                        
                        if (qty > quantidadePendente && !systemConfig.permitirRecebimentoParcial) {
                            showNotification(`A quantidade informada para o item ${item.codigo || item.descricao} excede a quantidade pendente (${quantidadePendente})`, 'error');
                            qtyInput.focus();
                            return false;
                        }
                    }
                }
            }
            
            // Validar lotes e validades se necessário
            if (systemConfig.obrigarLoteRecebimento || systemConfig.obrigarValidadeRecebimento) {
                for (let i = 0; i < currentOrder.itens.length; i++) {
                    const item = currentOrder.itens[i];
                    const qtyInput = document.getElementById(`qty_${i}`);
                    
                    if (qtyInput && parseFloat(qtyInput.value) > 0) {
                        if (systemConfig.obrigarLoteRecebimento) {
                            const loteInput = document.getElementById(`lote_${i}`);
                            if (!loteInput || !loteInput.value.trim()) {
                                showNotification(`O número do lote é obrigatório para o item ${item.codigo || item.descricao}`, 'error');
                                loteInput.focus();
                                return false;
                            }
                        }
                        
                        if (systemConfig.obrigarValidadeRecebimento) {
                            const validadeInput = document.getElementById(`validade_${i}`);
                            if (!validadeInput || !validadeInput.value) {
                                showNotification(`A data de validade é obrigatória para o item ${item.codigo || item.descricao}`, 'error');
                                validadeInput.focus();
                                return false;
                            }
                        }
                    }
                }
            }
            
            return true;
        }

        async function processReceipt() {
            // 1. Validar dados básicos primeiro
            if (!validateReceiptData()) {
                return;
            }

            const btnProcessar = document.querySelector('button[onclick="processReceipt()"]');
            const btnOriginalText = btnProcessar.innerHTML;

            try {
                // 2. Recarregar configurações do sistema
                btnProcessar.disabled = true;
                btnProcessar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Carregando configurações...';

                await loadSystemConfiguration();

                // 3. Determinar fluxo baseado na configuração de qualidade
                const qualityControlActive = systemConfig.controleQualidade;

                console.log('🔍 Configuração de qualidade:', {
                    controleQualidade: qualityControlActive,
                    inspecaoRecebimento: systemConfig.inspecaoRecebimento,
                    configuracaoCompleta: systemConfig
                });

                // 4. Mostrar confirmação com informações do fluxo
                const confirmationMessage = generateConfirmationMessage(qualityControlActive);

                if (!confirm(confirmationMessage)) {
                    return; // Usuário cancelou
                }

                // 5. Processar conforme configuração
                btnProcessar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';

                if (qualityControlActive) {
                    // Fluxo com controle de qualidade
                    await processReceivingWithQuality();
                } else {
                    // Fluxo direto para estoque
                    await processReceivingDirectToStock();
                }

            } catch (error) {
                console.error('Erro ao processar recebimento:', error);
                showNotification('Erro ao processar recebimento: ' + (error.message || 'Erro desconhecido'), 'error');
            } finally {
                // Reativar botão
                btnProcessar.disabled = false;
                btnProcessar.innerHTML = btnOriginalText;
            }
        }

        // ===== FUNÇÕES DE CONFIRMAÇÃO E FLUXO =====

        function generateConfirmationMessage(qualityControlActive) {
            const orderInfo = `Pedido: ${currentOrder.numero || currentOrder.id}
Fornecedor: ${currentOrder.fornecedorNome || 'N/A'}
Valor Total: R$ ${(currentOrder.valorTotal || 0).toFixed(2)}`;

            if (qualityControlActive) {
                return `🔍 CONFIRMAÇÃO DE RECEBIMENTO COM CONTROLE DE QUALIDADE

${orderInfo}

⚠️ ATENÇÃO: O módulo de Controle de Qualidade está ATIVO!

📋 Fluxo que será executado:
1. ✅ Registro do recebimento
2. 🧪 Materiais serão enviados para INSPEÇÃO DE QUALIDADE
3. ⏳ Aguardarão aprovação antes de entrar no estoque
4. 📊 Será criada tela de inspeção de recebimento

🎯 Tipo de inspeção configurado: ${getInspectionTypeText(systemConfig.inspecaoRecebimento)}

Confirma o recebimento com controle de qualidade?`;
            } else {
                return `📦 CONFIRMAÇÃO DE RECEBIMENTO DIRETO

${orderInfo}

✅ O módulo de Controle de Qualidade está INATIVO!

📋 Fluxo que será executado:
1. ✅ Registro do recebimento
2. 📦 Materiais irão DIRETAMENTE para o estoque
3. ✅ Disponibilização imediata para uso
4. 📊 Atualização automática de saldos

Confirma o recebimento direto para estoque?`;
            }
        }



        // ===== PROCESSAMENTO COM CONTROLE DE QUALIDADE =====

        async function processReceivingWithQuality() {
            try {
                showNotification('🧪 Processando recebimento com controle de qualidade...', 'info');

                // Coletar dados do recebimento
                const receiptData = collectReceiptData();
                receiptData.comControleQualidade = true;

                // Processar recebimento
                const resultado = await processReceiptItems(receiptData);

                showNotification('✅ Recebimento processado! Materiais enviados para inspeção de qualidade.', 'success');

                // Atualizar interface
                await loadInitialData();
                setupInterface();

                // Perguntar se quer abrir tela de inspeção
                setTimeout(() => {
                    if (confirm('🧪 Deseja abrir a tela de Inspeção de Recebimento para processar os materiais?\n\n(A tela será criada se não existir)')) {
                        // Tentar abrir tela de inspeção
                        const inspecaoWindow = window.open('inspecao_recebimento.html', '_blank');

                        // Se não conseguir abrir, mostrar aviso
                        setTimeout(() => {
                            if (!inspecaoWindow || inspecaoWindow.closed) {
                                alert('⚠️ Tela de inspeção não encontrada.\n\nOs materiais estão aguardando inspeção no módulo de Controle de Qualidade.\n\nAcesse: Qualidade > Inspeção de Recebimento');
                            }
                        }, 1000);
                    }
                }, 500);

            } catch (error) {
                console.error('Erro no processamento com qualidade:', error);
                throw error;
            }
        }

        // ===== PROCESSAMENTO DIRETO PARA ESTOQUE =====

        async function processReceivingDirectToStock() {
            try {
                showNotification('📦 Processando recebimento direto para estoque...', 'info');

                // Coletar dados do recebimento
                const receiptData = collectReceiptData();
                receiptData.comControleQualidade = false;

                // Processar recebimento
                const resultado = await processReceiptItems(receiptData);

                showNotification('✅ Recebimento processado! Materiais disponíveis no estoque.', 'success');

                // Atualizar interface
                await loadInitialData();
                setupInterface();

                // Mostrar resumo do recebimento
                setTimeout(() => {
                    showReceivingSummary(receiptData, resultado);
                }, 500);

            } catch (error) {
                console.error('Erro no processamento direto:', error);
                throw error;
            }
        }

        // ===== RESUMO DO RECEBIMENTO =====

        function showReceivingSummary(receiptData, resultado) {
            const itensProcessados = receiptData.itens.length;
            const valorTotal = receiptData.valorNF || 0;

            let resumo = `📦 RESUMO DO RECEBIMENTO CONCLUÍDO\n`;
            resumo += `${'='.repeat(50)}\n\n`;
            resumo += `📄 Nota Fiscal: ${receiptData.numeroNF}\n`;
            resumo += `📅 Data: ${new Date().toLocaleDateString('pt-BR')}\n`;
            resumo += `💰 Valor: R$ ${valorTotal.toFixed(2)}\n`;
            resumo += `📦 Itens processados: ${itensProcessados}\n\n`;

            if (receiptData.frete && receiptData.frete.valor > 0) {
                resumo += `🚛 Frete: R$ ${receiptData.frete.valor.toFixed(2)} (${receiptData.frete.tipo})\n`;
            }

            resumo += `✅ Status: Materiais disponíveis no estoque\n`;
            resumo += `🎯 Próximos passos: Materiais já podem ser utilizados na produção`;

            alert(resumo);
        }

        // Função para alternar campos de frete
        window.toggleFreightFields = function() {
            const freightType = document.getElementById('freightType').value;
            const freightValueInput = document.getElementById('freightValue');

            if (freightType === 'CIF') {
                freightValueInput.placeholder = 'Frete já incluído no valor dos produtos';
                freightValueInput.style.backgroundColor = '#f8f9fa';
            } else {
                freightValueInput.placeholder = 'Valor do frete a ser pago';
                freightValueInput.style.backgroundColor = '#fff';
            }

            calculateFreightDistribution();
        };

        /**
         * Calcula a distribuição do frete entre os itens do pedido
         * @param {Object} receiptData Dados do recebimento
         * @returns {Object} Objeto com a distribuição do frete por item
         */
        function calculateFreightForItems(receiptData) {
            const freightDistribution = {};
            const freightValue = parseFloat(document.getElementById('freightValue').value) || 0;
            const includeInCost = document.getElementById('includeFreightInCost').checked;
            const freightType = document.getElementById('freightType').value;
            
            // Se não houver frete ou não for para incluir no custo, retorna distribuição vazia
            if (freightValue <= 0 || !includeInCost || freightType !== 'FOB') {
                return freightDistribution;
            }
            
            // Calcular valor total dos itens para rateio proporcional
            let totalValue = 0;
            receiptData.itens.forEach(item => {
                if (item.quantidade > 0) {
                    totalValue += (item.precoUnitario * item.quantidade);
                }
            });
            
            // Se não houver valor total, não há como ratear
            if (totalValue <= 0) {
                return freightDistribution;
            }
            
            // Calcular valor do frete proporcional para cada item
            receiptData.itens.forEach((item, index) => {
                if (item.quantidade > 0) {
                    const itemValue = item.precoUnitario * item.quantidade;
                    const freightRatio = itemValue / totalValue;
                    freightDistribution[index] = {
                        valorFrete: freightValue * freightRatio,
                        percentual: freightRatio * 100
                    };
                }
            });
            
            return freightDistribution;
        }
        
        // Função para calcular distribuição do frete na interface
        window.calculateFreightDistribution = function() {
            const freightValue = parseFloat(document.getElementById('freightValue').value) || 0;
            const includeInCost = document.getElementById('includeFreightInCost').checked;
            const freightType = document.getElementById('freightType').value;

            if (!currentOrder || !currentOrder.itens) {
                document.getElementById('freightDistributionText').textContent = 'Selecione um pedido primeiro';
                return;
            }

            if (freightValue <= 0 || !includeInCost) {
                document.getElementById('freightDistributionText').textContent =
                    freightValue <= 0 ? 'Configure o valor do frete para ver o rateio' : 'Frete não será incluído no custo dos produtos';
                return;
            }

            // Calcular valor total dos itens para rateio
            const totalValue = currentOrder.itens.reduce((sum, item) => {
                return sum + (item.valorUnitario * item.quantidade);
            }, 0);

            if (totalValue <= 0) {
                document.getElementById('freightDistributionText').textContent = 'Erro: valor total dos itens é zero';
                return;
            }

            // Mostrar rateio por item
            let distributionText = `Frete ${freightType} de R$ ${freightValue.toFixed(2)} será rateado por valor:\n`;

            currentOrder.itens.forEach(item => {
                const itemValue = item.valorUnitario * item.quantidade;
                const itemPercentage = (itemValue / totalValue) * 100;
                const itemFreight = (itemValue / totalValue) * freightValue;
                const newUnitCost = item.valorUnitario + (itemFreight / item.quantidade);

                distributionText += `• ${item.codigo}: R$ ${itemFreight.toFixed(2)} (${itemPercentage.toFixed(1)}%) - Novo custo: R$ ${newUnitCost.toFixed(2)}/un\n`;
            });

            document.getElementById('freightDistributionText').innerHTML = distributionText.replace(/\n/g, '<br>');
        };

        /**
         * Exibe uma notificação para o usuário
         * @param {string} message - Mensagem a ser exibida
         * @param {string} type - Tipo da notificação (success, error, warning, info)
         * @param {number} [duration=5000] - Duração em milissegundos que a notificação ficará visível
         */
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.getElementById('notification');
            if (!notification) return;
            
            // Mapeia os tipos de notificação para classes CSS
            const typeClasses = {
                success: 'notification-success',
                error: 'notification-error',
                warning: 'notification-warning',
                info: 'notification-info'
            };
            
            // Remove classes de tipo existentes
            notification.className = 'notification';
            
            // Adiciona a classe do tipo de notificação
            if (typeClasses[type]) {
                notification.classList.add(typeClasses[type]);
            } else {
                notification.classList.add('notification-info');
            }
            
            // Define o conteúdo da notificação
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.style.display='none'">
                        &times;
                    </button>
                </div>
            `;
            
            // Exibe a notificação
            notification.style.display = 'block';
            notification.style.opacity = '1';
            
            // Esconde a notificação após o tempo especificado
            if (duration > 0) {
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);
                }, duration);
            }
            
            // Adiciona evento de clique para fechar a notificação
            const closeButton = notification.querySelector('.notification-close');
            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);
                });
            }
        }
        
        // Função para coletar os dados do formulário de recebimento
        function collectReceiptData() {
            // Coletar dados básicos do recebimento
            const receiptData = {
                pedidoId: currentOrder.id,
                numeroNF: document.getElementById('invoiceNumber').value.trim(),
                serieNF: document.getElementById('invoiceSeries').value.trim(),
                dataNF: new Date(document.getElementById('invoiceDate').value),
                valorNF: parseFloat(document.getElementById('invoiceValue').value) || 0,
                frete: {
                    tipo: document.getElementById('freightType').value,
                    valor: parseFloat(document.getElementById('freightValue').value) || 0,
                    transportadora: document.getElementById('carrier').value.trim(),
                    chaveNFe: document.getElementById('nfeKey').value.trim(),
                    incluirNoCusto: document.getElementById('includeFreightInCost').checked
                },
                observacoes: document.getElementById('observations').value.trim(),
                dataRecebimento: new Date(),
                usuarioRecebimento: currentUser.nome,
                itens: []
            };
            
            // Coletar dados dos itens
            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                if (!qtyInput) return;
                
                const quantidade = parseFloat(qtyInput.value) || 0;
                if (quantidade <= 0) return;
                
                const precoUnitario = parseFloat(document.getElementById(`price_${index}`)?.value) || item.valorUnitario || 0;
                const destinoSelect = document.getElementById(`destino_${index}`);
                const destinoId = destinoSelect ? destinoSelect.value : 'estoque';
                
                // Buscar informações adicionais do item
                const lote = document.getElementById(`lote_${index}`)?.value.trim() || '';
                const dataValidade = document.getElementById(`validade_${index}`)?.value || '';
                
                // Adicionar item ao array de itens
                receiptData.itens.push({
                    itemId: item.id || index,
                    produtoId: item.produtoId,
                    codigo: item.codigo,
                    descricao: item.descricao,
                    quantidade: quantidade,
                    unidade: item.unidade || 'UN',
                    precoUnitario: precoUnitario,
                    valorTotal: quantidade * precoUnitario,
                    destinoId: destinoId,
                    lote: lote,
                    dataValidade: dataValidade ? new Date(dataValidade) : null,
                    statusQualidade: item.statusQualidade || 'NAO_AVALIADO'
                });
            });
            
            // Validar se existem itens para processar
            if (receiptData.itens.length === 0) {
                throw new Error('Nenhum item válido para processar. Verifique as quantidades informadas.');
            }
            
            return receiptData;
        }
        


        function validateQuantity(index, value) {
            const item = currentOrder.itens[index];
            if (!item) return false;
            
            const qty = parseFloat(value) || 0;
            const saldoPendente = item.quantidade - (item.quantidadeRecebida || 0);
            const qtyInput = document.getElementById(`qty_${index}`);
            const statusElement = document.getElementById(`status_${index}`);
            
            if (qty <= 0) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge error">INVÁLIDO</span>';
                return false;
            }
            
            if (qty > saldoPendente && !systemConfig.permitirRecebimentoParcial) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge delayed">ACIMA DO PEDIDO</span>';
                return false;
            }
            
            // Validação de lote se necessário
            const loteInput = document.getElementById(`lote_${index}`);
            if (systemConfig.obrigarLoteRecebimento && (!loteInput || !loteInput.value.trim())) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge error">LOTE OBRIGATÓRIO</span>';
                return false;
            }
            
            // Se chegou até aqui, está tudo válido
            qtyInput.classList.remove('error');
            statusElement.innerHTML = qty > 0 ? 
                '<span class="status-badge ok">OK</span>' : 
                '<span class="status-badge pending">PENDENTE</span>';
                
            return true;
        }
        
        function validatePrice(index, value) {
            const price = parseFloat(value) || 0;
            const priceInput = document.getElementById(`price_${index}`);
            const statusElement = document.getElementById(`status_${index}`);
            
            if (price < 0) {
                priceInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge error">PREÇO INVÁLIDO</span>';
                return false;
            }
            
            // Se o preço for zero, avisar mas não bloquear
            if (price === 0) {
                priceInput.classList.add('warning');
                statusElement.innerHTML = '<span class="status-badge warning">PREÇO ZERADO</span>';
            } else {
                priceInput.classList.remove('error', 'warning');
                // Não sobrescrever o status se já estiver OK por outro motivo
                if (!statusElement.innerHTML.includes('OK') && !statusElement.innerHTML.includes('PENDENTE')) {
                    statusElement.innerHTML = '<span class="status-badge ok">OK</span>';
                }
            }
            
            return true;
        }
        


        // ===== FUNÇÕES DE CARREGAMENTO DE DADOS =====
        /**
         * Carrega as configurações do sistema necessárias para o fluxo de recebimento
         * @returns {Promise<Object>} Objeto com as configurações do sistema
         */
        async function loadSystemConfig() {
            try {
                // Carregar configuração do sistema
                const configDoc = await getDoc(doc(db, 'configuracoes', 'sistema'));
                
                if (!configDoc.exists()) {
                    console.warn('Configuração do sistema não encontrada. Usando valores padrão.');
                    return {
                        controleQualidade: false,
                        inspecaoRecebimento: 'criticos',
                        armazemQualidade: null,
                        obrigarLoteRecebimento: false,
                        obrigarValidadeRecebimento: false,
                        permitirRecebimentoParcial: true,
                        itensCriticos: []
                    };
                }
                
                const config = configDoc.data();
                
                // Mapear configurações para o formato esperado
                const systemConfig = {
                    controleQualidade: config.controleQualidade || false,
                    inspecaoRecebimento: config.inspecaoRecebimento || 'criticos',
                    armazemQualidade: config.armazemQualidade || null,
                    obrigarLoteRecebimento: config.obrigarLoteRecebimento || false,
                    obrigarValidadeRecebimento: config.obrigarValidadeRecebimento || false,
                    permitirRecebimentoParcial: config.permitirRecebimentoParcial !== false, // true por padrão
                    itensCriticos: config.itensCriticos || []
                };
                
                // Armazenar configuração globalmente
                window.systemConfig = systemConfig;
                
                console.log('Configuração do sistema carregada:', systemConfig);
                return systemConfig;
                
            } catch (error) {
                console.error('Erro ao carregar configuração do sistema:', error);
                // Retornar configuração padrão em caso de erro
                const defaultConfig = {
                    controleQualidade: false,
                    inspecaoRecebimento: 'criticos',
                    armazemQualidade: null,
                    obrigarLoteRecebimento: false,
                    obrigarValidadeRecebimento: false,
                    permitirRecebimentoParcial: true,
                    itensCriticos: []
                };
                window.systemConfig = defaultConfig;
                return defaultConfig;
            }
        }
        
        /**
         * Carrega os dados iniciais necessários para o funcionamento do módulo
         */
        async function loadInitialData() {
            try {
                // Carregar configurações do sistema em paralelo com outros dados
                const [
                    configData,
                    pedidosSnapshot,
                    produtosSnapshot,
                    fornecedoresSnapshot,
                    armazensSnapshot,
                    usersSnapshot,
                    estoqueQualidadeSnapshot
                ] = await Promise.all([
                    loadSystemConfig(),
                    getDocs(collection(db, 'pedidosCompra')),
                    getDocs(collection(db, 'produtos')),
                    getDocs(collection(db, 'fornecedores')),
                    getDocs(collection(db, 'armazens')),
                    getDocs(collection(db, 'users')),
                    getDocs(collection(db, 'estoqueQualidade'))
                ]);

                // Mapear pedidos de compra
                pedidosCompra = pedidosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    // Garantir que as datas sejam objetos Date
                    dataPedido: doc.data().dataPedido?.toDate(),
                    dataEntrega: doc.data().dataEntrega?.toDate(),
                    dataAprovacao: doc.data().dataAprovacao?.toDate(),
                    dataRecebimento: doc.data().dataRecebimento?.toDate()
                }));

                // Mapear produtos
                produtos = produtosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    // Verificar se o produto é crítico para inspeção de qualidade
                    requerInspecaoQualidade: configData.itensCriticos.includes(doc.id)
                }));

                // Mapear fornecedores
                fornecedores = fornecedoresSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Mapear armazéns
                armazens = armazensSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    // Identificar se é o armazém de qualidade
                    ehArmazemQualidade: doc.id === configData.armazemQualidade
                }));

                // Mapear usuários ativos
                users = usersSnapshot.docs.map(doc => ({
                    uid: doc.id,
                    ...doc.data()
                }));

                // Carregar itens em inspeção de qualidade
                const itensEmQualidade = [];
                estoqueQualidadeSnapshot.forEach(doc => {
                    const item = doc.data();
                    itensEmQualidade.push({
                        id: doc.id,
                        ...item,
                        dataEntrada: item.dataEntrada?.toDate(),
                        dataAprovacao: item.dataAprovacao?.toDate(),
                        dataRejeicao: item.dataRejeicao?.toDate()
                    });
                });
                
                // Armazenar itens em qualidade para referência
                window.itensEmQualidade = itensEmQualidade;

                console.log('Dados iniciais carregados com sucesso', {
                    pedidos: pedidosCompra.length,
                    produtos: produtos.length,
                    fornecedores: fornecedores.length,
                    armazens: armazens.length,
                    usuarios: users.length,
                    itensEmQualidade: itensEmQualidade.length,
                    configuracao: configData
                });
                
                return {
                    config: configData,
                    pedidos: pedidosCompra,
                    produtos,
                    fornecedores,
                    armazens,
                    usuarios: users,
                    itensEmQualidade
                };
                
            } catch (error) {
                console.error('Erro ao carregar dados iniciais:', error);
                showNotification('Erro ao carregar dados iniciais. Por favor, recarregue a página.', 'error');
                throw error;
            }
        }



        
        // ===== CÁLCULO E DISTRIBUIÇÃO DO FRETE =====
        function calculateFreightDistribution() {
            const freightValue = parseFloat(document.getElementById('freightValue').value) || 0;
            const includeInCost = document.getElementById('includeFreightInCost').checked;
            
            if (freightValue <= 0 || !currentOrder?.itens?.length) {
                return [];
            }
            
            // Calcular o valor total dos itens para rateio proporcional
            let totalItens = 0;
            const itensValidos = [];
            
            // Coletar itens com quantidade a receber > 0
            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                if (qtyInput) {
                    const qty = parseFloat(qtyInput.value) || 0;
                    if (qty > 0) {
                        const precoUnitario = parseFloat(document.getElementById(`price_${index}`)?.value) || item.valorUnitario || 0;
                        const valorTotal = qty * precoUnitario;
                        totalItens += valorTotal;
                        itensValidos.push({ index, qty, precoUnitario, valorTotal });
                    }
                }
            });
            
            // Se não houver itens válidos, retornar array vazio
            if (totalItens <= 0 || itensValidos.length === 0) {
                return [];
            }
            
            // Calcular o frete proporcional para cada item
            const freightDistribution = [];
            let totalDistributed = 0;
            
            itensValidos.forEach((item, idx) => {
                // Calcular a proporção do valor deste item em relação ao total
                const proporcao = item.valorTotal / totalItens;
                let freteItem = freightValue * proporcao;
                
                // Arredondar para 2 casas decimais para evitar problemas de ponto flutuante
                freteItem = Math.round(freteItem * 100) / 100;
                
                // Garantir que não ultrapasse o valor total do frete
                if (idx === itensValidos.length - 1) {
                    freteItem = freightValue - totalDistributed;
                } else {
                    totalDistributed += freteItem;
                }
                
                freightDistribution[item.index] = {
                    valor: freteItem,
                    incluirNoCusto: includeInCost
                };
                
                // Atualizar a interface para mostrar o valor do frete por item
                const freteCell = document.getElementById(`freight_${item.index}`);
                if (freteCell) {
                    freteCell.textContent = freteItem.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
                }
            });
            
            return freightDistribution;
        }
        

        
        // ===== CONFIGURAÇÃO DA INTERFACE =====
        function setupInterface() {
            // Popular o select de pedidos
            populateOrderSelect();

            // Configurar data atual como padrão para o campo de data
            document.getElementById('invoiceDate').valueAsDate = new Date();

            // Configurar evento para atualizar o cálculo do frete quando o valor for alterado
            document.getElementById('freightValue').addEventListener('input', calculateFreightDistribution);

            // Configurar validação em tempo real para quantidades
            document.addEventListener('input', function(e) {
                if (e.target && e.target.id && e.target.id.startsWith('qty_')) {
                    const index = parseInt(e.target.id.split('_')[1]);
                    validateQuantity(index, e.target.value);
                }
            });

            // Configurar validação em tempo real para preços
            document.addEventListener('input', function(e) {
                if (e.target && e.target.id && e.target.id.startsWith('price_')) {
                    const index = parseInt(e.target.id.split('_')[1]);
                    validatePrice(index, e.target.value);
                }
            });

            // Mostrar seção de recebimento se houver pedidos
            if (currentOrder) {
                document.getElementById('receiptSection').style.display = 'block';
                document.getElementById('itemsSection').style.display = 'block';
                loadOrderItems();
            }

            console.log('Interface configurada com sucesso');
        }

        // ===== HISTÓRICO DE RECEBIMENTOS =====
        async function loadReceiptHistory() {
            try {
                // Carregar histórico de recebimentos se necessário
                const recebimentosSnapshot = await getDocs(collection(db, 'recebimentoMateriais'));
                const recebimentos = recebimentosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    dataRecebimento: doc.data().dataRecebimento?.toDate()
                }));

                console.log('Histórico de recebimentos carregado:', recebimentos.length);
                return recebimentos;
            } catch (error) {
                console.warn('Erro ao carregar histórico de recebimentos:', error);
                return [];
            }
        }



        // ===== INICIALIZAÇÃO DA APLICAÇÃO =====
        async function initializeApp() {
            try {
                // Carregar configurações do sistema
                const configDoc = await getDoc(doc(db, "sistema", "configuracoes"));
                if (configDoc.exists()) {
                    systemConfig = configDoc.data();
                    updateConfigurationAlert();
                }

                // Carregar dados iniciais
                await loadInitialData();

                // Configurar interface
                setupInterface();

                // Carregar histórico de recebimentos
                await loadReceiptHistory();

            } catch (error) {
                console.error('Erro ao inicializar a aplicação:', error);
                showNotification('Erro ao carregar dados iniciais: ' + error.message, 'error');
            }
        }

        // Inicializar a aplicação quando o DOM estiver pronto
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }

        // ===== APROVAÇÃO DE PEDIDO =====
        window.approveOrderForReceipt = async function() {
            if (!currentOrder) {
                showNotification('Nenhum pedido selecionado', 'error');
                return;
            }

            if (!confirm('Confirma a aprovação deste pedido para prosseguir com o recebimento?')) {
                return;
            }

            try {
                showNotification('Aprovando pedido...', 'info');

                // Importar as funções necessárias do Firebase
                const { doc, updateDoc, Timestamp } = await import("https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js");

                // Atualizar status do pedido
                const pedidoRef = doc(db, "pedidosCompra", currentOrder.id);
                await updateDoc(pedidoRef, {
                    status: 'APROVADO',
                    dataAprovacao: Timestamp.now(),
                    aprovadoPor: currentUser.nome,
                    historico: [
                        ...(currentOrder.historico || []),
                        {
                            data: Timestamp.now(),
                            acao: 'Aprovado via Recebimento',
                            usuario: currentUser.nome
                        }
                    ]
                });

                // Atualizar o objeto local
                currentOrder.status = 'APROVADO';
                currentOrder.dataAprovacao = new Date();
                currentOrder.aprovadoPor = currentUser.nome;

                // Recarregar dados e interface
                await loadInitialData();
                setupInterface();

                // Reselecionar o pedido
                document.getElementById('orderSelect').value = currentOrder.id;
                await loadOrderDetails();

                showNotification('Pedido aprovado com sucesso! Agora você pode prosseguir com o recebimento.', 'success');

            } catch (error) {
                console.error('Erro ao aprovar pedido:', error);
                showNotification('Erro ao aprovar pedido: ' + error.message, 'error');
            }
        };

        // ===== CARREGAMENTO DE DETALHES DO PEDIDO =====
        async function loadOrderDetails() {
            if (!currentOrder) return;

            try {
                // Recarregar dados do pedido do banco
                const pedidoRef = doc(db, "pedidosCompra", currentOrder.id);
                const pedidoSnap = await getDoc(pedidoRef);

                if (pedidoSnap.exists()) {
                    currentOrder = {
                        id: pedidoSnap.id,
                        ...pedidoSnap.data(),
                        dataPedido: pedidoSnap.data().dataPedido?.toDate(),
                        dataEntrega: pedidoSnap.data().dataEntrega?.toDate(),
                        dataAprovacao: pedidoSnap.data().dataAprovacao?.toDate(),
                        dataRecebimento: pedidoSnap.data().dataRecebimento?.toDate()
                    };

                    // Recarregar itens na interface
                    loadOrderItems();

                    // Atualizar informações do pedido na tela
                    updateOrderInfo();
                }
            } catch (error) {
                console.error('Erro ao recarregar detalhes do pedido:', error);
            }
        }

        // ===== ATUALIZAÇÃO DE INFORMAÇÕES DO PEDIDO =====
        function updateOrderInfo() {
            if (!currentOrder) return;

            // Atualizar informações básicas do pedido na interface
            const orderInfoElement = document.getElementById('orderInfo');
            if (orderInfoElement) {
                const fornecedor = fornecedores.find(f => f.id === currentOrder.fornecedorId);
                orderInfoElement.innerHTML = `
                    <div class="order-summary">
                        <h4>Pedido: ${currentOrder.numero || currentOrder.id}</h4>
                        <p><strong>Fornecedor:</strong> ${fornecedor?.razaoSocial || 'N/A'}</p>
                        <p><strong>Status:</strong> <span class="status-badge status-${currentOrder.status.toLowerCase()}">${currentOrder.status}</span></p>
                        <p><strong>Valor Total:</strong> R$ ${(currentOrder.valorTotal || 0).toFixed(2)}</p>
                    </div>
                `;
            }
        }

        // ===== FUNÇÕES AUXILIARES =====
        function resetForm() {
            document.getElementById('orderSelect').value = '';
            document.getElementById('supplierName').value = '';
            document.getElementById('invoiceNumber').value = '';
            document.getElementById('invoiceDate').value = '';
            document.getElementById('invoiceValue').value = '';
            document.getElementById('observations').value = '';
            document.getElementById('receiptSection').style.display = 'none';
            document.getElementById('itemsSection').style.display = 'none';
            currentOrder = null;
        }


    </script>
</body>
</html>
