
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Aprovaç<PERSON>es <PERSON>erárquicas - Sistema ERP</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .approval-dashboard {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 20px;
      padding: 20px;
    }

    .sidebar {
      background: var(--secondary-color);
      padding: 20px;
      border-radius: 8px;
    }

    .main-content {
      background: white;
    }

    .approval-summary {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      border-left: 4px solid var(--primary-color);
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .summary-item {
      text-align: center;
      padding: 15px;
      background: var(--secondary-color);
      border-radius: 8px;
    }

    .summary-number {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .summary-label {
      font-size: 12px;
      color: var(--text-color);
      margin-top: 5px;
    }

    .workflow-levels {
      margin-bottom: 20px;
    }

    .level-item {
      background: white;
      padding: 15px;
      margin-bottom: 10px;
      border-radius: 8px;
      border-left: 4px solid var(--border-color);
      transition: all 0.3s;
    }

    .level-item.active {
      border-left-color: var(--primary-color);
      background: #f8f9ff;
    }

    .level-item.pending {
      border-left-color: var(--warning-color);
      background: #fffaf0;
    }

    .level-item.approved {
      border-left-color: var(--success-color);
      background: #f0fff4;
    }

    .level-item.rejected {
      border-left-color: var(--danger-color);
      background: #fff5f5;
    }

    .level-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .level-title {
      font-weight: 600;
      color: var(--primary-color);
    }

    .level-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: bold;
    }

    .badge-pending {
      background: var(--warning-color);
      color: white;
    }

    .badge-approved {
      background: var(--success-color);
      color: white;
    }

    .badge-rejected {
      background: var(--danger-color);
      color: white;
    }

    .approvals-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .approvals-table th,
    .approvals-table td {
      padding: 12px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .approvals-table th {
      background: var(--secondary-color);
      font-weight: 600;
    }

    .approvals-table tr:hover {
      background: #f8f9fa;
    }

    .priority-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
    }

    .priority-alta {
      background: var(--danger-color);
    }

    .priority-media {
      background: var(--warning-color);
    }

    .priority-baixa {
      background: var(--success-color);
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s;
      font-size: 12px;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 11px;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-danger {
      background: var(--danger-color);
      color: white;
    }

    .btn-warning {
      background: var(--warning-color);
      color: white;
    }

    .tabs {
      display: flex;
      background: var(--secondary-color);
      border-radius: 8px 8px 0 0;
    }

    .tab {
      padding: 12px 20px;
      cursor: pointer;
      background: transparent;
      border: none;
      color: var(--text-color);
      font-weight: 500;
    }

    .tab.active {
      background: var(--primary-color);
      color: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
    }

    .tab-content.active {
      display: block;
    }

    .approval-form {
      background: var(--secondary-color);
      padding: 20px;
      border-radius: 8px;
      margin-top: 20px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
    }

    .modal-content {
      background: white;
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 800px;
      border-radius: 8px;
      position: relative;
      max-height: 80vh;
      overflow-y: auto;
    }

    .close {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 24px;
      cursor: pointer;
    }

    .document-details {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    }

    .detail-label {
      font-weight: 500;
      color: var(--text-color);
    }

    .approval-history {
      background: var(--secondary-color);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }

    .history-item {
      background: white;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
      border-left: 3px solid var(--primary-color);
    }

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
    }

    .history-user {
      font-weight: 600;
      color: var(--primary-color);
    }

    .history-date {
      font-size: 12px;
      color: var(--text-color);
      opacity: 0.7;
    }

    .alerts-panel {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .alert-item {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
      border-left: 4px solid var(--warning-color);
      background: #fff3cd;
    }

    .filters-panel {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .urgency-timer {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
    }

    .timer-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
    }

    .timer-normal {
      background: var(--success-color);
    }

    .timer-warning {
      background: var(--warning-color);
    }

    .timer-critical {
      background: var(--danger-color);
    }

    .delegation-panel {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }

    .workflow-diagram {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20px 0;
      padding: 20px;
      background: var(--secondary-color);
      border-radius: 8px;
    }

    .workflow-step {
      text-align: center;
      flex: 1;
      position: relative;
    }

    .workflow-step:not(:last-child)::after {
      content: '→';
      position: absolute;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      color: var(--primary-color);
    }

    .step-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--border-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 10px;
      font-weight: bold;
    }

    .step-circle.completed {
      background: var(--success-color);
    }

    .step-circle.active {
      background: var(--primary-color);
    }

    .step-circle.pending {
      background: var(--warning-color);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Central de Aprovações</h1>
      <div>
        <button class="btn btn-primary" onclick="openBulkApproval()">Aprovação em Lote</button>
        <button class="btn btn-warning" onclick="configurarWorkflow()">Configurar Workflow</button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
      </div>
    </div>

    <div class="approval-dashboard">
      <div class="sidebar">
        <div class="approval-summary">
          <h3>Resumo de Aprovações</h3>
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-number" id="pendentes-count">8</div>
              <div class="summary-label">Pendentes</div>
            </div>
            <div class="summary-item">
              <div class="summary-number" id="urgentes-count">3</div>
              <div class="summary-label">Urgentes</div>
            </div>
            <div class="summary-item">
              <div class="summary-number" id="hoje-count">12</div>
              <div class="summary-label">Hoje</div>
            </div>
            <div class="summary-item">
              <div class="summary-number" id="semana-count">45</div>
              <div class="summary-label">Esta Semana</div>
            </div>
          </div>
        </div>

        <div class="workflow-levels">
          <h4>Níveis de Aprovação</h4>
          <div class="level-item active">
            <div class="level-header">
              <div class="level-title">Nível 1 - Supervisor</div>
              <div class="level-badge badge-pending">5</div>
            </div>
            <div>Até R$ 1.000,00</div>
          </div>
          <div class="level-item pending">
            <div class="level-header">
              <div class="level-title">Nível 2 - Gerente</div>
              <div class="level-badge badge-pending">2</div>
            </div>
            <div>R$ 1.000,01 até R$ 10.000,00</div>
          </div>
          <div class="level-item">
            <div class="level-header">
              <div class="level-title">Nível 3 - Diretor</div>
              <div class="level-badge badge-pending">1</div>
            </div>
            <div>Acima de R$ 10.000,00</div>
          </div>
        </div>

        <div class="alerts-panel">
          <h4>Alertas</h4>
          <div class="alert-item">
            <strong>3 solicitações</strong> com prazo vencendo hoje
          </div>
          <div class="alert-item">
            <strong>1 solicitação</strong> com prazo vencido
          </div>
        </div>
      </div>

      <div class="main-content">
        <div class="tabs">
          <button class="tab active" onclick="showTab('minhas-aprovacoes')">Minhas Aprovações</button>
          <button class="tab" onclick="showTab('historico')">Histórico</button>
          <button class="tab" onclick="showTab('delegacoes')">Delegações</button>
          <button class="tab" onclick="showTab('configuracoes')">Configurações</button>
        </div>

        <!-- Aba Minhas Aprovações -->
        <div id="minhas-aprovacoes" class="tab-content active">
          <div class="filters-panel">
            <div class="filter-row">
              <div class="form-group">
                <label>Tipo de Documento</label>
                <select class="form-control" id="tipo-documento">
                  <option value="">Todos</option>
                  <option value="solicitacao">Solicitação de Compra</option>
                  <option value="cotacao">Cotação</option>
                  <option value="pedido">Pedido de Compra</option>
                </select>
              </div>
              <div class="form-group">
                <label>Prioridade</label>
                <select class="form-control" id="prioridade">
                  <option value="">Todas</option>
                  <option value="alta">Alta</option>
                  <option value="media">Média</option>
                  <option value="baixa">Baixa</option>
                </select>
              </div>
              <div class="form-group">
                <label>Período</label>
                <select class="form-control" id="periodo">
                  <option value="hoje">Hoje</option>
                  <option value="semana">Esta Semana</option>
                  <option value="mes">Este Mês</option>
                </select>
              </div>
            </div>
            <button class="btn btn-primary">Aplicar Filtros</button>
          </div>

          <table class="approvals-table">
            <thead>
              <tr>
                <th>
                  <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                </th>
                <th>Documento</th>
                <th>Solicitante</th>
                <th>Valor</th>
                <th>Data</th>
                <th>Prioridade</th>
                <th>Prazo</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="approvals-table-body">
              <tr>
                <td><input type="checkbox" class="item-select"></td>
                <td>
                  <strong>SC001</strong><br>
                  <small>Solicitação de Compra</small>
                </td>
                <td>João Silva<br><small>Departamento TI</small></td>
                <td><strong>R$ 2.500,00</strong></td>
                <td>15/01/2025</td>
                <td>
                  <span class="priority-indicator priority-alta"></span>
                  Alta
                </td>
                <td>
                  <div class="urgency-timer">
                    <div class="timer-icon timer-warning"></div>
                    2 dias
                  </div>
                </td>
                <td>
                  <button class="btn btn-success btn-sm" onclick="approveDocument('SC001')">Aprovar</button>
                  <button class="btn btn-danger btn-sm" onclick="rejectDocument('SC001')">Rejeitar</button>
                  <button class="btn btn-primary btn-sm" onclick="viewDocument('SC001')">Detalhes</button>
                </td>
              </tr>
              <tr>
                <td><input type="checkbox" class="item-select"></td>
                <td>
                  <strong>CT002</strong><br>
                  <small>Cotação</small>
                </td>
                <td>Maria Santos<br><small>Compras</small></td>
                <td><strong>R$ 850,00</strong></td>
                <td>14/01/2025</td>
                <td>
                  <span class="priority-indicator priority-media"></span>
                  Média
                </td>
                <td>
                  <div class="urgency-timer">
                    <div class="timer-icon timer-normal"></div>
                    5 dias
                  </div>
                </td>
                <td>
                  <button class="btn btn-success btn-sm" onclick="approveDocument('CT002')">Aprovar</button>
                  <button class="btn btn-danger btn-sm" onclick="rejectDocument('CT002')">Rejeitar</button>
                  <button class="btn btn-primary btn-sm" onclick="viewDocument('CT002')">Detalhes</button>
                </td>
              </tr>
            </tbody>
          </table>

          <div style="margin-top: 20px;">
            <button class="btn btn-success" onclick="bulkApprove()">Aprovar Selecionados</button>
            <button class="btn btn-danger" onclick="bulkReject()">Rejeitar Selecionados</button>
            <button class="btn btn-warning" onclick="delegateSelected()">Delegar Selecionados</button>
          </div>
        </div>

        <!-- Aba Histórico -->
        <div id="historico" class="tab-content">
          <div class="filters-panel">
            <div class="filter-row">
              <div class="form-group">
                <label>Data Inicial</label>
                <input type="date" class="form-control">
              </div>
              <div class="form-group">
                <label>Data Final</label>
                <input type="date" class="form-control">
              </div>
              <div class="form-group">
                <label>Status</label>
                <select class="form-control">
                  <option value="">Todos</option>
                  <option value="aprovado">Aprovado</option>
                  <option value="rejeitado">Rejeitado</option>
                  <option value="delegado">Delegado</option>
                </select>
              </div>
            </div>
          </div>

          <table class="approvals-table">
            <thead>
              <tr>
                <th>Documento</th>
                <th>Solicitante</th>
                <th>Valor</th>
                <th>Data Aprovação</th>
                <th>Status</th>
                <th>Observações</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>SC005</strong></td>
                <td>Pedro Costa</td>
                <td>R$ 1.200,00</td>
                <td>10/01/2025 14:30</td>
                <td><span class="level-badge badge-approved">Aprovado</span></td>
                <td>Aprovado com ressalvas</td>
                <td>
                  <button class="btn btn-primary btn-sm" onclick="viewDocument('SC005')">Ver</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Aba Delegações -->
        <div id="delegacoes" class="tab-content">
          <div class="delegation-panel">
            <h3>Configurar Delegação de Aprovações</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>Delegar Para</label>
                <select class="form-control">
                  <option value="">Selecione um usuário...</option>
                  <option value="ana">Ana Silva - Supervisor</option>
                  <option value="carlos">Carlos Santos - Gerente</option>
                </select>
              </div>
              <div class="form-group">
                <label>Período - Início</label>
                <input type="datetime-local" class="form-control">
              </div>
              <div class="form-group">
                <label>Período - Fim</label>
                <input type="datetime-local" class="form-control">
              </div>
              <div class="form-group">
                <label>Limite de Valor</label>
                <input type="number" class="form-control" placeholder="0,00" step="0.01">
              </div>
            </div>
            <div class="form-group">
              <label>Motivo da Delegação</label>
              <textarea class="form-control" rows="3" placeholder="Ex: Férias, viagem a trabalho..."></textarea>
            </div>
            <button class="btn btn-success">Configurar Delegação</button>
          </div>

          <div style="margin-top: 30px;">
            <h4>Delegações Ativas</h4>
            <table class="approvals-table">
              <thead>
                <tr>
                  <th>Delegado Para</th>
                  <th>Período</th>
                  <th>Limite</th>
                  <th>Status</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Ana Silva</td>
                  <td>15/01 a 20/01/2025</td>
                  <td>R$ 5.000,00</td>
                  <td><span class="level-badge badge-approved">Ativa</span></td>
                  <td>
                    <button class="btn btn-danger btn-sm">Cancelar</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Aba Configurações -->
        <div id="configuracoes" class="tab-content">
          <div class="approval-form">
            <h3>Configurações de Aprovação</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>Limite Nível 1 (R$)</label>
                <input type="number" class="form-control" value="1000" step="0.01">
              </div>
              <div class="form-group">
                <label>Limite Nível 2 (R$)</label>
                <input type="number" class="form-control" value="10000" step="0.01">
              </div>
              <div class="form-group">
                <label>Prazo Padrão (dias)</label>
                <input type="number" class="form-control" value="3">
              </div>
              <div class="form-group">
                <label>Auto-Aprovação</label>
                <select class="form-control">
                  <option value="nao">Não</option>
                  <option value="sim">Sim</option>
                </select>
              </div>
            </div>
            <button class="btn btn-success">Salvar Configurações</button>
          </div>

          <div class="workflow-diagram">
            <div class="workflow-step">
              <div class="step-circle completed">1</div>
              <div>Solicitação</div>
            </div>
            <div class="workflow-step">
              <div class="step-circle active">2</div>
              <div>Supervisor</div>
            </div>
            <div class="workflow-step">
              <div class="step-circle pending">3</div>
              <div>Gerente</div>
            </div>
            <div class="workflow-step">
              <div class="step-circle pending">4</div>
              <div>Diretor</div>
            </div>
            <div class="workflow-step">
              <div class="step-circle pending">5</div>
              <div>Finalizado</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Detalhes do Documento -->
  <div id="modal-detalhes" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeModal('modal-detalhes')">&times;</span>
      <h2>Detalhes da Solicitação</h2>
      
      <div class="document-details">
        <div class="detail-row">
          <span class="detail-label">Número:</span>
          <span id="doc-numero">SC001</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Solicitante:</span>
          <span id="doc-solicitante">João Silva</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Departamento:</span>
          <span id="doc-departamento">TI</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Valor Total:</span>
          <span id="doc-valor">R$ 2.500,00</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Justificativa:</span>
          <span id="doc-justificativa">Equipamentos necessários para expansão do setor</span>
        </div>
      </div>

      <div class="approval-history">
        <h4>Histórico de Aprovações</h4>
        <div class="history-item">
          <div class="history-header">
            <span class="history-user">João Silva</span>
            <span class="history-date">15/01/2025 09:30</span>
          </div>
          <div>Solicitação criada</div>
        </div>
        <div class="history-item">
          <div class="history-header">
            <span class="history-user">Sistema</span>
            <span class="history-date">15/01/2025 09:31</span>
          </div>
          <div>Enviado para aprovação - Nível 1</div>
        </div>
      </div>

      <div class="approval-form">
        <h4>Ação de Aprovação</h4>
        <div class="form-group">
          <label>Observações</label>
          <textarea class="form-control" rows="3" placeholder="Adicione observações sobre a aprovação/rejeição..."></textarea>
        </div>
        <div style="margin-top: 15px;">
          <button class="btn btn-success" onclick="confirmApproval()">Aprovar</button>
          <button class="btn btn-danger" onclick="confirmRejection()">Rejeitar</button>
          <button class="btn btn-warning" onclick="requestMoreInfo()">Solicitar Informações</button>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      doc, 
      updateDoc, 
      addDoc,
      query,
      where,
      orderBy,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let currentUser = null;
    let pendingApprovals = [];

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }
      
      currentUser = JSON.parse(userSession);
      await loadPendingApprovals();
      updateDashboard();
    };

    async function loadPendingApprovals() {
      try {
        // Carregar aprovações baseadas no nível do usuário
        const userLevel = currentUser.nivel;
        
        const solicitacoesSnap = await getDocs(
          query(
            collection(db, "solicitacoesCompra"),
            where("workflowAtual", "<=", userLevel),
            where("status", "==", "AGUARDANDO_APROVACAO"),
            orderBy("dataCriacao", "desc")
          )
        );
        
        pendingApprovals = solicitacoesSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        updateApprovalsTable();
      } catch (error) {
        console.error("Erro ao carregar aprovações:", error);
      }
    }

    function updateApprovalsTable() {
      const tableBody = document.getElementById('approvals-table-body');
      if (!tableBody) return;
      
      tableBody.innerHTML = '';
      
      pendingApprovals.forEach(approval => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="checkbox" class="item-select" data-id="${approval.id}"></td>
          <td>
            <strong>${approval.numero}</strong><br>
            <small>Solicitação de Compra</small>
          </td>
          <td>
            ${approval.solicitanteNome}<br>
            <small>${approval.departamento || 'N/A'}</small>
          </td>
          <td><strong>R$ ${(approval.valorTotal || 0).toFixed(2)}</strong></td>
          <td>${formatDate(approval.dataCriacao)}</td>
          <td>
            <span class="priority-indicator priority-${approval.prioridade?.toLowerCase() || 'baixa'}"></span>
            ${approval.prioridade || 'Baixa'}
          </td>
          <td>
            <div class="urgency-timer">
              <div class="timer-icon ${getTimerClass(approval)}"></div>
              ${calculateDaysRemaining(approval)}
            </div>
          </td>
          <td>
            <button class="btn btn-success btn-sm" onclick="approveDocument('${approval.id}')">Aprovar</button>
            <button class="btn btn-danger btn-sm" onclick="rejectDocument('${approval.id}')">Rejeitar</button>
            <button class="btn btn-primary btn-sm" onclick="viewDocument('${approval.id}')">Detalhes</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function updateDashboard() {
      document.getElementById('pendentes-count').textContent = pendingApprovals.length;
      
      const urgentes = pendingApprovals.filter(a => a.prioridade === 'ALTA').length;
      document.getElementById('urgentes-count').textContent = urgentes;
      
      const hoje = pendingApprovals.filter(a => {
        const data = new Date(a.dataCriacao.seconds * 1000);
        const hoje = new Date();
        return data.toDateString() === hoje.toDateString();
      }).length;
      document.getElementById('hoje-count').textContent = hoje;
    }

    window.showTab = function(tabName) {
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
      document.getElementById(tabName).classList.add('active');
    };

    window.toggleSelectAll = function() {
      const selectAll = document.getElementById('select-all');
      const checkboxes = document.querySelectorAll('.item-select');
      
      checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
      });
    };

    window.approveDocument = async function(documentId) {
      if (confirm('Confirma a aprovação deste documento?')) {
        try {
          await updateDoc(doc(db, "solicitacoesCompra", documentId), {
            status: 'APROVADO',
            dataAprovacao: Timestamp.now(),
            aprovadoPor: currentUser.id,
            aprovadorNome: currentUser.nome
          });
          
          alert('Documento aprovado com sucesso!');
          await loadPendingApprovals();
          updateDashboard();
        } catch (error) {
          console.error("Erro ao aprovar documento:", error);
          alert("Erro ao aprovar documento.");
        }
      }
    };

    window.rejectDocument = async function(documentId) {
      const motivo = prompt('Informe o motivo da rejeição:');
      if (motivo) {
        try {
          await updateDoc(doc(db, "solicitacoesCompra", documentId), {
            status: 'REJEITADO',
            dataRejeicao: Timestamp.now(),
            rejeitadoPor: currentUser.id,
            rejeitadorNome: currentUser.nome,
            motivoRejeicao: motivo
          });
          
          alert('Documento rejeitado com sucesso!');
          await loadPendingApprovals();
          updateDashboard();
        } catch (error) {
          console.error("Erro ao rejeitar documento:", error);
          alert("Erro ao rejeitar documento.");
        }
      }
    };

    window.viewDocument = function(documentId) {
      const document = pendingApprovals.find(a => a.id === documentId);
      if (document) {
        // Preencher modal com dados do documento
        document.getElementById('doc-numero').textContent = document.numero;
        document.getElementById('doc-solicitante').textContent = document.solicitanteNome;
        document.getElementById('doc-departamento').textContent = document.departamento || 'N/A';
        document.getElementById('doc-valor').textContent = `R$ ${(document.valorTotal || 0).toFixed(2)}`;
        document.getElementById('doc-justificativa').textContent = document.justificativa || 'N/A';
        
        document.getElementById('modal-detalhes').style.display = 'block';
      }
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
    };

    window.bulkApprove = async function() {
      const selected = getSelectedDocuments();
      if (selected.length === 0) {
        alert('Selecione pelo menos um documento.');
        return;
      }
      
      if (confirm(`Aprovar ${selected.length} documento(s) selecionado(s)?`)) {
        try {
          for (const docId of selected) {
            await updateDoc(doc(db, "solicitacoesCompra", docId), {
              status: 'APROVADO',
              dataAprovacao: Timestamp.now(),
              aprovadoPor: currentUser.id,
              aprovadorNome: currentUser.nome
            });
          }
          
          alert(`${selected.length} documento(s) aprovado(s) com sucesso!`);
          await loadPendingApprovals();
          updateDashboard();
        } catch (error) {
          console.error("Erro na aprovação em lote:", error);
          alert("Erro na aprovação em lote.");
        }
      }
    };

    window.bulkReject = function() {
      const selected = getSelectedDocuments();
      if (selected.length === 0) {
        alert('Selecione pelo menos um documento.');
        return;
      }
      
      const motivo = prompt('Informe o motivo da rejeição:');
      if (motivo) {
        // Implementar rejeição em lote
        alert(`${selected.length} documento(s) rejeitado(s).`);
      }
    };

    function getSelectedDocuments() {
      const checkboxes = document.querySelectorAll('.item-select:checked');
      return Array.from(checkboxes).map(cb => cb.dataset.id).filter(id => id);
    }

    function formatDate(timestamp) {
      if (!timestamp) return '';
      return new Date(timestamp.seconds * 1000).toLocaleDateString('pt-BR');
    }

    function getTimerClass(approval) {
      const days = calculateDaysRemainingNumber(approval);
      if (days <= 1) return 'timer-critical';
      if (days <= 3) return 'timer-warning';
      return 'timer-normal';
    }

    function calculateDaysRemaining(approval) {
      const days = calculateDaysRemainingNumber(approval);
      if (days < 0) return 'Vencido';
      if (days === 0) return 'Hoje';
      return `${days} dia${days !== 1 ? 's' : ''}`;
    }

    function calculateDaysRemainingNumber(approval) {
      const created = new Date(approval.dataCriacao.seconds * 1000);
      const deadline = new Date(created);
      deadline.setDate(deadline.getDate() + 3); // 3 dias padrão
      
      const now = new Date();
      const diffTime = deadline - now;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    window.confirmApproval = function() {
      const observacoes = document.querySelector('#modal-detalhes textarea').value;
      // Implementar aprovação com observações
      alert('Documento aprovado com observações!');
      closeModal('modal-detalhes');
    };

    window.confirmRejection = function() {
      const observacoes = document.querySelector('#modal-detalhes textarea').value;
      if (!observacoes.trim()) {
        alert('Por favor, informe o motivo da rejeição.');
        return;
      }
      // Implementar rejeição com observações
      alert('Documento rejeitado!');
      closeModal('modal-detalhes');
    };

    window.requestMoreInfo = function() {
      const observacoes = document.querySelector('#modal-detalhes textarea').value;
      if (!observacoes.trim()) {
        alert('Por favor, especifique quais informações são necessárias.');
        return;
      }
      // Implementar solicitação de informações
      alert('Solicitação de informações enviada!');
      closeModal('modal-detalhes');
    };

    window.openBulkApproval = function() {
      alert('Funcionalidade de aprovação em lote será implementada.');
    };

    window.configurarWorkflow = function() {
      alert('Configuração de workflow será implementada.');
    };

    window.delegateSelected = function() {
      alert('Funcionalidade de delegação será implementada.');
    };
  </script>
</body>
</html>
