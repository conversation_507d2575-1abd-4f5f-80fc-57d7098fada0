<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🏷️ Cadastro de Grupo</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* ========================================
       🎨 CSS PADRONIZADO - CADASTRO GRUPO
       Baseado em: gestao_compras_integrada.html
       ======================================== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .form-container {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }

    .form-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
      display: block;
    }

    .form-control {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    textarea.form-control {
      resize: vertical;
      min-height: 80px;
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .groups-table {
      width: 100%;
      border-collapse: collapse;
    }

    .groups-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .groups-table th:hover {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .groups-table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .groups-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .edit-btn {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .edit-btn:hover, .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .required::after {
      content: " *";
      color: #e74c3c;
      font-weight: bold;
    }

    .info-text {
      font-size: 12px;
      color: #7f8c8d;
      margin-top: 5px;
      font-style: italic;
    }

    .table-actions {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      align-items: center;
    }

    .search-container {
      flex: 1;
    }

    .search-container input {
      width: 100%;
      max-width: 400px;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .search-container input:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .sort-indicator {
      margin-left: 8px;
      font-size: 14px;
      color: #ecf0f1;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    /* Responsividade */
    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .table-container {
        overflow-x: auto;
      }

      .table-actions {
        flex-direction: column;
        align-items: stretch;
      }

      .search-container input {
        max-width: none;
      }

      .form-actions {
        flex-direction: column;
      }

      .action-buttons {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-layer-group"></i>
        Cadastro de Grupo
      </h1>
      <div class="header-actions">
        <button class="btn btn-warning" onclick="window.location.href='index.html'">
          <i class="fas fa-home"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Formulário de Cadastro -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-plus-circle"></i>
          Cadastrar Novo Grupo
        </h2>
        <form id="groupForm">
          <input type="hidden" id="editingId">

          <div class="form-group">
            <label for="codigoGrupo" class="required">Código do Grupo</label>
            <input type="text" id="codigoGrupo" class="form-control" required>
            <div class="info-text">
              <i class="fas fa-info-circle"></i>
              Código único para identificação do grupo
            </div>
          </div>

          <div class="form-group">
            <label for="nomeGrupo" class="required">Nome do Grupo</label>
            <input type="text" id="nomeGrupo" class="form-control" required>
          </div>

          <div class="form-group">
            <label for="exemplosGrupo">Exemplos</label>
            <textarea id="exemplosGrupo" class="form-control" rows="3" placeholder="Digite exemplos de itens que podem estar neste grupo..."></textarea>
            <div class="info-text">
              <i class="fas fa-lightbulb"></i>
              Descreva exemplos de itens relacionados a este grupo (opcional)
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
              <i class="fas fa-times"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-success" id="submitButton">
              <i class="fas fa-save"></i>
              Cadastrar Grupo
            </button>
          </div>
        </form>
      </div>

      <!-- Lista de Grupos -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-list"></i>
          Grupos Cadastrados
        </h2>

        <div class="table-actions">
          <div class="search-container">
            <input type="text" id="searchInput" class="form-control" placeholder="🔍 Pesquisar por nome ou código..." oninput="filterGroups()">
          </div>
        </div>

        <div class="table-container">
          <table class="groups-table">
            <thead>
              <tr>
                <th onclick="sortTable('codigoGrupo')">
                  <i class="fas fa-code"></i>
                  Código
                  <span id="sortCodigo" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('nomeGrupo')">
                  <i class="fas fa-tag"></i>
                  Nome
                  <span id="sortNome" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('exemplosGrupo')">
                  <i class="fas fa-lightbulb"></i>
                  Exemplos
                  <span id="sortExemplos" class="sort-indicator"></span>
                </th>
                <th>
                  <i class="fas fa-cogs"></i>
                  Ações
                </th>
              </tr>
            </thead>
            <tbody id="groupsTableBody">
              <tr>
                <td colspan="4" style="text-align: center; padding: 40px;">
                  <i class="fas fa-spinner fa-spin"></i>
                  Carregando grupos...
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      updateDoc, 
      deleteDoc,
      query,
      where 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let grupos = [];
    let produtos = [];
    let sortDirection = 'asc'; // Direção da ordenação
    let currentSortColumn = ''; // Coluna atual sendo ordenada

    window.onload = async function() {
      await loadData();
      displayGroups();
    };

    async function loadData() {
      try {
        const [gruposSnapshot, produtosSnapshot] = await Promise.all([
          getDocs(collection(db, "grupos")),
          getDocs(collection(db, "produtos"))
        ]);

        grupos = [];
        gruposSnapshot.forEach((doc) => {
          grupos.push({ id: doc.id, ...doc.data() });
        });

        produtos = [];
        produtosSnapshot.forEach((doc) => {
          produtos.push({ id: doc.id, ...doc.data() });
        });
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function isGroupInUse(codigoGrupo) {
      return produtos.some(produto => produto.grupo === codigoGrupo);
    }

    function displayGroups(filteredGroups = grupos) {
      const tableBody = document.getElementById('groupsTableBody');
      tableBody.innerHTML = '';

      filteredGroups.forEach(group => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><i class="fas fa-code"></i> ${group.codigoGrupo}</td>
          <td><i class="fas fa-tag"></i> ${group.nomeGrupo}</td>
          <td><i class="fas fa-lightbulb"></i> ${group.exemplosGrupo || 'Nenhum exemplo'}</td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editGroup('${group.id}')">
              <i class="fas fa-edit"></i> Editar
            </button>
            <button class="delete-btn" onclick="deleteGroup('${group.id}')">
              <i class="fas fa-trash"></i> Excluir
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      if (sortBy === 'codigoGrupo') {
        grupos.sort((a, b) => sortDirection === 'asc' 
          ? a.codigoGrupo.localeCompare(b.codigoGrupo)
          : b.codigoGrupo.localeCompare(a.codigoGrupo));
      } else if (sortBy === 'nomeGrupo') {
        grupos.sort((a, b) => sortDirection === 'asc' 
          ? a.nomeGrupo.localeCompare(b.nomeGrupo)
          : b.nomeGrupo.localeCompare(a.nomeGrupo));
      } else if (sortBy === 'exemplosGrupo') {
        grupos.sort((a, b) => sortDirection === 'asc' 
          ? (a.exemplosGrupo || '').localeCompare(b.exemplosGrupo || '')
          : (b.exemplosGrupo || '').localeCompare(a.exemplosGrupo || ''));
      }

      updateSortIndicators(sortBy, sortDirection);
      displayGroups();
    };

    function updateSortIndicators(column, direction) {
      document.getElementById('sortCodigo').innerHTML = '';
      document.getElementById('sortNome').innerHTML = '';
      document.getElementById('sortExemplos').innerHTML = '';

      if (column === 'codigoGrupo') {
        document.getElementById('sortCodigo').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'nomeGrupo') {
        document.getElementById('sortNome').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'exemplosGrupo') {
        document.getElementById('sortExemplos').innerHTML = direction === 'asc' ? '▲' : '▼';
      }
    }

    window.filterGroups = function() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      let filteredGroups = grupos.filter(group => {
        const matchCodigo = group.codigoGrupo.toLowerCase().includes(searchTerm);
        const matchNome = group.nomeGrupo.toLowerCase().includes(searchTerm);
        const matchExemplos = (group.exemplosGrupo || '').toLowerCase().includes(searchTerm);
        return matchCodigo || matchNome || matchExemplos;
      });

      // Aplicar ordenação aos dados filtrados, se houver uma coluna ordenada
      if (currentSortColumn) {
        if (currentSortColumn === 'codigoGrupo') {
          filteredGroups.sort((a, b) => sortDirection === 'asc'
            ? a.codigoGrupo.localeCompare(b.codigoGrupo)
            : b.codigoGrupo.localeCompare(a.codigoGrupo));
        } else if (currentSortColumn === 'nomeGrupo') {
          filteredGroups.sort((a, b) => sortDirection === 'asc'
            ? a.nomeGrupo.localeCompare(b.nomeGrupo)
            : b.nomeGrupo.localeCompare(a.nomeGrupo));
        } else if (currentSortColumn === 'exemplosGrupo') {
          filteredGroups.sort((a, b) => sortDirection === 'asc'
            ? (a.exemplosGrupo || '').localeCompare(b.exemplosGrupo || '')
            : (b.exemplosGrupo || '').localeCompare(a.exemplosGrupo || ''));
        }
      }

      displayGroups(filteredGroups);
    };

    window.editGroup = function(groupId) {
      const group = grupos.find(g => g.id === groupId);
      if (group) {
        document.getElementById('editingId').value = groupId;
        document.getElementById('codigoGrupo').value = group.codigoGrupo;
        document.getElementById('nomeGrupo').value = group.nomeGrupo;
        document.getElementById('exemplosGrupo').value = group.exemplosGrupo || '';
        
        document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Atualizar Grupo';
      }
    };

    window.cancelEdit = function() {
      document.getElementById('groupForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('exemplosGrupo').value = '';
      document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Cadastrar Grupo';
    };

    window.deleteGroup = async function(groupId) {
      const group = grupos.find(g => g.id === groupId);
      if (isGroupInUse(group.codigoGrupo)) {
        alert('Este grupo não pode ser excluído pois está sendo usado em um ou mais produtos.');
        return;
      }

      if (confirm('Tem certeza que deseja excluir este grupo?')) {
        try {
          await deleteDoc(doc(db, "grupos", groupId));
          await loadData();
          displayGroups();
          alert('Grupo excluído com sucesso!');
        } catch (error) {
          console.error("Erro ao excluir grupo:", error);
          alert("Erro ao excluir grupo. Por favor, tente novamente.");
        }
      }
    };

    document.getElementById('groupForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const formData = {
        codigoGrupo: document.getElementById('codigoGrupo').value,
        nomeGrupo: document.getElementById('nomeGrupo').value,
        exemplosGrupo: document.getElementById('exemplosGrupo').value || '',
        dataCadastro: new Date()
      };

      const editingId = document.getElementById('editingId').value;

      try {
        if (editingId) {
          await updateDoc(doc(db, "grupos", editingId), formData);
          alert("Grupo atualizado com sucesso!");
        } else {
          await addDoc(collection(db, "grupos"), formData);
          alert("Grupo cadastrado com sucesso!");
        }

        await loadData();
        displayGroups();
        event.target.reset();
        cancelEdit();
      } catch (error) {
        console.error("Erro ao salvar grupo:", error);
        alert("Erro ao salvar grupo. Por favor, tente novamente.");
      }
    });

    document.getElementById('searchInput').addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const filteredGroups = grupos.filter(group => 
        group.codigoGrupo.toLowerCase().includes(searchTerm) || 
        group.nomeGrupo.toLowerCase().includes(searchTerm) ||
        (group.exemplosGrupo || '').toLowerCase().includes(searchTerm)
      );
      displayGroups(filteredGroups);
    });
    
  </script>
 
</body>
</html>