/**
 * SCRIPT DE CORREÇÃO DE DATAS - SISTEMA NALITECK
 * Corrige inconsistências de datas em todo o sistema de compras
 * EXECUTAR APENAS APÓS BACKUP COMPLETO DO BANCO
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    doc, 
    updateDoc, 
    Timestamp,
    writeBatch,
    query,
    limit
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

import DateUtils from '../utils/date-utils.js';

class DateMigrationService {
    constructor() {
        this.totalDocuments = 0;
        this.processedDocuments = 0;
        this.correctedDocuments = 0;
        this.errorDocuments = 0;
        this.migrationLog = [];
    }

    // ===== MÉTODO PRINCIPAL =====
    async executeMigration() {
        console.log('🚀 Iniciando migração de datas do sistema...');
        
        try {
            // 1. Verificar conexão
            await this.checkConnection();
            
            // 2. Fazer backup de segurança
            await this.createBackup();
            
            // 3. Analisar dados existentes
            await this.analyzeExistingData();
            
            // 4. Executar correções por coleção
            await this.migrateCollection('solicitacoesCompra');
            await this.migrateCollection('pedidosCompra');
            await this.migrateCollection('cotacoes');
            await this.migrateCollection('recebimentoMateriais');
            await this.migrateCollection('movimentacaoEstoque');
            await this.migrateCollection('contasAPagar');
            
            // 5. Gerar relatório final
            await this.generateMigrationReport();
            
            console.log('✅ Migração concluída com sucesso!');
            
        } catch (error) {
            console.error('❌ Erro na migração:', error);
            await this.rollbackChanges();
            throw error;
        }
    }

    // ===== VERIFICAÇÃO DE CONEXÃO =====
    async checkConnection() {
        try {
            const testQuery = query(collection(db, 'solicitacoesCompra'), limit(1));
            await getDocs(testQuery);
            console.log('✅ Conexão com Firebase confirmada');
        } catch (error) {
            throw new Error('❌ Falha na conexão com Firebase: ' + error.message);
        }
    }

    // ===== BACKUP DE SEGURANÇA =====
    async createBackup() {
        console.log('💾 Criando backup de segurança...');
        
        const backupData = {
            timestamp: new Date().toISOString(),
            collections: {}
        };
        
        const collections = [
            'solicitacoesCompra', 'pedidosCompra', 'cotacoes', 
            'recebimentoMateriais', 'movimentacaoEstoque', 'contasAPagar'
        ];
        
        for (const collectionName of collections) {
            try {
                const snapshot = await getDocs(collection(db, collectionName));
                backupData.collections[collectionName] = snapshot.docs.map(doc => ({
                    id: doc.id,
                    data: doc.data()
                }));
                
                console.log(`📦 Backup de ${collectionName}: ${snapshot.docs.length} documentos`);
                
            } catch (error) {
                console.warn(`⚠️ Erro no backup de ${collectionName}:`, error);
            }
        }
        
        // Salvar backup no localStorage para recuperação
        localStorage.setItem('datesMigrationBackup', JSON.stringify(backupData));
        console.log('✅ Backup salvo no localStorage');
    }

    // ===== ANÁLISE DE DADOS EXISTENTES =====
    async analyzeExistingData() {
        console.log('🔍 Analisando dados existentes...');
        
        const analysis = {
            totalDocuments: 0,
            documentsWithDateIssues: 0,
            dateFieldsFound: new Set(),
            issueTypes: {
                invalidFormat: 0,
                missingDates: 0,
                inconsistentTypes: 0
            }
        };
        
        const collections = ['solicitacoesCompra', 'pedidosCompra', 'cotacoes'];
        
        for (const collectionName of collections) {
            const snapshot = await getDocs(collection(db, collectionName));
            analysis.totalDocuments += snapshot.docs.length;
            
            snapshot.docs.forEach(docSnap => {
                const data = docSnap.data();
                let hasIssues = false;
                
                // Verificar campos de data conhecidos
                const dateFields = this.getDateFields(collectionName);
                
                dateFields.forEach(field => {
                    if (data[field]) {
                        analysis.dateFieldsFound.add(`${collectionName}.${field}`);
                        
                        // Verificar se tem problemas
                        if (!DateUtils.isValidDate(data[field])) {
                            analysis.issueTypes.invalidFormat++;
                            hasIssues = true;
                        }
                        
                        // Verificar inconsistência de tipo
                        if (typeof data[field] === 'string' && !data[field].includes('T')) {
                            analysis.issueTypes.inconsistentTypes++;
                            hasIssues = true;
                        }
                    } else if (this.isRequiredDateField(field)) {
                        analysis.issueTypes.missingDates++;
                        hasIssues = true;
                    }
                });
                
                if (hasIssues) {
                    analysis.documentsWithDateIssues++;
                }
            });
        }
        
        this.totalDocuments = analysis.totalDocuments;
        
        console.log('📊 Análise concluída:', {
            totalDocuments: analysis.totalDocuments,
            documentsWithIssues: analysis.documentsWithDateIssues,
            issueTypes: analysis.issueTypes,
            dateFields: Array.from(analysis.dateFieldsFound)
        });
    }

    // ===== MIGRAÇÃO POR COLEÇÃO =====
    async migrateCollection(collectionName) {
        console.log(`🔄 Migrando coleção: ${collectionName}`);
        
        try {
            const snapshot = await getDocs(collection(db, collectionName));
            const batch = writeBatch(db);
            let batchCount = 0;
            let correctedInCollection = 0;
            
            for (const docSnap of snapshot.docs) {
                const data = docSnap.data();
                const corrections = this.analyzeDocumentDates(data, collectionName);
                
                if (Object.keys(corrections).length > 0) {
                    batch.update(doc(db, collectionName, docSnap.id), corrections);
                    batchCount++;
                    correctedInCollection++;
                    
                    this.migrationLog.push({
                        collection: collectionName,
                        documentId: docSnap.id,
                        corrections: corrections,
                        timestamp: new Date().toISOString()
                    });
                    
                    // Executar batch a cada 500 documentos
                    if (batchCount >= 500) {
                        await batch.commit();
                        console.log(`📝 Batch executado: ${batchCount} documentos`);
                        batchCount = 0;
                    }
                }
                
                this.processedDocuments++;
            }
            
            // Executar batch restante
            if (batchCount > 0) {
                await batch.commit();
                console.log(`📝 Batch final executado: ${batchCount} documentos`);
            }
            
            this.correctedDocuments += correctedInCollection;
            console.log(`✅ ${collectionName}: ${correctedInCollection} documentos corrigidos`);
            
        } catch (error) {
            console.error(`❌ Erro na migração de ${collectionName}:`, error);
            this.errorDocuments++;
            throw error;
        }
    }

    // ===== ANÁLISE DE DATAS DO DOCUMENTO =====
    analyzeDocumentDates(data, collectionName) {
        const corrections = {};
        const dateFields = this.getDateFields(collectionName);
        
        dateFields.forEach(field => {
            if (data[field]) {
                const originalValue = data[field];
                const correctedValue = this.correctDateField(originalValue);
                
                if (correctedValue && this.isDifferent(originalValue, correctedValue)) {
                    corrections[field] = correctedValue;
                }
            }
        });
        
        // Verificar arrays com datas (histórico, parcelas, etc.)
        if (data.historico && Array.isArray(data.historico)) {
            const correctedHistorico = data.historico.map(item => {
                if (item.data && !DateUtils.isValidDate(item.data)) {
                    return { ...item, data: this.correctDateField(item.data) };
                }
                return item;
            });
            
            if (JSON.stringify(correctedHistorico) !== JSON.stringify(data.historico)) {
                corrections.historico = correctedHistorico;
            }
        }
        
        if (data.parcelas && Array.isArray(data.parcelas)) {
            const correctedParcelas = data.parcelas.map(parcela => {
                const corrected = { ...parcela };
                if (parcela.dataVencimento && !DateUtils.isValidDate(parcela.dataVencimento)) {
                    corrected.dataVencimento = this.correctDateField(parcela.dataVencimento);
                }
                if (parcela.dataPagamento && !DateUtils.isValidDate(parcela.dataPagamento)) {
                    corrected.dataPagamento = this.correctDateField(parcela.dataPagamento);
                }
                return corrected;
            });
            
            if (JSON.stringify(correctedParcelas) !== JSON.stringify(data.parcelas)) {
                corrections.parcelas = correctedParcelas;
            }
        }
        
        return corrections;
    }

    // ===== CORREÇÃO DE CAMPO DE DATA =====
    correctDateField(value) {
        if (!value) return null;
        
        try {
            // Se já é um Timestamp válido, manter
            if (value.seconds && typeof value.seconds === 'number') {
                return value;
            }
            
            // Tentar converter para Timestamp
            const timestamp = DateUtils.toTimestamp(value);
            return timestamp;
            
        } catch (error) {
            console.warn('Erro ao corrigir campo de data:', error);
            return null;
        }
    }

    // ===== CAMPOS DE DATA POR COLEÇÃO =====
    getDateFields(collectionName) {
        const dateFieldsMap = {
            'solicitacoesCompra': [
                'dataCriacao', 'dataAprovacao', 'dataNecessidade', 
                'dataUltimaAtualizacao', 'dataEnvio'
            ],
            'pedidosCompra': [
                'dataCriacao', 'dataAprovacao', 'dataEnvio', 'dataEntregaPrevista',
                'dataRecebimento', 'dataCancelamento', 'ultimaAtualizacao'
            ],
            'cotacoes': [
                'dataCriacao', 'dataEnvio', 'dataVencimento', 'dataAprovacao',
                'dataRecusa', 'ultimaAtualizacao'
            ],
            'recebimentoMateriais': [
                'dataRecebimento', 'dataCriacao', 'dataInspecao'
            ],
            'movimentacaoEstoque': [
                'dataMovimentacao', 'dataCriacao'
            ],
            'contasAPagar': [
                'dataEmissao', 'dataVencimento', 'dataPagamento', 'dataCadastro'
            ]
        };
        
        return dateFieldsMap[collectionName] || [];
    }

    // ===== CAMPOS OBRIGATÓRIOS =====
    isRequiredDateField(field) {
        const requiredFields = ['dataCriacao', 'dataEmissao', 'dataRecebimento'];
        return requiredFields.includes(field);
    }

    // ===== VERIFICAR SE VALORES SÃO DIFERENTES =====
    isDifferent(original, corrected) {
        if (!original && !corrected) return false;
        if (!original || !corrected) return true;
        
        // Comparar timestamps
        const originalTime = original.seconds || new Date(original).getTime() / 1000;
        const correctedTime = corrected.seconds || new Date(corrected).getTime() / 1000;
        
        return Math.abs(originalTime - correctedTime) > 1; // Diferença > 1 segundo
    }

    // ===== RELATÓRIO DE MIGRAÇÃO =====
    async generateMigrationReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalDocuments: this.totalDocuments,
                processedDocuments: this.processedDocuments,
                correctedDocuments: this.correctedDocuments,
                errorDocuments: this.errorDocuments,
                successRate: ((this.correctedDocuments / this.totalDocuments) * 100).toFixed(2) + '%'
            },
            details: this.migrationLog,
            recommendations: this.generateRecommendations()
        };
        
        // Salvar relatório
        localStorage.setItem('datesMigrationReport', JSON.stringify(report));
        
        // Exibir resumo
        console.log('📋 RELATÓRIO DE MIGRAÇÃO:');
        console.table(report.summary);
        
        // Baixar relatório como arquivo
        this.downloadReport(report);
        
        return report;
    }

    // ===== RECOMENDAÇÕES =====
    generateRecommendations() {
        const recommendations = [];
        
        if (this.errorDocuments > 0) {
            recommendations.push('Verificar documentos com erro e corrigir manualmente');
        }
        
        if (this.correctedDocuments > 0) {
            recommendations.push('Implementar validações de data nos formulários');
            recommendations.push('Usar DateUtils em todas as operações de data');
        }
        
        recommendations.push('Monitorar qualidade de dados regularmente');
        recommendations.push('Treinar equipe sobre novos padrões de data');
        
        return recommendations;
    }

    // ===== DOWNLOAD DO RELATÓRIO =====
    downloadReport(report) {
        const blob = new Blob([JSON.stringify(report, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `migration-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // ===== ROLLBACK EM CASO DE ERRO =====
    async rollbackChanges() {
        console.log('🔄 Executando rollback...');
        
        try {
            const backup = localStorage.getItem('datesMigrationBackup');
            if (!backup) {
                throw new Error('Backup não encontrado');
            }
            
            const backupData = JSON.parse(backup);
            
            for (const [collectionName, documents] of Object.entries(backupData.collections)) {
                const batch = writeBatch(db);
                let batchCount = 0;
                
                for (const docData of documents) {
                    batch.set(doc(db, collectionName, docData.id), docData.data);
                    batchCount++;
                    
                    if (batchCount >= 500) {
                        await batch.commit();
                        batchCount = 0;
                    }
                }
                
                if (batchCount > 0) {
                    await batch.commit();
                }
                
                console.log(`✅ Rollback de ${collectionName} concluído`);
            }
            
            console.log('✅ Rollback concluído com sucesso');
            
        } catch (error) {
            console.error('❌ Erro no rollback:', error);
            alert('ERRO CRÍTICO: Falha no rollback. Contate o administrador imediatamente!');
        }
    }
}

// ===== INTERFACE DE EXECUÇÃO =====
export async function executeDateMigration() {
    const confirmed = confirm(
        '⚠️ ATENÇÃO: Esta operação irá corrigir todas as datas do sistema.\n\n' +
        'Certifique-se de que:\n' +
        '1. Foi feito backup completo do banco\n' +
        '2. Não há usuários ativos no sistema\n' +
        '3. Você tem permissões de administrador\n\n' +
        'Deseja continuar?'
    );
    
    if (!confirmed) {
        console.log('❌ Migração cancelada pelo usuário');
        return;
    }
    
    const migrationService = new DateMigrationService();
    
    try {
        await migrationService.executeMigration();
        alert('✅ Migração de datas concluída com sucesso!\nVerifique o console para detalhes.');
        
    } catch (error) {
        console.error('❌ Erro na migração:', error);
        alert('❌ Erro na migração de datas. Verifique o console para detalhes.');
    }
}

// ===== FUNÇÃO DE TESTE =====
export async function testDateMigration() {
    console.log('🧪 Executando teste de migração...');
    
    const migrationService = new DateMigrationService();
    
    // Testar apenas alguns documentos
    await migrationService.analyzeExistingData();
    
    console.log('✅ Teste concluído. Verifique o console para resultados.');
}

// Exportar serviço
export default DateMigrationService;
