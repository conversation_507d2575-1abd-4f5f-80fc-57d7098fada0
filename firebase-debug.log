[debug] [2025-06-23T13:45:48.742Z] ----------------------------------------------------------------------
[debug] [2025-06-23T13:45:48.765Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-06-23T13:45:48.767Z] CLI Version:   14.8.0
[debug] [2025-06-23T13:45:48.767Z] Platform:      win32
[debug] [2025-06-23T13:45:48.767Z] Node Version:  v22.13.1
[debug] [2025-06-23T13:45:48.769Z] Time:          Mon Jun 23 2025 10:45:48 GMT-0300 (<PERSON><PERSON><PERSON><PERSON>ras<PERSON>)
[debug] [2025-06-23T13:45:48.769Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-23T13:45:49.318Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-23T13:45:49.320Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\app_naliteck

Before we get started, keep in mind:

  * You are currently outside your home directory
  * You are initializing within an existing Firebase project directory

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-06-23T13:47:41.146Z] Using project from CLI flag: naliteck
[debug] [2025-06-23T13:47:41.149Z] Checked if tokens are valid: true, expires at: 1750689888970
[debug] [2025-06-23T13:47:41.149Z] Checked if tokens are valid: true, expires at: 1750689888970
[debug] [2025-06-23T13:47:41.154Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/naliteck [none]
[debug] [2025-06-23T13:47:41.785Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/naliteck 200
[debug] [2025-06-23T13:47:41.785Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/naliteck {"projectId":"naliteck","projectNumber":"866077066540","displayName":"naliteck","name":"projects/naliteck","resources":{"hostingSite":"naliteck"},"state":"ACTIVE","etag":"1_a5e28827-3960-4d7f-83ce-57e6457ed67c"}
[info] i  Using project naliteck (naliteck) 
[info] 
=== Hosting Setup
[debug] [2025-06-23T13:47:41.798Z] Checked if tokens are valid: true, expires at: 1750689888970
[debug] [2025-06-23T13:47:41.798Z] Checked if tokens are valid: true, expires at: 1750689888970
[debug] [2025-06-23T13:47:41.799Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/naliteck [none]
[debug] [2025-06-23T13:47:42.140Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/naliteck 200
[debug] [2025-06-23T13:47:42.141Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/naliteck {"projectId":"naliteck","projectNumber":"866077066540","displayName":"naliteck","name":"projects/naliteck","resources":{"hostingSite":"naliteck"},"state":"ACTIVE","etag":"1_a5e28827-3960-4d7f-83ce-57e6457ed67c"}
[info] 
[info] Your public directory is the folder (relative to your project directory) that
[info] will contain Hosting assets to be uploaded with firebase deploy. If you
[info] have a build process for your assets, use your build's output directory.
[info] 
[info] i  public/404.html is unchanged 
[debug] [2025-06-23T13:47:49.627Z] >>> [apiv2][query] GET https://www.gstatic.com/firebasejs/releases.json [none]
[debug] [2025-06-23T13:47:49.742Z] <<< [apiv2][status] GET https://www.gstatic.com/firebasejs/releases.json 200
[debug] [2025-06-23T13:47:49.742Z] <<< [apiv2][body] GET https://www.gstatic.com/firebasejs/releases.json {"current":{"version":"11.9.1","browserURL":"https://www.gstatic.com/firebasejs/11.9.1/firebase.js","packageURL":"https://www.gstatic.com/firebasejs/11.9.1/firebase.tgz"},"live":{"version":"11.9","browserURL":"https://www.gstatic.com/firebasejs/live/11.9/firebase.js","packageURL":"https://www.gstatic.com/firebasejs/live/11.9/firebase.tgz"},"components":{"ai":"https://www.gstatic.com/firebasejs/11.9.1/firebase-ai.js","analytics":"https://www.gstatic.com/firebasejs/11.9.1/firebase-analytics.js","app":"https://www.gstatic.com/firebasejs/11.9.1/firebase-app.js","app-check":"https://www.gstatic.com/firebasejs/11.9.1/firebase-app-check.js","auth":"https://www.gstatic.com/firebasejs/11.9.1/firebase-auth.js","auth/cordova":"https://www.gstatic.com/firebasejs/11.9.1/firebase-auth/cordova.js","auth/web-extension":"https://www.gstatic.com/firebasejs/11.9.1/firebase-auth/web-extension.js","functions":"https://www.gstatic.com/firebasejs/11.9.1/firebase-functions.js","firestore":"https://www.gstatic.com/firebasejs/11.9.1/firebase-firestore.js","firestore/lite":"https://www.gstatic.com/firebasejs/11.9.1/firebase-firestore/lite.js","installations":"https://www.gstatic.com/firebasejs/11.9.1/firebase-installations.js","storage":"https://www.gstatic.com/firebasejs/11.9.1/firebase-storage.js","performance":"https://www.gstatic.com/firebasejs/11.9.1/firebase-performance.js","remote-config":"https://www.gstatic.com/firebasejs/11.9.1/firebase-remote-config.js","messaging":"https://www.gstatic.com/firebasejs/11.9.1/firebase-messaging.js","messaging/sw":"https://www.gstatic.com/firebasejs/11.9.1/firebase-messaging/sw.js","database":"https://www.gstatic.com/firebasejs/11.9.1/firebase-database.js","vertexai":"https://www.gstatic.com/firebasejs/11.9.1/firebase-vertexai.js","data-connect":"https://www.gstatic.com/firebasejs/11.9.1/firebase-data-connect.js","firestore.memory":"https://www.gstatic.com/firebasejs/11.9.1/firebase-firestore.memory.js"}}
[info] i  Skipping write of public/index.html 
[info] 
[info] +  Wrote configuration info to firebase.json 
[info] +  Wrote project information to .firebaserc 
[info] +  Wrote .gitignore 
[info] 
[info] +  Firebase initialization complete! 
