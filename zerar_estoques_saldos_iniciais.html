<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zerar Estoques e Definir Saldos Iniciais</title>
    <style>
        :root {
            --primary-color: #dc3545;
            --primary-hover: #c82333;
            --success-color: #28a745;
            --success-hover: #218838;
            --warning-color: #ffc107;
            --warning-hover: #e0a800;
            --info-color: #17a2b8;
            --info-hover: #138496;
            --secondary-color: #6c757d;
            --border-color: #dee2e6;
            --text-color: #333;
            --bg-light: #f8f9fa;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .warning-box {
            background: #fff3cd;
            border: 2px solid var(--warning-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .warning-box h3 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .warning-box p {
            color: #856404;
            font-size: 16px;
            line-height: 1.5;
        }

        .section {
            background: var(--bg-light);
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
        }

        .section h3 {
            color: var(--text-color);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-danger {
            background: var(--primary-color);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: var(--primary-hover);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background: var(--success-hover);
        }

        .btn-warning {
            background: var(--warning-color);
            color: #212529;
        }

        .btn-warning:hover:not(:disabled) {
            background: var(--warning-hover);
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .btn-info:hover:not(:disabled) {
            background: var(--info-hover);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .progress-container {
            display: none;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--info-color));
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .status-box {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .status-box.show {
            display: block;
        }

        .status-box h4 {
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .status-list {
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }

        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .log-entry.success {
            color: var(--success-color);
        }

        .log-entry.error {
            color: var(--primary-color);
        }

        .log-entry.info {
            color: var(--info-color);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .action-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            border-color: var(--info-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .action-card h4 {
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .action-card p {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.5;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .content {
                padding: 20px;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .back-button {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-secondary back-button" onclick="window.location.href='index.html'">
        ← Voltar ao Menu
    </button>

    <div class="container">
        <div class="header">
            <h1>🗑️ Zerar Estoques e Definir Saldos Iniciais</h1>
            <p>Ferramenta para reinicializar o sistema de estoque com novos saldos</p>
        </div>

        <div class="content">
            <div class="warning-box">
                <h3>⚠️ ATENÇÃO - OPERAÇÃO IRREVERSÍVEL</h3>
                <p>
                    Esta operação irá <strong>APAGAR PERMANENTEMENTE</strong> todos os dados de estoque e movimentações.<br>
                    Certifique-se de ter um backup antes de prosseguir!
                </p>
            </div>

            <div class="actions-grid">
                <div class="action-card">
                    <h4>🗑️ Zerar Estoques</h4>
                    <p>Remove todos os registros de estoque, zerando todos os saldos</p>
                    <button class="btn btn-danger" onclick="zerarEstoques()">
                        🗑️ Zerar Estoques
                    </button>
                </div>

                <div class="action-card">
                    <h4>📋 Zerar Movimentações</h4>
                    <p>Remove todo o histórico de movimentações de estoque</p>
                    <button class="btn btn-danger" onclick="zerarMovimentacoes()">
                        📋 Zerar Movimentações
                    </button>
                </div>

                <div class="action-card">
                    <h4>🔄 Zerar Tudo</h4>
                    <p>Remove estoques, movimentações e estoque de qualidade</p>
                    <button class="btn btn-danger" onclick="zerarTudo()">
                        🔄 Zerar Tudo
                    </button>
                </div>

                <div class="action-card">
                    <h4>📊 Definir Saldos Iniciais</h4>
                    <p>Abre tela para inserir saldos iniciais dos produtos</p>
                    <button class="btn btn-success" onclick="abrirSaldosIniciais()">
                        📊 Saldos Iniciais
                    </button>
                </div>

                <div class="action-card">
                    <h4>📈 Verificar Status</h4>
                    <p>Mostra estatísticas atuais do sistema de estoque</p>
                    <button class="btn btn-info" onclick="verificarStatus()">
                        📈 Ver Status
                    </button>
                </div>

                <div class="action-card">
                    <h4>💾 Backup Dados</h4>
                    <p>Exporta dados atuais antes de zerar (recomendado)</p>
                    <button class="btn btn-warning" onclick="exportarBackup()">
                        💾 Fazer Backup
                    </button>
                </div>
            </div>

            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill">0%</div>
                </div>
            </div>

            <div class="status-box" id="statusBox">
                <h4>📋 Log de Operações</h4>
                <div class="status-list" id="statusList"></div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            deleteDoc,
            doc,
            writeBatch,
            addDoc,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        function addLog(message, type = 'info') {
            const statusList = document.getElementById('statusList');
            const statusBox = document.getElementById('statusBox');
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            
            statusList.appendChild(logEntry);
            statusBox.classList.add('show');
            statusList.scrollTop = statusList.scrollHeight;
        }

        function updateProgress(current, total, text = '') {
            const container = document.getElementById('progressContainer');
            const fill = document.getElementById('progressFill');
            
            if (total === 0) {
                container.style.display = 'none';
                return;
            }
            
            container.style.display = 'block';
            const percentage = Math.round((current / total) * 100);
            fill.style.width = percentage + '%';
            fill.textContent = text || `${percentage}%`;
            
            if (current >= total) {
                setTimeout(() => {
                    container.style.display = 'none';
                }, 2000);
            }
        }

        window.zerarEstoques = async function() {
            if (!confirm('⚠️ ATENÇÃO: Esta operação irá APAGAR TODOS os estoques!\n\nTem certeza que deseja continuar?')) {
                return;
            }
            
            if (!confirm('🚨 ÚLTIMA CONFIRMAÇÃO: Todos os saldos de estoque serão PERDIDOS PERMANENTEMENTE!\n\nConfirma a operação?')) {
                return;
            }

            try {
                addLog('Iniciando exclusão de estoques...', 'info');
                
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const total = estoquesSnap.docs.length;
                
                addLog(`Encontrados ${total} registros de estoque para exclusão`, 'info');
                
                if (total === 0) {
                    addLog('Nenhum estoque encontrado para exclusão', 'info');
                    return;
                }

                let deleted = 0;
                const batch = writeBatch(db);
                
                for (const docSnap of estoquesSnap.docs) {
                    batch.delete(doc(db, "estoques", docSnap.id));
                    deleted++;
                    updateProgress(deleted, total, `Excluindo ${deleted}/${total}`);
                }
                
                await batch.commit();
                
                addLog(`✅ ${total} registros de estoque excluídos com sucesso!`, 'success');
                updateProgress(total, total, 'Concluído!');
                
            } catch (error) {
                console.error('Erro ao zerar estoques:', error);
                addLog(`❌ Erro ao zerar estoques: ${error.message}`, 'error');
            }
        };

        window.zerarMovimentacoes = async function() {
            if (!confirm('⚠️ ATENÇÃO: Esta operação irá APAGAR TODO o histórico de movimentações!\n\nTem certeza que deseja continuar?')) {
                return;
            }

            try {
                addLog('Iniciando exclusão de movimentações...', 'info');
                
                const movSnap = await getDocs(collection(db, "movimentacoesEstoque"));
                const total = movSnap.docs.length;
                
                addLog(`Encontrados ${total} registros de movimentação para exclusão`, 'info');
                
                if (total === 0) {
                    addLog('Nenhuma movimentação encontrada para exclusão', 'info');
                    return;
                }

                let deleted = 0;
                const batch = writeBatch(db);
                
                for (const docSnap of movSnap.docs) {
                    batch.delete(doc(db, "movimentacoesEstoque", docSnap.id));
                    deleted++;
                    updateProgress(deleted, total, `Excluindo ${deleted}/${total}`);
                }
                
                await batch.commit();
                
                addLog(`✅ ${total} registros de movimentação excluídos com sucesso!`, 'success');
                updateProgress(total, total, 'Concluído!');
                
            } catch (error) {
                console.error('Erro ao zerar movimentações:', error);
                addLog(`❌ Erro ao zerar movimentações: ${error.message}`, 'error');
            }
        };

        window.zerarTudo = async function() {
            if (!confirm('🚨 ATENÇÃO MÁXIMA: Esta operação irá APAGAR:\n\n• Todos os estoques\n• Todas as movimentações\n• Todo o estoque de qualidade\n\nTem certeza que deseja continuar?')) {
                return;
            }
            
            if (!confirm('🔥 CONFIRMAÇÃO FINAL: TODOS OS DADOS DE ESTOQUE SERÃO PERDIDOS!\n\nDigite "CONFIRMO" para prosseguir:') !== 'CONFIRMO') {
                const confirmacao = prompt('Digite "CONFIRMO" para prosseguir:');
                if (confirmacao !== 'CONFIRMO') {
                    addLog('Operação cancelada pelo usuário', 'info');
                    return;
                }
            }

            try {
                addLog('🔥 Iniciando LIMPEZA COMPLETA do sistema...', 'info');
                
                // Contar todos os registros
                const [estoquesSnap, movSnap, qualSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "estoqueQualidade"))
                ]);
                
                const totalEstoques = estoquesSnap.docs.length;
                const totalMov = movSnap.docs.length;
                const totalQual = qualSnap.docs.length;
                const totalGeral = totalEstoques + totalMov + totalQual;
                
                addLog(`📊 Registros encontrados:`, 'info');
                addLog(`   • Estoques: ${totalEstoques}`, 'info');
                addLog(`   • Movimentações: ${totalMov}`, 'info');
                addLog(`   • Estoque Qualidade: ${totalQual}`, 'info');
                addLog(`   • TOTAL: ${totalGeral}`, 'info');
                
                if (totalGeral === 0) {
                    addLog('Nenhum registro encontrado para exclusão', 'info');
                    return;
                }

                let deleted = 0;
                
                // Excluir estoques
                if (totalEstoques > 0) {
                    addLog('Excluindo estoques...', 'info');
                    const batch1 = writeBatch(db);
                    for (const docSnap of estoquesSnap.docs) {
                        batch1.delete(doc(db, "estoques", docSnap.id));
                        deleted++;
                        updateProgress(deleted, totalGeral, `Excluindo ${deleted}/${totalGeral}`);
                    }
                    await batch1.commit();
                    addLog(`✅ ${totalEstoques} estoques excluídos`, 'success');
                }
                
                // Excluir movimentações
                if (totalMov > 0) {
                    addLog('Excluindo movimentações...', 'info');
                    const batch2 = writeBatch(db);
                    for (const docSnap of movSnap.docs) {
                        batch2.delete(doc(db, "movimentacoesEstoque", docSnap.id));
                        deleted++;
                        updateProgress(deleted, totalGeral, `Excluindo ${deleted}/${totalGeral}`);
                    }
                    await batch2.commit();
                    addLog(`✅ ${totalMov} movimentações excluídas`, 'success');
                }
                
                // Excluir estoque qualidade
                if (totalQual > 0) {
                    addLog('Excluindo estoque de qualidade...', 'info');
                    const batch3 = writeBatch(db);
                    for (const docSnap of qualSnap.docs) {
                        batch3.delete(doc(db, "estoqueQualidade", docSnap.id));
                        deleted++;
                        updateProgress(deleted, totalGeral, `Excluindo ${deleted}/${totalGeral}`);
                    }
                    await batch3.commit();
                    addLog(`✅ ${totalQual} registros de qualidade excluídos`, 'success');
                }
                
                addLog(`🎉 LIMPEZA COMPLETA FINALIZADA! ${totalGeral} registros excluídos`, 'success');
                updateProgress(totalGeral, totalGeral, 'Concluído!');
                
            } catch (error) {
                console.error('Erro ao zerar tudo:', error);
                addLog(`❌ Erro na limpeza completa: ${error.message}`, 'error');
            }
        };

        window.verificarStatus = async function() {
            try {
                addLog('Verificando status do sistema...', 'info');
                
                const [estoquesSnap, movSnap, qualSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "estoqueQualidade")),
                    getDocs(collection(db, "produtos"))
                ]);
                
                const totalEstoques = estoquesSnap.docs.length;
                const totalMov = movSnap.docs.length;
                const totalQual = qualSnap.docs.length;
                const totalProdutos = produtosSnap.docs.length;
                
                addLog('📊 STATUS ATUAL DO SISTEMA:', 'info');
                addLog(`   • Produtos cadastrados: ${totalProdutos}`, 'info');
                addLog(`   • Registros de estoque: ${totalEstoques}`, 'info');
                addLog(`   • Movimentações: ${totalMov}`, 'info');
                addLog(`   • Estoque qualidade: ${totalQual}`, 'info');
                
                // Calcular saldo total
                let saldoTotal = 0;
                estoquesSnap.docs.forEach(doc => {
                    const data = doc.data();
                    saldoTotal += Number(data.saldo) || 0;
                });
                
                addLog(`   • Saldo total em estoque: ${saldoTotal.toFixed(2)}`, 'info');
                
                if (totalEstoques === 0 && totalMov === 0) {
                    addLog('✅ Sistema limpo - Pronto para saldos iniciais!', 'success');
                } else {
                    addLog('⚠️ Sistema contém dados - Considere fazer backup', 'info');
                }
                
            } catch (error) {
                console.error('Erro ao verificar status:', error);
                addLog(`❌ Erro ao verificar status: ${error.message}`, 'error');
            }
        };

        window.abrirSaldosIniciais = function() {
            window.location.href = 'saldos_iniciais.html';
        };

        window.exportarBackup = async function() {
            try {
                addLog('Iniciando exportação de backup...', 'info');
                
                const [estoquesSnap, movSnap, qualSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "estoqueQualidade"))
                ]);
                
                const backup = {
                    timestamp: new Date().toISOString(),
                    estoques: estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    movimentacoes: movSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                    estoqueQualidade: qualSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
                };
                
                const dataStr = JSON.stringify(backup, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `backup_estoques_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                addLog(`✅ Backup exportado com sucesso!`, 'success');
                addLog(`   • Estoques: ${backup.estoques.length}`, 'info');
                addLog(`   • Movimentações: ${backup.movimentacoes.length}`, 'info');
                addLog(`   • Qualidade: ${backup.estoqueQualidade.length}`, 'info');
                
            } catch (error) {
                console.error('Erro ao exportar backup:', error);
                addLog(`❌ Erro ao exportar backup: ${error.message}`, 'error');
            }
        };
    </script>
</body>
</html>
