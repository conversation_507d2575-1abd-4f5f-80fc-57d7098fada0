<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Rastreamento de Matéria-Prima</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 30px; }
    h1 { color: #0854a0; }
    .section { margin-bottom: 32px; }
    table { border-collapse: collapse; width: 100%; margin-top: 10px; }
    th, td { border: 1px solid #ccc; padding: 8px; }
    th { background: #f0f3f6; }
    input { padding: 6px; font-size: 16px; }
    button { padding: 6px 16px; background: #0854a0; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0a4d8c; }
  </style>
</head>
<body>
  <h1>Rastreamento de Matéria-Prima</h1>
  <div class="section">
    <label for="codigoInput">Código da MP:</label>
    <input type="text" id="codigoInput" placeholder="Digite o código da matéria-prima">
    <button onclick="buscarFluxo()">Buscar</button>
  </div>
  <div class="section" id="resultado"></div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function buscarFluxo() {
      const codigo = document.getElementById('codigoInput').value.trim();
      const resultado = document.getElementById('resultado');
      resultado.innerHTML = '';

      if (!codigo) {
        resultado.innerHTML = '<p>Digite o código da matéria-prima.</p>';
        return;
      }

      // 1. Buscar solicitações (pedidos de compra)
      const pedidosSnap = await getDocs(query(
        collection(db, 'pedidosCompra'),
        where('itens', 'array-contains-any', [{ codigo }])
      ));

      let pedidos = [];
      pedidosSnap.forEach(doc => {
        const data = doc.data();
        // Filtra apenas os itens com o código buscado
        const itensFiltrados = (data.itens || []).filter(item => item.codigo === codigo);
        if (itensFiltrados.length > 0) {
          pedidos.push({ ...data, id: doc.id, itens: itensFiltrados });
        }
      });

      // 2. Buscar recebimentos (estoqueQualidade)
      const recebimentosSnap = await getDocs(query(
        collection(db, 'estoqueQualidade'),
        where('codigo', '==', codigo)
      ));
      let recebimentos = [];
      recebimentosSnap.forEach(doc => recebimentos.push(doc.data()));

      // 3. Buscar entradas no estoque
      const estoquesSnap = await getDocs(query(
        collection(db, 'estoques'),
        where('codigo', '==', codigo)
      ));
      let estoques = [];
      estoquesSnap.forEach(doc => estoques.push(doc.data()));

      // Montar resultado
      let html = '';

      // Solicitações
      html += '<h2>Solicitações (Pedidos de Compra)</h2>';
      if (pedidos.length === 0) {
        html += '<p>Nenhum pedido encontrado para este código.</p>';
      } else {
        html += '<table><tr><th>Número</th><th>Data</th><th>Fornecedor</th><th>Qtd. Solicitada</th><th>Status</th></tr>';
        pedidos.forEach(p => {
          p.itens.forEach(item => {
            html += `<tr>
              <td>${p.numero || '-'}</td>
              <td>${p.dataEmissao ? new Date(p.dataEmissao.seconds * 1000).toLocaleDateString() : '-'}</td>
              <td>${p.fornecedorNome || '-'}</td>
              <td>${item.quantidade} ${item.unidade}</td>
              <td>${p.status || '-'}</td>
            </tr>`;
          });
        });
        html += '</table>';
      }

      // Recebimentos
      html += '<h2>Recebimentos (Inspeção/Qualidade)</h2>';
      if (recebimentos.length === 0) {
        html += '<p>Nenhum recebimento encontrado para este código.</p>';
      } else {
        html += '<table><tr><th>Qtd. Recebida</th><th>Unidade</th><th>NF</th><th>Data</th><th>Status</th></tr>';
        recebimentos.forEach(r => {
          html += `<tr>
            <td>${r.quantidade}</td>
            <td>${r.unidade}</td>
            <td>${r.notaFiscal?.numero || '-'}</td>
            <td>${r.dataEntrada && typeof r.dataEntrada.seconds === 'number' ? new Date(r.dataEntrada.seconds * 1000).toLocaleString() : '-'}</td>
            <td>${r.status || '-'}</td>
          </tr>`;
        });
        html += '</table>';
      }

      // Entradas no estoque
      html += '<h2>Entradas no Estoque</h2>';
      if (estoques.length === 0) {
        html += '<p>Nenhuma entrada definitiva encontrada para este código.</p>';
      } else {
        html += '<table><tr><th>Armazém</th><th>Saldo Atual</th></tr>';
        estoques.forEach(e => {
          html += `<tr>
            <td>${e.armazemId || '-'}</td>
            <td>${e.saldo || 0}</td>
          </tr>`;
        });
        html += '</table>';
      }

      resultado.innerHTML = html;
    }

    window.buscarFluxo = buscarFluxo;
  </script>
</body>
</html>