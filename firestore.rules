rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Permitir acesso completo para usuários autenticados (temporário para desenvolvimento)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }

    // Regra para a coleção de cotações
    match /cotacoes/{cotacaoId} {
      // Permite leitura apenas para usuários autenticados
      allow read: if request.auth != null;

      // Permite escrita para usuários autenticados
      allow create, update, delete: if request.auth != null;
    }

    // Regra para a coleção de solicitações de compra
    match /solicitacoesCompra/{solicitacaoId} {
      allow read, write: if request.auth != null;
    }

    // Regras para outras coleções importantes
    match /produtos/{produtoId} {
      allow read, write: if request.auth != null;
    }

    match /armazens/{armazemId} {
      allow read, write: if request.auth != null;
    }

    match /estoques/{estoqueId} {
      allow read, write: if request.auth != null;
    }

    match /movimentacoes/{movimentacaoId} {
      allow read, write: if request.auth != null;
    }
  }
}
