<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Relatório de Movimentações do Sistema</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .report-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .report-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        .filter-input {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        .filter-input:focus {
            border-color: #667eea;
            outline: none;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .movements-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .table-header {
            background: #667eea;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th {
            background: #f8f9fa;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .data-table th:hover {
            background: #e9ecef;
        }
        .data-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        .data-table tr:hover {
            background: #f8f9fa;
        }
        .movement-type {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .type-entrada { background: #d4edda; color: #155724; }
        .type-saida { background: #f8d7da; color: #721c24; }
        .type-transferencia { background: #d1ecf1; color: #0c5460; }
        .type-ajuste { background: #fff3cd; color: #856404; }
        .type-producao { background: #e2e3e5; color: #383d41; }
        .type-solicitacao { background: #cce5ff; color: #004085; }
        .type-cotacao { background: #e6f3ff; color: #0056b3; }
        .type-pedido { background: #fff0e6; color: #cc5500; }
        .type-recebimento { background: #e6ffe6; color: #006600; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
            margin: 5px;
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .font-weight-bold { font-weight: bold; }
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-ativo { background: #d4edda; color: #155724; }
        .status-pendente { background: #fff3cd; color: #856404; }
        .status-cancelado { background: #f8d7da; color: #721c24; }
        .status-finalizado { background: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Header -->
        <div class="report-header">
            <h1>📊 Relatório de Movimentações do Sistema</h1>
            <p>Acompanhe todas as atividades e transações do sistema em tempo real</p>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <button class="btn btn-info" onclick="atualizarAutomatico()">
                        🔄 Atualização Automática
                    </button>
                    <button class="btn btn-warning" onclick="window.location.href='index.html'">
                        🏠 Voltar
                    </button>
                </div>
                <div>
                    <strong>📊 Status:</strong> <span id="reportStatus">Pronto para carregar</span>
                </div>
            </div>

            <!-- Filtros -->
            <div style="border-top: 2px solid #e9ecef; padding-top: 20px;">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">🔍 Filtros Avançados</h3>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>Data Início:</label>
                        <input type="date" class="filter-input" id="dataInicio">
                    </div>
                    <div class="filter-group">
                        <label>Data Fim:</label>
                        <input type="date" class="filter-input" id="dataFim">
                    </div>
                    <div class="filter-group">
                        <label>Tipo de Movimentação:</label>
                        <select class="filter-input" id="tipoFilter">
                            <option value="">Todos os Tipos</option>
                            <option value="entrada">📦 Entrada de Estoque</option>
                            <option value="saida">📤 Saída de Estoque</option>
                            <option value="transferencia">🔄 Transferência</option>
                            <option value="ajuste">⚙️ Ajuste de Estoque</option>
                            <option value="producao">🏭 Produção</option>
                            <option value="solicitacao">📋 Solicitação</option>
                            <option value="cotacao">💰 Cotação</option>
                            <option value="pedido">🛒 Pedido</option>
                            <option value="recebimento">📥 Recebimento</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Usuário:</label>
                        <select class="filter-input" id="usuarioFilter">
                            <option value="">Todos os Usuários</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Armazém:</label>
                        <select class="filter-input" id="armazemFilter">
                            <option value="">Todos os Armazéns</option>
                            <option value="ALM01">🏢 ALM01 - Almoxarifado</option>
                            <option value="PROD1">🏭 PROD1 - Produção</option>
                            <option value="QUALIDADE">🔬 Qualidade</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Buscar:</label>
                        <input type="text" class="filter-input" id="searchInput" placeholder="Produto, documento, observação...">
                    </div>
                </div>
                <div class="text-right">
                    <button class="btn btn-primary" onclick="carregarMovimentacoes()">
                        🔍 Carregar Movimentações
                    </button>
                    <button class="btn btn-success" onclick="exportarCSV()" id="btnExport" disabled>
                        📥 Exportar CSV
                    </button>
                    <button class="btn btn-info" onclick="atualizarAutomatico()">
                        🔄 Atualização Automática
                    </button>
                    <button class="btn btn-warning" onclick="window.location.href='index.html'">
                        🏠 Voltar
                    </button>
                </div>
            </div>
        </div>

        <!-- Resumo -->
        <div class="summary-cards" id="summaryCards" style="display: none;">
            <div class="summary-card">
                <div class="summary-number" id="totalMovimentos">0</div>
                <div>Total de Movimentos</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="movimentosHoje">0</div>
                <div>Movimentos Hoje</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="usuariosAtivos">0</div>
                <div>Usuários Ativos</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="valorMovimentado">R$ 0</div>
                <div>Valor Movimentado</div>
            </div>
        </div>

        <!-- Tabela de Movimentações -->
        <div class="movements-table" id="movementsTable" style="display: none;">
            <div class="table-header">
                <h3>📋 Movimentações do Sistema</h3>
                <p><span id="itemCount">0 movimentos</span> | Última atualização: <span id="lastUpdate">-</span></p>
            </div>
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th onclick="ordenarTabela(0)">Data/Hora</th>
                            <th onclick="ordenarTabela(1)">Tipo</th>
                            <th onclick="ordenarTabela(2)">Documento</th>
                            <th onclick="ordenarTabela(3)">Produto/Item</th>
                            <th onclick="ordenarTabela(4)">Quantidade</th>
                            <th onclick="ordenarTabela(5)">Armazém</th>
                            <th onclick="ordenarTabela(6)">Usuário</th>
                            <th onclick="ordenarTabela(7)">Status</th>
                            <th onclick="ordenarTabela(8)">Observações</th>
                        </tr>
                    </thead>
                    <tbody id="movementsTableBody">
                        <!-- Dados serão inseridos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Loading -->
        <div class="loading" id="loadingIndicator">
            <h3>📊 Carregando movimentações...</h3>
            <p>Aguarde enquanto coletamos todas as atividades do sistema.</p>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            orderBy,
            where,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let allMovements = [];
        let filteredMovements = [];
        let autoUpdateInterval = null;

        function updateStatus(message) {
            document.getElementById('reportStatus').textContent = message;
        }

        // Definir datas padrão (últimos 30 dias)
        function setDefaultDates() {
            const hoje = new Date();
            const trintaDiasAtras = new Date();
            trintaDiasAtras.setDate(hoje.getDate() - 30);

            document.getElementById('dataFim').value = hoje.toISOString().split('T')[0];
            document.getElementById('dataInicio').value = trintaDiasAtras.toISOString().split('T')[0];
        }

        // Função principal para carregar movimentações
        window.carregarMovimentacoes = async function() {
            updateStatus('Carregando movimentações...');
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('summaryCards').style.display = 'none';
            document.getElementById('movementsTable').style.display = 'none';

            try {
                allMovements = [];

                // Carregar movimentações de estoque
                updateStatus('Carregando movimentações de estoque...');
                const movEstoqueSnap = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), orderBy("dataMovimentacao", "desc"), limit(1000))
                );

                movEstoqueSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: getTipoMovimentacao(data.tipo),
                        dataHora: data.dataMovimentacao,
                        documento: data.documento || data.numeroDocumento || 'N/A',
                        produto: data.produtoNome || data.produto || 'N/A',
                        quantidade: data.quantidade || 0,
                        armazem: data.armazemNome || data.armazem || 'N/A',
                        usuario: data.usuario || data.criadoPor || 'Sistema',
                        status: 'FINALIZADO',
                        observacoes: data.observacoes || data.motivo || '',
                        valor: data.valorUnitario ? (data.quantidade * data.valorUnitario) : 0,
                        origem: 'estoque'
                    });
                });

                // Carregar solicitações de compra
                updateStatus('Carregando solicitações de compra...');
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"), limit(500))
                );

                solicitacoesSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'solicitacao',
                        dataHora: data.dataCriacao,
                        documento: `SC-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: 'N/A',
                        usuario: data.solicitante || data.criadoPor || 'Sistema',
                        status: data.status || 'PENDENTE',
                        observacoes: data.justificativa || data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'solicitacao'
                    });
                });

                // Carregar cotações
                updateStatus('Carregando cotações...');
                const cotacoesSnap = await getDocs(
                    query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"), limit(500))
                );

                cotacoesSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'cotacao',
                        dataHora: data.dataCriacao,
                        documento: `CT-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: 'N/A',
                        usuario: data.criadoPor || 'Sistema',
                        status: data.status || 'ABERTA',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'cotacao'
                    });
                });

                // Carregar pedidos de compra
                updateStatus('Carregando pedidos de compra...');
                const pedidosSnap = await getDocs(
                    query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"), limit(500))
                );

                pedidosSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'pedido',
                        dataHora: data.dataCriacao,
                        documento: `PC-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: 'N/A',
                        usuario: data.criadoPor || 'Sistema',
                        status: data.status || 'ABERTO',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'pedido'
                    });
                });

                // Carregar recebimentos
                updateStatus('Carregando recebimentos...');
                const recebimentosSnap = await getDocs(
                    query(collection(db, "recebimentos"), orderBy("dataRecebimento", "desc"), limit(500))
                );

                recebimentosSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'recebimento',
                        dataHora: data.dataRecebimento,
                        documento: data.numeroNota || data.documento || 'N/A',
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidadeRecebida || 0), 0) : 0,
                        armazem: data.armazemDestino || 'N/A',
                        usuario: data.recebidoPor || 'Sistema',
                        status: data.status || 'RECEBIDO',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'recebimento'
                    });
                });

                // Ordenar por data (mais recente primeiro)
                allMovements.sort((a, b) => {
                    const dateA = a.dataHora?.seconds ? new Date(a.dataHora.seconds * 1000) : new Date(a.dataHora);
                    const dateB = b.dataHora?.seconds ? new Date(b.dataHora.seconds * 1000) : new Date(b.dataHora);
                    return dateB - dateA;
                });

                filteredMovements = [...allMovements];

                // Aplicar filtros se houver
                aplicarFiltros();

                // Mostrar resultados
                atualizarResumo();
                renderizarTabela();

                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('summaryCards').style.display = 'grid';
                document.getElementById('movementsTable').style.display = 'block';
                document.getElementById('btnExport').disabled = false;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('pt-BR');

                updateStatus(`${allMovements.length} movimentações carregadas`);

            } catch (error) {
                console.error('Erro ao carregar movimentações:', error);
                updateStatus('Erro ao carregar movimentações');
                alert('Erro ao carregar movimentações: ' + error.message);
            }
        };

        function getTipoMovimentacao(tipo) {
            const tipos = {
                'entrada': 'entrada',
                'saida': 'saida',
                'transferencia': 'transferencia',
                'ajuste': 'ajuste',
                'producao': 'producao',
                'consumo': 'saida',
                'devolucao': 'entrada',
                'inventario': 'ajuste'
            };
            return tipos[tipo] || 'ajuste';
        }

        // Aplicar filtros
        function aplicarFiltros() {
            const dataInicio = document.getElementById('dataInicio').value;
            const dataFim = document.getElementById('dataFim').value;
            const tipoFilter = document.getElementById('tipoFilter').value;
            const usuarioFilter = document.getElementById('usuarioFilter').value;
            const armazemFilter = document.getElementById('armazemFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredMovements = allMovements.filter(movement => {
                // Filtro de data
                if (dataInicio || dataFim) {
                    const movDate = movement.dataHora?.seconds ?
                        new Date(movement.dataHora.seconds * 1000) :
                        new Date(movement.dataHora);

                    if (dataInicio) {
                        const startDate = new Date(dataInicio);
                        if (movDate < startDate) return false;
                    }

                    if (dataFim) {
                        const endDate = new Date(dataFim);
                        endDate.setHours(23, 59, 59);
                        if (movDate > endDate) return false;
                    }
                }

                // Filtro de tipo
                if (tipoFilter && movement.tipo !== tipoFilter) return false;

                // Filtro de usuário
                if (usuarioFilter && !movement.usuario.toLowerCase().includes(usuarioFilter.toLowerCase())) return false;

                // Filtro de armazém
                if (armazemFilter && !movement.armazem.includes(armazemFilter)) return false;

                // Filtro de busca
                if (searchTerm) {
                    const searchFields = [
                        movement.documento,
                        movement.produto,
                        movement.observacoes,
                        movement.usuario
                    ].join(' ').toLowerCase();

                    if (!searchFields.includes(searchTerm)) return false;
                }

                return true;
            });
        }

        // Atualizar resumo
        function atualizarResumo() {
            const totalMovimentos = filteredMovements.length;

            // Movimentos de hoje
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);
            const movimentosHoje = filteredMovements.filter(mov => {
                const movDate = mov.dataHora?.seconds ?
                    new Date(mov.dataHora.seconds * 1000) :
                    new Date(mov.dataHora);
                movDate.setHours(0, 0, 0, 0);
                return movDate.getTime() === hoje.getTime();
            }).length;

            // Usuários únicos
            const usuariosUnicos = new Set(filteredMovements.map(mov => mov.usuario)).size;

            // Valor total movimentado
            const valorTotal = filteredMovements.reduce((sum, mov) => sum + (mov.valor || 0), 0);

            document.getElementById('totalMovimentos').textContent = totalMovimentos.toLocaleString();
            document.getElementById('movimentosHoje').textContent = movimentosHoje.toLocaleString();
            document.getElementById('usuariosAtivos').textContent = usuariosUnicos.toLocaleString();
            document.getElementById('valorMovimentado').textContent = 'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
        }

        // Renderizar tabela
        function renderizarTabela() {
            const tbody = document.getElementById('movementsTableBody');
            tbody.innerHTML = '';

            filteredMovements.slice(0, 500).forEach(movement => {
                const row = document.createElement('tr');

                const dataHora = movement.dataHora?.seconds ?
                    new Date(movement.dataHora.seconds * 1000) :
                    new Date(movement.dataHora);

                row.innerHTML = `
                    <td class="font-weight-bold">${dataHora.toLocaleString('pt-BR')}</td>
                    <td><span class="movement-type type-${movement.tipo}">${getTipoText(movement.tipo)}</span></td>
                    <td>${movement.documento}</td>
                    <td>${movement.produto}</td>
                    <td class="text-right font-weight-bold">${movement.quantidade.toLocaleString()}</td>
                    <td>${movement.armazem}</td>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">${movement.usuario.charAt(0).toUpperCase()}</div>
                            <span>${movement.usuario}</span>
                        </div>
                    </td>
                    <td><span class="status-badge status-${movement.status.toLowerCase()}">${movement.status}</span></td>
                    <td>${movement.observacoes || '-'}</td>
                `;

                tbody.appendChild(row);
            });

            document.getElementById('itemCount').textContent = `${filteredMovements.length} movimentos`;
        }

        function getTipoText(tipo) {
            const tipos = {
                'entrada': 'Entrada',
                'saida': 'Saída',
                'transferencia': 'Transferência',
                'ajuste': 'Ajuste',
                'producao': 'Produção',
                'solicitacao': 'Solicitação',
                'cotacao': 'Cotação',
                'pedido': 'Pedido',
                'recebimento': 'Recebimento'
            };
            return tipos[tipo] || tipo;
        }

        // Filtrar em tempo real
        ['dataInicio', 'dataFim', 'tipoFilter', 'usuarioFilter', 'armazemFilter', 'searchInput'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                if (allMovements.length > 0) {
                    aplicarFiltros();
                    atualizarResumo();
                    renderizarTabela();
                }
            });
        });

        document.getElementById('searchInput').addEventListener('keyup', () => {
            if (allMovements.length > 0) {
                aplicarFiltros();
                atualizarResumo();
                renderizarTabela();
            }
        });

        // Exportar CSV
        window.exportarCSV = function() {
            if (filteredMovements.length === 0) {
                alert('Nenhuma movimentação para exportar!');
                return;
            }

            let csv = 'Data/Hora,Tipo,Documento,Produto/Item,Quantidade,Armazém,Usuário,Status,Observações,Valor\n';

            filteredMovements.forEach(movement => {
                const dataHora = movement.dataHora?.seconds ?
                    new Date(movement.dataHora.seconds * 1000) :
                    new Date(movement.dataHora);

                csv += `"${dataHora.toLocaleString('pt-BR')}","${getTipoText(movement.tipo)}","${movement.documento}","${movement.produto}",${movement.quantidade},"${movement.armazem}","${movement.usuario}","${movement.status}","${movement.observacoes || ''}","R$ ${(movement.valor || 0).toFixed(2)}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `movimentacoes_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };

        // Ordenação da tabela
        let sortDirection = {};
        window.ordenarTabela = function(columnIndex) {
            const direction = sortDirection[columnIndex] === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = direction;

            const columns = ['dataHora', 'tipo', 'documento', 'produto', 'quantidade', 'armazem', 'usuario', 'status', 'observacoes'];
            const column = columns[columnIndex];

            filteredMovements.sort((a, b) => {
                let valueA = a[column];
                let valueB = b[column];

                if (column === 'dataHora') {
                    valueA = valueA?.seconds ? new Date(valueA.seconds * 1000) : new Date(valueA);
                    valueB = valueB?.seconds ? new Date(valueB.seconds * 1000) : new Date(valueB);
                } else if (typeof valueA === 'string') {
                    valueA = valueA.toLowerCase();
                    valueB = valueB.toLowerCase();
                }

                if (direction === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });

            renderizarTabela();
        };

        // Atualização automática
        window.atualizarAutomatico = function() {
            const btn = document.getElementById('btnAutoUpdate');

            if (autoUpdateInterval) {
                clearInterval(autoUpdateInterval);
                autoUpdateInterval = null;
                btn.textContent = '🔄 Atualização Automática';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-info');
            } else {
                autoUpdateInterval = setInterval(() => {
                    carregarMovimentacoes();
                }, 30000); // Atualizar a cada 30 segundos

                btn.textContent = '⏹️ Parar Atualização';
                btn.classList.remove('btn-info');
                btn.classList.add('btn-success');
            }
        };

        // Inicialização
        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            setDefaultDates();
            updateStatus('Pronto para carregar movimentações');

            // Carregar automaticamente
            carregarMovimentacoes();
        };

    </script>
</body>
</html>
