# Guia do Sistema CSS Padronizado

## Visão Geral

Este sistema CSS foi criado baseado no design elegante da tela de cotações, padronizando todos os componentes do sistema para garantir consistência visual e melhor experiência do usuário.

## Arquivo Principal

**`styles/sistema-padronizado.css`** - Arquivo principal que deve ser incluído em todas as páginas.

## Como Usar

### 1. Incluir o CSS nas páginas HTML

```html
<link rel="stylesheet" href="styles/sistema-padronizado.css">
```

### 2. Estrutura HTML Básica

```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Título da Página</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/sistema-padronizado.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-icon"></i> Título da Página</h1>
            <div class="header-actions">
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i> Novo
                </button>
                <a href="index.html" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <!-- Conteúdo Principal -->
        <div class="main-content">
            <!-- Seu conteúdo aqui -->
        </div>
    </div>
</body>
</html>
```

## Componentes Principais

### Containers
- `.container` - Container principal (max-width: 1400px)
- `.container-sm` - Container pequeno (max-width: 800px)
- `.container-lg` - Container grande (max-width: 1600px)
- `.container-fluid` - Container fluido (100% width)

### Headers
```html
<div class="header">
    <h1><i class="fas fa-icon"></i> Título</h1>
    <div class="header-actions">
        <!-- Botões de ação -->
    </div>
</div>
```

### Botões
```html
<button class="btn btn-primary">Primário</button>
<button class="btn btn-success">Sucesso</button>
<button class="btn btn-warning">Aviso</button>
<button class="btn btn-danger">Perigo</button>
<button class="btn btn-info">Info</button>
<button class="btn btn-secondary">Secundário</button>
<button class="btn btn-sm">Pequeno</button>
```

### Cards de Estatísticas
```html
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number">150</div>
        <div class="stat-label">Total</div>
    </div>
</div>
```

### Abas
```html
<div class="tabs">
    <button class="tab active">
        <i class="fas fa-list"></i> Aba 1
    </button>
    <button class="tab">
        <i class="fas fa-chart"></i> Aba 2
    </button>
</div>

<div class="tab-content active">
    Conteúdo da Aba 1
</div>
<div class="tab-content">
    Conteúdo da Aba 2
</div>
```

### Formulários
```html
<div class="form-row">
    <div class="form-col">
        <label>Campo 1</label>
        <input type="text" class="form-control">
    </div>
    <div class="form-col">
        <label>Campo 2</label>
        <select class="form-control">
            <option>Opção 1</option>
        </select>
    </div>
</div>
```

### Barra de Pesquisa
```html
<div class="search-bar">
    <input type="text" class="search-input" placeholder="Pesquisar...">
    <i class="fas fa-search search-icon"></i>
</div>
```

### Filtros
```html
<div class="filters">
    <h3><i class="fas fa-filter"></i> Filtros</h3>
    <div class="filter-row">
        <div class="form-group">
            <label>Filtro 1</label>
            <input type="text" class="form-control">
        </div>
    </div>
</div>
```

### Tabelas
```html
<div class="table-container">
    <table class="table">
        <thead>
            <tr>
                <th>Coluna 1</th>
                <th>Coluna 2</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Dados 1</td>
                <td>Dados 2</td>
            </tr>
        </tbody>
    </table>
</div>
```

### Status e Badges
```html
<span class="status aberta">Aberta</span>
<span class="status enviada">Enviada</span>
<span class="status respondida">Respondida</span>
<span class="status aprovada">Aprovada</span>
<span class="status fechada">Fechada</span>
```

### Modais
```html
<div class="modal" id="meuModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-edit"></i> Título do Modal</h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <!-- Conteúdo do modal -->
        </div>
    </div>
</div>
```

### Alertas
```html
<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    Mensagem informativa
</div>
<div class="alert alert-success">Sucesso</div>
<div class="alert alert-warning">Aviso</div>
<div class="alert alert-danger">Erro</div>
```

### Workflow Steps
```html
<div class="workflow-steps">
    <div class="workflow-step completed">
        <div class="workflow-icon">
            <i class="fas fa-check"></i>
        </div>
        <div class="workflow-label">Concluído</div>
    </div>
    <div class="workflow-step active">
        <div class="workflow-icon">
            <i class="fas fa-cog"></i>
        </div>
        <div class="workflow-label">Em Andamento</div>
    </div>
</div>
```

## Variáveis CSS Disponíveis

### Cores
- `--primary-color`, `--primary-hover`, `--primary-light`
- `--success-color`, `--success-hover`, `--success-light`
- `--warning-color`, `--warning-hover`, `--warning-light`
- `--danger-color`, `--danger-hover`, `--danger-light`
- `--info-color`, `--info-hover`, `--info-light`

### Espaçamentos
- `--spacing-xs` (4px)
- `--spacing-sm` (8px)
- `--spacing-md` (15px)
- `--spacing-lg` (25px)
- `--spacing-xl` (40px)

### Bordas
- `--border-radius-sm` (4px)
- `--border-radius-md` (8px)
- `--border-radius-lg` (15px)
- `--border-radius-xl` (20px)

### Sombras
- `--shadow-sm`, `--shadow-md`, `--shadow-lg`, `--shadow-xl`

## Classes Utilitárias

### Texto
- `.text-center`, `.text-left`, `.text-right`
- `.text-primary`, `.text-success`, `.text-warning`, `.text-danger`

### Margens e Padding
- `.m-0` a `.m-5` (margin)
- `.mt-0` a `.mt-5` (margin-top)
- `.mb-0` a `.mb-5` (margin-bottom)
- `.p-0` a `.p-5` (padding)

### Display
- `.d-none`, `.d-block`, `.d-flex`, `.d-grid`
- `.justify-content-center`, `.align-items-center`

### Largura e Altura
- `.w-25`, `.w-50`, `.w-75`, `.w-100`
- `.h-25`, `.h-50`, `.h-75`, `.h-100`

## Responsividade

O sistema é totalmente responsivo com breakpoints:
- Desktop: > 1200px
- Tablet: 768px - 1200px
- Mobile: < 768px
- Mobile pequeno: < 480px

## Próximos Passos

1. Substituir os estilos inline nos arquivos HTML existentes
2. Atualizar as referências de CSS nos arquivos
3. Testar todas as telas para garantir consistência
4. Ajustar componentes específicos conforme necessário
