:root {
  --primary-color: #0854a0;
  --primary-hover: #0a4d8c;
  --secondary-color: #f0f3f6;
  --border-color: #d4d4d4;
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #107e3e;
  --success-hover: #0d6e36;
  --danger-color: #bb0000;
  --danger-hover: #a30000;
  --warning-color: #e9730c;
  --header-bg: #354a5f;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f7f7f7;
  color: var(--text-color);
  line-height: 1.6;
}

.container {
  width: 95%;
  max-width: 1400px;
  margin: 20px auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  background-color: var(--header-bg);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  margin: -20px -20px 20px -20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
}

.header-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: #fff;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.form-col {
  display: flex;
  flex-direction: column;
}

label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
}

input, select, textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.stock-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  font-size: 14px;
}

.stock-table th,
.stock-table td {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.stock-table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.stock-table tbody tr {
  transition: background-color 0.2s;
}

.stock-table tbody tr:hover {
  background-color: #f8f9fa;
}

.table-container {
  max-height: 600px;
  overflow-y: auto;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
  padding: 10px;
}

.pagination button {
  padding: 5px 10px;
  border: 1px solid var(--border-color);
  background-color: white;
  color: var(--text-color);
}

.pagination button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.loading {
  display: none;
  text-align: center;
  padding: 20px;
}

.loading.active {
  display: block;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  width: 80%;
  max-width: 600px;
  border-radius: 8px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.modal-body {
  padding: 15px 5px;
  overflow-y: auto;
  max-height: 65vh;
}

.close-button {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

.movement-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.type-entrada {
  background-color: var(--success-color);
  color: white;
}

.type-saida {
  background-color: var(--danger-color);
  color: white;
}

.status-message {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  display: none;
  font-size: 14px;
}

.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.stock-table td:nth-child(6),
.stock-table td:nth-child(7),
.stock-table td:nth-child(8) {
  text-align: right;
}

.stock-table th:nth-child(6),
.stock-table th:nth-child(7),
.stock-table th:nth-child(8) {
  text-align: right;
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: 0;
    border-radius: 0;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .header-buttons {
    justify-content: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .stock-table {
    font-size: 12px;
  }

  .stock-table th,
  .stock-table td {
    padding: 8px;
  }

  .modal-content {
    width: 95%;
    margin: 5% auto;
    padding: 15px;
  }

  .modal-body {
    padding: 10px 0;
  }
} 