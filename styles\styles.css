/* Variáveis CSS - Cores e estilos padrão */
:root {
  /* Cores principais */
  --primary-color: #0854a0;
  --primary-hover: #0a4d8c;
  --secondary-color: #f0f3f6;
  --border-color: #d4d4d4;
  --text-color: #333;
  --text-secondary: #666;
  
  /* Cores de status */
  --success-color: #107e3e;
  --success-hover: #0d6e36;
  --danger-color: #bb0000;
  --danger-hover: #a30000;
  --warning-color: #e9730c;
  --warning-hover: #d1660a;
  --info-color: #0c5460;
  --info-hover: #0a4550;
  
  /* Cores de fundo */
  --header-bg: #354a5f;
  --body-bg: #f7f7f7;
  --card-bg: #ffffff;
  
  /* Sombras */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Espaçamentos */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Bordas */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
}

/* Reset e estilos base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--body-bg);
  color: var(--text-color);
  line-height: 1.6;
  font-size: 14px;
}

/* Container principal */
.container {
  width: 95%;
  max-width: 1400px;
  margin: 20px auto;
  padding: var(--spacing-md);
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

/* Cabeçalho */
.header {
  background-color: var(--header-bg);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  margin: calc(-1 * var(--spacing-md)) calc(-1 * var(--spacing-md)) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
}

/* Botões */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: var(--warning-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Formulários */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-col {
  display: flex;
  flex-direction: column;
}

label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
}

input, select, textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
}

/* Tabelas */
.table-container {
  max-height: 600px;
  overflow-y: auto;
  margin-top: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th,
.table td {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  text-align: left;
}

.table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tbody tr {
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Paginação */
.pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
}

.pagination button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  background-color: white;
  color: var(--text-color);
  border-radius: var(--border-radius-sm);
}

.pagination button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Mensagens de status */
.status-message {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  display: none;
  font-size: 14px;
}

.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 10% auto;
  padding: var(--spacing-lg);
  width: 80%;
  max-width: 600px;
  border-radius: var(--border-radius-md);
  position: relative;
  box-shadow: var(--shadow-lg);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.modal-body {
  padding: var(--spacing-md) 0;
  overflow-y: auto;
  max-height: 65vh;
}

.close-button {
  position: absolute;
  right: var(--spacing-md);
  top: var(--spacing-md);
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

/* Utilitários */
.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* Responsividade */
@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: 0;
    border-radius: 0;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
  }

  .modal-content {
    width: 95%;
    margin: 5% auto;
    padding: var(--spacing-md);
  }

  .modal-body {
    padding: var(--spacing-sm) 0;
  }
} 