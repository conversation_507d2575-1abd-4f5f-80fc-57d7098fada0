/**
 * 🔔 SERVIÇO DE NOTIFICAÇÕES PARA COMPRAS
 * Sistema completo de alertas e notificações pós-aprovação
 */

import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  Timestamp,
  updateDoc,
  doc 
} from 'firebase/firestore';
import { db } from '../firebase-config.js';

export class NotificationServiceCompras {
  
  /**
   * 📧 NOTIFICAR ATRASO NA ENTREGA
   */
  static async notificarAtrasoEntrega(pedidoId, diasAtraso) {
    try {
      const notification = {
        tipo: 'ATRASO_ENTREGA',
        titulo: `⏰ Pedido em Atraso - ${diasAtraso} dias`,
        mensagem: `O pedido ${pedidoId} está ${diasAtraso} dias em atraso para entrega`,
        prioridade: diasAtraso > 7 ? 'ALTA' : 'MEDIA',
        destinatarios: ['COMPRAS', 'GERENCIA'],
        pedidoId: pedidoId,
        dataNotificacao: Timestamp.now(),
        status: 'PENDENTE',
        acoes: [
          { tipo: 'CONTATAR_FORNECEDOR', label: 'Contatar Fornecedor' },
          { tipo: 'CANCELAR_PEDIDO', label: 'Cancelar Pedido' }
        ]
      };

      await addDoc(collection(db, "notificacoes"), notification);
      
      // Enviar email se configurado
      await this.enviarEmailAtraso(pedidoId, diasAtraso);
      
    } catch (error) {
      console.error('Erro ao notificar atraso:', error);
    }
  }

  /**
   * 🔍 NOTIFICAR PENDÊNCIA DE INSPEÇÃO
   */
  static async notificarPendenciaInspecao(itemId, diasPendente) {
    try {
      const notification = {
        tipo: 'INSPECAO_PENDENTE',
        titulo: `🔬 Inspeção Pendente - ${diasPendente} dias`,
        mensagem: `Item ${itemId} aguarda inspeção há ${diasPendente} dias`,
        prioridade: diasPendente > 3 ? 'ALTA' : 'MEDIA',
        destinatarios: ['QUALIDADE', 'COMPRAS'],
        itemId: itemId,
        dataNotificacao: Timestamp.now(),
        status: 'PENDENTE'
      };

      await addDoc(collection(db, "notificacoes"), notification);
      
    } catch (error) {
      console.error('Erro ao notificar pendência de inspeção:', error);
    }
  }

  /**
   * 💰 NOTIFICAR VENCIMENTO DE PAGAMENTO
   */
  static async notificarVencimentoPagamento(contaId, diasVencimento) {
    try {
      const notification = {
        tipo: 'VENCIMENTO_PAGAMENTO',
        titulo: `💳 Pagamento Vencendo em ${diasVencimento} dias`,
        mensagem: `Conta ${contaId} vence em ${diasVencimento} dias`,
        prioridade: diasVencimento <= 3 ? 'ALTA' : 'MEDIA',
        destinatarios: ['FINANCEIRO', 'GERENCIA'],
        contaId: contaId,
        dataNotificacao: Timestamp.now(),
        status: 'PENDENTE'
      };

      await addDoc(collection(db, "notificacoes"), notification);
      
    } catch (error) {
      console.error('Erro ao notificar vencimento:', error);
    }
  }

  /**
   * 📦 NOTIFICAR RECEBIMENTO COMPLETO
   */
  static async notificarRecebimentoCompleto(pedidoId, valorTotal) {
    try {
      const notification = {
        tipo: 'RECEBIMENTO_COMPLETO',
        titulo: `✅ Pedido Recebido Completamente`,
        mensagem: `Pedido ${pedidoId} foi recebido integralmente - Valor: R$ ${valorTotal.toFixed(2)}`,
        prioridade: 'BAIXA',
        destinatarios: ['COMPRAS', 'ESTOQUE'],
        pedidoId: pedidoId,
        dataNotificacao: Timestamp.now(),
        status: 'INFORMATIVO'
      };

      await addDoc(collection(db, "notificacoes"), notification);
      
    } catch (error) {
      console.error('Erro ao notificar recebimento:', error);
    }
  }

  /**
   * 🚨 VERIFICAR ALERTAS AUTOMÁTICOS
   * Função para ser executada periodicamente
   */
  static async verificarAlertasAutomaticos() {
    try {
      console.log('🔍 Verificando alertas automáticos...');
      
      // Verificar pedidos em atraso
      await this.verificarPedidosAtraso();
      
      // Verificar inspeções pendentes
      await this.verificarInspecoesPendentes();
      
      // Verificar vencimentos
      await this.verificarVencimentosPagamento();
      
    } catch (error) {
      console.error('Erro na verificação automática:', error);
    }
  }

  /**
   * ⏰ VERIFICAR PEDIDOS EM ATRASO
   */
  static async verificarPedidosAtraso() {
    try {
      const hoje = new Date();
      const pedidosQuery = query(
        collection(db, "pedidosCompra"),
        where("status", "in", ["APROVADO", "ENVIADO"])
      );
      
      const pedidosSnap = await getDocs(pedidosQuery);
      
      for (const pedidoDoc of pedidosSnap.docs) {
        const pedido = pedidoDoc.data();
        const dataEntregaPrevista = pedido.dataEntregaPrevista?.toDate();
        
        if (dataEntregaPrevista && dataEntregaPrevista < hoje) {
          const diasAtraso = Math.floor((hoje - dataEntregaPrevista) / (1000 * 60 * 60 * 24));
          
          // Verificar se já foi notificado hoje
          const jaNotificado = await this.verificarNotificacaoExistente(
            'ATRASO_ENTREGA', 
            pedidoDoc.id, 
            hoje
          );
          
          if (!jaNotificado) {
            await this.notificarAtrasoEntrega(pedidoDoc.id, diasAtraso);
          }
        }
      }
      
    } catch (error) {
      console.error('Erro ao verificar atrasos:', error);
    }
  }

  /**
   * 🔬 VERIFICAR INSPEÇÕES PENDENTES
   */
  static async verificarInspecoesPendentes() {
    try {
      const hoje = new Date();
      const inspecoesQuery = query(
        collection(db, "estoqueQualidade"),
        where("status", "==", "PENDENTE")
      );
      
      const inspecoesSnap = await getDocs(inspecoesQuery);
      
      for (const inspecaoDoc of inspecoesSnap.docs) {
        const inspecao = inspecaoDoc.data();
        const dataEntrada = inspecao.dataEntrada?.toDate();
        
        if (dataEntrada) {
          const diasPendente = Math.floor((hoje - dataEntrada) / (1000 * 60 * 60 * 24));
          
          if (diasPendente >= 2) { // Alertar após 2 dias
            const jaNotificado = await this.verificarNotificacaoExistente(
              'INSPECAO_PENDENTE', 
              inspecaoDoc.id, 
              hoje
            );
            
            if (!jaNotificado) {
              await this.notificarPendenciaInspecao(inspecaoDoc.id, diasPendente);
            }
          }
        }
      }
      
    } catch (error) {
      console.error('Erro ao verificar inspeções:', error);
    }
  }

  /**
   * 💳 VERIFICAR VENCIMENTOS DE PAGAMENTO
   */
  static async verificarVencimentosPagamento() {
    try {
      const hoje = new Date();
      const proximosDias = new Date(hoje.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 dias
      
      const contasQuery = query(
        collection(db, "contasAPagar"),
        where("status", "==", "PENDENTE")
      );
      
      const contasSnap = await getDocs(contasQuery);
      
      for (const contaDoc of contasSnap.docs) {
        const conta = contaDoc.data();
        
        if (conta.parcelas) {
          for (const parcela of conta.parcelas) {
            if (parcela.status === 'PENDENTE') {
              const dataVencimento = parcela.dataVencimento?.toDate();
              
              if (dataVencimento && dataVencimento <= proximosDias) {
                const diasVencimento = Math.floor((dataVencimento - hoje) / (1000 * 60 * 60 * 24));
                
                const jaNotificado = await this.verificarNotificacaoExistente(
                  'VENCIMENTO_PAGAMENTO', 
                  contaDoc.id, 
                  hoje
                );
                
                if (!jaNotificado) {
                  await this.notificarVencimentoPagamento(contaDoc.id, diasVencimento);
                }
              }
            }
          }
        }
      }
      
    } catch (error) {
      console.error('Erro ao verificar vencimentos:', error);
    }
  }

  /**
   * 🔍 VERIFICAR SE JÁ FOI NOTIFICADO HOJE
   */
  static async verificarNotificacaoExistente(tipo, itemId, data) {
    try {
      const inicioHoje = new Date(data);
      inicioHoje.setHours(0, 0, 0, 0);
      
      const fimHoje = new Date(data);
      fimHoje.setHours(23, 59, 59, 999);
      
      const notificacoesQuery = query(
        collection(db, "notificacoes"),
        where("tipo", "==", tipo),
        where("dataNotificacao", ">=", Timestamp.fromDate(inicioHoje)),
        where("dataNotificacao", "<=", Timestamp.fromDate(fimHoje))
      );
      
      const notificacoesSnap = await getDocs(notificacoesQuery);
      
      return notificacoesSnap.docs.some(doc => {
        const data = doc.data();
        return data.pedidoId === itemId || data.itemId === itemId || data.contaId === itemId;
      });
      
    } catch (error) {
      console.error('Erro ao verificar notificação existente:', error);
      return false;
    }
  }

  /**
   * 📧 ENVIAR EMAIL DE ATRASO
   */
  static async enviarEmailAtraso(pedidoId, diasAtraso) {
    try {
      // Implementar integração com serviço de email
      console.log(`📧 Email de atraso enviado para pedido ${pedidoId} - ${diasAtraso} dias`);
      
    } catch (error) {
      console.error('Erro ao enviar email:', error);
    }
  }
}

// Configurar execução automática (executar a cada hora)
if (typeof window !== 'undefined') {
  setInterval(() => {
    NotificationServiceCompras.verificarAlertasAutomaticos();
  }, 60 * 60 * 1000); // 1 hora
}
