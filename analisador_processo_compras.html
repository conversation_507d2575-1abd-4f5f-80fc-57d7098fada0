<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analisador do Processo de Compras</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .controls {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #666;
        }

        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .status-good {
            color: #27ae60;
        }

        .status-warning {
            color: #f39c12;
        }

        .status-critical {
            color: #e74c3c;
        }

        .flow-diagram {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .flow-step {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }

        .flow-step.warning {
            border-left-color: #f39c12;
            background: #fef9e7;
        }

        .flow-step.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }

        .flow-step.success {
            border-left-color: #27ae60;
            background: #f0f9f4;
        }

        .recommendations {
            background: white;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .recommendation-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }

        .recommendation-item.high {
            border-left-color: #e74c3c;
        }

        .recommendation-item.medium {
            border-left-color: #f39c12;
        }

        .recommendation-item.low {
            border-left-color: #27ae60;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <button class="btn btn-warning back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-home"></i>
        Voltar
    </button>

    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-search"></i>
                Analisador do Processo de Compras
            </h1>
            <p>Análise completa do fluxo: Solicitação → Cotação → Pedido → Recebimento</p>
        </div>

        <div class="controls">
            <h3><i class="fas fa-cogs"></i> Controles de Análise</h3>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="analisarProcessoCompleto()">
                    <i class="fas fa-search"></i>
                    Analisar Processo Completo
                </button>
                <button class="btn btn-success" onclick="verificarConfiguracoes()">
                    <i class="fas fa-cog"></i>
                    Verificar Configurações
                </button>
                <button class="btn btn-warning" onclick="gerarRelatorioMelhorias()">
                    <i class="fas fa-chart-line"></i>
                    Gerar Relatório de Melhorias
                </button>
            </div>
        </div>

        <div class="analysis-grid" id="analysisGrid">
            <!-- Cards de análise serão inseridos aqui -->
        </div>

        <div class="flow-diagram" id="flowDiagram" style="display: none;">
            <h3><i class="fas fa-sitemap"></i> Fluxo do Processo de Compras</h3>
            <div id="flowSteps"></div>
        </div>

        <div class="recommendations" id="recommendations" style="display: none;">
            <h3><i class="fas fa-lightbulb"></i> Recomendações de Melhoria</h3>
            <div id="recommendationsList"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let dadosAnalise = {
            solicitacoes: [],
            cotacoes: [],
            pedidos: [],
            parametros: {},
            problemas: [],
            melhorias: []
        };

        window.analisarProcessoCompleto = async function() {
            showLoading();
            
            try {
                // Buscar dados do Firebase
                const [solicitacoesSnap, cotacoesSnap, pedidosSnap, parametrosSnap] = await Promise.all([
                    getDocs(collection(db, "solicitacoesCompra")),
                    getDocs(collection(db, "cotacoes")),
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "parametros"))
                ]);

                dadosAnalise.solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                dadosAnalise.cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                dadosAnalise.pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                dadosAnalise.parametros = parametrosSnap.docs.length > 0 ? parametrosSnap.docs[0].data() : {};

                // Executar análises
                analisarFluxoSolicitacoes();
                analisarFluxoCotacoes();
                analisarFluxoPedidos();
                analisarIntegracaoFluxo();
                analisarConfiguracoes();

                // Mostrar resultados
                renderizarAnalise();
                renderizarFluxo();
                renderizarRecomendacoes();

                hideLoading();

            } catch (error) {
                console.error('Erro na análise:', error);
                hideLoading();
                alert('Erro ao analisar processo: ' + error.message);
            }
        };

        function analisarFluxoSolicitacoes() {
            const solicitacoes = dadosAnalise.solicitacoes;
            
            // Solicitações pendentes há muito tempo
            const agora = new Date();
            const solicitacoesPendentes = solicitacoes.filter(s => {
                if (s.status !== 'PENDENTE') return false;
                const dataCriacao = s.dataCriacao?.toDate ? s.dataCriacao.toDate() : new Date(s.dataCriacao);
                const diasPendente = (agora - dataCriacao) / (1000 * 60 * 60 * 24);
                return diasPendente > 7;
            });

            if (solicitacoesPendentes.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'SOLICITAÇÕES',
                    tipo: 'ATRASO',
                    problema: `${solicitacoesPendentes.length} solicitações pendentes há mais de 7 dias`,
                    impacto: 'Alto - Atraso no processo de compras',
                    solucao: 'Revisar aprovações pendentes e agilizar processo'
                });
            }

            // Solicitações sem cotação gerada
            const solicitacoesSemCotacao = solicitacoes.filter(s => 
                s.status === 'APROVADO' && !s.cotacaoId
            );

            if (solicitacoesSemCotacao.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'SOLICITAÇÕES',
                    tipo: 'PROCESSO',
                    problema: `${solicitacoesSemCotacao.length} solicitações aprovadas sem cotação`,
                    impacto: 'Médio - Processo incompleto',
                    solucao: 'Gerar cotações para solicitações aprovadas'
                });
            }
        }

        function analisarFluxoCotacoes() {
            const cotacoes = dadosAnalise.cotacoes;
            
            // Cotações sem respostas
            const cotacoesSemResposta = cotacoes.filter(c => 
                !c.respostas || Object.keys(c.respostas).length === 0
            );

            if (cotacoesSemResposta.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'COTAÇÕES',
                    tipo: 'PROCESSO',
                    problema: `${cotacoesSemResposta.length} cotações sem respostas de fornecedores`,
                    impacto: 'Alto - Processo de compra parado',
                    solucao: 'Acompanhar fornecedores ou buscar novos fornecedores'
                });
            }

            // Cotações com seleção final mas sem pedido
            const cotacoesComSelecaoSemPedido = cotacoes.filter(c => 
                c.selecaoFinal && Object.keys(c.selecaoFinal).length > 0 && !c.pedidoId
            );

            if (cotacoesComSelecaoSemPedido.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'COTAÇÕES',
                    tipo: 'PROCESSO',
                    problema: `${cotacoesComSelecaoSemPedido.length} cotações finalizadas sem pedido gerado`,
                    impacto: 'Alto - Processo incompleto',
                    solucao: 'Gerar pedidos para cotações finalizadas'
                });
            }
        }

        function analisarFluxoPedidos() {
            const pedidos = dadosAnalise.pedidos;

            // Pedidos sem recebimento há muito tempo
            const agora = new Date();
            const pedidosSemRecebimento = pedidos.filter(p => {
                if (p.status !== 'ENVIADO' && p.status !== 'CONFIRMADO') return false;
                const dataEnvio = p.dataEnvio?.toDate ? p.dataEnvio.toDate() : new Date(p.dataEnvio);
                const diasSemRecebimento = (agora - dataEnvio) / (1000 * 60 * 60 * 24);
                return diasSemRecebimento > 30;
            });

            if (pedidosSemRecebimento.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'PEDIDOS',
                    tipo: 'ATRASO',
                    problema: `${pedidosSemRecebimento.length} pedidos sem recebimento há mais de 30 dias`,
                    impacto: 'Alto - Atraso nas entregas',
                    solucao: 'Acompanhar fornecedores e cobrar entregas'
                });
            }

            // Pedidos com valores divergentes
            const pedidosValorDivergente = pedidos.filter(p => {
                if (!p.itens || !p.valorTotal) return false;
                const valorCalculado = p.itens.reduce((total, item) =>
                    total + (item.quantidade * item.precoUnitario), 0
                );
                const diferenca = Math.abs(valorCalculado - p.valorTotal);
                return diferenca > 0.01;
            });

            if (pedidosValorDivergente.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'PEDIDOS',
                    tipo: 'DADOS',
                    problema: `${pedidosValorDivergente.length} pedidos com valores divergentes`,
                    impacto: 'Médio - Inconsistência financeira',
                    solucao: 'Recalcular valores dos pedidos'
                });
            }
        }

        function analisarIntegracaoFluxo() {
            // Verificar se o fluxo está sendo seguido corretamente
            const solicitacoes = dadosAnalise.solicitacoes;
            const cotacoes = dadosAnalise.cotacoes;
            const pedidos = dadosAnalise.pedidos;

            // Solicitações órfãs (sem cotação)
            const solicitacoesOrfas = solicitacoes.filter(s =>
                s.status === 'APROVADO' && !cotacoes.some(c => c.solicitacaoId === s.id)
            );

            // Cotações órfãs (sem solicitação)
            const cotacoesOrfas = cotacoes.filter(c =>
                c.solicitacaoId && !solicitacoes.some(s => s.id === c.solicitacaoId)
            );

            // Pedidos órfãos (sem cotação)
            const pedidosOrfaos = pedidos.filter(p =>
                p.cotacaoId && !cotacoes.some(c => c.id === p.cotacaoId)
            );

            if (solicitacoesOrfas.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'INTEGRAÇÃO',
                    tipo: 'FLUXO',
                    problema: `${solicitacoesOrfas.length} solicitações aprovadas sem cotação vinculada`,
                    impacto: 'Alto - Quebra do fluxo de compras',
                    solucao: 'Criar cotações para solicitações aprovadas'
                });
            }

            if (cotacoesOrfas.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'INTEGRAÇÃO',
                    tipo: 'DADOS',
                    problema: `${cotacoesOrfas.length} cotações órfãs (sem solicitação)`,
                    impacto: 'Médio - Dados inconsistentes',
                    solucao: 'Revisar e corrigir vínculos de cotações'
                });
            }

            if (pedidosOrfaos.length > 0) {
                dadosAnalise.problemas.push({
                    modulo: 'INTEGRAÇÃO',
                    tipo: 'DADOS',
                    problema: `${pedidosOrfaos.length} pedidos órfãos (sem cotação)`,
                    impacto: 'Médio - Dados inconsistentes',
                    solucao: 'Revisar e corrigir vínculos de pedidos'
                });
            }
        }

        function analisarConfiguracoes() {
            const params = dadosAnalise.parametros;

            // Verificar configurações críticas para compras
            if (!params.aprovacaoAutomatica && !params.nivelAprovacao) {
                dadosAnalise.melhorias.push({
                    tipo: 'CONFIGURAÇÃO',
                    prioridade: 'ALTA',
                    melhoria: 'Definir níveis de aprovação para compras',
                    beneficio: 'Controle e governança do processo',
                    implementacao: 'Configurar parâmetros de aprovação'
                });
            }

            if (!params.alertasCompras) {
                dadosAnalise.melhorias.push({
                    tipo: 'CONFIGURAÇÃO',
                    prioridade: 'MÉDIA',
                    melhoria: 'Ativar alertas automáticos para compras',
                    beneficio: 'Acompanhamento proativo do processo',
                    implementacao: 'Configurar sistema de alertas'
                });
            }

            if (!params.validacaoOrcamento) {
                dadosAnalise.melhorias.push({
                    tipo: 'CONFIGURAÇÃO',
                    prioridade: 'ALTA',
                    melhoria: 'Implementar validação de orçamento',
                    beneficio: 'Controle financeiro das compras',
                    implementacao: 'Configurar limites orçamentários'
                });
            }
        }

        function renderizarAnalise() {
            const grid = document.getElementById('analysisGrid');

            // Card de Resumo Geral
            const resumoCard = `
                <div class="analysis-card">
                    <div class="card-header">
                        <div class="card-icon" style="background: #3498db;">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="card-title">Resumo Geral</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Solicitações Ativas</span>
                        <span class="metric-value">${dadosAnalise.solicitacoes.length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Cotações em Andamento</span>
                        <span class="metric-value">${dadosAnalise.cotacoes.length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Pedidos Ativos</span>
                        <span class="metric-value">${dadosAnalise.pedidos.length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Problemas Detectados</span>
                        <span class="metric-value status-critical">${dadosAnalise.problemas.length}</span>
                    </div>
                </div>
            `;

            // Card de Status do Fluxo
            const statusCard = `
                <div class="analysis-card">
                    <div class="card-header">
                        <div class="card-icon" style="background: #27ae60;">
                            <i class="fas fa-flow-chart"></i>
                        </div>
                        <div class="card-title">Status do Fluxo</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Solicitações Pendentes</span>
                        <span class="metric-value status-warning">${dadosAnalise.solicitacoes.filter(s => s.status === 'PENDENTE').length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Cotações Sem Resposta</span>
                        <span class="metric-value status-critical">${dadosAnalise.cotacoes.filter(c => !c.respostas || Object.keys(c.respostas).length === 0).length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Pedidos Atrasados</span>
                        <span class="metric-value status-critical">${calcularPedidosAtrasados()}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Eficiência do Processo</span>
                        <span class="metric-value ${calcularEficiencia() > 80 ? 'status-good' : calcularEficiencia() > 60 ? 'status-warning' : 'status-critical'}">${calcularEficiencia()}%</span>
                    </div>
                </div>
            `;

            // Card de Problemas Críticos
            const problemasCard = `
                <div class="analysis-card">
                    <div class="card-header">
                        <div class="card-icon" style="background: #e74c3c;">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-title">Problemas Críticos</div>
                    </div>
                    ${dadosAnalise.problemas.slice(0, 4).map(p => `
                        <div class="metric">
                            <span class="metric-label">${p.modulo}</span>
                            <span class="metric-value status-critical">${p.problema}</span>
                        </div>
                    `).join('')}
                    ${dadosAnalise.problemas.length === 0 ? '<div class="metric"><span class="metric-label">Status</span><span class="metric-value status-good">Nenhum problema crítico</span></div>' : ''}
                </div>
            `;

            // Card de Melhorias Sugeridas
            const melhorias = dadosAnalise.melhorias.filter(m => m.prioridade === 'ALTA');
            const melhoriasCard = `
                <div class="analysis-card">
                    <div class="card-header">
                        <div class="card-icon" style="background: #f39c12;">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="card-title">Melhorias Prioritárias</div>
                    </div>
                    ${melhorias.slice(0, 4).map(m => `
                        <div class="metric">
                            <span class="metric-label">${m.tipo}</span>
                            <span class="metric-value status-warning">${m.melhoria}</span>
                        </div>
                    `).join('')}
                    ${melhorias.length === 0 ? '<div class="metric"><span class="metric-label">Status</span><span class="metric-value status-good">Sistema otimizado</span></div>' : ''}
                </div>
            `;

            grid.innerHTML = resumoCard + statusCard + problemasCard + melhoriasCard;
        }

        function calcularPedidosAtrasados() {
            const agora = new Date();
            return dadosAnalise.pedidos.filter(p => {
                if (p.status !== 'ENVIADO' && p.status !== 'CONFIRMADO') return false;
                const dataEnvio = p.dataEnvio?.toDate ? p.dataEnvio.toDate() : new Date(p.dataEnvio);
                const diasSemRecebimento = (agora - dataEnvio) / (1000 * 60 * 60 * 24);
                return diasSemRecebimento > 30;
            }).length;
        }

        function calcularEficiencia() {
            const totalProcessos = dadosAnalise.solicitacoes.length;
            if (totalProcessos === 0) return 100;

            const processosCompletos = dadosAnalise.pedidos.filter(p => p.status === 'RECEBIDO').length;
            const problemasGraves = dadosAnalise.problemas.filter(p => p.tipo === 'ATRASO' || p.tipo === 'FLUXO').length;

            const eficienciaBase = (processosCompletos / totalProcessos) * 100;
            const penalidade = problemasGraves * 5;

            return Math.max(0, Math.round(eficienciaBase - penalidade));
        }

        function showLoading() {
            document.getElementById('analysisGrid').innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <div>Analisando processo de compras...</div>
                </div>
            `;
        }

        function hideLoading() {
            // Loading será substituído pelos resultados
        }

        function renderizarFluxo() {
            const flowDiagram = document.getElementById('flowDiagram');
            const flowSteps = document.getElementById('flowSteps');

            const steps = [
                {
                    titulo: '1. Solicitação de Compra',
                    status: avaliarStatusSolicitacoes(),
                    detalhes: `${dadosAnalise.solicitacoes.length} solicitações | ${dadosAnalise.solicitacoes.filter(s => s.status === 'PENDENTE').length} pendentes`,
                    problemas: dadosAnalise.problemas.filter(p => p.modulo === 'SOLICITAÇÕES')
                },
                {
                    titulo: '2. Cotação de Preços',
                    status: avaliarStatusCotacoes(),
                    detalhes: `${dadosAnalise.cotacoes.length} cotações | ${dadosAnalise.cotacoes.filter(c => !c.respostas || Object.keys(c.respostas).length === 0).length} sem resposta`,
                    problemas: dadosAnalise.problemas.filter(p => p.modulo === 'COTAÇÕES')
                },
                {
                    titulo: '3. Pedido de Compra',
                    status: avaliarStatusPedidos(),
                    detalhes: `${dadosAnalise.pedidos.length} pedidos | ${calcularPedidosAtrasados()} atrasados`,
                    problemas: dadosAnalise.problemas.filter(p => p.modulo === 'PEDIDOS')
                },
                {
                    titulo: '4. Integração do Fluxo',
                    status: avaliarIntegracao(),
                    detalhes: 'Verificação de vínculos entre etapas',
                    problemas: dadosAnalise.problemas.filter(p => p.modulo === 'INTEGRAÇÃO')
                }
            ];

            flowSteps.innerHTML = steps.map(step => `
                <div class="flow-step ${step.status}">
                    <div style="flex: 1;">
                        <h4>${step.titulo}</h4>
                        <p>${step.detalhes}</p>
                        ${step.problemas.length > 0 ? `
                            <div style="margin-top: 10px;">
                                <strong>Problemas:</strong>
                                <ul style="margin: 5px 0 0 20px;">
                                    ${step.problemas.map(p => `<li>${p.problema}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                    <div style="font-size: 24px; margin-left: 15px;">
                        <i class="fas fa-${step.status === 'success' ? 'check-circle' : step.status === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                    </div>
                </div>
            `).join('');

            flowDiagram.style.display = 'block';
        }

        function avaliarStatusSolicitacoes() {
            const pendentes = dadosAnalise.solicitacoes.filter(s => s.status === 'PENDENTE').length;
            const total = dadosAnalise.solicitacoes.length;

            if (total === 0) return 'success';
            if (pendentes / total > 0.3) return 'error';
            if (pendentes / total > 0.1) return 'warning';
            return 'success';
        }

        function avaliarStatusCotacoes() {
            const semResposta = dadosAnalise.cotacoes.filter(c => !c.respostas || Object.keys(c.respostas).length === 0).length;
            const total = dadosAnalise.cotacoes.length;

            if (total === 0) return 'success';
            if (semResposta / total > 0.5) return 'error';
            if (semResposta / total > 0.2) return 'warning';
            return 'success';
        }

        function avaliarStatusPedidos() {
            const atrasados = calcularPedidosAtrasados();
            const total = dadosAnalise.pedidos.length;

            if (total === 0) return 'success';
            if (atrasados / total > 0.3) return 'error';
            if (atrasados / total > 0.1) return 'warning';
            return 'success';
        }

        function avaliarIntegracao() {
            const problemasIntegracao = dadosAnalise.problemas.filter(p => p.modulo === 'INTEGRAÇÃO').length;

            if (problemasIntegracao === 0) return 'success';
            if (problemasIntegracao > 5) return 'error';
            return 'warning';
        }

        function renderizarRecomendacoes() {
            const recommendations = document.getElementById('recommendations');
            const recommendationsList = document.getElementById('recommendationsList');

            // Combinar problemas e melhorias em recomendações
            const todasRecomendacoes = [
                ...dadosAnalise.problemas.map(p => ({
                    tipo: 'CORREÇÃO',
                    prioridade: p.tipo === 'ATRASO' || p.tipo === 'FLUXO' ? 'high' : 'medium',
                    titulo: `${p.modulo}: ${p.problema}`,
                    descricao: p.solucao,
                    impacto: p.impacto
                })),
                ...dadosAnalise.melhorias.map(m => ({
                    tipo: 'MELHORIA',
                    prioridade: m.prioridade === 'ALTA' ? 'high' : m.prioridade === 'MÉDIA' ? 'medium' : 'low',
                    titulo: `${m.tipo}: ${m.melhoria}`,
                    descricao: m.implementacao,
                    impacto: m.beneficio
                }))
            ];

            // Ordenar por prioridade
            const prioridadeOrdem = { 'high': 3, 'medium': 2, 'low': 1 };
            todasRecomendacoes.sort((a, b) => prioridadeOrdem[b.prioridade] - prioridadeOrdem[a.prioridade]);

            recommendationsList.innerHTML = todasRecomendacoes.map(rec => `
                <div class="recommendation-item ${rec.prioridade}">
                    <h4>
                        <i class="fas fa-${rec.tipo === 'CORREÇÃO' ? 'wrench' : 'lightbulb'}"></i>
                        ${rec.titulo}
                    </h4>
                    <p><strong>Ação:</strong> ${rec.descricao}</p>
                    <p><strong>Impacto:</strong> ${rec.impacto}</p>
                    <div style="margin-top: 10px;">
                        <span class="badge ${rec.prioridade}">
                            Prioridade ${rec.prioridade === 'high' ? 'Alta' : rec.prioridade === 'medium' ? 'Média' : 'Baixa'}
                        </span>
                    </div>
                </div>
            `).join('');

            recommendations.style.display = 'block';
        }

        window.verificarConfiguracoes = async function() {
            alert('Verificando configurações do sistema...');
            // Implementar verificação específica de configurações
        };

        window.gerarRelatorioMelhorias = async function() {
            if (dadosAnalise.problemas.length === 0 && dadosAnalise.melhorias.length === 0) {
                alert('Execute primeiro a análise completa do processo.');
                return;
            }

            const relatorio = gerarRelatorioTexto();

            // Criar e baixar arquivo
            const blob = new Blob([relatorio], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `relatorio_processo_compras_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        function gerarRelatorioTexto() {
            const agora = new Date().toLocaleString('pt-BR');

            return `
RELATÓRIO DE ANÁLISE DO PROCESSO DE COMPRAS
==========================================

Data: ${agora}
Sistema: Gestão Empresarial

RESUMO EXECUTIVO
================
- Solicitações Ativas: ${dadosAnalise.solicitacoes.length}
- Cotações em Andamento: ${dadosAnalise.cotacoes.length}
- Pedidos Ativos: ${dadosAnalise.pedidos.length}
- Problemas Detectados: ${dadosAnalise.problemas.length}
- Eficiência do Processo: ${calcularEficiencia()}%

PROBLEMAS IDENTIFICADOS
=======================
${dadosAnalise.problemas.map((p, i) => `
${i + 1}. ${p.modulo} - ${p.tipo}
   Problema: ${p.problema}
   Impacto: ${p.impacto}
   Solução: ${p.solucao}
`).join('')}

MELHORIAS SUGERIDAS
===================
${dadosAnalise.melhorias.map((m, i) => `
${i + 1}. ${m.tipo} - Prioridade ${m.prioridade}
   Melhoria: ${m.melhoria}
   Benefício: ${m.beneficio}
   Implementação: ${m.implementacao}
`).join('')}

RECOMENDAÇÕES PRIORITÁRIAS
==========================
1. Resolver problemas críticos de fluxo
2. Implementar configurações de aprovação
3. Ativar sistema de alertas
4. Melhorar acompanhamento de fornecedores
5. Otimizar integração entre etapas

Relatório gerado automaticamente pelo Sistema de Análise de Processos.
            `.trim();
        }

        // Adicionar estilos para badges
        const style = document.createElement('style');
        style.textContent = `
            .badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
                color: white;
            }
            .badge.high { background: #e74c3c; }
            .badge.medium { background: #f39c12; }
            .badge.low { background: #27ae60; }
        `;
        document.head.appendChild(style);

        console.log('Analisador do Processo de Compras carregado');
    </script>
</body>
</html>
