// ===================================================================
// SERVIÇO CENTRALIZADO DE GERAÇÃO DE NÚMEROS - WiZAR ERP
// ===================================================================
// Gera números únicos e sequenciais para todos os documentos do sistema
// Padroniza a numeração e evita duplicações
// ===================================================================

import { db } from '../firebase-config.js';
import { 
    doc, 
    getDoc, 
    setDoc, 
    updateDoc, 
    runTransaction,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class NumberGeneratorService {
    
    // ===================================================================
    // CONFIGURAÇÕES DE NUMERAÇÃO
    // ===================================================================
    
    static DOCUMENT_TYPES = {
        SOLICITACAO_COMPRA: {
            prefix: 'SC',
            collection: 'solicitacoesCompra',
            counter: 'solicitacoes',
            format: 'SC-{AAMM}-{NNNN}',
            description: 'Solicitações de Compra'
        },
        COTACAO: {
            prefix: 'CT',
            collection: 'cotacoes',
            counter: 'cotacoes',
            format: 'CT-{AAMM}-{NNNN}',
            description: 'Cotações'
        },
        PEDIDO_COMPRA: {
            prefix: 'PC',
            collection: 'pedidosCompra',
            counter: 'pedidosCompra',
            format: 'PC-{AAMM}-{NNNN}',
            description: 'Pedidos de Compra'
        },
        ORDEM_PRODUCAO: {
            prefix: 'OP',
            collection: 'ordensProducao',
            counter: 'ordensProducao',
            format: 'OP-{AAMM}-{NNNN}',
            description: 'Ordens de Produção'
        },
        TRANSFERENCIA: {
            prefix: 'TRF',
            collection: 'transferenciasArmazem',
            counter: 'transferencias',
            format: 'TRF-{AAMM}-{NNNN}',
            description: 'Transferências de Armazém'
        },
        PEDIDO_VENDA: {
            prefix: 'PV',
            collection: 'pedidosVenda',
            counter: 'pedidosVenda',
            format: 'PV-{AAMM}-{NNNN}',
            description: 'Pedidos de Venda'
        },
        NOTA_FISCAL: {
            prefix: 'NF',
            collection: 'notasFiscais',
            counter: 'notasFiscais',
            format: 'NF-{AAMM}-{NNNN}',
            description: 'Notas Fiscais'
        }
    };
    
    // ===================================================================
    // GERAÇÃO DE NÚMEROS PRINCIPAIS
    // ===================================================================
    
    /**
     * Gera número para solicitação de compra
     */
    static async generateSolicitacaoNumber() {
        return await this.generateNumber('SOLICITACAO_COMPRA');
    }
    
    /**
     * Gera número para cotação
     */
    static async generateCotacaoNumber() {
        return await this.generateNumber('COTACAO');
    }
    
    /**
     * Gera número para pedido de compra
     */
    static async generatePedidoCompraNumber() {
        return await this.generateNumber('PEDIDO_COMPRA');
    }
    
    /**
     * Gera número para ordem de produção
     */
    static async generateOrdemProducaoNumber() {
        return await this.generateNumber('ORDEM_PRODUCAO');
    }
    
    /**
     * Gera número para transferência
     */
    static async generateTransferenciaNumber() {
        return await this.generateNumber('TRANSFERENCIA');
    }
    
    /**
     * Gera número para pedido de venda
     */
    static async generatePedidoVendaNumber() {
        return await this.generateNumber('PEDIDO_VENDA');
    }
    
    /**
     * Gera número para nota fiscal
     */
    static async generateNotaFiscalNumber() {
        return await this.generateNumber('NOTA_FISCAL');
    }
    
    // ===================================================================
    // MÉTODO PRINCIPAL DE GERAÇÃO
    // ===================================================================
    
    /**
     * Método principal para gerar números únicos
     */
    static async generateNumber(documentType) {
        try {
            const config = this.DOCUMENT_TYPES[documentType];
            if (!config) {
                throw new Error(`Tipo de documento não configurado: ${documentType}`);
            }
            
            return await runTransaction(db, async (transaction) => {
                // Obter data atual
                const now = new Date();
                const year = now.getFullYear().toString().slice(-2); // YY
                const month = (now.getMonth() + 1).toString().padStart(2, '0'); // MM
                const yearMonth = year + month; // YYMM
                
                // Referência do contador
                const counterRef = doc(db, "contadores", config.counter);
                const counterDoc = await transaction.get(counterRef);
                
                let nextNumber = 1;
                let currentYearMonth = yearMonth;
                
                if (counterDoc.exists()) {
                    const counterData = counterDoc.data();
                    
                    // Verificar se mudou o mês/ano
                    if (counterData.yearMonth === yearMonth) {
                        // Mesmo mês, incrementar sequência
                        nextNumber = counterData.sequence + 1;
                    } else {
                        // Novo mês, resetar sequência
                        nextNumber = 1;
                    }
                } else {
                    // Criar contador se não existir
                    await this.initializeCounter(transaction, counterRef, config, yearMonth);
                }
                
                // Atualizar contador
                transaction.update(counterRef, {
                    sequence: nextNumber,
                    yearMonth: yearMonth,
                    lastGenerated: Timestamp.now(),
                    totalGenerated: (counterDoc.exists() ? (counterDoc.data().totalGenerated || 0) : 0) + 1
                });
                
                // Gerar número formatado
                const formattedNumber = this.formatNumber(config, yearMonth, nextNumber);
                
                // Log da geração
                console.log(`📄 Número gerado: ${formattedNumber} (${config.description})`);
                
                return formattedNumber;
            });
            
        } catch (error) {
            console.error(`Erro ao gerar número para ${documentType}:`, error);
            
            // Fallback para método seguro
            return this.generateFallbackNumber(documentType);
        }
    }
    
    // ===================================================================
    // MÉTODOS AUXILIARES
    // ===================================================================
    
    /**
     * Inicializa contador para novo tipo de documento
     */
    static async initializeCounter(transaction, counterRef, config, yearMonth) {
        const counterData = {
            sequence: 0,
            yearMonth: yearMonth,
            documentType: config.prefix,
            description: config.description,
            created: Timestamp.now(),
            lastGenerated: null,
            totalGenerated: 0
        };
        
        transaction.set(counterRef, counterData);
        console.log(`🔢 Contador inicializado para: ${config.description}`);
    }
    
    /**
     * Formata o número conforme padrão definido
     */
    static formatNumber(config, yearMonth, sequence) {
        const sequenceFormatted = sequence.toString().padStart(4, '0');
        
        return config.format
            .replace('{PREFIX}', config.prefix)
            .replace('{AAMM}', yearMonth)
            .replace('{NNNN}', sequenceFormatted);
    }
    
    /**
     * Método de fallback em caso de erro
     */
    static generateFallbackNumber(documentType) {
        const config = this.DOCUMENT_TYPES[documentType];
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const timestamp = Date.now().toString().slice(-4); // Últimos 4 dígitos do timestamp
        
        const fallbackNumber = `${config.prefix}-${year}${month}-${timestamp}`;
        console.warn(`⚠️ Usando número fallback: ${fallbackNumber}`);
        
        return fallbackNumber;
    }
    
    // ===================================================================
    // UTILITÁRIOS DE ADMINISTRAÇÃO
    // ===================================================================
    
    /**
     * Obtém informações de todos os contadores
     */
    static async getCountersInfo() {
        try {
            const countersInfo = {};
            
            for (const [type, config] of Object.entries(this.DOCUMENT_TYPES)) {
                const counterRef = doc(db, "contadores", config.counter);
                const counterDoc = await getDoc(counterRef);
                
                if (counterDoc.exists()) {
                    countersInfo[type] = {
                        ...counterDoc.data(),
                        config: config
                    };
                } else {
                    countersInfo[type] = {
                        exists: false,
                        config: config
                    };
                }
            }
            
            return countersInfo;
            
        } catch (error) {
            console.error('Erro ao obter informações dos contadores:', error);
            throw error;
        }
    }
    
    /**
     * Reseta contador para um tipo específico
     */
    static async resetCounter(documentType, newValue = 0) {
        try {
            const config = this.DOCUMENT_TYPES[documentType];
            if (!config) {
                throw new Error(`Tipo de documento não configurado: ${documentType}`);
            }
            
            const now = new Date();
            const year = now.getFullYear().toString().slice(-2);
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const yearMonth = year + month;
            
            const counterRef = doc(db, "contadores", config.counter);
            await setDoc(counterRef, {
                sequence: newValue,
                yearMonth: yearMonth,
                documentType: config.prefix,
                description: config.description,
                created: Timestamp.now(),
                lastGenerated: null,
                totalGenerated: 0,
                resetAt: Timestamp.now(),
                resetBy: 'Sistema'
            });
            
            console.log(`🔄 Contador resetado: ${config.description} = ${newValue}`);
            return true;
            
        } catch (error) {
            console.error(`Erro ao resetar contador ${documentType}:`, error);
            throw error;
        }
    }
    
    /**
     * Inicializa todos os contadores do sistema
     */
    static async initializeAllCounters() {
        try {
            const results = [];
            
            for (const [type, config] of Object.entries(this.DOCUMENT_TYPES)) {
                try {
                    const counterRef = doc(db, "contadores", config.counter);
                    const counterDoc = await getDoc(counterRef);
                    
                    if (!counterDoc.exists()) {
                        await this.resetCounter(type, 0);
                        results.push({ type, status: 'created', config });
                    } else {
                        results.push({ type, status: 'exists', config });
                    }
                } catch (error) {
                    results.push({ type, status: 'error', error: error.message, config });
                }
            }
            
            console.log('🔢 Inicialização de contadores concluída:', results);
            return results;
            
        } catch (error) {
            console.error('Erro na inicialização dos contadores:', error);
            throw error;
        }
    }
    
    /**
     * Valida se um número está no formato correto
     */
    static validateNumber(documentType, number) {
        const config = this.DOCUMENT_TYPES[documentType];
        if (!config) return false;
        
        // Regex baseado no formato configurado
        const pattern = config.format
            .replace('{PREFIX}', config.prefix)
            .replace('{AAMM}', '\\d{4}')
            .replace('{NNNN}', '\\d{4}')
            .replace('-', '\\-');
        
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(number);
    }
    
    /**
     * Extrai informações de um número
     */
    static parseNumber(number) {
        const parts = number.split('-');
        if (parts.length !== 3) return null;
        
        const [prefix, yearMonth, sequence] = parts;
        
        return {
            prefix,
            year: '20' + yearMonth.slice(0, 2),
            month: yearMonth.slice(2, 4),
            yearMonth,
            sequence: parseInt(sequence),
            full: number
        };
    }
}

// ===================================================================
// INICIALIZAÇÃO AUTOMÁTICA
// ===================================================================

// Inicializar contadores na primeira execução
NumberGeneratorService.initializeAllCounters().then(results => {
    const created = results.filter(r => r.status === 'created').length;
    const existing = results.filter(r => r.status === 'exists').length;
    const errors = results.filter(r => r.status === 'error').length;
    
    console.log(`🔢 Contadores: ${created} criados, ${existing} existentes, ${errors} erros`);
});

// ===================================================================
// COMPATIBILIDADE COM CÓDIGO EXISTENTE
// ===================================================================

// Funções globais para compatibilidade
window.generateRequestNumber = () => NumberGeneratorService.generateSolicitacaoNumber();
window.generateQuotationNumber = () => NumberGeneratorService.generateCotacaoNumber();
window.generatePurchaseOrderNumber = () => NumberGeneratorService.generatePedidoCompraNumber();
window.generateOrderNumber = () => NumberGeneratorService.generateOrdemProducaoNumber();
window.generateTransferNumber = () => NumberGeneratorService.generateTransferenciaNumber();

console.log('🔢 Serviço de Geração de Números carregado e inicializado');
