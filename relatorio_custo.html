<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de Custos</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 30px auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    h1, h2, h3 {
      color: #333;
      margin-bottom: 20px;
    }

    .filter-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: bold;
    }

    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    button:hover {
      background-color: #45a049;
    }

    .back-button {
      background-color: #6c757d;
    }

    .cost-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .cost-table th,
    .cost-table td {
      padding: 12px;
      border: 1px solid #ddd;
      text-align: left;
    }

    .cost-table th {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    .cost-details {
      background-color: #fff;
      padding: 15px;
      border-radius: 4px;
      margin-top: 10px;
      border: 1px solid #ddd;
    }

    .cost-breakdown {
      display: flex;
      gap: 20px;
      margin-top: 10px;
    }

    .cost-item {
      flex: 1;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .total-cost {
      font-size: 1.2em;
      font-weight: bold;
      color: #2196F3;
      margin-top: 10px;
      padding: 10px;
      background-color: #e3f2fd;
      border-radius: 4px;
    }

    .export-button {
      background-color: #2196F3;
      margin-left: 10px;
    }

    .cost-chart {
      margin-top: 20px;
      padding: 20px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #ddd;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      width: 90%;
      max-width: 800px;
      border-radius: 8px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .close-button {
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close-button:hover {
      color: #999;
    }

    .tab-container {
      margin-top: 20px;
    }

    .tab-buttons {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .tab-button {
      padding: 10px 20px;
      background-color: #f0f0f0;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .tab-button.active {
      background-color: #4CAF50;
      color: white;
    }

    .tab-content {
      display: none;
      padding: 15px;
      background-color: #fff;
      border-radius: 4px;
    }

    .tab-content.active {
      display: block;
    }

    .history-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .history-item:last-child {
      border-bottom: none;
    }

    .movement-type {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }

    .movement-entrada {
      background-color: #4CAF50;
      color: white;
    }

    .movement-saida {
      background-color: #f44336;
      color: white;
    }

    .info-box {
      background-color: #e3f2fd;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .info-box h3 {
      margin-top: 0;
      color: #1976d2;
    }

    .cost-evolution {
      margin-top: 20px;
      padding: 15px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #ddd;
    }

    .cost-evolution-chart {
      height: 300px;
      margin-top: 15px;
    }

    .tree-view {
      margin: 10px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .tree-item {
      margin: 5px 0;
      padding: 5px;
      border-left: 2px solid #4CAF50;
    }

    .tree-item-child {
      margin-left: 20px;
      border-left: 2px solid #2196F3;
    }

    .operation-item {
      margin: 5px 0;
      padding: 5px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #ddd;
    }

    .cost-badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      background-color: #e3f2fd;
      color: #1976d2;
      margin-left: 5px;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Relatório de Custos</h1>
      <div>
        <button onclick="exportToExcel()" class="export-button">Exportar Excel</button>
        <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
      </div>
    </div>

    <div class="filter-section">
      <div class="form-row">
        <div class="form-col">
          <label>Período:</label>
          <div style="display: flex; gap: 10px;">
            <input type="date" id="startDate">
            <input type="date" id="endDate">
          </div>
        </div>
        <div class="form-col">
          <label>Produto:</label>
          <select id="productFilter">
            <option value="">Todos os produtos</option>
          </select>
        </div>
        <div class="form-col">
          <label>Tipo:</label>
          <select id="typeFilter">
            <option value="">Todos os tipos</option>
            <option value="MP">Matéria Prima</option>
            <option value="SP">Semi-Produto</option>
            <option value="PA">Produto Acabado</option>
          </select>
        </div>
      </div>
      <button onclick="updateReport()">Atualizar Relatório</button>
    </div>

    <div class="info-box">
      <h3>Resumo de Custos</h3>
      <div id="costSummary"></div>
    </div>

    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" onclick="switchTab('overview')">Visão Geral</button>
        <button class="tab-button" onclick="switchTab('structure')">Estrutura de Custos</button>
        <button class="tab-button" onclick="switchTab('materials')">Custos de Materiais</button>
        <button class="tab-button" onclick="switchTab('production')">Custos de Produção</button>
        <button class="tab-button" onclick="switchTab('history')">Histórico de Custos</button>
      </div>

      <div id="overviewTab" class="tab-content active">
        <div class="cost-chart">
          <canvas id="costOverviewChart"></canvas>
        </div>
        <table class="cost-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Tipo</th>
              <th>Custo Material</th>
              <th>Custo MO</th>
              <th>Custo Total</th>
              <th>Última Atualização</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="costOverviewBody"></tbody>
        </table>
      </div>

      <div id="structureTab" class="tab-content">
        <div class="tree-view" id="costStructureTree"></div>
      </div>

      <div id="materialsTab" class="tab-content">
        <div class="cost-chart">
          <canvas id="materialsCostChart"></canvas>
        </div>
        <table class="cost-table">
          <thead>
            <tr>
              <th>Material</th>
              <th>Último Preço</th>
              <th>Custo Médio</th>
              <th>Qtd. em Estoque</th>
              <th>Valor Total</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="materialsCostBody"></tbody>
        </table>
      </div>

      <div id="productionTab" class="tab-content">
        <div class="cost-chart">
          <canvas id="productionCostChart"></canvas>
        </div>
        <table class="cost-table">
          <thead>
            <tr>
              <th>Ordem</th>
              <th>Produto</th>
              <th>Custo Material</th>
              <th>Custo MO</th>
              <th>Custo Total</th>
              <th>Custo Unitário</th>
              <th>Data</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="productionCostBody"></tbody>
        </table>
      </div>

      <div id="historyTab" class="tab-content">
        <div class="cost-evolution">
          <h3>Evolução do Custo</h3>
          <canvas id="costEvolutionChart"></canvas>
        </div>
        <div id="costHistory"></div>
      </div>
    </div>
  </div>

  <!-- Modal de Detalhes -->
  <div id="detailsModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal()">&times;</span>
      <h2>Detalhes de Custo</h2>
      <div id="costDetails"></div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs,
      query,
      where,
      Timestamp,
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let ordensProducao = [];
    let movimentacoesEstoque = [];
    let estruturas = [];
    let recursos = [];
    let operacoes = [];
    let charts = {};

    window.onload = async function() {
      await loadInitialData();
      setupFilters();
      await updateReport();
    };

    async function loadInitialData() {
      try {
        const [
          produtosSnap,
          ordensSnap,
          movimentacoesSnap,
          estruturasSnap,
          recursosSnap,
          operacoesSnap
        ] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "movimentacoesEstoque")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "recursos")),
          getDocs(collection(db, "operacoes"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        movimentacoesEstoque = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    function setupFilters() {
      const productFilter = document.getElementById('productFilter');
      productFilter.innerHTML = '<option value="">Todos os produtos</option>';
      
      const produtosFiltrados = produtos
        .sort((a, b) => a.codigo.localeCompare(b.codigo));

      produtosFiltrados.forEach(produto => {
        productFilter.innerHTML += `
          <option value="${produto.id}">
            ${produto.codigo} - ${produto.descricao} (${produto.tipo})
          </option>`;
      });
    }

    window.updateReport = async function() {
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      const productId = document.getElementById('productFilter').value;
      const type = document.getElementById('typeFilter').value;

      await Promise.all([
        updateCostOverview(startDate, endDate, productId, type),
        updateCostStructure(productId),
        updateMaterialsCost(startDate, endDate, productId, type),
        updateProductionCost(startDate, endDate, productId, type),
        updateCostHistory(startDate, endDate, productId, type)
      ]);

      updateCostSummary();
    };

    async function updateCostOverview(startDate, endDate, productId, type) {
      const filteredProducts = filterProducts(productId, type);
      const overviewBody = document.getElementById('costOverviewBody');
      overviewBody.innerHTML = '';

      const chartData = {
        labels: [],
        datasets: [{
          label: 'Custo Material',
          data: [],
          backgroundColor: 'rgba(54, 162, 235, 0.5)'
        }, {
          label: 'Custo MO',
          data: [],
          backgroundColor: 'rgba(255, 99, 132, 0.5)'
        }]
      };

      for (const produto of filteredProducts) {
        const custos = await calcularCustoProduto(produto.id);
        const ultimaAtualizacao = await getUltimaAtualizacaoCusto(produto.id);

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${produto.codigo}</td>
          <td>${produto.descricao}</td>
          <td>${produto.tipo}</td>
          <td>R$ ${custos.custoMaterial.toFixed(2)}</td>
          <td>R$ ${custos.custoMaoDeObra.toFixed(2)}</td>
          <td>R$ ${(custos.custoMaterial + custos.custoMaoDeObra).toFixed(2)}</td>
          <td>${ultimaAtualizacao ? new Date(ultimaAtualizacao.seconds * 1000).toLocaleDateString() : '-'}</td>
          <td>
            <button onclick="showProductDetails('${produto.id}')" style="padding: 5px 10px;">
              Detalhes
            </button>
          </td>
        `;
        overviewBody.appendChild(row);

        chartData.labels.push(produto.codigo);
        chartData.datasets[0].data.push(custos.custoMaterial);
        chartData.datasets[1].data.push(custos.custoMaoDeObra);
      }

      updateChart('costOverviewChart', chartData, {
        type: 'bar',
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              stacked: true,
              title: {
                display: true,
                text: 'Custo (R$)'
              }
            },
            x: {
              stacked: true
            }
          },
          plugins: {
            title: {
              display: true,
              text: 'Visão Geral de Custos por Produto'
            }
          }
        }
      });
    }

    async function updateCostStructure(productId) {
      const treeDiv = document.getElementById('costStructureTree');
      treeDiv.innerHTML = '';

      if (!productId) {
        treeDiv.innerHTML = '<p>Selecione um produto para ver sua estrutura de custos.</p>';
        return;
      }

      const estrutura = await buildCostStructure(productId);
      treeDiv.appendChild(createTreeNode(estrutura));
    }

    async function buildCostStructure(productId, level = 0) {
      const produto = produtos.find(p => p.id === productId);
      const estrutura = estruturas.find(e => e.produtoPaiId === productId);
      const custos = await calcularCustoProduto(productId);

      const node = {
        produto,
        custos,
        level,
        children: [],
        operacoes: []
      };

      if (estrutura) {
        // Adicionar operações
        if (estrutura.operacoes) {
          for (const op of estrutura.operacoes) {
            const operacao = operacoes.find(o => o.id === op.operacaoId);
            const recurso = recursos.find(r => r.id === op.recursoId);
            const custoOperacao = calcularCustoOperacao(op, recurso);

            node.operacoes.push({
              operacao,
              recurso,
              tempo: op.tempo,
              custo: custoOperacao
            });
          }
        }

        // Adicionar componentes
        if (estrutura.componentes) {
          for (const comp of estrutura.componentes) {
            const componenteProduto = produtos.find(p => p.id === comp.componentId);
            if (componenteProduto && (componenteProduto.tipo === 'SP' || componenteProduto.tipo === 'PA')) {
              const childNode = await buildCostStructure(comp.componentId, level + 1);
              childNode.quantidade = comp.quantidade;
              node.children.push(childNode);
            } else if (componenteProduto) {
              node.children.push({
                produto: componenteProduto,
                quantidade: comp.quantidade,
                custoUnitario: await calcularCustoMedio(comp.componentId),
                level: level + 1
              });
            }
          }
        }
      }

      return node;
    }

    function createTreeNode(node) {
      const div = document.createElement('div');
      div.className = `tree-item ${node.level > 0 ? 'tree-item-child' : ''}`;

      // Informações do produto
      const produtoInfo = document.createElement('div');
      produtoInfo.innerHTML = `
        <strong>${node.produto.codigo} - ${node.produto.descricao}</strong>
        ${node.quantidade ? ` (${node.quantidade} ${node.produto.unidade})` : ''}
        <span class="cost-badge">
          Custo Total: R$ ${(node.custos?.custoMaterial + node.custos?.custoMaoDeObra).toFixed(2)}
        </span>
      `;
      div.appendChild(produtoInfo);

      // Operações
      if (node.operacoes && node.operacoes.length > 0) {
        const operacoesDiv = document.createElement('div');
        operacoesDiv.style.marginLeft = '20px';
        
        node.operacoes.forEach(op => {
          const opDiv = document.createElement('div');
          opDiv.className = 'operation-item';
          opDiv.innerHTML = `
            <i class="fas fa-cog"></i>
            ${op.operacao.numero} - ${op.operacao.operacao}
            (${op.recurso.codigo} - ${op.recurso.maquina})
            <span class="cost-badge">
              Tempo: ${op.tempo}min | Custo: R$ ${op.custo.toFixed(2)}
            </span>
          `;
          operacoesDiv.appendChild(opDiv);
        });
        
        div.appendChild(operacoesDiv);
      }

      // Componentes
      if (node.children && node.children.length > 0) {
        const childrenDiv = document.createElement('div');
        childrenDiv.style.marginLeft = '20px';
        
        node.children.forEach(child => {
          childrenDiv.appendChild(createTreeNode(child));
        });
        
        div.appendChild(childrenDiv);
      }

      return div;
    }

    async function calcularCustoProduto(produtoId) {
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
      let custoMaterial = 0;
      let custoMaoDeObra = 0;

      if (estrutura) {
        // Calcular custo dos materiais
        if (estrutura.componentes) {
          for (const componente of estrutura.componentes) {
            const custoComponente = await calcularCustoMedio(componente.componentId);
            custoMaterial += custoComponente * componente.quantidade;
          }
        }

        // Calcular custo das operações
        if (estrutura.operacoes) {
          for (const operacao of estrutura.operacoes) {
            const recurso = recursos.find(r => r.id === operacao.recursoId);
            if (recurso && recurso.custoHora) {
              custoMaoDeObra += calcularCustoOperacao(operacao, recurso);
            }
          }
        }
      } else {
        // Se não tem estrutura, usar o custo médio das entradas
        custoMaterial = await calcularCustoMedio(produtoId);
      }

      return {
        custoMaterial,
        custoMaoDeObra
      };
    }

    function calcularCustoOperacao(operacao, recurso) {
      if (!recurso || !recurso.custoHora) return 0;
      return (operacao.tempo / 60) * recurso.custoHora;
    }

    async function calcularCustoMedio(produtoId) {
      const movimentacoes = movimentacoesEstoque
        .filter(m => m.produtoId === produtoId && m.tipo === 'ENTRADA' && m.valorUnitario)
        .sort((a, b) => a.dataHora.seconds - b.dataHora.seconds);

      let custoMedio = 0;
      let quantidadeTotal = 0;

      movimentacoes.forEach(mov => {
        const quantidade = mov.quantidade || 0;
        const valorTotal = quantidade * mov.valorUnitario;
        
        quantidadeTotal += quantidade;
        custoMedio = ((custoMedio * (quantidadeTotal - quantidade)) + valorTotal) / quantidadeTotal;
      });

      return custoMedio;
    }
 async function calcularCustosOrdem(ordem) {
      const custoMaterial = await calcularCustoMaterialOrdem(ordem);
      const custoMaoDeObra = await calcularCustoMaoDeObraOrdem(ordem);

      return {
        custoMaterial,
        custoMaoDeObra,
        detalhes: {
          materiais: await getDetalhesMateriais(ordem),
          operacoes: await getDetalhesOperacoes(ordem)
        }
      };
    }

    async function calcularCustoMaterialOrdem(ordem) {
      let custoTotal = 0;

      if (ordem.materiaisNecessarios) {
        for (const material of ordem.materiaisNecessarios) {
          const custoMedio = await calcularCustoMedio(material.produtoId);
          custoTotal += custoMedio * material.quantidade;
        }
      }

      return custoTotal;
    }

    async function calcularCustoMaoDeObraOrdem(ordem) {
      let custoTotal = 0;
      const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);

      if (estrutura && estrutura.operacoes) {
        for (const operacao of estrutura.operacoes) {
          const recurso = recursos.find(r => r.id === operacao.recursoId);
          if (recurso && recurso.custoHora) {
            custoTotal += (operacao.tempo / 60) * recurso.custoHora;
          }
        }
      }

      return custoTotal;
    }

    async function getUltimoPrecoCompra(produtoId) {
      const movimentacoes = movimentacoesEstoque
        .filter(m => 
          m.produto === produtoId && 
          m.tipo === 'ENTRADA' && 
          m.tipoDocumento === 'COMPRA' &&
          m.valorUnitario
        )
        .sort((a, b) => b.dataHora.seconds - a.dataHora.seconds);

      return movimentacoes[0]?.valorUnitario || 0;
    }

    async function getSaldoEstoque(produtoId) {
      let saldo = 0;
      movimentacoesEstoque
        .filter(m => m.produtoId === produtoId)
        .forEach(mov => {
          saldo += mov.tipo === 'ENTRADA' ? mov.quantidade : -mov.quantidade;
        });
      return saldo;
    }

    async function getUltimaAtualizacaoCusto(produtoId) {
      const movimentacoes = movimentacoesEstoque
        .filter(m => 
          m.produtoId === produtoId && 
          m.tipo === 'ENTRADA' && 
          m.valorUnitario
        )
        .sort((a, b) => b.dataHora.seconds - a.dataHora.seconds);

      return movimentacoes[0]?.dataHora;
    }

    async function calcularVariacaoCusto(produtoId) {
      const movimentacoes = movimentacoesEstoque
        .filter(m => 
          m.produtoId === produtoId && 
          m.tipo === 'ENTRADA' && 
          m.valorUnitario
        )
        .sort((a, b) => b.dataHora.seconds - a.dataHora.seconds);

      if (movimentacoes.length < 2) return 0;

      const custoAtual = movimentacoes[0].valorUnitario;
      const custoAnterior = movimentacoes[1].valorUnitario;

      return ((custoAtual - custoAnterior) / custoAnterior) * 100;
    }

    async function getMovimentacoesProduto(produtoId, startDate, endDate) {
      let movimentacoes = movimentacoesEstoque
        .filter(m => m.produtoId === produtoId)
        .sort((a, b) => a.dataHora.seconds - b.dataHora.seconds);

      if (startDate) {
        movimentacoes = movimentacoes.filter(m => 
          m.dataHora.seconds >= new Date(startDate).getTime() / 1000);
      }
      if (endDate) {
        movimentacoes = movimentacoes.filter(m => 
          m.dataHora.seconds <= new Date(endDate).getTime() / 1000);
      }

      return movimentacoes;
    }

    function filterProducts(productId, type) {
      let filtered = produtos;

      if (productId) {
        filtered = filtered.filter(p => p.id === productId);
      }
      if (type) {
        filtered = filtered.filter(p => p.tipo === type);
      }

      return filtered;
    }

    function updateChart(canvasId, data, config) {
      if (charts[canvasId]) {
        charts[canvasId].destroy();
      }

      const ctx = document.getElementById(canvasId).getContext('2d');
      charts[canvasId] = new Chart(ctx, {
        ...config,
        data: data
      });
    }

    window.showProductDetails = async function(produtoId) {
  const produto = produtos.find(p => p.id === produtoId);
  const custoMedio = await calcularCustoMedio(produtoId);
  const custoOperacoes = await calcularCustoOperacoes(produtoId); // Custo das operações
  const saldoEstoque = await getSaldoEstoque(produtoId);
  const ultimaAtualizacao = await getUltimaAtualizacaoCusto(produtoId);
  const variacao = await calcularVariacaoCusto(produtoId);

  document.getElementById('costDetails').innerHTML = `
    <div class="cost-details">
      <h3>${produto.codigo} - ${produto.descricao}</h3>
      <p><strong>Tipo:</strong> ${produto.tipo}</p>
      <p><strong>Custo Médio Atual:</strong> R$ ${custoMedio.toFixed(2)}</p>
      <p><strong>Custo das Operações:</strong> R$ ${custoOperacoes.toFixed(2)}</p> <!-- Exibe o custo das operações -->
      <p><strong>Saldo em Estoque:</strong> ${saldoEstoque} ${produto.unidade}</p>
      <p><strong>Valor em Estoque:</strong> R$ ${(custoMedio * saldoEstoque).toFixed(2)}</p>
      <p><strong>Última Atualização:</strong> ${ultimaAtualizacao ? new Date(ultimaAtualizacao.seconds * 1000).toLocaleDateString() : '-'}</p>
      <p><strong>Variação:</strong> ${variacao > 0 ? '+' : ''}${variacao.toFixed(2)}%</p>
    </div>
  `;

  document.getElementById('detailsModal').style.display = 'block';
};

    window.showMaterialHistory = async function(materialId) {
      const material = produtos.find(p => p.id === materialId);
      const movimentacoes = await getMovimentacoesProduto(materialId);

      let html = `
        <div class="cost-details">
          <h3>${material.codigo} - ${material.descricao}</h3>
          <table class="cost-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Tipo</th>
                <th>Documento</th>
                <th>Quantidade</th>
                <th>Valor Unit.</th>
                <th>Valor Total</th>
              </tr>
            </thead>
            <tbody>
      `;

      movimentacoes.forEach(mov => {
        const valorTotal = (mov.valorUnitario || 0) * mov.quantidade;
        html += `
          <tr>
            <td>${new Date(mov.dataHora.seconds * 1000).toLocaleDateString()}</td>
            <td>${mov.tipo}</td>
            <td>${mov.tipoDocumento} ${mov.numeroDocumento || ''}</td>
            <td>${mov.quantidade} ${material.unidade}</td>
            <td>${mov.valorUnitario ? `R$ ${mov.valorUnitario.toFixed(2)}` : '-'}</td>
            <td>${mov.valorUnitario ? `R$ ${valorTotal.toFixed(2)}` : '-'}</td>
          </tr>
        `;
      });

      html += `
            </tbody>
          </table>
        </div>
      `;

      document.getElementById('costDetails').innerHTML = html;
      document.getElementById('detailsModal').style.display = 'block';
    };

    window.showOrderDetails = async function(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
            const produto = produtos.find(p => p.id === ordem.produtoId);
      const custos = await calcularCustosOrdem(ordem);

      let html = `
        <div class="cost-details">
          <h3>${ordem.numero} - ${produto.codigo} - ${produto.descricao}</h3>
          <p><strong>Quantidade:</strong> ${ordem.quantidade} ${produto.unidade}</p>
          <p><strong>Data:</strong> ${new Date(ordem.dataCriacao.seconds * 1000).toLocaleDateString()}</p>

          <h4>Materiais</h4>
          <table class="cost-table">
            <thead>
              <tr>
                <th>Material</th>
                <th>Quantidade</th>
                <th>Custo Unit.</th>
                <th>Custo Total</th>
              </tr>
            </thead>
            <tbody>
      `;

      for (const material of custos.detalhes.materiais) {
        html += `
          <tr>
            <td>${material.codigo} - ${material.descricao}</td>
            <td>${material.quantidade}</td>
            <td>R$ ${material.custoUnitario.toFixed(2)}</td>
            <td>R$ ${material.custoTotal.toFixed(2)}</td>
          </tr>
        `;
      }

      html += `
            </tbody>
          </table>

          <h4>Operações</h4>
          <table class="cost-table">
            <thead>
              <tr>
                <th>Seq.</th>
                <th>Operação</th>
                <th>Recurso</th>
                <th>Tempo (min)</th>
                <th>Custo/Hora</th>
                <th>Custo Total</th>
              </tr>
            </thead>
            <tbody>
      `;

      for (const op of custos.detalhes.operacoes) {
        html += `
          <tr>
            <td>${op.sequencia}</td>
            <td>${op.descricao}</td>
            <td>${op.recurso}</td>
            <td>${op.tempo}</td>
            <td>R$ ${op.custoHora.toFixed(2)}</td>
            <td>R$ ${op.custoTotal.toFixed(2)}</td>
          </tr>
        `;
      }

      html += `
            </tbody>
          </table>

          <div class="total-cost">
            <p>Custo Material: R$ ${custos.custoMaterial.toFixed(2)}</p>
            <p>Custo Mão de Obra: R$ ${custos.custoMaoDeObra.toFixed(2)}</p>
            <p>Custo Total: R$ ${(custos.custoMaterial + custos.custoMaoDeObra).toFixed(2)}</p>
            <p>Custo Unitário: R$ ${((custos.custoMaterial + custos.custoMaoDeObra) / ordem.quantidade).toFixed(2)}</p>
          </div>
        </div>
      `;

      document.getElementById('costDetails').innerHTML = html;
      document.getElementById('detailsModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('detailsModal').style.display = 'none';
    };

    window.switchTab = function(tab) {
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
      });

      document.getElementById(`${tab}Tab`).classList.add('active');
      document.querySelector(`button[onclick="switchTab('${tab}')"]`).classList.add('active');
    };

    window.exportToExcel = function() {
      alert('Funcionalidade de exportação será implementada em breve!');
    };

    async function calcularCustoOperacoes(produtoId) {
  const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
  let custoOperacoesTotal = 0;

  if (estrutura && estrutura.operacoes) {
    for (const operacao of estrutura.operacoes) {
      const recurso = recursos.find(r => r.id === operacao.recursoId);
      if (recurso && recurso.custoHora) {
        const custoOperacao = (operacao.tempo / 60) * recurso.custoHora; // Calcula o custo da operação
        custoOperacoesTotal += custoOperacao;
      }
    }
  }

  return custoOperacoesTotal;
}
  </script>
</body>
</html>