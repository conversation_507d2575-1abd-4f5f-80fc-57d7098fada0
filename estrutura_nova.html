<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Estrutura e Processo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .header .user-info {
            font-size: 14px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .date-time-info {
            font-size: 12px;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 120px);
        }

        .search-panel {
            width: 30%;
            padding: 20px;
            background-color: #ffffff;
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
        }

        .structure-panel {
            width: 70%;
            padding: 20px;
            background-color: #ffffff;
            overflow-y: auto;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--success-hover);
        }

        .btn-success:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .filters-section {
            background-color: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .product-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .product-item {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            background-color: #fff;
            cursor: move;
            transition: background-color 0.2s;
        }

        .product-item:hover {
            background-color: var(--secondary-color);
        }

        .product-item.dragging {
            opacity: 0.5;
        }

        .structure-container {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: #fff;
            margin-bottom: 20px;
        }

        .structure-header {
            background-color: #e3f2fd;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            border-radius: 8px 8px 0 0;
            border-left: 4px solid var(--primary-color);
        }

        .structure-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .structure-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .structure-content {
            padding: 20px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--primary-color);
        }

        .component, .operation {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-bottom: 2px;
            background-color: #fff;
            transition: all 0.2s ease;
            position: relative;
        }

        .component:nth-child(even), .operation:nth-child(even) {
            background-color: #f8f9fa;
        }

        .component:hover, .operation:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .component.clicked, .operation.clicked {
            background-color: #e0e0e0 !important;
        }

        .component.has-substructure {
            border-left: 4px solid var(--primary-color);
            background-color: #e3f2fd;
        }

        .component.has-substructure:hover {
            background-color: #bbdefb;
        }

        /* Estilos para componentes hierárquicos */
        .component.level-0 {
            margin-left: 0px;
            background-color: #e3f2fd;
        }

        .component.level-1 {
            margin-left: 40px;
            background-color: #f3e5f5;
            position: relative;
        }

        .component.level-2 {
            margin-left: 80px;
            background-color: #e8f5e8;
            position: relative;
        }

        .component.level-3 {
            margin-left: 120px;
            background-color: #fff3e0;
            position: relative;
        }

        .component.level-4 {
            margin-left: 160px;
            background-color: #fce4ec;
            position: relative;
        }

        /* Linhas de conexão hierárquica */
        .component.level-1::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            width: 20px;
            height: 2px;
            background-color: #4a90e2;
            border-radius: 1px;
        }

        .component.level-1::after {
            content: '';
            position: absolute;
            left: -25px;
            top: 0;
            width: 2px;
            height: 50%;
            background-color: #4a90e2;
            border-radius: 1px;
        }

        .component.level-2::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            width: 20px;
            height: 2px;
            background-color: #9c27b0;
            border-radius: 1px;
        }

        .component.level-2::after {
            content: '';
            position: absolute;
            left: -25px;
            top: 0;
            width: 2px;
            height: 50%;
            background-color: #9c27b0;
            border-radius: 1px;
        }

        .component.level-3::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            width: 20px;
            height: 2px;
            background-color: #4caf50;
            border-radius: 1px;
        }

        .component.level-3::after {
            content: '';
            position: absolute;
            left: -25px;
            top: 0;
            width: 2px;
            height: 50%;
            background-color: #4caf50;
            border-radius: 1px;
        }

        .component.level-4::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            width: 20px;
            height: 2px;
            background-color: #ff9800;
            border-radius: 1px;
        }

        .component.level-4::after {
            content: '';
            position: absolute;
            left: -25px;
            top: 0;
            width: 2px;
            height: 50%;
            background-color: #ff9800;
            border-radius: 1px;
        }

        /* Conectores verticais para componentes filhos */
        .component:not(:last-child).has-children::after {
            height: calc(100% + 2px);
        }

        .expand-icon {
            transition: transform 0.2s ease;
            cursor: pointer;
            color: var(--primary-color);
            font-weight: bold;
        }

        .expand-icon:hover {
            transform: scale(1.1);
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .sub-structure-container {
            animation: slideDown 0.3s ease-in-out;
            margin-left: 0;
            margin-top: 0;
            padding: 0;
            border: none;
            background: transparent;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                overflow: hidden;
            }
            to {
                opacity: 1;
                max-height: 2000px;
                overflow: visible;
            }
        }

        .sequence-input {
            width: 60px !important;
            text-align: center;
        }

        .quantity-input {
            width: 80px !important;
        }

        .operation-select {
            width: 150px !important;
        }

        .resource-select {
            width: 150px !important;
        }

        .time-input {
            width: 80px !important;
        }

        .description-input {
            flex: 1;
            min-width: 200px;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .footer-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            background-color: var(--secondary-color);
            border-top: 1px solid var(--border-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background-color: #ffffff;
            margin: 5% auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
        }

        .close:hover {
            color: #333;
        }

        /* ===== ESTILOS PARA MODAL "ONDE É USADO" ===== */
        .usage-section {
            margin-bottom: 25px;
        }

        .usage-section h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .usage-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .usage-table th {
            background-color: #f8f9fa;
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-color);
        }

        .usage-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .usage-table tr:last-child td {
            border-bottom: none;
        }

        .usage-table tr:hover {
            background-color: #f8f9fa;
        }

        .usage-summary {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid var(--primary-color);
        }

        .usage-summary h4 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .usage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .usage-stat {
            text-align: center;
        }

        .usage-stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .usage-stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
        }

        .no-usage-message {
            padding: 20px;
            text-align: center;
            color: var(--text-secondary);
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .no-usage-message i {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }

        .structure-info-card {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .structure-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .structure-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        /* ===== INDICADOR DE MUDANÇAS NÃO SALVAS ===== */
        .unsaved-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
            z-index: 100;
        }

        .unsaved-indicator i {
            font-size: 14px;
            animation: shake 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        /* Ajustar posição do header para acomodar o indicador */
        .structure-header {
            position: relative;
            padding-right: 200px;
        }

        /* Estilo para confirmação de saída */
        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .confirm-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .confirm-content h3 {
            color: #ff6b6b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .confirm-content p {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #666;
        }

        .confirm-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .confirm-buttons button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .confirm-save {
            background: #28a745;
            color: white;
        }

        .confirm-save:hover {
            background: #218838;
        }

        .confirm-discard {
            background: #dc3545;
            color: white;
        }

        .confirm-discard:hover {
            background: #c82333;
        }

        .confirm-cancel {
            background: #6c757d;
            color: white;
        }

        .confirm-cancel:hover {
            background: #5a6268;
        }

        /* ===== ESTILOS PARA SISTEMA DE REVISÕES ===== */
        .revision-badge {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .revisions-timeline {
            position: relative;
        }

        .timeline-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
        }

        .timeline-header h4 {
            color: var(--primary-color);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .revision-item {
            position: relative;
            display: flex;
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .revision-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .revision-item.current-revision {
            border-left: 5px solid #28a745;
            background: linear-gradient(135deg, #f8fff9, #ffffff);
        }

        .revision-marker {
            flex-shrink: 0;
            width: 80px;
            text-align: center;
            margin-right: 20px;
        }

        .revision-number {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .current-star {
            color: #ffc107;
            font-size: 16px;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }

        .revision-content {
            flex: 1;
        }

        .revision-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .revision-header h5 {
            margin: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .current-badge {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .historical-badge {
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .revision-actions {
            display: flex;
            gap: 5px;
        }

        .revision-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            margin-bottom: 15px;
        }

        .revision-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .stat-item i {
            color: var(--primary-color);
        }

        .revision-dates {
            text-align: right;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .revision-dates div {
            margin-bottom: 4px;
        }

        .revision-reason {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: var(--text-secondary);
        }

        .revision-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .comparison-controls {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }

        .comparison-controls label {
            font-weight: 600;
            color: var(--primary-color);
        }

        .comparison-controls select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: white;
        }

        .details-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid var(--border-color);
        }

        .components-section, .operations-section {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
        }

        .components-section h4, .operations-section h4 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        /* ===== ESTILOS PARA HISTÓRICO DE USUÁRIOS ===== */
        .user-history-timeline {
            position: relative;
        }

        .user-change-item {
            position: relative;
            display: flex;
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .user-change-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .user-change-item.recent-change {
            border-left: 5px solid #17a2b8;
            background: linear-gradient(135deg, #f0f9ff, #ffffff);
        }

        .change-marker {
            flex-shrink: 0;
            width: 80px;
            text-align: center;
            margin-right: 20px;
        }

        .change-avatar {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
        }

        .recent-star {
            color: #17a2b8;
            font-size: 16px;
            animation: sparkle 2s infinite;
        }

        .change-content {
            flex: 1;
        }

        .change-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .change-header h5 {
            margin: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .change-type-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .change-type-badge.criacao {
            background: #d4edda;
            color: #155724;
        }

        .change-type-badge.modificacao {
            background: #fff3cd;
            color: #856404;
        }

        .change-type-badge.restauracao {
            background: #d1ecf1;
            color: #0c5460;
        }

        .change-info {
            margin-bottom: 15px;
        }

        .change-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        .change-summary {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        .summary-grid {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 8px 0;
        }

        .summary-item {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .summary-item.added {
            background: #d4edda;
            color: #155724;
        }

        .summary-item.removed {
            background: #f8d7da;
            color: #721c24;
        }

        .summary-item.modified {
            background: #fff3cd;
            color: #856404;
        }

        .change-details {
            margin-top: 10px;
        }

        .change-details ul {
            margin: 5px 0 0 20px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .change-details li {
            margin-bottom: 2px;
        }

        .user-history-controls {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .summary-section {
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .summary-card {
            background-color: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .summary-number {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .summary-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }

        .type-PA { background-color: #2196F3; color: white; }
        .type-SP { background-color: #FF9800; color: white; }
        .type-MP { background-color: #4CAF50; color: white; }

        @media print {
            .no-print {
                display: none;
            }
            .container {
                margin: 0;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-sitemap"></i> Cadastro de Estrutura e Processo</h1>
            <div class="user-info">
                <div>Usuário: <span id="userStatus">Carregando...</span> | Empresa: 1000</div>
                <div class="date-time-info" id="currentDateTime"></div>
            </div>
        </div>

        <div class="main-content">
            <div class="search-panel">
                <div class="panel-title">
                    <i class="fas fa-search"></i> Pesquisar Produtos
                </div>

                <div class="filters-section">
                    <div class="form-group">
                        <label for="parentProductSelect">
                            <i class="fas fa-box"></i> Produto Pai
                        </label>
                        <select id="parentProductSelect" onchange="loadStructureForProduct()">
                            <option value="">Selecione o produto pai...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="searchInput">
                            <i class="fas fa-search"></i> Buscar Componentes
                        </label>
                        <input type="text" id="searchInput" placeholder="Digite para pesquisar...">
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="searchProducts()">
                            <i class="fas fa-search"></i> Pesquisar
                        </button>
                        <button class="btn btn-secondary" onclick="updateProductsList()" title="Atualizar lista">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="openProductModal()">
                            <i class="fas fa-plus"></i> Novo Produto
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label>Produtos Disponíveis</label>
                    <div class="product-list" id="productList"></div>
                </div>
            </div>

            <div class="structure-panel">
                <div class="panel-title">
                    <i class="fas fa-project-diagram"></i> Estrutura e Processo
                </div>

                <div class="loading">
                    <div class="spinner"></div>
                    <p>Carregando estrutura...</p>
                </div>

                <div id="structureTree"></div>

                <div class="footer-actions no-print">
                    <button class="btn btn-primary" onclick="openHistoryModal()">
                        <i class="fas fa-history"></i> Histórico
                    </button>
                    <button class="btn btn-primary" onclick="openUsageModal()">
                        <i class="fas fa-share-alt"></i> Onde é Usado
                    </button>
                    <button class="btn btn-primary" onclick="openRevisionHistoryModal()">
                        <i class="fas fa-code-branch"></i> Revisões
                    </button>
                    <button class="btn btn-info" onclick="openUserHistoryModal()">
                        <i class="fas fa-users"></i> Histórico de Usuários
                    </button>
                    <button class="btn btn-secondary" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Exportar Excel
                    </button>
                    <button class="btn btn-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                    <button class="btn btn-success" onclick="saveStructure()" disabled>
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                    <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </button>
                    <button class="btn btn-danger" onclick="deleteCurrentStructure()">
                        <i class="fas fa-trash"></i> Excluir
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal de Histórico -->
        <div id="historyModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeHistoryModal()">&times;</span>
                <div class="panel-title">Histórico de Estruturas</div>
                <div id="historyList" style="max-height: 60vh; overflow-y: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: var(--primary-color); color: white;">
                                <th style="padding: 8px;">Código</th>
                                <th style="padding: 8px;">Descrição</th>
                                <th style="padding: 8px;">Data Criação</th>
                                <th style="padding: 8px;">Última Alteração</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody"></tbody>
                    </table>
                </div>
                <div class="footer-actions">
                    <button class="btn btn-success" onclick="loadSelectedStructure()">Carregar</button>
                    <button class="btn btn-secondary" onclick="closeHistoryModal()">Fechar</button>
                </div>
            </div>
        </div>

        <!-- Modal de Cadastro de Produto -->
        <div id="productModal" class="modal">
            <div class="modal-content" style="max-width: 900px;">
                <span class="close" onclick="closeProductModal()">&times;</span>
                <div class="panel-title">Cadastrar Novo Produto</div>
                <form id="productForm" onsubmit="handleProductSubmit(event)">
                    <div class="summary-section">
                        <div class="section-title">Dados Básicos</div>
                        <div class="summary-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                            <div class="form-group">
                                <label class="required">Código</label>
                                <input type="text" id="productCode" required>
                            </div>
                            <div class="form-group">
                                <label class="required">Descrição</label>
                                <input type="text" id="productDescription" required>
                            </div>
                            <div class="form-group">
                                <label class="required">Tipo</label>
                                <select id="productType" required>
                                    <option value="PA">PA - Produto Acabado</option>
                                    <option value="SP">SP - Sub-Produto</option>
                                    <option value="MP">MP - Matéria Prima</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="required">Unidade</label>
                                <select id="productUnit" required>
                                    <option value="PC">PC - Peça</option>
                                    <option value="KG">KG - Quilograma</option>
                                    <option value="MT">MT - Metro</option>
                                    <option value="MM">MM - Milímetro</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="footer-actions">
                        <button type="submit" class="btn btn-success">Cadastrar</button>
                        <button type="button" class="btn btn-secondary" onclick="closeProductModal()">Cancelar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs,
            query,
            where,
            doc,
            updateDoc,
            deleteDoc,
            getDoc,
            orderBy 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let produtosPaisFiltrados = [];
        let operacoes = [];
        let recursos = [];
        let estruturas = [];
        let grupos = [];
        let familias = [];
        let central = [];
        let usuarioAtual = null;
        let initialStructureState = null;
        let hasChanges = false;
        let isNavigatingAway = false;
        let selectedStructureId = null;

        window.onload = async function() {
            usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
            if (!usuarioAtual) {
                window.location.href = 'login.html';
                return;
            }
            document.getElementById('userStatus').textContent = usuarioAtual.nome;
            updateDateTime();

            await loadData();
            setupDragAndDrop();
            populateParentProductSelect();
            searchProducts();

            // Proteção contra fechamento da página com mudanças não salvas
            window.addEventListener('beforeunload', function(e) {
                if (hasChanges) {
                    const message = 'Você tem alterações não salvas. Tem certeza que deseja sair?';
                    e.preventDefault();
                    e.returnValue = message;
                    return message;
                }
            });
        };

        function updateDateTime() {
            const now = new Date();
            const dateTime = `${now.toLocaleDateString('pt-BR')} ${now.toLocaleTimeString('pt-BR')}`;
            document.getElementById('currentDateTime').textContent = dateTime;
        }



        async function loadData() {
            try {
                document.querySelector('.loading').classList.add('active');

                const [produtosSnap, operacoesSnap, recursosSnap, estruturasSnap, gruposSnap, familiasSnap, centralSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "operacoes")),
                    getDocs(collection(db, "recursos")),
                    getDocs(collection(db, "estruturas")),
                    getDocs(collection(db, "grupos")),
                    getDocs(collection(db, "familias")),
                    getDocs(collection(db, "central"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtosPaisFiltrados = produtos.filter(p => p.tipo === 'PA' || p.tipo === 'SP');
                operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                central = centralSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`Carregados ${produtos.length} produtos e ${estruturas.length} estruturas`);
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados do sistema.");
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        }

        function populateParentProductSelect() {
            const parentSelect = document.getElementById('parentProductSelect');
            parentSelect.innerHTML = '<option value="">Selecione o produto pai...</option>';

            const sortedProducts = [...produtosPaisFiltrados].sort((a, b) => 
                a.codigo.localeCompare(b.codigo)
            );

            sortedProducts.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `${produto.codigo} - ${produto.descricao}`;
                option.innerHTML += `<span class="type-badge type-${produto.tipo}">${produto.tipo}</span>`;
                parentSelect.appendChild(option);
            });
        }

        window.loadStructureForProduct = async function() {
            // Verificar se há mudanças não salvas antes de carregar nova estrutura
            if (!checkUnsavedChanges()) {
                // Se o usuário cancelou, restaurar seleção anterior
                const currentContainer = document.querySelector('.structure-container');
                if (currentContainer) {
                    const currentProductId = currentContainer.dataset.id;
                    document.getElementById('parentProductSelect').value = currentProductId;
                }
                return;
            }

            const productId = document.getElementById('parentProductSelect').value;
            const structureTree = document.getElementById('structureTree');
            const saveButton = document.querySelector('.btn-success');

            structureTree.innerHTML = '';
            saveButton.disabled = true;
            initialStructureState = null;
            hasChanges = false;
            updateUnsavedChangesIndicator();

            if (!productId) {
                return;
            }

            try {
                document.querySelector('.loading').classList.add('active');

                const produtoPai = produtos.find(p => p.id === productId);
                const existingStructure = estruturas.find(e => e.produtoPaiId === productId);

                if (produtoPai) {
                    const container = createStructureContainer(produtoPai, existingStructure);
                    if (existingStructure) {
                        populateStructureWithData(container, existingStructure);
                        initialStructureState = JSON.parse(JSON.stringify({
                            componentes: existingStructure.componentes || [],
                            operacoes: existingStructure.operacoes || []
                        }));
                    } else {
                        saveButton.disabled = false;
                    }
                    structureTree.appendChild(container);
                    setupDragAndDrop();
                    setupChangeListeners(container);
                }
            } catch (error) {
                console.error("Erro ao carregar estrutura:", error);
                alert("Erro ao carregar estrutura do produto.");
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        function createStructureContainer(produto, estrutura = null) {
            const container = document.createElement('div');
            container.className = 'structure-container';
            container.dataset.id = produto.id;

            const creationDate = estrutura?.dataCriacao ? new Date(estrutura.dataCriacao).toLocaleString('pt-BR') : 'Não criada';
            const lastUpdateDate = estrutura?.dataUltimaAlteracao ? new Date(estrutura.dataUltimaAlteracao).toLocaleString('pt-BR') : 'Não alterada';
            const revisionNumber = estrutura?.revisaoAtual ? `REV${String(estrutura.revisaoAtual).padStart(3, '0')}` : 'REV000';

            container.innerHTML = `
                <div class="structure-header">
                    <div class="structure-title">
                        ${produto.codigo} - ${produto.descricao}
                        <span class="type-badge type-${produto.tipo}">${produto.tipo}</span>
                        <span style="background-color: var(--primary-color); color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">
                            ${revisionNumber}
                        </span>
                    </div>
                    <div class="structure-info">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                            <div>
                                <i class="fas fa-calendar-plus"></i> <strong>Criado em:</strong><br>
                                <small>${creationDate}</small>
                            </div>
                            <div>
                                <i class="fas fa-calendar-edit"></i> <strong>Última alteração:</strong><br>
                                <small>${lastUpdateDate}</small>
                            </div>
                            ${estrutura?.usuarioUltimaAlteracao ? `
                                <div>
                                    <i class="fas fa-user-edit"></i> <strong>Alterado por:</strong><br>
                                    <small><strong>${estrutura.usuarioUltimaAlteracao.nome}</strong></small><br>
                                    <small style="color: var(--text-secondary);">${estrutura.usuarioUltimaAlteracao.email}</small>
                                    ${estrutura.usuarioUltimaAlteracao.ip && estrutura.usuarioUltimaAlteracao.ip !== 'N/A' ? `<br><small style="color: var(--text-secondary);"><i class="fas fa-globe"></i> ${estrutura.usuarioUltimaAlteracao.ip}</small>` : ''}
                                </div>
                            ` : ''}
                            ${estrutura?.historicoUsuarios && estrutura.historicoUsuarios.length > 0 ? `
                                <div>
                                    <i class="fas fa-users"></i> <strong>Total de alterações:</strong><br>
                                    <small>${estrutura.historicoUsuarios.length} por ${[...new Set(estrutura.historicoUsuarios.map(h => h.id))].length} usuário(s)</small>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <div class="structure-content">
                    <div class="summary-section">
                        <div class="section-title" style="margin-bottom: 15px;">
                            <i class="fas fa-chart-bar"></i> Resumo da Estrutura
                        </div>
                        <div class="summary-grid" id="structureSummary">
                            <div class="summary-card">
                                <div class="summary-number" id="componentsCount">0</div>
                                <div class="summary-label">Componentes</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="operationsCount">0</div>
                                <div class="summary-label">Operações</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="mpCount">0</div>
                                <div class="summary-label">Matérias-Primas</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="spCount">0</div>
                                <div class="summary-label">Sub-Produtos</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-cubes"></i> Componentes da Estrutura
                            </div>

                        </div>
                        <div class="components-list" id="componentsList"></div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-cogs"></i> Processo de Fabricação
                            </div>
                             <button class="btn btn-primary btn-sm" onclick="addOperation(this)">
                                <i class="fas fa-plus"></i> Adicionar Operação
                            </button>
                        </div>
                        <div class="operations-list" id="operationsList"></div>
                    </div>
                </div>
            `;

            return container;
        }

        function populateStructureWithData(container, estrutura) {
            const componentsList = container.querySelector('#componentsList');
            const operationsList = container.querySelector('#operationsList');

            // Adicionar componentes
            if (estrutura.componentes) {
                estrutura.componentes.forEach((comp, index) => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        const component = createComponentElement(produto, comp.quantidade, index + 1, 0);
                        componentsList.appendChild(component);
                    }
                });
            }

            // Adicionar operações
            if (estrutura.operacoes) {
                estrutura.operacoes.forEach(op => {
                    const operation = createOperationElement(op);
                    operationsList.appendChild(operation);
                });
            }

            updateSummary(container);
        }

        function createComponentElement(produto, quantidade = 1, sequence = 1, level = 0) {
            const component = document.createElement('div');
            component.className = `component level-${level}`;
            component.dataset.id = produto.id;
            component.dataset.level = level;

            const hasSubStructure = estruturas.some(e => e.produtoPaiId === produto.id);
            const canHaveSubStructure = produto.tipo === 'SP';

            // Adicionar classe especial se pode ter sub-estrutura
            if (canHaveSubStructure) {
                component.classList.add('has-substructure');
            }

            const expandIconHtml = canHaveSubStructure ? 
                `<i class="fas fa-chevron-right expand-icon" style="margin-right: 8px; cursor: pointer;" onclick="toggleSubStructure(this.closest('.component').querySelector('.btn-primary'))"></i>` : 
                `<span style="width: 16px; margin-right: 8px;"></span>`;

            component.innerHTML = `
                <input type="number" class="sequence-input" value="${sequence}" readonly>
                <span style="flex: 1; display: flex; align-items: center;">
                    ${expandIconHtml}
                    <strong>${produto.codigo}</strong> - ${produto.descricao}
                    <span class="type-badge type-${produto.tipo}">${produto.tipo}</span>
                    ${hasSubStructure ? '<i class="fas fa-sitemap" title="Possui estrutura" style="margin-left: 8px; color: var(--primary-color);"></i>' : ''}
                </span>
                <input type="number" class="quantity-input" value="${quantidade}" min="0.001" step="0.001">
                <span>${produto.unidade}</span>
                <div class="action-buttons">
                    ${canHaveSubStructure ? `
                        <button class="btn btn-primary btn-sm" onclick="toggleSubStructure(this)" title="Gerenciar sub-estrutura">
                            <i class="fas fa-project-diagram"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-danger btn-sm" onclick="removeComponent(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            return component;
        }

        function createOperationElement(operationData) {
            const operation = document.createElement('div');
            operation.className = 'operation';

            const sortedOperacoes = [...operacoes].sort((a, b) => a.numero.localeCompare(b.numero));
            const sortedRecursos = [...recursos].sort((a, b) => a.codigo.localeCompare(b.codigo));

            operation.innerHTML = `
                <input type="number" class="sequence-input" value="${operationData.sequencia || 1}">
                <select class="operation-select">
                    <option value="">Selecione...</option>
                    ${sortedOperacoes.map(op => `
                        <option value="${op.id}" ${op.id === operationData.operacaoId ? 'selected' : ''}>
                            ${op.numero} - ${op.operacao}
                        </option>
                    `).join('')}
                </select>
                <select class="resource-select">
                    <option value="">Selecione...</option>
                    ${sortedRecursos.map(rec => `
                        <option value="${rec.id}" ${rec.id === operationData.recursoId ? 'selected' : ''}>
                            ${rec.codigo} - ${rec.maquina}
                        </option>
                    `).join('')}
                </select>
                <input type="number" class="time-input" value="${operationData.tempo || 1}" min="0.1" step="0.1">
                <input type="text" class="description-input" value="${operationData.descricao || ''}" placeholder="Descrição">
                <div class="action-buttons">
                    <button class="btn btn-secondary btn-sm" onclick="moveOperation(this, -1)">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="moveOperation(this, 1)">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="removeOperation(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            return operation;
        }

        function updateSummary(container) {
            const components = container.querySelectorAll('.component');
            const operations = container.querySelectorAll('.operation');

            let mpCount = 0;
            let spCount = 0;

            components.forEach(comp => {
                const productId = comp.dataset.id;
                const produto = produtos.find(p => p.id === productId);
                if (produto) {
                    if (produto.tipo === 'MP') mpCount++;
                    if (produto.tipo === 'SP') spCount++;
                }
            });

            container.querySelector('#componentsCount').textContent = components.length;
            container.querySelector('#operationsCount').textContent = operations.length;
            container.querySelector('#mpCount').textContent = mpCount;
            container.querySelector('#spCount').textContent = spCount;
        }

        function setupDragAndDrop() {
            document.addEventListener('dragstart', handleDragStart);
            document.addEventListener('dragend', handleDragEnd);
            document.addEventListener('dragover', handleDragOver);
            document.addEventListener('drop', handleDrop);

            function handleDragStart(e) {
                if (e.target.classList.contains('product-item')) {
                    e.target.classList.add('dragging');
                    e.dataTransfer.setData('text/plain', e.target.dataset.id);
                }
            }

            function handleDragEnd(e) {
                if (e.target.classList.contains('product-item')) {
                    e.target.classList.remove('dragging');
                }
            }

            function handleDragOver(e) {
                if (e.target.closest('.structure-container')) {
                    e.preventDefault();
                }
            }

            function handleDrop(e) {
                if (e.target.closest('.structure-container')) {
                    e.preventDefault();
                    const container = e.target.closest('.structure-container');
                    const productId = e.dataTransfer.getData('text/plain');
                    const produto = produtos.find(p => p.id === productId);
                    if (produto) {
                        addComponentToStructure(container, produto);
                    }
                }
            }
        }

        function addComponentToStructure(container, produto) {
            const componentsList = container.querySelector('#componentsList');
            const existingComponent = componentsList.querySelector(`.component[data-id="${produto.id}"]`);

            if (existingComponent) {
                return; // Evita duplicatas
            }

            const sequence = componentsList.querySelectorAll('.component[data-level="0"]').length + 1;
            const component = createComponentElement(produto, 1, sequence, 0);
            componentsList.appendChild(component);

            updateSummary(container);
            markAsChanged();
        }

        function setupChangeListeners(container) {
            const saveButton = document.querySelector('.btn-success');

            function markAsChanged() {
                hasChanges = true;
                saveButton.disabled = false;
                updateUnsavedChangesIndicator();
            }

            function markAsSaved() {
                hasChanges = false;
                saveButton.disabled = true;
                updateUnsavedChangesIndicator();
            }

            window.markAsChanged = markAsChanged;
            window.markAsSaved = markAsSaved;

            container.addEventListener('input', markAsChanged);
            container.addEventListener('change', markAsChanged);
        }

        function updateUnsavedChangesIndicator() {
            const container = document.querySelector('.structure-container');
            if (!container) return;

            let indicator = container.querySelector('.unsaved-indicator');

            if (hasChanges) {
                if (!indicator) {
                    indicator = document.createElement('div');
                    indicator.className = 'unsaved-indicator';
                    indicator.innerHTML = `
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Alterações não salvas</span>
                    `;
                    container.querySelector('.structure-header').appendChild(indicator);
                }
            } else {
                if (indicator) {
                    indicator.remove();
                }
            }
        }

        function checkUnsavedChanges(callback) {
            if (hasChanges && !isNavigatingAway) {
                showUnsavedChangesDialog(callback);
                return false;
            } else {
                if (callback) callback();
                return true;
            }
        }

        function showUnsavedChangesDialog(callback) {
            const dialog = document.createElement('div');
            dialog.className = 'confirm-dialog';
            dialog.innerHTML = `
                <div class="confirm-content">
                    <h3>
                        <i class="fas fa-exclamation-triangle"></i>
                        Alterações não salvas
                    </h3>
                    <p>
                        Você tem alterações não salvas nesta estrutura.<br>
                        O que deseja fazer?
                    </p>
                    <div class="confirm-buttons">
                        <button class="confirm-save" onclick="saveAndContinue(this, ${callback ? 'true' : 'false'})">
                            <i class="fas fa-save"></i> Salvar e Continuar
                        </button>
                        <button class="confirm-discard" onclick="discardAndContinue(this, ${callback ? 'true' : 'false'})">
                            <i class="fas fa-trash"></i> Descartar Alterações
                        </button>
                        <button class="confirm-cancel" onclick="cancelNavigation(this)">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                    </div>
                </div>
            `;

            // Armazenar callback no dialog para uso posterior
            dialog._callback = callback;

            document.body.appendChild(dialog);

            // Focar no botão salvar
            dialog.querySelector('.confirm-save').focus();
        }

        window.saveAndContinue = function(button, hasCallback) {
            const dialog = button.closest('.confirm-dialog');
            const callback = dialog._callback;

            // Salvar estrutura primeiro
            saveStructure().then(() => {
                dialog.remove();
                if (callback) {
                    isNavigatingAway = true;
                    callback();
                    setTimeout(() => { isNavigatingAway = false; }, 100);
                }
            }).catch(error => {
                alert('Erro ao salvar: ' + error.message);
            });
        };

        window.discardAndContinue = function(button, hasCallback) {
            const dialog = button.closest('.confirm-dialog');
            const callback = dialog._callback;

            hasChanges = false;
            isNavigatingAway = true;
            updateUnsavedChangesIndicator();

            dialog.remove();

            if (callback) {
                callback();
            }

            setTimeout(() => { isNavigatingAway = false; }, 100);
        };

        window.cancelNavigation = function(button) {
            const dialog = button.closest('.confirm-dialog');
            dialog.remove();
        };

        window.searchProducts = function() {
            const searchTerm = document.getElementById('searchInput').value.toUpperCase();
            const productList = document.getElementById('productList');
            productList.innerHTML = '';

            const filteredProducts = produtos.filter(p => 
                (p.codigo.toUpperCase().includes(searchTerm) || p.descricao.toUpperCase().includes(searchTerm)) &&
                !['PA'].includes(p.tipo)
            ).slice(0, 20);

            filteredProducts.forEach(produto => {
                const item = document.createElement('div');
                item.className = 'product-item';
                item.draggable = true;
                item.dataset.id = produto.id;
                item.innerHTML = `
                    ${produto.codigo} - ${produto.descricao}
                    <span class="type-badge type-${produto.tipo}">${produto.tipo}</span>
                `;
                productList.appendChild(item);
            });
        };

        window.addOperation = function(button) {
            const container = button.closest('.structure-container');
            const operationsList = container.querySelector('#operationsList');

            const operationData = {
                sequencia: operationsList.querySelectorAll('.operation').length + 1,
                operacaoId: '',
                recursoId: '',
                tempo: 1,
                descricao: ''
            };

            const operation = createOperationElement(operationData);
            operationsList.appendChild(operation);

            updateSummary(container);
            markAsChanged();
        };

        window.removeComponent = function(button) {
            const container = button.closest('.structure-container');
            button.closest('.component').remove();
            updateSummary(container);
            markAsChanged();
        };

        window.removeOperation = function(button) {
            const container = button.closest('.structure-container');
            button.closest('.operation').remove();
            updateSummary(container);
            markAsChanged();
        };

        window.moveOperation = function(button, direction) {
            const operation = button.closest('.operation');
            const operationsList = operation.parentElement;
            const operations = Array.from(operationsList.querySelectorAll('.operation'));
            const index = operations.indexOf(operation);
            const newIndex = index + direction;

            if (newIndex >= 0 && newIndex < operations.length) {
                if (direction === 1) {
                    operationsList.insertBefore(operations[newIndex], operation);
                } else {
                    operationsList.insertBefore(operation, operations[newIndex]);
                }
                markAsChanged();
            }
        };

        window.saveStructure = async function() {
            if (!hasChanges) {
                alert('Não há alterações para salvar.');
                return;
            }

            const container = document.querySelector('.structure-container');
            if (!container) {
                alert('Selecione um produto pai para criar uma estrutura.');
                return;
            }

            try {
                document.querySelector('.loading').classList.add('active');

                const productId = container.dataset.id;
                const components = [];
                const operations = [];

                // Coletar componentes
                container.querySelectorAll('.component').forEach(comp => {
                    const componentId = comp.dataset.id;
                    const quantidade = parseFloat(comp.querySelector('.quantity-input').value) || 1;
                    if (quantidade > 0) {
                        const produto = produtos.find(p => p.id === componentId);
                        components.push({
                            componentId,
                            quantidade,
                            unidade: produto.unidade
                        });
                    }
                });

                // Coletar operações
                container.querySelectorAll('.operation').forEach((op, index) => {
                    const sequencia = parseInt(op.querySelector('.sequence-input').value) || index + 1;
                    const operacaoId = op.querySelector('.operation-select').value;
                    const recursoId = op.querySelector('.resource-select').value;
                    const tempo = parseFloat(op.querySelector('.time-input').value) || 1;
                    const descricao = op.querySelector('.description-input').value;

                    if (operacaoId && recursoId && tempo > 0) {
                        operations.push({
                            sequencia,
                            operacaoId,
                            recursoId,
                            tempo,
                            descricao: descricao || ''
                        });
                    }
                });

                const now = new Date().toISOString();
                const existingStructure = estruturas.find(e => e.produtoPaiId === productId);

                const structureData = {
                    produtoPaiId: productId,
                    componentes: components,
                    operacoes: operations,
                    dataCriacao: existingStructure?.dataCriacao || now,
                    dataUltimaAlteracao: now,
                    revisaoAtual: existingStructure ? (existingStructure.revisaoAtual || 0) + 1 : 0,
                    usuarioUltimaAlteracao: {
                        id: usuarioAtual.id,
                        nome: usuarioAtual.nome,
                        email: usuarioAtual.email
                    },
                    // Log oculto de alterações para auditoria
                    logAlteracoes: [
                        ...(existingStructure?.logAlteracoes || []),
                        {
                            dataHora: now,
                            revisao: existingStructure ? (existingStructure.revisaoAtual || 0) + 1 : 0,
                            usuario: {
                                id: usuarioAtual.id,
                                nome: usuarioAtual.nome,
                                email: usuarioAtual.email
                            }
                        }
                    ]
                };

                if (existingStructure) {
                    await updateDoc(doc(db, "estruturas", existingStructure.id), structureData);
                    const index = estruturas.findIndex(e => e.id === existingStructure.id);
                    estruturas[index] = { id: existingStructure.id, ...structureData };
                } else {
                    const docRef = await addDoc(collection(db, "estruturas"), structureData);
                    estruturas.push({ id: docRef.id, ...structureData });
                }

                alert("Estrutura salva com sucesso!");
                markAsSaved();
                await loadStructureForProduct();

            } catch (error) {
                console.error("Erro ao salvar estrutura:", error);
                alert("Erro ao salvar estrutura: " + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        // Funções dos modais
        window.openProductModal = function() {
            document.getElementById('productModal').style.display = 'block';
        };

        window.closeProductModal = function() {
            document.getElementById('productModal').style.display = 'none';
        };

        window.handleProductSubmit = async function(event) {
            event.preventDefault();

            const codigo = document.getElementById('productCode').value.trim();
            const descricao = document.getElementById('productDescription').value.trim();
            const tipo = document.getElementById('productType').value;
            const unidade = document.getElementById('productUnit').value;

            if (!codigo || !descricao || !tipo || !unidade) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }

            if (produtos.some(p => p.codigo === codigo)) {
                alert('Já existe um produto com este código.');
                return;
            }

            try {
                const newProduct = {
                    codigo,
                    descricao,
                    tipo,
                    unidade,
                    dataCadastro: new Date().toISOString()
                };

                const docRef = await addDoc(collection(db, "produtos"), newProduct);
                produtos.push({ id: docRef.id, ...newProduct });

                await updateProductsList();
                closeProductModal();
                alert("Produto cadastrado com sucesso!");

            } catch (error) {
                console.error("Erro ao cadastrar produto:", error);
                alert("Erro ao cadastrar produto: " + error.message);
            }
        };

        window.updateProductsList = async function() {
            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtosPaisFiltrados = produtos.filter(p => p.tipo === 'PA' || p.tipo === 'SP');

                populateParentProductSelect();
                searchProducts();

            } catch (error) {
                console.error("Erro ao atualizar lista de produtos:", error);
                alert("Erro ao atualizar lista de produtos.");
            }
        };

        window.openHistoryModal = function() {
            document.getElementById('historyModal').style.display = 'block';
            loadStructureHistory();
        };

        window.closeHistoryModal = function() {
            document.getElementById('historyModal').style.display = 'none';
        };

        function loadStructureHistory() {
            const tbody = document.getElementById('historyTableBody');
            tbody.innerHTML = '';

            if (estruturas.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">Nenhuma estrutura encontrada.</td></tr>';
                return;
            }

            const sortedEstruturas = [...estruturas].sort((a, b) => {
                const dateA = a.dataUltimaAlteracao ? new Date(a.dataUltimaAlteracao) : new Date(0);
                const dateB = b.dataUltimaAlteracao ? new Date(b.dataUltimaAlteracao) : new Date(0);
                return dateB - dateA;
            });

            sortedEstruturas.forEach(estrutura => {
                const produtoPai = produtos.find(p => p.id === estrutura.produtoPaiId);
                if (!produtoPai) return;

                const dataCriacao = estrutura.dataCriacao 
                    ? new Date(estrutura.dataCriacao).toLocaleString('pt-BR')
                    : 'Não disponível';
                const dataUltimaAlteracao = estrutura.dataUltimaAlteracao 
                    ? new Date(estrutura.dataUltimaAlteracao).toLocaleString('pt-BR')
                    : 'Não alterada';

                const row = document.createElement('tr');
                row.dataset.structureId = estrutura.id;
                row.style.cursor = 'pointer';
                row.innerHTML = `
                    <td>${produtoPai.codigo}</td>
                    <td>${produtoPai.descricao}</td>
                    <td>${dataCriacao}</td>
                    <td>${dataUltimaAlteracao}</td>
                `;

                row.addEventListener('click', () => {
                    const previouslySelected = tbody.querySelector('.selected');
                    if (previouslySelected) previouslySelected.classList.remove('selected');
                    row.classList.add('selected');
                    selectedStructureId = estrutura.id;
                });

                tbody.appendChild(row);
            });
        }

        window.loadSelectedStructure = function() {
            if (!selectedStructureId) {
                alert("Por favor, selecione uma estrutura no histórico.");
                return;
            }

            const estrutura = estruturas.find(e => e.id === selectedStructureId);
            if (!estrutura) {
                alert("Estrutura selecionada não encontrada.");
                return;
            }

            const produtoPai = produtos.find(p => p.id === estrutura.produtoPaiId);
            if (!produtoPai) {
                alert("Produto pai associado à estrutura não encontrado.");
                return;
            }

            document.getElementById('parentProductSelect').value = produtoPai.id;
            loadStructureForProduct();
            closeHistoryModal();
        };

        window.exportToExcel = function() {
            const container = document.querySelector('.structure-container');
            if (!container) {
                alert('Carregue uma estrutura primeiro.');
                return;
            }

            try {
                const workbook = XLSX.utils.book_new();
                const productId = container.dataset.id;
                const produto = produtos.find(p => p.id === productId);

                // Aba 1: Componentes
                const componentsData = [];
                container.querySelectorAll('.component').forEach((comp, index) => {
                    const componentId = comp.dataset.id;
                    const componentProduct = produtos.find(p => p.id === componentId);
                    const quantity = comp.querySelector('.quantity-input').value;

                    if (componentProduct) {
                        componentsData.push({
                            'Sequência': index + 1,
                            'Código': componentProduct.codigo,
                            'Descrição': componentProduct.descricao,
                            'Tipo': componentProduct.tipo,
                            'Quantidade': parseFloat(quantity),
                            'Unidade': componentProduct.unidade
                        });
                    }
                });

                // Aba 2: Operações
                const operationsData = [];
                container.querySelectorAll('.operation').forEach((op, index) => {
                    const sequencia = op.querySelector('.sequence-input').value;
                    const operacaoId = op.querySelector('.operation-select').value;
                    const recursoId = op.querySelector('.resource-select').value;
                    const tempo = op.querySelector('.time-input').value;
                    const descricao = op.querySelector('.description-input').value;

                    const operacao = operacoes.find(o => o.id === operacaoId);
                    const recurso = recursos.find(r => r.id === recursoId);

                    operationsData.push({
                        'Sequência': sequencia,
                        'Operação': operacao ? `${operacao.numero} - ${operacao.operacao}` : '',
                        'Recurso': recurso ? `${recurso.codigo} - ${recurso.maquina}` : '',
                        'Tempo (min)': parseFloat(tempo) || 0,
                        'Descrição': descricao
                    });
                });

                const wsComponents = XLSX.utils.json_to_sheet(componentsData);
                const wsOperations = XLSX.utils.json_to_sheet(operationsData);

                XLSX.utils.book_append_sheet(workbook, wsComponents, "Componentes");
                XLSX.utils.book_append_sheet(workbook, wsOperations, "Operações");

                const fileName = `Estrutura_${produto.codigo}_${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(workbook, fileName);

                alert('Estrutura exportada com sucesso!');
            } catch (error) {
                console.error("Erro ao exportar:", error);
                alert("Erro ao exportar estrutura.");
            }
        };

        // Função para abrir modal "Onde é Usado"
        window.openUsageModal = function() {
            const container = document.querySelector('.structure-container');
            if (!container) {
                alert("Nenhuma estrutura está carregada.");
                return;
            }

            const productId = container.dataset.id;
            const produto = produtos.find(p => p.id === productId);

            if (!produto) {
                alert("Produto não encontrado.");
                return;
            }

            // Buscar onde este produto é usado
            const usageData = findProductUsage(productId);

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 900px; max-height: 80vh; overflow-y: auto;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-share-alt"></i> Onde é Usado: ${produto.codigo} - ${produto.descricao}
                    </div>

                    <div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
                        <h4 style="margin: 0 0 10px 0; color: var(--primary-color);">
                            <i class="fas fa-info-circle"></i> Informações do Produto
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>Código:</strong> ${produto.codigo}</div>
                            <div><strong>Tipo:</strong> <span class="type-badge type-${produto.tipo}">${produto.tipo}</span></div>
                            <div><strong>Unidade:</strong> ${produto.unidade}</div>
                            <div><strong>Status:</strong> ${produto.ativo ? 'Ativo' : 'Inativo'}</div>
                        </div>
                    </div>

                    <div id="usageResults">
                        ${renderUsageResults(usageData, produto)}
                    </div>

                    <div class="footer-actions" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="exportUsageReport('${productId}')">
                            <i class="fas fa-download"></i> Exportar Relatório
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Função para buscar onde um produto é usado
        function findProductUsage(productId) {
            const usage = {
                asComponent: [], // Onde é usado como componente
                asParent: [],    // Estruturas que este produto possui
                summary: {
                    totalUsages: 0,
                    totalParents: 0,
                    totalQuantity: 0
                }
            };

            // Buscar onde é usado como componente
            estruturas.forEach(estrutura => {
                if (estrutura.componentes) {
                    estrutura.componentes.forEach(comp => {
                        if (comp.componentId === productId) {
                            const parentProduct = produtos.find(p => p.id === estrutura.produtoPaiId);
                            if (parentProduct) {
                                usage.asComponent.push({
                                    parentProduct: parentProduct,
                                    quantity: comp.quantidade,
                                    unit: comp.unidade,
                                    estrutura: estrutura
                                });
                                usage.summary.totalUsages++;
                                usage.summary.totalQuantity += comp.quantidade;
                            }
                        }
                    });
                }
            });

            // Buscar estruturas que este produto possui (como pai)
            const ownStructures = estruturas.filter(e => e.produtoPaiId === productId);
            ownStructures.forEach(estrutura => {
                usage.asParent.push({
                    estrutura: estrutura,
                    componentsCount: estrutura.componentes ? estrutura.componentes.length : 0,
                    operationsCount: estrutura.operacoes ? estrutura.operacoes.length : 0
                });
                usage.summary.totalParents++;
            });

            return usage;
        }

        // Função para renderizar os resultados de uso
        function renderUsageResults(usageData, produto) {
            let html = '';

            // Seção: Usado como componente
            html += `
                <div class="usage-section" style="margin-bottom: 25px;">
                    <h4 style="color: var(--primary-color); margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid var(--border-color);">
                        <i class="fas fa-puzzle-piece"></i> Usado como Componente (${usageData.asComponent.length})
                    </h4>
            `;

            if (usageData.asComponent.length === 0) {
                html += `
                    <div style="padding: 20px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <p>Este produto não é usado como componente em nenhuma estrutura.</p>
                    </div>
                `;
            } else {
                html += `
                    <div style="background-color: white; border: 1px solid var(--border-color); border-radius: 6px; overflow: hidden;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead style="background-color: #f8f9fa;">
                                <tr>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Produto Pai</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Tipo</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Quantidade</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Unidade</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Última Atualização</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                usageData.asComponent.forEach((usage, index) => {
                    const lastUpdate = usage.estrutura.dataUltimaAlteracao ?
                        new Date(usage.estrutura.dataUltimaAlteracao).toLocaleDateString('pt-BR') : 'N/A';

                    html += `
                        <tr style="border-bottom: 1px solid #f0f0f0;">
                            <td style="padding: 12px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <strong>${usage.parentProduct.codigo}</strong>
                                    <span style="color: var(--text-secondary);">- ${usage.parentProduct.descricao}</span>
                                </div>
                            </td>
                            <td style="padding: 12px;">
                                <span class="type-badge type-${usage.parentProduct.tipo}">${usage.parentProduct.tipo}</span>
                            </td>
                            <td style="padding: 12px; text-align: center; font-weight: 600;">
                                ${parseFloat(usage.quantity).toFixed(3).replace('.', ',')}
                            </td>
                            <td style="padding: 12px; text-align: center;">
                                ${usage.unit}
                            </td>
                            <td style="padding: 12px; text-align: center; color: var(--text-secondary);">
                                ${lastUpdate}
                            </td>
                            <td style="padding: 12px; text-align: center;">
                                <button class="btn btn-primary btn-sm" onclick="viewParentStructure('${usage.parentProduct.id}')" title="Ver Estrutura">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            html += '</div>';

            // Seção: Estruturas próprias
            html += `
                <div class="usage-section" style="margin-bottom: 25px;">
                    <h4 style="color: var(--primary-color); margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid var(--border-color);">
                        <i class="fas fa-sitemap"></i> Estruturas Próprias (${usageData.asParent.length})
                    </h4>
            `;

            if (usageData.asParent.length === 0) {
                html += `
                    <div style="padding: 20px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <p>Este produto não possui estrutura própria cadastrada.</p>
                    </div>
                `;
            } else {
                usageData.asParent.forEach(parentData => {
                    const lastUpdate = parentData.estrutura.dataUltimaAlteracao ?
                        new Date(parentData.estrutura.dataUltimaAlteracao).toLocaleDateString('pt-BR') : 'N/A';

                    html += `
                        <div style="background-color: white; border: 1px solid var(--border-color); border-radius: 6px; padding: 15px; margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <h5 style="margin: 0; color: var(--primary-color);">
                                    <i class="fas fa-sitemap"></i> Estrutura do Produto
                                </h5>
                                <button class="btn btn-primary btn-sm" onclick="loadStructureInModal('${produto.id}')" title="Carregar Estrutura">
                                    <i class="fas fa-external-link-alt"></i> Carregar
                                </button>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                                <div><strong>Componentes:</strong> ${parentData.componentsCount}</div>
                                <div><strong>Operações:</strong> ${parentData.operationsCount}</div>
                                <div><strong>Revisão:</strong> ${parentData.estrutura.revisaoAtual || 0}</div>
                                <div><strong>Atualização:</strong> ${lastUpdate}</div>
                            </div>
                        </div>
                    `;
                });
            }

            html += '</div>';

            // Resumo
            html += `
                <div class="usage-summary" style="background-color: #e3f2fd; padding: 15px; border-radius: 6px; border-left: 4px solid var(--primary-color);">
                    <h4 style="margin: 0 0 10px 0; color: var(--primary-color);">
                        <i class="fas fa-chart-bar"></i> Resumo de Utilização
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--primary-color);">${usageData.summary.totalUsages}</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">Usos como Componente</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--success-color);">${usageData.summary.totalParents}</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">Estruturas Próprias</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--warning-color);">${usageData.summary.totalQuantity.toFixed(3).replace('.', ',')}</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">Quantidade Total Usada</div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // Função para visualizar estrutura pai
        window.viewParentStructure = function(parentProductId) {
            // Fechar modal atual
            document.querySelector('.modal')?.remove();

            // Carregar a estrutura do produto pai
            const parentSelect = document.getElementById('parentProductSelect');
            parentSelect.value = parentProductId;
            loadStructure();
        };

        // Função para carregar estrutura em modal
        window.loadStructureInModal = function(productId) {
            // Fechar modal atual
            document.querySelector('.modal')?.remove();

            // Carregar a estrutura do produto
            const parentSelect = document.getElementById('parentProductSelect');
            parentSelect.value = productId;
            loadStructure();
        };

        // Função para exportar relatório de uso
        window.exportUsageReport = function(productId) {
            const produto = produtos.find(p => p.id === productId);
            const usageData = findProductUsage(productId);

            if (!produto) {
                alert('Produto não encontrado.');
                return;
            }

            try {
                // Criar dados para exportação
                const reportData = {
                    produto: {
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        tipo: produto.tipo,
                        unidade: produto.unidade,
                        ativo: produto.ativo
                    },
                    resumo: usageData.summary,
                    usoComoComponente: usageData.asComponent.map(usage => ({
                        produtoPai: {
                            codigo: usage.parentProduct.codigo,
                            descricao: usage.parentProduct.descricao,
                            tipo: usage.parentProduct.tipo
                        },
                        quantidade: usage.quantity,
                        unidade: usage.unit,
                        ultimaAtualizacao: usage.estrutura.dataUltimaAlteracao
                    })),
                    estruturasProprias: usageData.asParent.map(parent => ({
                        componentes: parent.componentsCount,
                        operacoes: parent.operationsCount,
                        revisao: parent.estrutura.revisaoAtual || 0,
                        ultimaAtualizacao: parent.estrutura.dataUltimaAlteracao
                    })),
                    dataRelatorio: new Date().toISOString()
                };

                // Criar e baixar arquivo JSON
                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `relatorio-uso-${produto.codigo}-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                alert('Relatório exportado com sucesso!');

            } catch (error) {
                console.error('Erro ao exportar relatório:', error);
                alert('Erro ao exportar relatório: ' + error.message);
            }
        };

        window.openRevisionHistoryModal = function() {
            const container = document.querySelector('.structure-container');
            if (!container) {
                alert("Nenhuma estrutura está carregada.");
                return;
            }

            const productId = container.dataset.id;
            const produto = produtos.find(p => p.id === productId);

            if (!produto) {
                alert("Produto não encontrado.");
                return;
            }

            // Buscar todas as revisões desta estrutura
            const revisions = getStructureRevisions(productId);

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 1000px; max-height: 85vh; overflow-y: auto;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-history"></i> Histórico de Revisões: ${produto.codigo} - ${produto.descricao}
                    </div>

                    <div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
                        <h4 style="margin: 0 0 10px 0; color: var(--primary-color);">
                            <i class="fas fa-info-circle"></i> Informações do Produto
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>Código:</strong> ${produto.codigo}</div>
                            <div><strong>Tipo:</strong> <span class="type-badge type-${produto.tipo}">${produto.tipo}</span></div>
                            <div><strong>Revisão Atual:</strong> <span class="revision-badge">REV${String(revisions.current?.revisaoAtual || 0).padStart(3, '0')}</span></div>
                            <div><strong>Total de Revisões:</strong> ${revisions.all.length}</div>
                        </div>
                    </div>

                    <div class="revision-controls" style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <button class="btn btn-primary" onclick="createNewRevision('${productId}')" ${!revisions.current ? 'disabled' : ''}>
                            <i class="fas fa-plus"></i> Nova Revisão
                        </button>
                        <button class="btn btn-info" onclick="compareRevisions('${productId}')" ${revisions.all.length < 2 ? 'disabled' : ''}>
                            <i class="fas fa-balance-scale"></i> Comparar Revisões
                        </button>
                        <button class="btn btn-warning" onclick="exportRevisionHistory('${productId}')">
                            <i class="fas fa-download"></i> Exportar Histórico
                        </button>
                    </div>

                    <div id="revisionsContainer">
                        ${renderRevisionsHistory(revisions)}
                    </div>

                    <div class="footer-actions" style="margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        window.toggleSubStructure = function(button) {
            const component = button.closest('.component');
            const productId = component.dataset.id;
            const produto = produtos.find(p => p.id === productId);
            const currentLevel = parseInt(component.dataset.level) || 0;

            if (!produto) return;

            const componentsList = component.closest('#componentsList, .sub-components-list');
            const expandIcon = component.querySelector('.expand-icon');

            // Encontrar componentes filhos existentes
            const nextLevel = currentLevel + 1;
            let nextSibling = component.nextElementSibling;
            const childComponents = [];

            while (nextSibling && nextSibling.classList.contains('component')) {
                const siblingLevel = parseInt(nextSibling.dataset.level) || 0;
                if (siblingLevel <= currentLevel) break;
                if (siblingLevel === nextLevel) {
                    childComponents.push(nextSibling);
                }
                nextSibling = nextSibling.nextElementSibling;
            }

            if (childComponents.length > 0) {
                // Se já existem componentes filhos, apenas mostra/esconde
                const isExpanded = expandIcon.classList.contains('expanded');

                childComponents.forEach(child => {
                    if (isExpanded) {
                        child.style.display = 'none';
                        hideAllChildren(child);
                    } else {
                        child.style.display = 'flex';
                    }
                });

                if (isExpanded) {
                    expandIcon.classList.remove('expanded');
                    expandIcon.className = 'fas fa-chevron-right expand-icon';
                    button.querySelector('i').className = 'fas fa-project-diagram';
                } else {
                    expandIcon.classList.add('expanded');
                    expandIcon.className = 'fas fa-chevron-down expand-icon expanded';
                    button.querySelector('i').className = 'fas fa-eye-slash';
                }
            } else {
                // Carregar sub-estrutura do banco de dados
                const existingStructure = estruturas.find(e => e.produtoPaiId === productId);

                if (existingStructure && existingStructure.componentes) {
                    existingStructure.componentes.forEach((comp, index) => {
                        const subProduto = produtos.find(p => p.id === comp.componentId);
                        if (subProduto) {
                            const subComponent = createComponentElement(
                                subProduto, 
                                comp.quantidade, 
                                index + 1, 
                                nextLevel
                            );

                            // Inserir após o componente pai
                            component.insertAdjacentElement('afterend', subComponent);
                        }
                    });

                    expandIcon.classList.add('expanded');
                    expandIcon.className = 'fas fa-chevron-down expand-icon expanded';
                    button.querySelector('i').className = 'fas fa-eye-slash';

                    // Adicionar classe para indicar que tem filhos
                    component.classList.add('has-children');
                }
            }

            updateSummary(component.closest('.structure-container'));
            markAsChanged();
        };

        // Função auxiliar para esconder todos os filhos recursivamente
        function hideAllChildren(parentComponent) {
            const parentLevel = parseInt(parentComponent.dataset.level) || 0;
            let nextSibling = parentComponent.nextElementSibling;

            while (nextSibling && nextSibling.classList.contains('component')) {
                const siblingLevel = parseInt(nextSibling.dataset.level) || 0;
                if (siblingLevel <= parentLevel) break;

                nextSibling.style.display = 'none';
                nextSibling = nextSibling.nextElementSibling;
            }
        }

        window.openSubComponentModal = function(parentProductId) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">Adicionar Componente à Sub-estrutura</div>

                    <div class="form-group">
                        <label>Selecione o componente:</label>
                        <select id="subComponentSelect" style="width: 100%; margin-bottom: 10px;">
                            <option value="">Selecione um componente...</option>
                            ${produtos
                                .filter(p => p.tipo === 'MP' || p.tipo === 'SP')
                                .filter(p => p.id !== parentProductId)
                                .map(p => `
                                    <option value="${p.id}" data-unidade="${p.unidade}">
                                        ${p.codigo} - ${p.descricao} (${p.tipo})
                                    </option>
                                `).join('')}
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Quantidade:</label>
                        <input type="number" id="subComponentQuantity" min="0.001" step="0.001" value="1" style="width: 100%;">
                    </div>

                    <div class="footer-actions">
                        <button class="btn btn-success" onclick="addSubComponent('${parentProductId}')">Adicionar</button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancelar</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        window.addSubComponent = function(parentProductId) {
            const componentId = document.getElementById('subComponentSelect').value;
            const quantity = parseFloat(document.getElementById('subComponentQuantity').value) || 1;

            if (!componentId) {
                alert('Selecione um componente.');
                return;
            }

            const produto = produtos.find(p => p.id === componentId);
            const subComponentsList = document.getElementById(`subComponentsList_${parentProductId}`);

            // Verifica se já existe
            if (subComponentsList.querySelector(`[data-id="${componentId}"]`)) {
                alert('Este componente já foi adicionado.');
                return;
            }

            const sequence = subComponentsList.querySelectorAll('.sub-component').length + 1;
            const subComponent = document.createElement('div');
            subComponent.className = 'sub-component';
            subComponent.dataset.id = componentId;
            subComponent.style.display = 'flex';
            subComponent.style.alignItems = 'center';
            subComponent.style.gap = '5px';
            subComponent.style.padding = '5px';
            subComponent.style.marginBottom = '4px';
            subComponent.style.marginLeft = '10px';
            subComponent.style.backgroundColor = 'white';
            subComponent.style.border = '1px solid var(--border-color)';
            subComponent.style.borderLeft = '2px solid #FF9800';
            subComponent.style.borderRadius = '3px';
            subComponent.style.position = 'relative';
            subComponent.style.fontSize = '11px';

            // Adicionar indicador visual de hierarquia
            const hasSubSubStructure = estruturas.some(e => e.produtoPaiId === componentId);
            const canHaveSubSubStructure = produto.tipo === 'SP';

            const expandIconHtml = canHaveSubSubStructure ? 
                `<i class="fas fa-chevron-right expand-icon" style="margin-right: 8px; color: #FF9800; cursor: pointer; font-size: 12px;"></i>` : 
                `<span style="width: 16px; margin-right: 8px;">└─</span>`;

            subComponent.innerHTML = `
                <span style="width: 20px; text-align: center; color: var(--text-secondary); font-size: 10px;">${sequence}</span>
                <span style="flex: 1; display: flex; align-items: center;">
                    ${expandIconHtml}
                    <strong style="font-size: 10px;">${produto.codigo}</strong><span style="font-size: 9px;"> - ${produto.descricao}</span>
                    <span class="type-badge type-${produto.tipo}" style="font-size: 7px; padding: 1px 3px; margin-left: 3px;">${produto.tipo}</span>
                    ${hasSubSubStructure ? '<i class="fas fa-sitemap" title="Possui estrutura" style="margin-left: 4px; color: #FF9800; font-size: 8px;"></i>' : ''}
                </span>
                <input type="number" value="${quantity}" min="0.001" step="0.001" style="width: 60px; font-size: 10px; padding: 2px;">
                <span style="font-size: 9px;">${produto.unidade}</span>
                <button class="btn btn-danger btn-sm" onclick="this.closest('.sub-component').remove(); markAsChanged();" style="padding: 2px 4px;">
                    <i class="fas fa-times" style="font-size: 8px;"></i>
                </button>
            `;

            subComponentsList.appendChild(subComponent);
            document.querySelector('.modal').remove();
            markAsChanged();
        };

        window.addSubOperation = function(parentProductId) {
            const subOperationsList = document.getElementById(`subOperationsList_${parentProductId}`);
            const sequence = subOperationsList.querySelectorAll('.sub-operation').length + 1;

            const subOperation = document.createElement('div');
            subOperation.className = 'sub-operation';
            subOperation.style.display = 'flex';
            subOperation.style.alignItems = 'center';
            subOperation.style.gap = '5px';
            subOperation.style.padding = '5px';
            subOperation.style.marginBottom = '4px';
            subOperation.style.marginLeft = '10px';
            subOperation.style.backgroundColor = 'white';
            subOperation.style.border = '1px solid var(--border-color)';
            subOperation.style.borderLeft = '2px solid #4CAF50';
            subOperation.style.borderRadius = '3px';
            subOperation.style.position = 'relative';
            subOperation.style.fontSize = '11px';

            const sortedOperacoes = [...operacoes].sort((a, b) => a.numero.localeCompare(b.numero));
            const sortedRecursos = [...recursos].sort((a, b) => a.codigo.localeCompare(b.codigo));

            subOperation.innerHTML = `
                <input type="number" value="${sequence}" style="width: 30px; text-align: center; font-size: 10px; padding: 2px;">
                <span style="width: 12px; margin-right: 4px; color: #4CAF50; font-size: 10px;">⚙</span>
                <select style="width: 100px; font-size: 10px; padding: 2px;">
                    <option value="">Operação...</option>
                    ${sortedOperacoes.map(op => `
                        <option value="${op.id}">${op.numero} - ${op.operacao}</option>
                    `).join('')}
                </select>
                <select style="width: 100px; font-size: 10px; padding: 2px;">
                    <option value="">Recurso...</option>
                    ${sortedRecursos.map(rec => `
                        <option value="${rec.id}">${rec.codigo} - ${rec.maquina}</option>
                    `).join('')}
                </select>
                <input type="number" value="1" min="0.1" step="0.1" style="width: 40px; font-size: 10px; padding: 2px;" placeholder="Tempo">
                <input type="text" placeholder="Descrição" style="flex: 1; font-size: 10px; padding: 2px;">
                <button class="btn btn-danger btn-sm" onclick="this.closest('.sub-operation').remove(); markAsChanged();" style="padding: 2px 4px;">
                    <i class="fas fa-times" style="font-size: 8px;"></i>
                </button>
            `;

            subOperationsList.appendChild(subOperation);
            markAsChanged();
        };

        window.saveSubStructure = async function(parentProductId) {
            try {
                const subComponentsList = document.getElementById(`subComponentsList_${parentProductId}`);
                const subOperationsList = document.getElementById(`subOperationsList_${parentProductId}`);

                const components = [];
                const operations = [];

                // Coletar componentes
                subComponentsList.querySelectorAll('.sub-component').forEach(comp => {
                    const componentId = comp.dataset.id;
                    const quantidade = parseFloat(comp.querySelector('input[type="number"]').value) || 1;
                    const produto = produtos.find(p => p.id === componentId);

                    components.push({
                        componentId,
                        quantidade,
                        unidade: produto.unidade
                    });
                });

                // Coletar operações
                subOperationsList.querySelectorAll('.sub-operation').forEach((op, index) => {
                    const inputs = op.querySelectorAll('input');
                    const selects = op.querySelectorAll('select');

                    const sequencia = parseInt(inputs[0].value) || index + 1;
                    const operacaoId = selects[0].value;
                    const recursoId = selects[1].value;
                    const tempo = parseFloat(inputs[1].value) || 1;
                    const descricao = inputs[2].value;

                    if (operacaoId && recursoId) {
                        operations.push({
                            sequencia,
                            operacaoId,
                            recursoId,
                            tempo,
                            descricao
                        });
                    }
                });

                const now = new Date().toISOString();
                const existingStructure = estruturas.find(e => e.produtoPaiId === parentProductId);

                const structureData = {
                    produtoPaiId: parentProductId,
                    componentes: components,
                    operacoes: operations,
                    dataCriacao: existingStructure?.dataCriacao || now,
                    dataUltimaAlteracao: now,
                    revisaoAtual: existingStructure ? (existingStructure.revisaoAtual || 0) + 1 : 0,
                    usuarioUltimaAlteracao: {
                        id: usuarioAtual.id,
                        nome: usuarioAtual.nome,
                        email: usuarioAtual.email
                    }
                };

                if (existingStructure) {
                    await updateDoc(doc(db, "estruturas", existingStructure.id), structureData);
                    const index = estruturas.findIndex(e => e.id === existingStructure.id);
                    estruturas[index] = { id: existingStructure.id, ...structureData };
                } else {
                    const docRef = await addDoc(collection(db, "estruturas"), structureData);
                    estruturas.push({ id: docRef.id, ...structureData });
                }

                alert("Sub-estrutura salva com sucesso!");

                // Atualizar o ícone do componente principal
                const mainComponent = document.querySelector(`.component[data-id="${parentProductId}"]`);
                const productTitle = mainComponent.querySelector('span');
                if (!productTitle.querySelector('.fas.fa-sitemap')) {
                    productTitle.innerHTML += '<i class="fas fa-sitemap" title="Possui estrutura" style="margin-left: 8px; color: var(--primary-color);"></i>';
                }

            } catch (error) {
                console.error("Erro ao salvar sub-estrutura:", error);
                alert("Erro ao salvar sub-estrutura: " + error.message);
            }
        };

        function loadSubStructureData(productId, structure) {
            const subComponentsList = document.getElementById(`subComponentsList_${productId}`);
            const subOperationsList = document.getElementById(`subOperationsList_${productId}`);

            // Carregar componentes
            if (structure.componentes) {
                structure.componentes.forEach((comp, index) => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        const subComponent = document.createElement('div');
                        subComponent.className = 'sub-component';
                        subComponent.dataset.id = produto.id;
                        subComponent.style.display = 'flex';
                        subComponent.style.alignItems = 'center';
                        subComponent.style.gap = '5px';
                        subComponent.style.padding = '4px';
                        subComponent.style.marginBottom = '3px';
                        subComponent.style.backgroundColor = 'white';
                        subComponent.style.border = '1px solid var(--border-color)';
                        subComponent.style.borderRadius = '3px';
                        subComponent.style.fontSize = '11px';

                        subComponent.innerHTML = `
                            <span style="width: 20px; text-align: center; font-size: 10px;">${index + 1}</span>
                            <span style="flex: 1;">
                                <strong style="font-size: 10px;">${produto.codigo}</strong><span style="font-size: 9px;"> - ${produto.descricao}</span>
                                <span class="type-badge type-${produto.tipo}" style="font-size: 7px; padding: 1px 3px;">${produto.tipo}</span>
                            </span>
                            <input type="number" value="${comp.quantidade}" min="0.001" step="0.001" style="width: 60px; font-size: 10px; padding: 2px;">
                            <span style="font-size: 9px;">${produto.unidade}</span>
                            <button class="btn btn-danger btn-sm" onclick="this.closest('.sub-component').remove()" style="padding: 2px 4px;">
                                <i class="fas fa-times" style="font-size: 8px;"></i>
                            </button>
                        `;

                        subComponentsList.appendChild(subComponent);
                    }
                });
            }

            // Carregar operações
            if (structure.operacoes) {
                structure.operacoes.forEach(op => {
                    const subOperation = document.createElement('div');
                    subOperation.className = 'sub-operation';
                    subOperation.style.display = 'flex';
                    subOperation.style.alignItems = 'center';
                    subOperation.style.gap = '5px';
                    subOperation.style.padding = '4px';
                    subOperation.style.marginBottom = '3px';
                    subOperation.style.backgroundColor = 'white';
                    subOperation.style.border = '1px solid var(--border-color)';
                    subOperation.style.borderRadius = '3px';
                    subOperation.style.fontSize = '11px';

                    const sortedOperacoes = [...operacoes].sort((a, b) => a.numero.localeCompare(b.numero));
                    const sortedRecursos = [...recursos].sort((a, b) => a.codigo.localeCompare(b.codigo));

                    subOperation.innerHTML = `
                        <input type="number" value="${op.sequencia}" style="width: 30px; text-align: center; font-size: 10px; padding: 2px;">
                        <select style="width: 100px; font-size: 10px; padding: 2px;">
                            <option value="">Operação...</option>
                            ${sortedOperacoes.map(oper => `
                                <option value="${oper.id}" ${oper.id === op.operacaoId ? 'selected' : ''}>
                                    ${oper.numero} - ${oper.operacao}
                                </option>
                            `).join('')}
                        </select>
                        <select style="width: 100px; font-size: 10px; padding: 2px;">
                            <option value="">Recurso...</option>
                            ${sortedRecursos.map(rec => `
                                <option value="${rec.id}" ${rec.id === op.recursoId ? 'selected' : ''}>
                                    ${rec.codigo} - ${rec.maquina}
                                </option>
                            `).join('')}
                        </select>
                        <input type="number" value="${op.tempo}" min="0.1" step="0.1" style="width: 40px; font-size: 10px; padding: 2px;" placeholder="Tempo">
                        <input type="text" value="${op.descricao || ''}" placeholder="Descrição" style="flex: 1; font-size: 10px; padding: 2px;">
                        <button class="btn btn-danger btn-sm" onclick="this.closest('.sub-operation').remove()" style="padding: 2px 4px;">
                            <i class="fas fa-times" style="font-size: 8px;"></i>
                        </button>
                    `;

                    subOperationsList.appendChild(subOperation);
                });
            }
        }

        // Função para buscar revisões de uma estrutura
        function getStructureRevisions(productId) {
            const allRevisions = estruturas.filter(e => e.produtoPaiId === productId);
            const currentRevision = allRevisions.find(e => !e.isHistorical) || allRevisions[allRevisions.length - 1];

            // Ordenar por revisão (mais recente primeiro)
            const sortedRevisions = allRevisions.sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

            return {
                current: currentRevision,
                all: sortedRevisions,
                count: allRevisions.length
            };
        }

        // Função para renderizar histórico de revisões
        function renderRevisionsHistory(revisions) {
            if (revisions.all.length === 0) {
                return `
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-history" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <h3>Nenhuma revisão encontrada</h3>
                        <p>Esta estrutura ainda não possui histórico de revisões.</p>
                    </div>
                `;
            }

            let html = `
                <div class="revisions-timeline">
                    <div class="timeline-header">
                        <h4><i class="fas fa-timeline"></i> Linha do Tempo das Revisões</h4>
                    </div>
            `;

            revisions.all.forEach((revision, index) => {
                const isCurrentRevision = revision.id === revisions.current?.id;
                const revisionNumber = String(revision.revisaoAtual || 0).padStart(3, '0');
                const creationDate = revision.dataCriacao ? new Date(revision.dataCriacao).toLocaleString('pt-BR') : 'N/A';
                const updateDate = revision.dataUltimaAlteracao ? new Date(revision.dataUltimaAlteracao).toLocaleString('pt-BR') : 'N/A';
                const user = revision.usuarioUltimaAlteracao?.nome || 'Sistema';

                const componentsCount = revision.componentes ? revision.componentes.length : 0;
                const operationsCount = revision.operacoes ? revision.operacoes.length : 0;

                html += `
                    <div class="revision-item ${isCurrentRevision ? 'current-revision' : ''}" data-revision-id="${revision.id}">
                        <div class="revision-marker">
                            <div class="revision-number">REV${revisionNumber}</div>
                            ${isCurrentRevision ? '<i class="fas fa-star current-star" title="Revisão Atual"></i>' : ''}
                        </div>

                        <div class="revision-content">
                            <div class="revision-header">
                                <h5>
                                    Revisão ${revisionNumber}
                                    ${isCurrentRevision ? '<span class="current-badge">ATUAL</span>' : ''}
                                    ${revision.isHistorical ? '<span class="historical-badge">HISTÓRICA</span>' : ''}
                                </h5>
                                <div class="revision-actions">
                                    <button class="btn btn-sm btn-info" onclick="viewRevisionDetails('${revision.id}')" title="Ver Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    ${!isCurrentRevision ? `
                                        <button class="btn btn-sm btn-warning" onclick="restoreRevision('${revision.id}')" title="Restaurar Revisão">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-sm btn-primary" onclick="duplicateRevision('${revision.id}')" title="Duplicar Revisão">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="revision-info">
                                <div class="revision-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-cubes"></i>
                                        <span>${componentsCount} Componentes</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-cogs"></i>
                                        <span>${operationsCount} Operações</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-user"></i>
                                        <span>${user}</span>
                                    </div>
                                </div>

                                <div class="revision-dates">
                                    <div><strong>Criada:</strong> ${creationDate}</div>
                                    <div><strong>Modificada:</strong> ${updateDate}</div>
                                </div>
                            </div>

                            ${revision.motivoRevisao ? `
                                <div class="revision-reason">
                                    <strong>Motivo da Revisão:</strong> ${revision.motivoRevisao}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
            `;

            return html;
        }

        // Função para criar nova revisão
        window.createNewRevision = function(productId) {
            if (hasChanges) {
                alert('Salve as alterações atuais antes de criar uma nova revisão.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-plus"></i> Criar Nova Revisão
                    </div>

                    <form onsubmit="processNewRevision(event, '${productId}')">
                        <div class="form-group">
                            <label for="revisionReason">
                                <i class="fas fa-comment"></i> Motivo da Revisão *
                            </label>
                            <textarea id="revisionReason" required rows="3" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;"
                                placeholder="Descreva o motivo desta nova revisão..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="markAsHistorical">
                                Marcar revisão atual como histórica
                            </label>
                            <small style="display: block; color: var(--text-secondary); margin-top: 5px;">
                                A revisão atual será preservada no histórico e uma nova revisão será criada.
                            </small>
                        </div>

                        <div class="footer-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> Criar Revisão
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                                <i class="fas fa-times"></i> Cancelar
                            </button>
                        </div>
                    </form>
                </div>
            `;
            document.body.appendChild(modal);
        };

        window.deleteCurrentStructure = async function() {
            const container = document.querySelector('.structure-container');
            if (!container) {
                alert("Nenhuma estrutura está carregada.");
                return;
            }

            const productId = container.dataset.id;
            const estrutura = estruturas.find(e => e.produtoPaiId === productId);

            if (!estrutura) {
                alert("Nenhuma estrutura registrada encontrada para este produto.");
                return;
            }

            if (!confirm("Tem certeza que deseja excluir esta estrutura? Esta ação não pode ser desfeita.")) {
                return;
            }

            try {
                await deleteDoc(doc(db, "estruturas", estrutura.id));
                estruturas = estruturas.filter(e => e.id !== estrutura.id);

                document.getElementById('structureTree').innerHTML = '';
                document.getElementById('parentProductSelect').value = '';
                document.querySelector('.btn-success').disabled = true;
                hasChanges = false;

                alert("Estrutura excluída com sucesso!");
            } catch (error) {
                console.error("Erro ao excluir estrutura:", error);
                alert("Erro ao excluir a estrutura: " + error.message);
            }
        };

        // Função para processar criação de nova revisão
        window.processNewRevision = async function(event, productId) {
            event.preventDefault();

            const reason = document.getElementById('revisionReason').value.trim();
            const markAsHistorical = document.getElementById('markAsHistorical').checked;

            if (!reason) {
                alert('Por favor, informe o motivo da revisão.');
                return;
            }

            try {
                const currentStructure = estruturas.find(e => e.produtoPaiId === productId && !e.isHistorical);
                if (!currentStructure) {
                    alert('Estrutura atual não encontrada.');
                    return;
                }

                const now = new Date().toISOString();
                const newRevisionNumber = (currentStructure.revisaoAtual || 0) + 1;

                // Se marcar como histórica, atualizar a estrutura atual
                if (markAsHistorical) {
                    const historicalData = {
                        ...currentStructure,
                        isHistorical: true,
                        dataArquivamento: now,
                        usuarioArquivamento: {
                            id: usuarioAtual.id,
                            nome: usuarioAtual.nome,
                            email: usuarioAtual.email
                        }
                    };

                    await updateDoc(doc(db, "estruturas", currentStructure.id), historicalData);

                    // Atualizar no array local
                    const index = estruturas.findIndex(e => e.id === currentStructure.id);
                    estruturas[index] = { id: currentStructure.id, ...historicalData };
                }

                // Criar nova revisão baseada na atual
                const newRevisionData = {
                    produtoPaiId: productId,
                    componentes: [...(currentStructure.componentes || [])],
                    operacoes: [...(currentStructure.operacoes || [])],
                    dataCriacao: now,
                    dataUltimaAlteracao: now,
                    revisaoAtual: newRevisionNumber,
                    motivoRevisao: reason,
                    revisaoAnterior: currentStructure.revisaoAtual || 0,
                    usuarioUltimaAlteracao: {
                        id: usuarioAtual.id,
                        nome: usuarioAtual.nome,
                        email: usuarioAtual.email
                    },
                    isHistorical: false
                };

                const docRef = await addDoc(collection(db, "estruturas"), newRevisionData);
                estruturas.push({ id: docRef.id, ...newRevisionData });

                // Fechar modal e recarregar estrutura
                document.querySelector('.modal').remove();
                alert(`Nova revisão REV${String(newRevisionNumber).padStart(3, '0')} criada com sucesso!`);

                // Recarregar a estrutura atual
                await loadStructureForProduct();

            } catch (error) {
                console.error("Erro ao criar nova revisão:", error);
                alert("Erro ao criar nova revisão: " + error.message);
            }
        };

        // Função para ver detalhes de uma revisão
        window.viewRevisionDetails = function(revisionId) {
            const revision = estruturas.find(e => e.id === revisionId);
            if (!revision) {
                alert('Revisão não encontrada.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-info-circle"></i> Detalhes da Revisão REV${String(revision.revisaoAtual || 0).padStart(3, '0')}
                    </div>

                    ${renderRevisionDetails(revision)}

                    <div class="footer-actions" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="exportRevisionDetails('${revisionId}')">
                            <i class="fas fa-download"></i> Exportar Detalhes
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Função para renderizar detalhes de uma revisão
        function renderRevisionDetails(revision) {
            const creationDate = revision.dataCriacao ? new Date(revision.dataCriacao).toLocaleString('pt-BR') : 'N/A';
            const updateDate = revision.dataUltimaAlteracao ? new Date(revision.dataUltimaAlteracao).toLocaleString('pt-BR') : 'N/A';
            const user = revision.usuarioUltimaAlteracao?.nome || 'Sistema';

            let html = `
                <div class="revision-details">
                    <div class="details-header" style="background-color: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div><strong>Revisão:</strong> REV${String(revision.revisaoAtual || 0).padStart(3, '0')}</div>
                            <div><strong>Status:</strong> ${revision.isHistorical ? '<span class="historical-badge">HISTÓRICA</span>' : '<span class="current-badge">ATUAL</span>'}</div>
                            <div><strong>Criada em:</strong> ${creationDate}</div>
                            <div><strong>Modificada em:</strong> ${updateDate}</div>
                            <div>
                                <strong>Usuário:</strong> ${user}<br>
                                ${revision.usuarioUltimaAlteracao?.email ? `<small style="color: var(--text-secondary);">${revision.usuarioUltimaAlteracao.email}</small>` : ''}
                                ${revision.usuarioUltimaAlteracao?.ip && revision.usuarioUltimaAlteracao.ip !== 'N/A' ? `<br><small style="color: var(--text-secondary);"><i class="fas fa-globe"></i> ${revision.usuarioUltimaAlteracao.ip}</small>` : ''}
                            </div>
                            <div><strong>Revisão Anterior:</strong> ${revision.revisaoAnterior ? 'REV' + String(revision.revisaoAnterior).padStart(3, '0') : 'N/A'}</div>
                        </div>
                        ${revision.motivoRevisao ? `
                            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color);">
                                <strong>Motivo da Revisão:</strong><br>
                                <em>${revision.motivoRevisao}</em>
                            </div>
                        ` : ''}
                    </div>

                    <div class="components-section" style="margin-bottom: 25px;">
                        <h4 style="color: var(--primary-color); margin-bottom: 15px;">
                            <i class="fas fa-cubes"></i> Componentes (${revision.componentes ? revision.componentes.length : 0})
                        </h4>
                        ${renderRevisionComponents(revision.componentes || [])}
                    </div>

                    <div class="operations-section">
                        <h4 style="color: var(--primary-color); margin-bottom: 15px;">
                            <i class="fas fa-cogs"></i> Operações (${revision.operacoes ? revision.operacoes.length : 0})
                        </h4>
                        ${renderRevisionOperations(revision.operacoes || [])}
                    </div>
                </div>
            `;

            return html;
        }

        // Função para renderizar componentes de uma revisão
        function renderRevisionComponents(components) {
            if (components.length === 0) {
                return `
                    <div style="padding: 20px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-info-circle"></i> Nenhum componente nesta revisão.
                    </div>
                `;
            }

            let html = `
                <div style="background-color: white; border: 1px solid var(--border-color); border-radius: 6px; overflow: hidden;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background-color: #f8f9fa;">
                            <tr>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Seq</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Código</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Descrição</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Quantidade</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Unidade</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Tipo</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            components.forEach((comp, index) => {
                const produto = produtos.find(p => p.id === comp.componentId);
                if (produto) {
                    html += `
                        <tr style="border-bottom: 1px solid #f0f0f0;">
                            <td style="padding: 12px; text-align: center;">${index + 1}</td>
                            <td style="padding: 12px;"><strong>${produto.codigo}</strong></td>
                            <td style="padding: 12px;">${produto.descricao}</td>
                            <td style="padding: 12px; text-align: center; font-weight: 600;">${parseFloat(comp.quantidade).toFixed(3).replace('.', ',')}</td>
                            <td style="padding: 12px; text-align: center;">${comp.unidade}</td>
                            <td style="padding: 12px; text-align: center;"><span class="type-badge type-${produto.tipo}">${produto.tipo}</span></td>
                        </tr>
                    `;
                }
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            return html;
        }

        // Função para renderizar operações de uma revisão
        function renderRevisionOperations(operations) {
            if (operations.length === 0) {
                return `
                    <div style="padding: 20px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-info-circle"></i> Nenhuma operação nesta revisão.
                    </div>
                `;
            }

            let html = `
                <div style="background-color: white; border: 1px solid var(--border-color); border-radius: 6px; overflow: hidden;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background-color: #f8f9fa;">
                            <tr>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Seq</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Operação</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Recurso</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid var(--border-color);">Tempo (min)</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid var(--border-color);">Descrição</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            operations.forEach(op => {
                const operacao = operacoes.find(o => o.id === op.operacaoId);
                const recurso = recursos.find(r => r.id === op.recursoId);

                html += `
                    <tr style="border-bottom: 1px solid #f0f0f0;">
                        <td style="padding: 12px; text-align: center; font-weight: 600;">${op.sequencia}</td>
                        <td style="padding: 12px;">${operacao ? `${operacao.numero} - ${operacao.operacao}` : 'N/A'}</td>
                        <td style="padding: 12px;">${recurso ? `${recurso.codigo} - ${recurso.maquina}` : 'N/A'}</td>
                        <td style="padding: 12px; text-align: center; font-weight: 600;">${parseFloat(op.tempo).toFixed(1).replace('.', ',')}</td>
                        <td style="padding: 12px;">${op.descricao || '-'}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            return html;
        }

        // Função para restaurar uma revisão
        window.restoreRevision = function(revisionId) {
            if (hasChanges) {
                alert('Salve as alterações atuais antes de restaurar uma revisão.');
                return;
            }

            const revision = estruturas.find(e => e.id === revisionId);
            if (!revision) {
                alert('Revisão não encontrada.');
                return;
            }

            const confirmRestore = confirm(
                `Tem certeza que deseja restaurar a revisão REV${String(revision.revisaoAtual || 0).padStart(3, '0')}?\n\n` +
                `Esta ação criará uma nova revisão baseada na revisão selecionada.\n` +
                `A revisão atual será preservada no histórico.`
            );

            if (confirmRestore) {
                createRevisionFromExisting(revision);
            }
        };

        // Função para criar revisão baseada em uma existente
        async function createRevisionFromExisting(sourceRevision) {
            try {
                const productId = sourceRevision.produtoPaiId;
                const currentStructure = estruturas.find(e => e.produtoPaiId === productId && !e.isHistorical);

                const now = new Date().toISOString();
                const newRevisionNumber = Math.max(...estruturas.filter(e => e.produtoPaiId === productId).map(e => e.revisaoAtual || 0)) + 1;

                // Marcar estrutura atual como histórica se existir
                if (currentStructure) {
                    const historicalData = {
                        ...currentStructure,
                        isHistorical: true,
                        dataArquivamento: now,
                        usuarioArquivamento: {
                            id: usuarioAtual.id,
                            nome: usuarioAtual.nome,
                            email: usuarioAtual.email
                        }
                    };

                    await updateDoc(doc(db, "estruturas", currentStructure.id), historicalData);

                    const index = estruturas.findIndex(e => e.id === currentStructure.id);
                    estruturas[index] = { id: currentStructure.id, ...historicalData };
                }

                // Criar nova revisão baseada na selecionada
                const newRevisionData = {
                    produtoPaiId: productId,
                    componentes: [...(sourceRevision.componentes || [])],
                    operacoes: [...(sourceRevision.operacoes || [])],
                    dataCriacao: now,
                    dataUltimaAlteracao: now,
                    revisaoAtual: newRevisionNumber,
                    motivoRevisao: `Restaurada da revisão REV${String(sourceRevision.revisaoAtual || 0).padStart(3, '0')}`,
                    revisaoAnterior: currentStructure?.revisaoAtual || 0,
                    revisaoOrigem: sourceRevision.revisaoAtual || 0,
                    usuarioUltimaAlteracao: {
                        id: usuarioAtual.id,
                        nome: usuarioAtual.nome,
                        email: usuarioAtual.email
                    },
                    isHistorical: false
                };

                const docRef = await addDoc(collection(db, "estruturas"), newRevisionData);
                estruturas.push({ id: docRef.id, ...newRevisionData });

                alert(`Revisão REV${String(newRevisionNumber).padStart(3, '0')} criada com base na REV${String(sourceRevision.revisaoAtual || 0).padStart(3, '0')}!`);

                // Recarregar estrutura e fechar modal
                document.querySelector('.modal').remove();
                await loadStructureForProduct();

            } catch (error) {
                console.error("Erro ao restaurar revisão:", error);
                alert("Erro ao restaurar revisão: " + error.message);
            }
        }

        // Função para duplicar uma revisão
        window.duplicateRevision = function(revisionId) {
            const revision = estruturas.find(e => e.id === revisionId);
            if (!revision) {
                alert('Revisão não encontrada.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-copy"></i> Duplicar Revisão REV${String(revision.revisaoAtual || 0).padStart(3, '0')}
                    </div>

                    <form onsubmit="processDuplicateRevision(event, '${revisionId}')">
                        <div class="form-group">
                            <label for="duplicateReason">
                                <i class="fas fa-comment"></i> Motivo da Duplicação *
                            </label>
                            <textarea id="duplicateReason" required rows="3" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;"
                                placeholder="Descreva o motivo desta duplicação..."></textarea>
                        </div>

                        <div class="footer-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> Duplicar Revisão
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                                <i class="fas fa-times"></i> Cancelar
                            </button>
                        </div>
                    </form>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Função para processar duplicação de revisão
        window.processDuplicateRevision = async function(event, revisionId) {
            event.preventDefault();

            const reason = document.getElementById('duplicateReason').value.trim();
            if (!reason) {
                alert('Por favor, informe o motivo da duplicação.');
                return;
            }

            const sourceRevision = estruturas.find(e => e.id === revisionId);
            if (!sourceRevision) {
                alert('Revisão não encontrada.');
                return;
            }

            try {
                const productId = sourceRevision.produtoPaiId;
                const now = new Date().toISOString();
                const newRevisionNumber = Math.max(...estruturas.filter(e => e.produtoPaiId === productId).map(e => e.revisaoAtual || 0)) + 1;

                const newRevisionData = {
                    produtoPaiId: productId,
                    componentes: [...(sourceRevision.componentes || [])],
                    operacoes: [...(sourceRevision.operacoes || [])],
                    dataCriacao: now,
                    dataUltimaAlteracao: now,
                    revisaoAtual: newRevisionNumber,
                    motivoRevisao: reason,
                    revisaoAnterior: sourceRevision.revisaoAtual || 0,
                    revisaoOrigem: sourceRevision.revisaoAtual || 0,
                    isDuplicate: true,
                    usuarioUltimaAlteracao: {
                        id: usuarioAtual.id,
                        nome: usuarioAtual.nome,
                        email: usuarioAtual.email
                    },
                    isHistorical: false
                };

                const docRef = await addDoc(collection(db, "estruturas"), newRevisionData);
                estruturas.push({ id: docRef.id, ...newRevisionData });

                document.querySelector('.modal').remove();
                alert(`Revisão REV${String(newRevisionNumber).padStart(3, '0')} duplicada com sucesso!`);

            } catch (error) {
                console.error("Erro ao duplicar revisão:", error);
                alert("Erro ao duplicar revisão: " + error.message);
            }
        };

        // Função para comparar revisões
        window.compareRevisions = function(productId) {
            const revisions = estruturas.filter(e => e.produtoPaiId === productId).sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

            if (revisions.length < 2) {
                alert('É necessário ter pelo menos 2 revisões para comparar.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 1200px; max-height: 85vh; overflow-y: auto;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-balance-scale"></i> Comparar Revisões
                    </div>

                    <div class="comparison-controls" style="margin-bottom: 20px; display: flex; gap: 15px; align-items: center;">
                        <div>
                            <label>Revisão A:</label>
                            <select id="revisionA" style="margin-left: 10px; padding: 5px;">
                                ${revisions.map(r => `
                                    <option value="${r.id}">REV${String(r.revisaoAtual || 0).padStart(3, '0')} - ${new Date(r.dataCriacao || r.dataUltimaAlteracao).toLocaleDateString('pt-BR')}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div>
                            <label>Revisão B:</label>
                            <select id="revisionB" style="margin-left: 10px; padding: 5px;">
                                ${revisions.map((r, index) => `
                                    <option value="${r.id}" ${index === 1 ? 'selected' : ''}>REV${String(r.revisaoAtual || 0).padStart(3, '0')} - ${new Date(r.dataCriacao || r.dataUltimaAlteracao).toLocaleDateString('pt-BR')}</option>
                                `).join('')}
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="performComparison()">
                            <i class="fas fa-search"></i> Comparar
                        </button>
                    </div>

                    <div id="comparisonResults">
                        <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                            <i class="fas fa-balance-scale" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>Selecione as revisões e clique em "Comparar" para ver as diferenças.</p>
                        </div>
                    </div>

                    <div class="footer-actions" style="margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Função para exportar histórico de revisões
        window.exportRevisionHistory = function(productId) {
            const produto = produtos.find(p => p.id === productId);
            const revisions = estruturas.filter(e => e.produtoPaiId === productId).sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

            if (!produto || revisions.length === 0) {
                alert('Nenhuma revisão encontrada para exportar.');
                return;
            }

            try {
                const reportData = {
                    produto: {
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        tipo: produto.tipo
                    },
                    totalRevisoes: revisions.length,
                    revisaoAtual: revisions[0]?.revisaoAtual || 0,
                    revisoes: revisions.map(rev => ({
                        revisao: rev.revisaoAtual || 0,
                        dataCriacao: rev.dataCriacao,
                        dataUltimaAlteracao: rev.dataUltimaAlteracao,
                        usuario: rev.usuarioUltimaAlteracao?.nome || 'Sistema',
                        motivoRevisao: rev.motivoRevisao || '',
                        isHistorical: rev.isHistorical || false,
                        componentes: rev.componentes?.length || 0,
                        operacoes: rev.operacoes?.length || 0,
                        detalhesComponentes: rev.componentes?.map(comp => {
                            const prod = produtos.find(p => p.id === comp.componentId);
                            return {
                                codigo: prod?.codigo || 'N/A',
                                descricao: prod?.descricao || 'N/A',
                                quantidade: comp.quantidade,
                                unidade: comp.unidade
                            };
                        }) || [],
                        detalhesOperacoes: rev.operacoes?.map(op => {
                            const operacao = operacoes.find(o => o.id === op.operacaoId);
                            const recurso = recursos.find(r => r.id === op.recursoId);
                            return {
                                sequencia: op.sequencia,
                                operacao: operacao ? `${operacao.numero} - ${operacao.operacao}` : 'N/A',
                                recurso: recurso ? `${recurso.codigo} - ${recurso.maquina}` : 'N/A',
                                tempo: op.tempo,
                                descricao: op.descricao || ''
                            };
                        }) || []
                    })),
                    dataExportacao: new Date().toISOString()
                };

                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `historico-revisoes-${produto.codigo}-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                alert('Histórico de revisões exportado com sucesso!');

            } catch (error) {
                console.error('Erro ao exportar histórico:', error);
                alert('Erro ao exportar histórico: ' + error.message);
            }
        };

        // Função para realizar comparação entre revisões
        window.performComparison = function() {
            const revisionAId = document.getElementById('revisionA').value;
            const revisionBId = document.getElementById('revisionB').value;

            if (revisionAId === revisionBId) {
                alert('Selecione revisões diferentes para comparar.');
                return;
            }

            const revisionA = estruturas.find(e => e.id === revisionAId);
            const revisionB = estruturas.find(e => e.id === revisionBId);

            if (!revisionA || !revisionB) {
                alert('Revisões não encontradas.');
                return;
            }

            const comparison = compareStructureRevisions(revisionA, revisionB);
            document.getElementById('comparisonResults').innerHTML = renderComparisonResults(comparison, revisionA, revisionB);
        };

        // Função para comparar duas revisões
        function compareStructureRevisions(revisionA, revisionB) {
            const comparison = {
                components: {
                    added: [],
                    removed: [],
                    modified: [],
                    unchanged: []
                },
                operations: {
                    added: [],
                    removed: [],
                    modified: [],
                    unchanged: []
                }
            };

            // Comparar componentes
            const componentsA = revisionA.componentes || [];
            const componentsB = revisionB.componentes || [];

            // Componentes adicionados (estão em B mas não em A)
            componentsB.forEach(compB => {
                const foundInA = componentsA.find(compA => compA.componentId === compB.componentId);
                if (!foundInA) {
                    comparison.components.added.push(compB);
                }
            });

            // Componentes removidos (estão em A mas não em B)
            componentsA.forEach(compA => {
                const foundInB = componentsB.find(compB => compB.componentId === compA.componentId);
                if (!foundInB) {
                    comparison.components.removed.push(compA);
                }
            });

            // Componentes modificados ou inalterados
            componentsA.forEach(compA => {
                const foundInB = componentsB.find(compB => compB.componentId === compA.componentId);
                if (foundInB) {
                    if (compA.quantidade !== foundInB.quantidade) {
                        comparison.components.modified.push({
                            componentId: compA.componentId,
                            oldQuantity: compA.quantidade,
                            newQuantity: foundInB.quantidade,
                            unidade: compA.unidade
                        });
                    } else {
                        comparison.components.unchanged.push(compA);
                    }
                }
            });

            // Comparar operações
            const operationsA = revisionA.operacoes || [];
            const operationsB = revisionB.operacoes || [];

            // Operações adicionadas
            operationsB.forEach(opB => {
                const foundInA = operationsA.find(opA =>
                    opA.operacaoId === opB.operacaoId &&
                    opA.recursoId === opB.recursoId &&
                    opA.sequencia === opB.sequencia
                );
                if (!foundInA) {
                    comparison.operations.added.push(opB);
                }
            });

            // Operações removidas
            operationsA.forEach(opA => {
                const foundInB = operationsB.find(opB =>
                    opB.operacaoId === opA.operacaoId &&
                    opB.recursoId === opA.recursoId &&
                    opB.sequencia === opA.sequencia
                );
                if (!foundInB) {
                    comparison.operations.removed.push(opA);
                }
            });

            // Operações modificadas ou inalteradas
            operationsA.forEach(opA => {
                const foundInB = operationsB.find(opB =>
                    opB.operacaoId === opA.operacaoId &&
                    opB.recursoId === opA.recursoId &&
                    opB.sequencia === opA.sequencia
                );
                if (foundInB) {
                    if (opA.tempo !== foundInB.tempo || opA.descricao !== foundInB.descricao) {
                        comparison.operations.modified.push({
                            ...opA,
                            oldTempo: opA.tempo,
                            newTempo: foundInB.tempo,
                            oldDescricao: opA.descricao,
                            newDescricao: foundInB.descricao
                        });
                    } else {
                        comparison.operations.unchanged.push(opA);
                    }
                }
            });

            return comparison;
        }

        // Função para renderizar resultados da comparação
        function renderComparisonResults(comparison, revisionA, revisionB) {
            const revA = String(revisionA.revisaoAtual || 0).padStart(3, '0');
            const revB = String(revisionB.revisaoAtual || 0).padStart(3, '0');

            let html = `
                <div class="comparison-results">
                    <div class="comparison-header" style="background: linear-gradient(135deg, #e3f2fd, #bbdefb); padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                        <h4 style="margin: 0 0 15px 0; color: var(--primary-color);">
                            <i class="fas fa-balance-scale"></i> Comparação: REV${revA} vs REV${revB}
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <strong>REV${revA}:</strong><br>
                                <small>${new Date(revisionA.dataCriacao || revisionA.dataUltimaAlteracao).toLocaleString('pt-BR')}</small>
                            </div>
                            <div>
                                <strong>REV${revB}:</strong><br>
                                <small>${new Date(revisionB.dataCriacao || revisionB.dataUltimaAlteracao).toLocaleString('pt-BR')}</small>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-summary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 25px;">
                        <div class="summary-card" style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #155724;">${comparison.components.added.length + comparison.operations.added.length}</div>
                            <div style="font-size: 12px; color: #155724;">Adicionados</div>
                        </div>
                        <div class="summary-card" style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #721c24;">${comparison.components.removed.length + comparison.operations.removed.length}</div>
                            <div style="font-size: 12px; color: #721c24;">Removidos</div>
                        </div>
                        <div class="summary-card" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #856404;">${comparison.components.modified.length + comparison.operations.modified.length}</div>
                            <div style="font-size: 12px; color: #856404;">Modificados</div>
                        </div>
                        <div class="summary-card" style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #0c5460;">${comparison.components.unchanged.length + comparison.operations.unchanged.length}</div>
                            <div style="font-size: 12px; color: #0c5460;">Inalterados</div>
                        </div>
                    </div>

                    ${renderComponentsComparison(comparison.components)}
                    ${renderOperationsComparison(comparison.operations)}
                </div>
            `;

            return html;
        }

        // Função para renderizar comparação de componentes
        function renderComponentsComparison(componentsComparison) {
            let html = `
                <div class="components-comparison" style="margin-bottom: 25px;">
                    <h4 style="color: var(--primary-color); margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid var(--border-color);">
                        <i class="fas fa-cubes"></i> Comparação de Componentes
                    </h4>
            `;

            // Componentes adicionados
            if (componentsComparison.added.length > 0) {
                html += `
                    <div class="comparison-section" style="margin-bottom: 20px;">
                        <h5 style="color: #155724; margin-bottom: 10px;">
                            <i class="fas fa-plus-circle"></i> Componentes Adicionados (${componentsComparison.added.length})
                        </h5>
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; padding: 15px;">
                `;

                componentsComparison.added.forEach(comp => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #c3e6cb;">
                                <span><strong>${produto.codigo}</strong> - ${produto.descricao}</span>
                                <span style="font-weight: 600;">${parseFloat(comp.quantidade).toFixed(3)} ${comp.unidade}</span>
                            </div>
                        `;
                    }
                });

                html += `</div></div>`;
            }

            // Componentes removidos
            if (componentsComparison.removed.length > 0) {
                html += `
                    <div class="comparison-section" style="margin-bottom: 20px;">
                        <h5 style="color: #721c24; margin-bottom: 10px;">
                            <i class="fas fa-minus-circle"></i> Componentes Removidos (${componentsComparison.removed.length})
                        </h5>
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 15px;">
                `;

                componentsComparison.removed.forEach(comp => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f5c6cb;">
                                <span><strong>${produto.codigo}</strong> - ${produto.descricao}</span>
                                <span style="font-weight: 600;">${parseFloat(comp.quantidade).toFixed(3)} ${comp.unidade}</span>
                            </div>
                        `;
                    }
                });

                html += `</div></div>`;
            }

            // Componentes modificados
            if (componentsComparison.modified.length > 0) {
                html += `
                    <div class="comparison-section" style="margin-bottom: 20px;">
                        <h5 style="color: #856404; margin-bottom: 10px;">
                            <i class="fas fa-edit"></i> Componentes Modificados (${componentsComparison.modified.length})
                        </h5>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px;">
                `;

                componentsComparison.modified.forEach(comp => {
                    const produto = produtos.find(p => p.id === comp.componentId);
                    if (produto) {
                        html += `
                            <div style="padding: 12px 0; border-bottom: 1px solid #ffeaa7;">
                                <div style="font-weight: 600; margin-bottom: 5px;">${produto.codigo} - ${produto.descricao}</div>
                                <div style="display: flex; gap: 20px; font-size: 14px;">
                                    <span style="color: #721c24;">Antes: ${parseFloat(comp.oldQuantity).toFixed(3)} ${comp.unidade}</span>
                                    <span style="color: #155724;">Depois: ${parseFloat(comp.newQuantity).toFixed(3)} ${comp.unidade}</span>
                                </div>
                            </div>
                        `;
                    }
                });

                html += `</div></div>`;
            }

            html += `</div>`;
            return html;
        }

        // Função para renderizar comparação de operações
        function renderOperationsComparison(operationsComparison) {
            let html = `
                <div class="operations-comparison">
                    <h4 style="color: var(--primary-color); margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid var(--border-color);">
                        <i class="fas fa-cogs"></i> Comparação de Operações
                    </h4>
            `;

            // Operações adicionadas
            if (operationsComparison.added.length > 0) {
                html += `
                    <div class="comparison-section" style="margin-bottom: 20px;">
                        <h5 style="color: #155724; margin-bottom: 10px;">
                            <i class="fas fa-plus-circle"></i> Operações Adicionadas (${operationsComparison.added.length})
                        </h5>
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; padding: 15px;">
                `;

                operationsComparison.added.forEach(op => {
                    const operacao = operacoes.find(o => o.id === op.operacaoId);
                    const recurso = recursos.find(r => r.id === op.recursoId);

                    html += `
                        <div style="padding: 8px 0; border-bottom: 1px solid #c3e6cb;">
                            <div style="font-weight: 600;">Seq ${op.sequencia}: ${operacao ? operacao.operacao : 'N/A'}</div>
                            <div style="font-size: 14px; color: #155724;">
                                Recurso: ${recurso ? recurso.maquina : 'N/A'} | Tempo: ${op.tempo}min
                                ${op.descricao ? ` | ${op.descricao}` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            // Operações removidas
            if (operationsComparison.removed.length > 0) {
                html += `
                    <div class="comparison-section" style="margin-bottom: 20px;">
                        <h5 style="color: #721c24; margin-bottom: 10px;">
                            <i class="fas fa-minus-circle"></i> Operações Removidas (${operationsComparison.removed.length})
                        </h5>
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 15px;">
                `;

                operationsComparison.removed.forEach(op => {
                    const operacao = operacoes.find(o => o.id === op.operacaoId);
                    const recurso = recursos.find(r => r.id === op.recursoId);

                    html += `
                        <div style="padding: 8px 0; border-bottom: 1px solid #f5c6cb;">
                            <div style="font-weight: 600;">Seq ${op.sequencia}: ${operacao ? operacao.operacao : 'N/A'}</div>
                            <div style="font-size: 14px; color: #721c24;">
                                Recurso: ${recurso ? recurso.maquina : 'N/A'} | Tempo: ${op.tempo}min
                                ${op.descricao ? ` | ${op.descricao}` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            // Operações modificadas
            if (operationsComparison.modified.length > 0) {
                html += `
                    <div class="comparison-section">
                        <h5 style="color: #856404; margin-bottom: 10px;">
                            <i class="fas fa-edit"></i> Operações Modificadas (${operationsComparison.modified.length})
                        </h5>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px;">
                `;

                operationsComparison.modified.forEach(op => {
                    const operacao = operacoes.find(o => o.id === op.operacaoId);
                    const recurso = recursos.find(r => r.id === op.recursoId);

                    html += `
                        <div style="padding: 12px 0; border-bottom: 1px solid #ffeaa7;">
                            <div style="font-weight: 600; margin-bottom: 5px;">Seq ${op.sequencia}: ${operacao ? operacao.operacao : 'N/A'}</div>
                            <div style="font-size: 14px;">Recurso: ${recurso ? recurso.maquina : 'N/A'}</div>
                            ${op.oldTempo !== op.newTempo ? `
                                <div style="font-size: 14px; margin-top: 5px;">
                                    <span style="color: #721c24;">Tempo antes: ${op.oldTempo}min</span> →
                                    <span style="color: #155724;">Tempo depois: ${op.newTempo}min</span>
                                </div>
                            ` : ''}
                            ${op.oldDescricao !== op.newDescricao ? `
                                <div style="font-size: 14px; margin-top: 5px;">
                                    <span style="color: #721c24;">Descrição antes: "${op.oldDescricao || 'Vazio'}"</span><br>
                                    <span style="color: #155724;">Descrição depois: "${op.newDescricao || 'Vazio'}"</span>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            html += `</div>`;
            return html;
        }

        // Função para abrir modal de histórico de usuários
        window.openUserHistoryModal = function() {
            const container = document.querySelector('.structure-container');
            if (!container) {
                alert("Nenhuma estrutura está carregada.");
                return;
            }

            const productId = container.dataset.id;
            const produto = produtos.find(p => p.id === productId);

            if (!produto) {
                alert("Produto não encontrado.");
                return;
            }

            // Buscar histórico de usuários de todas as revisões
            const userHistory = getUserHistory(productId);

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 1000px; max-height: 85vh; overflow-y: auto;">
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    <div class="panel-title">
                        <i class="fas fa-users"></i> Histórico de Usuários: ${produto.codigo} - ${produto.descricao}
                    </div>

                    <div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
                        <h4 style="margin: 0 0 10px 0; color: var(--primary-color);">
                            <i class="fas fa-info-circle"></i> Resumo de Atividade
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>Total de Alterações:</strong> ${userHistory.totalChanges}</div>
                            <div><strong>Usuários Únicos:</strong> ${userHistory.uniqueUsers}</div>
                            <div><strong>Primeira Alteração:</strong> ${userHistory.firstChange}</div>
                            <div><strong>Última Alteração:</strong> ${userHistory.lastChange}</div>
                        </div>
                    </div>

                    <div class="user-history-controls" style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <button class="btn btn-primary" onclick="exportUserHistory('${productId}')">
                            <i class="fas fa-download"></i> Exportar Histórico
                        </button>
                        <button class="btn btn-info" onclick="filterUserHistory('${productId}')">
                            <i class="fas fa-filter"></i> Filtrar por Usuário
                        </button>
                    </div>

                    <div id="userHistoryContainer">
                        ${renderUserHistory(userHistory)}
                    </div>

                    <div class="footer-actions" style="margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Função para buscar histórico de usuários
        function getUserHistory(productId) {
            const allRevisions = estruturas.filter(e => e.produtoPaiId === productId);
            const allUserChanges = [];

            allRevisions.forEach(revision => {
                if (revision.historicoUsuarios) {
                    allUserChanges.push(...revision.historicoUsuarios);
                }

                // Adicionar também o usuário da última alteração se não estiver no histórico
                if (revision.usuarioUltimaAlteracao && !revision.historicoUsuarios?.some(h =>
                    h.id === revision.usuarioUltimaAlteracao.id &&
                    h.dataAlteracao === revision.usuarioUltimaAlteracao.dataAlteracao
                )) {
                    allUserChanges.push({
                        id: revision.usuarioUltimaAlteracao.id,
                        nome: revision.usuarioUltimaAlteracao.nome,
                        email: revision.usuarioUltimaAlteracao.email,
                        dataAlteracao: revision.dataUltimaAlteracao,
                        revisao: revision.revisaoAtual || 0,
                        tipoAlteracao: 'MODIFICACAO',
                        ip: revision.usuarioUltimaAlteracao.ip || 'N/A',
                        userAgent: revision.usuarioUltimaAlteracao.userAgent || 'N/A'
                    });
                }
            });

            // Ordenar por data (mais recente primeiro)
            allUserChanges.sort((a, b) => new Date(b.dataAlteracao) - new Date(a.dataAlteracao));

            // Calcular estatísticas
            const uniqueUsers = [...new Set(allUserChanges.map(change => change.id))].length;
            const firstChange = allUserChanges.length > 0 ?
                new Date(allUserChanges[allUserChanges.length - 1].dataAlteracao).toLocaleDateString('pt-BR') : 'N/A';
            const lastChange = allUserChanges.length > 0 ?
                new Date(allUserChanges[0].dataAlteracao).toLocaleDateString('pt-BR') : 'N/A';

            return {
                changes: allUserChanges,
                totalChanges: allUserChanges.length,
                uniqueUsers: uniqueUsers,
                firstChange: firstChange,
                lastChange: lastChange
            };
        }

        // Função para renderizar histórico de usuários
        function renderUserHistory(userHistory) {
            if (userHistory.changes.length === 0) {
                return `
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary); background-color: #f8f9fa; border-radius: 6px;">
                        <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <h3>Nenhum histórico encontrado</h3>
                        <p>Esta estrutura ainda não possui histórico de usuários.</p>
                    </div>
                `;
            }

            let html = `
                <div class="user-history-timeline">
                    <div class="timeline-header">
                        <h4><i class="fas fa-clock"></i> Linha do Tempo de Alterações</h4>
                    </div>
            `;

            userHistory.changes.forEach((change, index) => {
                const changeDate = new Date(change.dataAlteracao).toLocaleString('pt-BR');
                const isRecent = index < 3; // Destacar as 3 mais recentes

                html += `
                    <div class="user-change-item ${isRecent ? 'recent-change' : ''}" data-user-id="${change.id}">
                        <div class="change-marker">
                            <div class="change-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            ${isRecent ? '<i class="fas fa-star recent-star" title="Alteração Recente"></i>' : ''}
                        </div>

                        <div class="change-content">
                            <div class="change-header">
                                <h5>
                                    ${change.nome}
                                    <span class="change-type-badge ${change.tipoAlteracao.toLowerCase()}">${change.tipoAlteracao}</span>
                                </h5>
                                <div class="change-actions">
                                    <button class="btn btn-sm btn-info" onclick="viewChangeDetails('${change.id}', '${change.dataAlteracao}')" title="Ver Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="change-info">
                                <div class="change-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-envelope"></i>
                                        <span>${change.email}</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>${changeDate}</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-code-branch"></i>
                                        <span>REV${String(change.revisao || 0).padStart(3, '0')}</span>
                                    </div>
                                    ${change.ip !== 'N/A' ? `
                                        <div class="stat-item">
                                            <i class="fas fa-globe"></i>
                                            <span>${change.ip}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            ${change.alteracoes ? `
                                <div class="change-summary">
                                    <strong>Resumo das Alterações:</strong>
                                    <div class="summary-grid">
                                        ${change.alteracoes.componentes.adicionados > 0 ? `<span class="summary-item added">+${change.alteracoes.componentes.adicionados} componentes</span>` : ''}
                                        ${change.alteracoes.componentes.removidos > 0 ? `<span class="summary-item removed">-${change.alteracoes.componentes.removidos} componentes</span>` : ''}
                                        ${change.alteracoes.componentes.modificados > 0 ? `<span class="summary-item modified">${change.alteracoes.componentes.modificados} componentes alterados</span>` : ''}
                                        ${change.alteracoes.operacoes.adicionadas > 0 ? `<span class="summary-item added">+${change.alteracoes.operacoes.adicionadas} operações</span>` : ''}
                                        ${change.alteracoes.operacoes.removidas > 0 ? `<span class="summary-item removed">-${change.alteracoes.operacoes.removidas} operações</span>` : ''}
                                        ${change.alteracoes.operacoes.modificadas > 0 ? `<span class="summary-item modified">${change.alteracoes.operacoes.modificadas} operações alteradas</span>` : ''}
                                    </div>
                                    ${change.alteracoes.detalhes.length > 0 ? `
                                        <div class="change-details">
                                            <strong>Detalhes:</strong>
                                            <ul>
                                                ${change.alteracoes.detalhes.slice(0, 3).map(detail => `<li>${detail}</li>`).join('')}
                                                ${change.alteracoes.detalhes.length > 3 ? `<li><em>... e mais ${change.alteracoes.detalhes.length - 3} alterações</em></li>` : ''}
                                            </ul>
                                        </div>
                                    ` : ''}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
            `;

            return html;
        }

        // Função para exportar histórico de usuários
        window.exportUserHistory = function(productId) {
            const produto = produtos.find(p => p.id === productId);
            const userHistory = getUserHistory(productId);

            if (!produto || userHistory.changes.length === 0) {
                alert('Nenhum histórico encontrado para exportar.');
                return;
            }

            try {
                const reportData = {
                    produto: {
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        tipo: produto.tipo
                    },
                    resumo: {
                        totalAlteracoes: userHistory.totalChanges,
                        usuariosUnicos: userHistory.uniqueUsers,
                        primeiraAlteracao: userHistory.firstChange,
                        ultimaAlteracao: userHistory.lastChange
                    },
                    historico: userHistory.changes.map(change => ({
                        usuario: {
                            id: change.id,
                            nome: change.nome,
                            email: change.email
                        },
                        dataAlteracao: change.dataAlteracao,
                        revisao: change.revisao,
                        tipoAlteracao: change.tipoAlteracao,
                        ip: change.ip,
                        userAgent: change.userAgent,
                        alteracoes: change.alteracoes
                    })),
                    dataExportacao: new Date().toISOString()
                };

                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `historico-usuarios-${produto.codigo}-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                alert('Histórico de usuários exportado com sucesso!');

            } catch (error) {
                console.error('Erro ao exportar histórico:', error);
                alert('Erro ao exportar histórico: ' + error.message);
            }
        };
    </script>
</body>
</html>