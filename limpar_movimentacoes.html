<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpar Movimentações</title>
    <script type="module" src="js/main.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background-color: #c82333;
        }
        #status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Limpar Movimentações de Armazém</h1>

        <div class="warning">
            <strong>Atenção!</strong> Esta operação irá:
            <ul>
                <li>Excluir todas as transferências entre armazéns</li>
                <li>Excluir todas as movimentações de estoque</li>
            </ul>
            Esta ação não pode ser desfeita!
        </div>

        <button onclick="confirmarLimpeza()">Limpar Todas as Movimentações</button>

        <div id="status"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, deleteDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        window.confirmarLimpeza = async function() {
            if (!confirm('Tem certeza que deseja limpar todas as movimentações? Esta ação não pode ser desfeita!')) {
                return;
            }

            const statusDiv = document.getElementById('status');
            statusDiv.className = '';
            statusDiv.textContent = 'Iniciando limpeza...';

            try {
                // Coleções a serem limpas
                const colecoes = [
                    "transferenciasArmazem",
                    "movimentacoesEstoque"
                ];

                for (const colecao of colecoes) {
                    statusDiv.textContent = `Limpando coleção: ${colecao}...`;
                    const snapshot = await getDocs(collection(db, colecao));

                    // Deletar cada documento
                    const deletePromises = snapshot.docs.map(doc => 
                        deleteDoc(doc.ref)
                    );

                    await Promise.all(deletePromises);
                }

                statusDiv.className = 'success';
                statusDiv.textContent = 'Todas as movimentações foram limpas com sucesso!';
            } catch (error) {
                console.error("Erro ao limpar movimentações:", error);
                statusDiv.className = 'error';
                statusDiv.textContent = 'Erro ao limpar movimentações: ' + error.message;
            }
        };
    </script>
</body>
</html> 