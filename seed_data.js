
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./database.sqlite');

const usuarios = [
  ['Admin', '<EMAIL>', '123456'],
  ['Usuario', '<EMAIL>', '123456']
];

const produtos = [
  ['P001', 'Produto Teste 1', 'PRODUTO', 'UN'],
  ['P002', 'Produto Teste 2', 'MATERIA_PRIMA', 'KG']
];

db.serialize(() => {
  // Inserir usuários
  const stmtUser = db.prepare('INSERT INTO usuarios (nome, email, senha) VALUES (?, ?, ?)');
  usuarios.forEach(user => stmtUser.run(user));
  stmtUser.finalize();

  // Inserir produtos
  const stmtProd = db.prepare('INSERT INTO produtos (codigo, descricao, tipo, unidade) VALUES (?, ?, ?, ?)');
  produtos.forEach(prod => stmtProd.run(prod));
  stmtProd.finalize();

  console.log('Dados de teste inseridos com sucesso!');
  db.close();
});
