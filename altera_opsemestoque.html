<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Alterar Ordem de Produção</title>
  <style>
    body { background: #f4f5f7; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; }
    .header-bar {
      background: #3a4a5d;
      color: #fff;
      padding: 18px 24px;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 12px 0 12px;
    }
    .header-bar h1 { font-size: 26px; font-weight: 500; margin: 0; }
    .header-bar button {
      background: #fff;
      color: #233047;
      border: none;
      border-radius: 5px;
      padding: 7px 18px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s;
    }
    .header-bar button:hover { background: #e0e3e7; }
    .card {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      margin: 0 12px 24px 12px;
      padding: 28px 24px 24px 24px;
    }
    .section-title {
      color: #1761a0;
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 18px;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 8px;
    }
    .form-group { margin-bottom: 18px; }
    label { display: block; font-weight: 500; color: #233047; margin-bottom: 6px; }
    input[type="text"], input[type="number"], input[type="date"], select, textarea {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d4d4d4;
      border-radius: 5px;
      font-size: 15px;
      background: #fff;
      box-sizing: border-box;
      transition: border 0.2s;
    }
    input:focus, select:focus, textarea:focus {
      border-color: #1761a0;
      outline: none;
    }
    textarea { resize: vertical; min-height: 60px; }
    .search-row { display: flex; gap: 10px; margin-bottom: 18px; }
    .search-row input { flex: 1; }
    .op-list { background: #f0f3f6; border-radius: 6px; margin-top: 8px; max-height: 180px; overflow-y: auto; border: 1px solid #d4d4d4; }
    .op-item { padding: 10px 14px; cursor: pointer; border-bottom: 1px solid #e0e0e0; }
    .op-item:last-child { border-bottom: none; }
    .op-item:hover, .op-item.selected { background: #e6f0fa; }
    .componentes-section { margin-top: 18px; }
    .component-list { margin-top: 8px; }
    .component-item { display: flex; gap: 8px; align-items: center; margin-bottom: 8px; }
    .component-item input[type="text"] { width: 120px; }
    .component-item input[type="number"] { width: 80px; }
    .component-item select { width: 70px; }
    .remove-btn { background: #bb0000; color: #fff; border: none; border-radius: 4px; padding: 6px 10px; cursor: pointer; }
    .remove-btn:hover { background: #a30000; }
    .add-btn { background: #1761a0; color: #fff; border: none; border-radius: 4px; padding: 8px 18px; cursor: pointer; margin-top: 8px; font-size: 15px; font-weight: 500; }
    .add-btn:hover { background: #0a4d8c; }
    .actions { display: flex; gap: 12px; margin-top: 24px; }
    .actions button { padding: 10px 22px; border-radius: 4px; font-size: 16px; font-weight: 500; border: none; cursor: pointer; }
    .save-btn { background: #107e3e; color: #fff; }
    .save-btn:hover { background: #0d6e36; }
    .cancel-btn { background: #6c757d; color: #fff; }
    .cancel-btn:hover { background: #5a6268; }

    /* Modal styles */
    .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
    .modal-content { background-color: #fff; margin: 5% auto; padding: 20px; border-radius: 8px; width: 80%; max-width: 800px; max-height: 80vh; overflow-y: auto; }
    .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #e0e0e0; padding-bottom: 10px; }
    .modal-title { font-size: 20px; font-weight: 600; color: #1761a0; }
    .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    .close:hover { color: #000; }
    .revision-item { padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 10px; cursor: pointer; transition: all 0.2s; }
    .revision-item:hover { background-color: #f0f3f6; border-color: #1761a0; }
    .revision-item.selected { background-color: #e6f0fa; border-color: #1761a0; }
    .revision-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .revision-number { font-weight: 600; color: #1761a0; font-size: 16px; }
    .revision-date { color: #666; font-size: 14px; }
    .revision-details { font-size: 14px; color: #333; }
    .revision-changes { margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 13px; }
    .op-selection { margin-bottom: 20px; }
    .op-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; max-height: 300px; overflow-y: auto; }
    .op-card { padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; cursor: pointer; transition: all 0.2s; }
    .op-card:hover { background-color: #f0f3f6; border-color: #1761a0; }
    .op-card.selected { background-color: #e6f0fa; border-color: #1761a0; }
    .op-number { font-weight: 600; color: #1761a0; margin-bottom: 5px; }
    .op-product { font-size: 14px; color: #333; margin-bottom: 3px; }
    .op-status { font-size: 12px; color: #666; }
    .progress-bar { width: 100%; height: 4px; background: #e0e0e0; border-radius: 2px; margin-top: 15px; }
    .progress-fill { height: 100%; background: #1761a0; border-radius: 2px; transition: width 0.3s; }

    /* Comparison styles */
    .comparison-section { display: none; margin-bottom: 20px; }
    .comparison-header { text-align: center; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; }
    .comparison-title { font-size: 18px; font-weight: 600; color: #1761a0; margin-bottom: 10px; }
    .comparison-subtitle { color: #666; font-size: 14px; }
    .comparison-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-height: 500px; overflow-y: auto; }
    .comparison-side { border: 1px solid #e0e0e0; border-radius: 6px; padding: 15px; }
    .comparison-side.old { background: #fff5f5; border-color: #fecaca; }
    .comparison-side.new { background: #f0fdf4; border-color: #bbf7d0; }
    .side-title { font-weight: 600; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #e0e0e0; }
    .side-title.old { color: #dc2626; }
    .side-title.new { color: #16a34a; }
    .materials-table { width: 100%; border-collapse: collapse; font-size: 13px; }
    .materials-table th { background: #f8f9fa; padding: 8px; text-align: left; border-bottom: 1px solid #e0e0e0; font-weight: 600; }
    .materials-table td { padding: 8px; border-bottom: 1px solid #f0f0f0; }
    .materials-table tr:hover { background: #f8f9fa; }
    .quantity-change { font-weight: 600; }
    .quantity-increase { color: #16a34a; }
    .quantity-decrease { color: #dc2626; }
    .quantity-same { color: #6b7280; }
    .material-new { background: #f0fdf4; }
    .material-removed { background: #fff5f5; text-decoration: line-through; opacity: 0.7; }
    .summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px; }
    .summary-card { text-align: center; padding: 10px; border-radius: 4px; border: 1px solid #e0e0e0; }
    .summary-card.old { background: #fff5f5; border-color: #fecaca; }
    .summary-card.new { background: #f0fdf4; border-color: #bbf7d0; }
    .summary-number { font-size: 18px; font-weight: 600; }
    .summary-label { font-size: 12px; color: #666; margin-top: 5px; }
    .comparison-actions { text-align: center; margin-top: 20px; }
    .approve-btn { background: #16a34a; color: white; padding: 10px 20px; border: none; border-radius: 4px; font-weight: 600; cursor: pointer; margin-right: 10px; }
    .approve-btn:hover { background: #15803d; }
    .reject-btn { background: #dc2626; color: white; padding: 10px 20px; border: none; border-radius: 4px; font-weight: 600; cursor: pointer; }
    .reject-btn:hover { background: #b91c1c; }
    .current-revision { background: #e3f2fd !important; border-color: #1976d2 !important; }
    .cascade-section { margin-top: 20px; }
    .cascade-header { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 15px; }
    .cascade-title { color: #856404; margin: 0 0 10px 0; font-size: 16px; font-weight: 600; }
    .cascade-subtitle { color: #856404; margin: 0; font-size: 14px; }
    .cascade-ops { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
    .cascade-op { background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 6px; padding: 15px; }
    .cascade-op-header { font-weight: 600; color: #1761a0; margin-bottom: 10px; }
    .cascade-op-details { font-size: 14px; color: #666; }
    .cascade-sp-list { margin-top: 10px; }
    .cascade-sp-item { background: #e3f2fd; padding: 8px; border-radius: 4px; margin-bottom: 5px; font-size: 13px; }

    @media (max-width: 600px) {
      .container, .card { padding: 10px 2px; }
      .modal-content { width: 95%; margin: 10% auto; }
      .comparison-container { grid-template-columns: 1fr; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <h1>Alterar Ordem de Produção</h1>
    <div>
      <button onclick="openRecalculateModal()" style="background: #1761a0; color: white; margin-right: 10px;">🔄 Recalcular por Revisão</button>
      <button onclick="window.location.href='ordens_producao.html'">Voltar</button>
    </div>
  </div>
  <div class="card">
    <div class="section-title">Dados da Ordem de Produção</div>
    <div id="solicitacaoInfo" style="margin-bottom: 16px; font-weight: 500; color: #1761a0;"></div>
    <div class="search-row">
      <input type="text" id="buscaProduto" placeholder="Buscar por código..." autocomplete="off" oninput="filtrarOPsPorProduto()">
    </div>
    <div id="opList" class="op-list" style="display:none;"></div>
    <form id="editOpForm" style="display:none;">
      <div class="form-group">
        <label for="numeroOP">Número da OP</label>
        <input type="text" id="numeroOP" readonly>
      </div>
      <div class="form-group">
        <label for="produto">Produto</label>
        <input type="text" id="produto" readonly>
      </div>
      <div class="form-group">
        <label for="quantidade">Quantidade Necessária</label>
        <input type="number" id="quantidade" min="0.001" step="0.001" required>
      </div>
      <div class="form-group">
        <label for="dataEntrega">Data de Entrega</label>
        <input type="date" id="dataEntrega" required>
      </div>
      <div class="form-group">
        <label for="prioridade">Prioridade</label>
        <select id="prioridade" required>
          <option value="Baixa">Baixa</option>
          <option value="Média">Média</option>
          <option value="Alta">Alta</option>
        </select>
      </div>
      <div class="form-group">
        <label for="observacoes">Observações</label>
        <textarea id="observacoes"></textarea>
      </div>
      <div class="componentes-section">
        <label>Componentes e Consumo</label>
        <div id="componentList" class="component-list"></div>
        <button type="button" class="add-btn" onclick="addComponent()">Adicionar Componente</button>
      </div>
      <div class="actions">
        <button type="submit" class="save-btn">Salvar Alterações</button>
        <button type="button" class="cancel-btn" onclick="window.location.reload()">Cancelar</button>
      </div>
    </form>
  </div>

  <!-- Modal de Recálculo por Revisão -->
  <div id="recalculateModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">🔄 Recalcular OPs por Revisão de Estrutura</h2>
        <span class="close" onclick="closeRecalculateModal()">&times;</span>
      </div>

      <div class="op-selection">
        <h3>📋 Ordem de Produção Selecionada</h3>
        <div id="currentOPInfo" style="padding: 15px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e0e0e0; margin-bottom: 20px;">
          <div style="color: #666;">Nenhuma OP selecionada. Busque e selecione uma OP primeiro.</div>
        </div>
      </div>

      <div class="revision-selection" style="margin-bottom: 20px;">
        <h3>🔄 Selecione a Revisão da Estrutura</h3>
        <p style="color: #666; margin-bottom: 15px;">Escolha qual revisão da estrutura deve ser usada para recalcular a OP atual:</p>
        <div id="revisionList"></div>
      </div>

      <div class="comparison-section" id="comparisonSection">
        <div class="comparison-header">
          <div class="comparison-title">📊 Comparação: Estrutura Atual vs Nova Revisão</div>
          <div class="comparison-subtitle">Analise as diferenças antes de aprovar o recálculo</div>
        </div>

        <div id="comparisonContent"></div>

        <!-- Seção de Análise de Cascata -->
        <div id="cascadeAnalysisSection" style="display: none; margin-top: 20px;">
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 15px;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ Impacto em Cascata Detectado</h4>
            <p style="color: #856404; margin: 0; font-size: 14px;">
              Esta alteração afeta Sub-Produtos (SP) que são usados em outras OPs abertas.
              O recálculo será expandido automaticamente para manter a consistência.
            </p>
          </div>
          <div id="cascadeContent"></div>
        </div>

        <div class="comparison-actions">
          <button class="approve-btn" onclick="approveRecalculation()">✅ Aprovar Recálculo</button>
          <button class="reject-btn" onclick="rejectComparison()">❌ Cancelar</button>
        </div>
      </div>

      <div class="progress-section" style="display: none;">
        <h3>3. Progresso do Recálculo</h3>
        <div class="progress-bar">
          <div id="progressFill" class="progress-fill" style="width: 0%;"></div>
        </div>
        <div id="progressText" style="text-align: center; margin-top: 10px; color: #666;"></div>
        <div id="progressLog" style="margin-top: 15px; max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
      </div>

      <div class="modal-actions" style="margin-top: 20px; text-align: right;">
        <button id="showComparison" onclick="showComparison()" class="save-btn" disabled>Ver Comparação</button>
        <button id="startRecalculate" onclick="startRecalculation()" class="save-btn" disabled style="display: none;">Recalcular OP</button>
        <button onclick="closeRecalculateModal()" class="cancel-btn">Cancelar</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, doc, updateDoc, addDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensProducao = [];
    let produtos = [];
    let produtosMap = {};
    let opSelecionada = null;
    let estruturas = [];
    let selectedOPs = [];
    let selectedRevision = null;
    let cascadeAnalysis = null;

    // Função para determinar a unidade correta baseada no produto
    function getCorrectUnit(op) {
      const produto = produtosMap[op.produtoId];

      // Se o produto tem unidade definida, usar ela
      if (produto && produto.unidade) {
        return produto.unidade;
      }

      // Se a OP tem unidade definida, verificar se faz sentido
      if (op.unidade) {
        // Para produtos acabados (PA), geralmente é UN ou PC
        if (produto && produto.tipo === 'PA') {
          // Se está como KG mas é produto acabado, provavelmente deveria ser UN
          if (op.unidade === 'KG' && produto.descricao &&
              (produto.descricao.includes('ESTEIRA') ||
               produto.descricao.includes('EQUIPAMENTO') ||
               produto.descricao.includes('MÁQUINA'))) {
            return 'UN';
          }
        }
        return op.unidade;
      }

      // Padrão baseado no tipo de produto
      if (produto) {
        switch (produto.tipo) {
          case 'PA': // Produto Acabado
            return 'UN';
          case 'SP': // Sub-Produto
            return 'UN';
          case 'MP': // Matéria Prima
            return 'KG';
          default:
            return 'UN';
        }
      }

      return 'UN'; // Padrão
    }

    window.onload = async function() {
      try {
        const [opsSnap, produtosSnap, estruturasSnap] = await Promise.all([
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas"))
        ]);
        ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtosMap = Object.fromEntries(produtos.map(p => [p.id, p]));
      } catch (error) {
        alert("Erro ao carregar dados: " + error.message);
      }
    };

    window.filtrarOPsPorProduto = function() {
      const termo = document.getElementById('buscaProduto').value.trim().toUpperCase();
      const opList = document.getElementById('opList');
      if (!termo) { opList.style.display = 'none'; return; }
      // Filtra produtos
      const produtosFiltrados = produtos.filter(p => p.codigo?.toUpperCase().includes(termo));
      if (produtosFiltrados.length === 0) { opList.innerHTML = '<div class="op-item">Nenhum produto encontrado.</div>'; opList.style.display = 'block'; return; }
      // Filtra OPs abertas relacionadas
      let opsRelacionadas = [];
      produtosFiltrados.forEach(prod => {
        opsRelacionadas = opsRelacionadas.concat(ordensProducao.filter(op => op.produtoId === prod.id && op.status !== 'Concluída' && op.status !== 'Cancelada'));
      });
      if (opsRelacionadas.length === 0) { opList.innerHTML = '<div class="op-item">Nenhuma OP aberta para este produto.</div>'; opList.style.display = 'block'; return; }
      opList.innerHTML = opsRelacionadas.map(op => {
        const prod = produtosMap[op.produtoId];
        const numero = op.numeroOP || op.numero || 'Sem número';
        const status = op.status;
        const quantidade = op.quantidade;
        const dataEntrega = op.dataEntrega ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : '';
        return `<div class='op-item' onclick='window.selecionarOP("${op.id}")'><strong>${numero}</strong> - ${prod?.codigo || ''} - ${prod?.descricao || ''} - ${status} - Qtde: ${quantidade} - Entrega: ${dataEntrega}</div>`;
      }).join('');
      opList.style.display = 'block';
    };

    window.selecionarOP = async function(opId) {
      opSelecionada = ordensProducao.find(op => op.id === opId);
      if (!opSelecionada) return;
      document.getElementById('opList').style.display = 'none';
      document.getElementById('editOpForm').style.display = 'block';
      const prod = produtosMap[opSelecionada.produtoId];
      document.getElementById('numeroOP').value = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
      document.getElementById('produto').value = `${prod?.codigo || ''} - ${prod?.descricao || ''}`;
      document.getElementById('quantidade').value = opSelecionada.quantidade;
      document.getElementById('dataEntrega').value = opSelecionada.dataEntrega ? new Date(opSelecionada.dataEntrega.seconds * 1000).toISOString().split('T')[0] : '';
      document.getElementById('prioridade').value = opSelecionada.prioridade || 'Média';
      document.getElementById('observacoes').value = opSelecionada.observacoes || '';
      renderComponentes(opSelecionada.materiaisNecessarios || []);

      // Buscar solicitação de compra vinculada
      const solicitacaoInfo = document.getElementById('solicitacaoInfo');
      solicitacaoInfo.textContent = 'Verificando solicitação de compra...';
      try {
        const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const q = query(collection(db, 'solicitacoesCompra'), where('opId', '==', opSelecionada.id));
        const snap = await getDocs(q);
        if (!snap.empty) {
          const docData = snap.docs[0].data();
          solicitacaoInfo.textContent = `Solicitação de Compra: ${docData.numero || snap.docs[0].id}`;
        } else {
          solicitacaoInfo.textContent = 'Nenhuma solicitação de compra gerada para esta OP.';
        }
      } catch (e) {
        solicitacaoInfo.textContent = 'Erro ao buscar solicitação de compra.';
      }
    };

    window.renderComponentes = function(componentes) {
      const compList = document.getElementById('componentList');
      compList.innerHTML = '';
      componentes.forEach((comp, idx) => {
        const prod = produtosMap[comp.produtoId];
        compList.innerHTML += `
          <div class="component-item">
            <input type="text" class="busca-comp" placeholder="Código..." value="${prod?.codigo || ''}" oninput="window.buscarComponentePorCodigo(this, ${idx})">
            <input type="text" class="desc-comp" value="${prod?.descricao || ''}" readonly style="width: 220px; background: #f7f7f7; border: 1px solid #e0e0e0; color: #333;">
            <select onchange="window.atualizarComponenteSelect(this, ${idx})">
              ${produtos.map(p => `<option value="${p.id}" ${p.id === comp.produtoId ? 'selected' : ''}>${p.codigo} - ${p.descricao}</option>`).join('')}
            </select>
            <input type="number" min="0.001" step="0.001" value="${comp.quantidade}">
            <select class="unidade">
              <option value="PC" ${comp.unidade === 'PC' ? 'selected' : ''}>PC</option>
              <option value="KG" ${comp.unidade === 'KG' ? 'selected' : ''}>KG</option>
            </select>
            <button type="button" class="remove-btn" onclick="window.removerComponente(${idx})">Remover</button>
          </div>
        `;
      });
    };

    window.addComponent = function() {
      const compList = document.getElementById('componentList');
      const idx = compList.children.length;
      compList.innerHTML += `
        <div class="component-item">
          <input type="text" class="busca-comp" placeholder="Código..." oninput="window.buscarComponentePorCodigo(this, ${idx})">
          <input type="text" class="desc-comp" value="" readonly style="width: 220px; background: #f7f7f7; border: 1px solid #e0e0e0; color: #333;">
          <select onchange="window.atualizarComponenteSelect(this, ${idx})">
            ${produtos.map(p => `<option value="${p.id}">${p.codigo} - ${p.descricao}</option>`).join('')}
          </select>
          <input type="number" min="0.001" step="0.001" value="1">
          <select class="unidade">
            <option value="PC">PC</option>
            <option value="KG" selected>KG</option>
          </select>
          <button type="button" class="remove-btn" onclick="window.removerComponente(${idx})">Remover</button>
        </div>
      `;
    };

    window.buscarComponentePorCodigo = function(input, idx) {
      const valor = input.value.trim().toUpperCase();
      const select = input.nextElementSibling.nextElementSibling;
      const descInput = input.nextElementSibling;
      if (!valor) { descInput.value = ''; return; }
      const option = Array.from(select.options).find(opt => opt.textContent.toUpperCase().startsWith(valor));
      if (option) {
        select.value = option.value;
        const prod = produtosMap[option.value];
        if (prod) descInput.value = prod.descricao;
      } else {
        descInput.value = '';
      }
    };

    window.atualizarComponenteSelect = function(select, idx) {
      const input = select.previousElementSibling.previousElementSibling;
      const descInput = select.previousElementSibling;
      const prod = produtosMap[select.value];
      if (prod) {
        input.value = prod.codigo;
        descInput.value = prod.descricao;
      }
    };

    window.removerComponente = function(idx) {
      const compList = document.getElementById('componentList');
      compList.removeChild(compList.children[idx]);
      // Re-render para corrigir os índices dos botões
      const componentes = [];
      Array.from(compList.children).forEach((div, i) => {
        const select = div.querySelector('select');
        const input = div.querySelector('input[type="text"]');
        const qtd = div.querySelector('input[type="number"]');
        const unidade = div.querySelector('select.unidade');
        componentes.push({ produtoId: select.value, quantidade: parseFloat(qtd.value), unidade: unidade.value });
      });
      renderComponentes(componentes);
    };

    document.getElementById('editOpForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      if (!opSelecionada) return;

      const novaQuantidade = parseFloat(document.getElementById('quantidade').value);
      const dataEntrega = document.getElementById('dataEntrega').value;
      const prioridade = document.getElementById('prioridade').value;
      const observacoes = document.getElementById('observacoes').value;

      // Validações
      if (novaQuantidade <= 0) {
        alert('Quantidade deve ser maior que zero!');
        return;
      }

      if (!dataEntrega) {
        alert('Data de entrega é obrigatória!');
        return;
      }

      // Confirmar alteração se quantidade mudou significativamente
      const quantidadeOriginal = opSelecionada.quantidade;
      const percentualMudanca = Math.abs((novaQuantidade - quantidadeOriginal) / quantidadeOriginal) * 100;

      if (percentualMudanca > 20) {
        if (!confirm(`ATENÇÃO: A quantidade está sendo alterada em ${percentualMudanca.toFixed(1)}%.\n\nIsso irá:\n• Recalcular todos os materiais necessários\n• Atualizar solicitações de compra vinculadas\n• Recalcular empenhos de estoque\n\nConfirma a alteração?`)) {
          return;
        }
      }

      try {
        // Mostrar progresso
        const btnSalvar = document.querySelector('.save-btn');
        const textoOriginal = btnSalvar.textContent;
        btnSalvar.textContent = 'Processando...';
        btnSalvar.disabled = true;

        // 1. Buscar estrutura do produto para recalcular materiais
        const estruturaSnap = await getDocs(collection(db, "estruturasProduto"));
        const estrutura = estruturaSnap.docs.find(doc => doc.data().produtoId === opSelecionada.produtoId);

        let materiaisRecalculados = [];

        if (estrutura) {
          // Recalcular materiais baseado na estrutura e nova quantidade
          btnSalvar.textContent = 'Recalculando materiais...';
          materiaisRecalculados = await recalcularMateriaisNecessarios(estrutura.data(), novaQuantidade);
        } else {
          // Usar componentes manuais se não há estrutura
          Array.from(document.getElementById('componentList').children).forEach(div => {
            const select = div.querySelector('select');
            const qtdUnitaria = parseFloat(div.querySelector('input[type="number"]').value);
            const unidade = div.querySelector('select.unidade');
            const qtdTotal = qtdUnitaria * novaQuantidade;

            materiaisRecalculados.push({
              produtoId: select.value,
              quantidade: qtdTotal,
              quantidadeUnitaria: qtdUnitaria,
              unidade: unidade.value
            });
          });
        }

        // 2. Verificar disponibilidade de estoque para novos materiais
        btnSalvar.textContent = 'Verificando estoque...';
        const verificacaoEstoque = await verificarDisponibilidadeEstoque(materiaisRecalculados, opSelecionada.armazemProducaoId);

        // 3. Liberar empenhos antigos
        btnSalvar.textContent = 'Atualizando empenhos...';
        if (opSelecionada.materiaisNecessarios) {
          await liberarEmpenhosAntigos(opSelecionada.materiaisNecessarios, opSelecionada.armazemProducaoId);
        }

        // 4. Criar novos empenhos
        const novosEmpenhos = await criarNovosEmpenhos(materiaisRecalculados, opSelecionada.armazemProducaoId);

        // 5. Atualizar ordem de produção
        btnSalvar.textContent = 'Salvando alterações...';
        const opRef = doc(db, "ordensProducao", opSelecionada.id);
        await updateDoc(opRef, {
          quantidade: novaQuantidade,
          unidade: getCorrectUnit(opSelecionada),
          dataEntrega: new Date(dataEntrega),
          prioridade,
          observacoes,
          materiaisNecessarios: novosEmpenhos,
          dataAlteracao: new Date(),
          alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          historicoAlteracoes: [...(opSelecionada.historicoAlteracoes || []), {
            data: new Date(),
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
            alteracoes: {
              quantidadeAnterior: quantidadeOriginal,
              quantidadeNova: novaQuantidade,
              materiaisRecalculados: materiaisRecalculados.length
            }
          }]
        });

        // 6. Atualizar solicitações de compra vinculadas
        btnSalvar.textContent = 'Atualizando solicitações...';
        await atualizarSolicitacoesVinculadas(opSelecionada.id, materiaisRecalculados);

        // 7. Registrar log de alteração
        await registrarLogAlteracao(opSelecionada, {
          quantidadeAnterior: quantidadeOriginal,
          quantidadeNova: novaQuantidade,
          materiaisRecalculados: materiaisRecalculados.length,
          verificacaoEstoque
        });

        alert(`✅ Ordem de produção atualizada com sucesso!\n\n📊 Resumo das alterações:\n• Quantidade: ${quantidadeOriginal} → ${novaQuantidade}\n• Materiais recalculados: ${materiaisRecalculados.length}\n• Empenhos atualizados: ${novosEmpenhos.length}\n• Solicitações atualizadas: Sim`);

        window.location.reload();

      } catch (error) {
        console.error('Erro ao atualizar OP:', error);
        alert('❌ Erro ao atualizar OP: ' + error.message);

        // Restaurar botão
        const btnSalvar = document.querySelector('.save-btn');
        btnSalvar.textContent = textoOriginal;
        btnSalvar.disabled = false;
      }
    });

    // Funções auxiliares para o processo de alteração
    async function recalcularMateriaisNecessarios(estrutura, novaQuantidade) {
      const materiais = [];

      for (const componente of estrutura.componentes) {
        const quantidadeNecessaria = componente.quantidade * novaQuantidade;

        // Buscar dados do produto componente
        const produtoSnap = await getDocs(collection(db, "produtos"));
        const produto = produtoSnap.docs.find(doc => doc.id === componente.componentId);

        if (produto) {
          materiais.push({
            produtoId: componente.componentId,
            quantidade: quantidadeNecessaria,
            quantidadeUnitaria: componente.quantidade,
            unidade: componente.unidade || produto.data().unidade || 'PC',
            codigo: produto.data().codigo,
            descricao: produto.data().descricao
          });
        }
      }

      return materiais;
    }

    async function verificarDisponibilidadeEstoque(materiais, armazemId) {
      const verificacao = {
        totalItens: materiais.length,
        itensDisponiveis: 0,
        itensInsuficientes: 0,
        detalhes: []
      };

      for (const material of materiais) {
        try {
          const estoqueSnap = await getDocs(collection(db, "estoques"));
          const estoque = estoqueSnap.docs.find(doc =>
            doc.data().produtoId === material.produtoId &&
            doc.data().armazemId === armazemId
          );

          const saldoDisponivel = estoque ? estoque.data().saldo : 0;
          const suficiente = saldoDisponivel >= material.quantidade;

          if (suficiente) {
            verificacao.itensDisponiveis++;
          } else {
            verificacao.itensInsuficientes++;
          }

          verificacao.detalhes.push({
            produtoId: material.produtoId,
            codigo: material.codigo,
            necessario: material.quantidade,
            disponivel: saldoDisponivel,
            suficiente
          });

        } catch (error) {
          console.error(`Erro ao verificar estoque do produto ${material.produtoId}:`, error);
        }
      }

      return verificacao;
    }

    async function liberarEmpenhosAntigos(materiaisAntigos, armazemId) {
      for (const material of materiaisAntigos) {
        if (material.quantidadeReservada > 0) {
          try {
            // Buscar estoque atual
            const estoqueSnap = await getDocs(collection(db, "estoques"));
            const estoqueDoc = estoqueSnap.docs.find(doc =>
              doc.data().produtoId === material.produtoId &&
              doc.data().armazemId === armazemId
            );

            if (estoqueDoc) {
              const estoqueAtual = estoqueDoc.data();
              const novoSaldoReservado = Math.max(0, (estoqueAtual.saldoReservado || 0) - material.quantidadeReservada);

              await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                saldoReservado: novoSaldoReservado
              });
            }
          } catch (error) {
            console.error(`Erro ao liberar empenho do produto ${material.produtoId}:`, error);
          }
        }
      }
    }

    async function criarNovosEmpenhos(materiais, armazemId) {
      const empenhosAtualizados = [];

      for (const material of materiais) {
        try {
          // Buscar estoque atual
          const estoqueSnap = await getDocs(collection(db, "estoques"));
          const estoqueDoc = estoqueSnap.docs.find(doc =>
            doc.data().produtoId === material.produtoId &&
            doc.data().armazemId === armazemId
          );

          let saldoDisponivel = 0;
          let quantidadeReservada = 0;

          if (estoqueDoc) {
            const estoqueAtual = estoqueDoc.data();
            saldoDisponivel = estoqueAtual.saldo || 0;
            quantidadeReservada = Math.min(saldoDisponivel, material.quantidade);

            // Atualizar reserva no estoque
            if (quantidadeReservada > 0) {
              const novoSaldoReservado = (estoqueAtual.saldoReservado || 0) + quantidadeReservada;
              await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                saldoReservado: novoSaldoReservado
              });
            }
          }

          const necessidade = Math.max(0, material.quantidade - quantidadeReservada);

          empenhosAtualizados.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            quantidadeUnitaria: material.quantidadeUnitaria,
            unidade: material.unidade,
            saldoEstoque: saldoDisponivel,
            quantidadeReservada,
            necessidade,
            tipo: 'MP'
          });

        } catch (error) {
          console.error(`Erro ao criar empenho do produto ${material.produtoId}:`, error);

          // Adicionar mesmo com erro para não perder o material
          empenhosAtualizados.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            quantidadeUnitaria: material.quantidadeUnitaria,
            unidade: material.unidade,
            saldoEstoque: 0,
            quantidadeReservada: 0,
            necessidade: material.quantidade,
            tipo: 'MP',
            erro: error.message
          });
        }
      }

      return empenhosAtualizados;
    }

    async function atualizarSolicitacoesVinculadas(opId, materiaisRecalculados) {
      try {
        // Buscar solicitações de compra vinculadas a esta OP
        const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));
        const solicitacoesVinculadas = solicitacoesSnap.docs.filter(doc =>
          doc.data().opId === opId || doc.data().origem === `OP-${opId}`
        );

        for (const solicitacaoDoc of solicitacoesVinculadas) {
          const solicitacao = solicitacaoDoc.data();

          // Recalcular itens da solicitação baseado nos novos materiais
          const itensAtualizados = [];

          for (const material of materiaisRecalculados) {
            if (material.necessidade > 0) {
              // Buscar dados do produto
              const produtoSnap = await getDocs(collection(db, "produtos"));
              const produto = produtoSnap.docs.find(doc => doc.id === material.produtoId);

              if (produto) {
                const produtoData = produto.data();
                itensAtualizados.push({
                  produtoId: material.produtoId,
                  codigo: produtoData.codigo,
                  descricao: produtoData.descricao,
                  quantidade: material.necessidade,
                  unidade: material.unidade,
                  observacoes: `Recalculado por alteração da OP`
                });
              }
            }
          }

          // Atualizar solicitação se há itens
          if (itensAtualizados.length > 0) {
            await updateDoc(doc(db, "solicitacoesCompra", solicitacaoDoc.id), {
              itens: itensAtualizados,
              dataAlteracao: new Date(),
              alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
              observacoes: (solicitacao.observacoes || '') + `\n[${new Date().toLocaleString()}] Recalculada por alteração da OP`
            });
          }
        }

      } catch (error) {
        console.error('Erro ao atualizar solicitações vinculadas:', error);
        // Não falhar o processo principal por erro nas solicitações
      }
    }

    async function registrarLogAlteracao(opOriginal, alteracoes) {
      try {
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'ALTERACAO_OP',
          opId: opOriginal.id,
          numeroOP: opOriginal.numeroOP || opOriginal.numero,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataHora: new Date(),
          alteracoes: {
            quantidadeAnterior: alteracoes.quantidadeAnterior,
            quantidadeNova: alteracoes.quantidadeNova,
            percentualMudanca: ((alteracoes.quantidadeNova - alteracoes.quantidadeAnterior) / alteracoes.quantidadeAnterior * 100).toFixed(2),
            materiaisRecalculados: alteracoes.materiaisRecalculados,
            verificacaoEstoque: alteracoes.verificacaoEstoque
          },
          dadosOriginais: {
            quantidade: opOriginal.quantidade,
            materiaisNecessarios: opOriginal.materiaisNecessarios?.length || 0,
            status: opOriginal.status
          }
        });
      } catch (error) {
        console.error('Erro ao registrar log de alteração:', error);
        // Não falhar o processo principal por erro no log
      }
    }

    // ===== FUNÇÕES DO MODAL DE RECÁLCULO POR REVISÃO =====

    window.openRecalculateModal = function() {
      if (!opSelecionada) {
        alert('Selecione uma OP primeiro através da busca.');
        return;
      }

      selectedRevision = null;
      document.getElementById('recalculateModal').style.display = 'block';
      showCurrentOPInfo();
      loadRevisionsForCurrentOP();
      document.getElementById('showComparison').disabled = true;
    };

    window.closeRecalculateModal = function() {
      document.getElementById('recalculateModal').style.display = 'none';

      // Reset all sections
      document.querySelector('.op-selection').style.display = 'block';
      document.querySelector('.revision-selection').style.display = 'block';
      document.querySelector('.comparison-section').style.display = 'none';
      document.getElementById('cascadeAnalysisSection').style.display = 'none';
      document.querySelector('.progress-section').style.display = 'none';

      // Reset buttons
      document.getElementById('showComparison').style.display = 'inline-block';
      document.getElementById('startRecalculate').style.display = 'none';
      document.getElementById('showComparison').textContent = 'Ver Comparação';
      document.getElementById('showComparison').disabled = true;

      // Reset progress
      document.getElementById('progressFill').style.width = '0%';
      document.getElementById('progressText').textContent = '';
      document.getElementById('progressLog').innerHTML = '';

      // Clear selections
      selectedRevision = null;
      cascadeAnalysis = null;

      // Clear visual selections
      document.querySelectorAll('.revision-item.selected').forEach(item => {
        item.classList.remove('selected');
      });
    };

    function showCurrentOPInfo() {
      const currentOPInfo = document.getElementById('currentOPInfo');

      if (!opSelecionada) {
        currentOPInfo.innerHTML = '<div style="color: #666;">Nenhuma OP selecionada. Busque e selecione uma OP primeiro.</div>';
        return;
      }

      const produto = produtosMap[opSelecionada.produtoId];
      const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
      const dataEntrega = opSelecionada.dataEntrega ?
        new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';
      const revisaoAtual = opSelecionada.revisaoEstrutura || 0;

      currentOPInfo.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <div>
            <strong style="color: #1761a0;">Número da OP:</strong><br>
            <span style="font-size: 16px; font-weight: 600;">${numeroOP}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Produto:</strong><br>
            <span>${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Quantidade:</strong><br>
            <span style="font-size: 16px; font-weight: 600;">${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Status:</strong><br>
            <span style="padding: 2px 8px; background: #e3f2fd; border-radius: 12px; font-size: 12px;">${opSelecionada.status}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Entrega:</strong><br>
            <span>${dataEntrega}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Revisão Atual:</strong><br>
            <span style="padding: 2px 8px; background: #f3e5f5; border-radius: 12px; font-size: 12px;">REV${String(revisaoAtual).padStart(3, '0')}</span>
          </div>
        </div>
      `;
    }

    function loadRevisionsForCurrentOP() {
      const revisionList = document.getElementById('revisionList');

      if (!opSelecionada) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma OP selecionada.</div>';
        return;
      }

      // Buscar todas as revisões do produto da OP atual
      const revisoesProduct = estruturas
        .filter(est => est.produtoPaiId === opSelecionada.produtoId)
        .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

      if (revisoesProduct.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma revisão de estrutura encontrada para este produto.</div>';
        return;
      }

      const produto = produtosMap[opSelecionada.produtoId];
      let html = `<h4 style="margin: 15px 0 10px 0; color: #1761a0;">${produto?.codigo} - ${produto?.descricao}</h4>`;

      revisoesProduct.forEach(revisao => {
        const revNumber = String(revisao.revisaoAtual || 0).padStart(3, '0');
        const dataAlteracao = revisao.dataUltimaAlteracao ?
          new Date(revisao.dataUltimaAlteracao).toLocaleString() :
          'Data não disponível';
        const usuario = revisao.usuarioUltimaAlteracao?.nome || 'Usuário não identificado';
        const componentes = revisao.componentes?.length || 0;
        const operacoes = revisao.operacoes?.length || 0;
        const isCurrentRevision = (revisao.revisaoAtual || 0) === (opSelecionada.revisaoEstrutura || 0);

        html += `
          <div class="revision-item ${isCurrentRevision ? 'current-revision' : ''}" onclick="selectRevisionForCurrentOP('${revisao.id}')">
            <div class="revision-header">
              <span class="revision-number">REV${revNumber}</span>
              <span class="revision-date">${dataAlteracao}</span>
              ${isCurrentRevision ? '<span style="background: #e3f2fd; color: #1761a0; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">ATUAL</span>' : ''}
            </div>
            <div class="revision-details">
              <strong>Usuário:</strong> ${usuario}<br>
              <strong>Componentes:</strong> ${componentes} | <strong>Operações:</strong> ${operacoes}
            </div>
            ${revisao.motivoRevisao ? `<div class="revision-changes"><strong>Motivo:</strong> ${revisao.motivoRevisao}</div>` : ''}
          </div>
        `;
      });

      revisionList.innerHTML = html;
    }

    function loadOPsForRecalculation() {
      const opGrid = document.getElementById('opGrid');

      // Filtrar OPs abertas que têm estrutura
      const opsComEstrutura = ordensProducao.filter(op => {
        if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

        // Verificar se existe estrutura para o produto
        const temEstrutura = estruturas.some(est => est.produtoPaiId === op.produtoId);
        return temEstrutura;
      });

      if (opsComEstrutura.length === 0) {
        opGrid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; color: #666; padding: 20px;">Nenhuma OP aberta com estrutura encontrada.</div>';
        return;
      }

      opGrid.innerHTML = opsComEstrutura.map(op => {
        const produto = produtosMap[op.produtoId];
        const numeroOP = op.numeroOP || op.numero || 'Sem número';
        const dataEntrega = op.dataEntrega ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

        return `
          <div class="op-card" onclick="toggleOPSelection('${op.id}')">
            <div class="op-number">${numeroOP}</div>
            <div class="op-product">${produto?.codigo || ''} - ${produto?.descricao || ''}</div>
            <div class="op-status">Status: ${op.status} | Qtde: ${op.quantidade} | Entrega: ${dataEntrega}</div>
          </div>
        `;
      }).join('');
    }

    window.toggleOPSelection = function(opId) {
      const opCard = event.currentTarget;
      const index = selectedOPs.indexOf(opId);

      if (index > -1) {
        selectedOPs.splice(index, 1);
        opCard.classList.remove('selected');
      } else {
        selectedOPs.push(opId);
        opCard.classList.add('selected');
      }

      // Carregar revisões quando pelo menos uma OP for selecionada
      if (selectedOPs.length > 0) {
        loadRevisionsForSelectedOPs();
      } else {
        document.getElementById('revisionList').innerHTML = '';
        selectedRevision = null;
      }

      updateRecalculateButton();
    };

    function loadRevisionsForSelectedOPs() {
      const revisionList = document.getElementById('revisionList');

      // Obter produtos únicos das OPs selecionadas
      const produtosUnicos = [...new Set(selectedOPs.map(opId => {
        const op = ordensProducao.find(o => o.id === opId);
        return op?.produtoId;
      }).filter(Boolean))];

      if (produtosUnicos.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhum produto válido encontrado.</div>';
        return;
      }

      // Buscar todas as revisões dos produtos selecionados
      const revisoesDisponiveis = [];

      produtosUnicos.forEach(produtoId => {
        const produto = produtosMap[produtoId];
        const revisoesProduct = estruturas
          .filter(est => est.produtoPaiId === produtoId)
          .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

        revisoesProduct.forEach(revisao => {
          revisoesDisponiveis.push({
            ...revisao,
            produtoInfo: produto
          });
        });
      });

      if (revisoesDisponiveis.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma revisão de estrutura encontrada para os produtos selecionados.</div>';
        return;
      }

      // Agrupar por produto
      const revisoesPorProduto = {};
      revisoesDisponiveis.forEach(rev => {
        const produtoId = rev.produtoPaiId;
        if (!revisoesPorProduto[produtoId]) {
          revisoesPorProduto[produtoId] = [];
        }
        revisoesPorProduto[produtoId].push(rev);
      });

      let html = '';
      Object.entries(revisoesPorProduto).forEach(([produtoId, revisoes]) => {
        const produto = produtosMap[produtoId];
        html += `<h4 style="margin: 15px 0 10px 0; color: #1761a0;">${produto?.codigo} - ${produto?.descricao}</h4>`;

        revisoes.forEach(revisao => {
          const revNumber = String(revisao.revisaoAtual || 0).padStart(3, '0');
          const dataAlteracao = revisao.dataUltimaAlteracao ?
            new Date(revisao.dataUltimaAlteracao).toLocaleString() :
            'Data não disponível';
          const usuario = revisao.usuarioUltimaAlteracao?.nome || 'Usuário não identificado';
          const componentes = revisao.componentes?.length || 0;
          const operacoes = revisao.operacoes?.length || 0;

          html += `
            <div class="revision-item" onclick="selectRevision('${revisao.id}', '${produtoId}')">
              <div class="revision-header">
                <span class="revision-number">REV${revNumber}</span>
                <span class="revision-date">${dataAlteracao}</span>
              </div>
              <div class="revision-details">
                <strong>Usuário:</strong> ${usuario}<br>
                <strong>Componentes:</strong> ${componentes} | <strong>Operações:</strong> ${operacoes}
              </div>
              ${revisao.motivoRevisao ? `<div class="revision-changes"><strong>Motivo:</strong> ${revisao.motivoRevisao}</div>` : ''}
            </div>
          `;
        });
      });

      revisionList.innerHTML = html;
    }

    window.selectRevisionForCurrentOP = function(revisionId) {
      // Remover seleção anterior
      document.querySelectorAll('.revision-item').forEach(item => {
        item.classList.remove('selected');
      });

      // Selecionar nova revisão
      event.currentTarget.classList.add('selected');
      selectedRevision = {
        id: revisionId,
        produtoId: opSelecionada.produtoId,
        data: estruturas.find(e => e.id === revisionId)
      };

      updateRecalculateButton();
    };

    // Manter função antiga para compatibilidade
    window.selectRevision = function(revisionId, produtoId) {
      return selectRevisionForCurrentOP(revisionId);
    };

    function updateRecalculateButton() {
      const showComparisonBtn = document.getElementById('showComparison');
      const startRecalculateBtn = document.getElementById('startRecalculate');

      const hasSelections = opSelecionada && selectedRevision;

      showComparisonBtn.disabled = !hasSelections;

      if (hasSelections) {
        const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'OP';
        showComparisonBtn.textContent = `Ver Comparação - ${numeroOP}`;
      } else {
        showComparisonBtn.textContent = 'Ver Comparação';
      }
    }

    window.showComparison = async function() {
      if (!opSelecionada || !selectedRevision) {
        alert('Selecione uma OP e uma revisão de estrutura.');
        return;
      }

      // Garantir que a OP atual esteja no array selectedOPs para o processo de aprovação
      if (selectedOPs.length === 0) {
        selectedOPs = [opSelecionada.id];
      }

      try {
        // Mostrar loading
        document.getElementById('showComparison').textContent = 'Calculando...';
        document.getElementById('showComparison').disabled = true;

        // Calcular comparação para a OP atual
        const comparison = await calculateComparison(opSelecionada, selectedRevision.data);

        // Analisar impacto em cascata
        cascadeAnalysis = await analyzeCascadeImpact(opSelecionada, selectedRevision.data, comparison);

        // Mostrar seção de comparação
        document.querySelector('.comparison-section').style.display = 'block';
        renderComparison([comparison]); // Array com uma OP apenas

        // Mostrar análise de cascata se necessário
        if (cascadeAnalysis && cascadeAnalysis.hasImpact) {
          document.getElementById('cascadeAnalysisSection').style.display = 'block';
          renderCascadeAnalysis(cascadeAnalysis);
        }

        // Esconder seções anteriores e mostrar botão de aprovação
        document.querySelector('.op-selection').style.display = 'none';
        document.querySelector('.revision-selection').style.display = 'none';
        document.getElementById('showComparison').style.display = 'none';
        document.getElementById('startRecalculate').style.display = 'inline-block';
        document.getElementById('startRecalculate').disabled = false;

      } catch (error) {
        console.error('Erro ao calcular comparação:', error);
        alert('Erro ao calcular comparação: ' + error.message);
        document.getElementById('showComparison').textContent = 'Ver Comparação';
        document.getElementById('showComparison').disabled = false;
      }
    };

    async function calculateComparison(op, novaRevisao) {
      const produto = produtosMap[op.produtoId];

      // Materiais atuais da OP
      const materiaisAtuais = op.materiaisNecessarios || [];

      // Calcular novos materiais baseado na revisão
      const novosMateriaisCalculados = await calcularMateriaisComRevisao(novaRevisao, op.quantidade);

      // Buscar estrutura atual (se existir)
      const estruturaAtual = estruturas
        .filter(e => e.produtoPaiId === op.produtoId)
        .find(e => e.revisaoAtual === (op.revisaoEstrutura || 0));

      return {
        op: op,
        produto: produto,
        estruturaAtual: estruturaAtual,
        novaRevisao: novaRevisao,
        materiaisAtuais: materiaisAtuais,
        novosMateriaisCalculados: novosMateriaisCalculados,
        diferencas: calculateDifferences(materiaisAtuais, novosMateriaisCalculados)
      };
    }

    function calculateDifferences(materiaisAtuais, novosMateriaisCalculados) {
      const diferencas = {
        adicionados: [],
        removidos: [],
        alterados: [],
        mantidos: []
      };

      // Criar maps para facilitar comparação
      const atuaisMap = new Map();
      materiaisAtuais.forEach(m => {
        atuaisMap.set(m.produtoId, m);
      });

      const novosMap = new Map();
      novosMateriaisCalculados.forEach(m => {
        novosMap.set(m.produtoId, m);
      });

      // Verificar materiais novos e alterados
      novosMateriaisCalculados.forEach(novoMaterial => {
        const materialAtual = atuaisMap.get(novoMaterial.produtoId);

        if (!materialAtual) {
          // Material adicionado
          diferencas.adicionados.push({
            ...novoMaterial,
            produto: produtosMap[novoMaterial.produtoId]
          });
        } else {
          // Verificar se quantidade mudou
          const qtdAtual = materialAtual.quantidade || 0;
          const qtdNova = novoMaterial.quantidade || 0;

          if (Math.abs(qtdAtual - qtdNova) > 0.001) {
            diferencas.alterados.push({
              ...novoMaterial,
              produto: produtosMap[novoMaterial.produtoId],
              quantidadeAnterior: qtdAtual,
              quantidadeNova: qtdNova,
              diferenca: qtdNova - qtdAtual
            });
          } else {
            diferencas.mantidos.push({
              ...novoMaterial,
              produto: produtosMap[novoMaterial.produtoId],
              quantidadeAnterior: qtdAtual
            });
          }
        }
      });

      // Verificar materiais removidos
      materiaisAtuais.forEach(materialAtual => {
        if (!novosMap.has(materialAtual.produtoId)) {
          diferencas.removidos.push({
            ...materialAtual,
            produto: produtosMap[materialAtual.produtoId]
          });
        }
      });

      return diferencas;
    }

    function renderComparison(comparisons) {
      const comparisonContent = document.getElementById('comparisonContent');

      let html = '';

      comparisons.forEach((comparison, index) => {
        const { op, produto, estruturaAtual, novaRevisao, materiaisAtuais, novosMateriaisCalculados, diferencas } = comparison;

        const numeroOP = op.numeroOP || op.numero || 'Sem número';
        const revisaoAtual = estruturaAtual?.revisaoAtual || 0;
        const revisaoNova = novaRevisao.revisaoAtual || 0;

        html += `
          <div style="margin-bottom: 30px; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px;">
            <h4 style="margin: 0 0 15px 0; color: #1761a0; text-align: center;">
              ${numeroOP} - ${produto?.codigo} - ${produto?.descricao}
            </h4>

            <div class="summary-cards">
              <div class="summary-card old">
                <div class="summary-number">${materiaisAtuais.length}</div>
                <div class="summary-label">Materiais Atuais<br>REV${String(revisaoAtual).padStart(3, '0')}</div>
              </div>
              <div class="summary-card new">
                <div class="summary-number">${novosMateriaisCalculados.length}</div>
                <div class="summary-label">Novos Materiais<br>REV${String(revisaoNova).padStart(3, '0')}</div>
              </div>
              <div class="summary-card" style="background: #fef3c7; border-color: #fbbf24;">
                <div class="summary-number">${diferencas.adicionados.length}</div>
                <div class="summary-label">Adicionados</div>
              </div>
              <div class="summary-card" style="background: #fee2e2; border-color: #f87171;">
                <div class="summary-number">${diferencas.removidos.length}</div>
                <div class="summary-label">Removidos</div>
              </div>
              <div class="summary-card" style="background: #dbeafe; border-color: #60a5fa;">
                <div class="summary-number">${diferencas.alterados.length}</div>
                <div class="summary-label">Alterados</div>
              </div>
            </div>

            <div class="comparison-container">
              <div class="comparison-side old">
                <div class="side-title old">📋 Estrutura Atual (REV${String(revisaoAtual).padStart(3, '0')})</div>
                ${renderMaterialsTable(materiaisAtuais, 'old')}
              </div>

              <div class="comparison-side new">
                <div class="side-title new">🔄 Nova Revisão (REV${String(revisaoNova).padStart(3, '0')})</div>
                ${renderMaterialsTable(novosMateriaisCalculados, 'new', diferencas)}
              </div>
            </div>
          </div>
        `;
      });

      comparisonContent.innerHTML = html;
    }

    async function analyzeCascadeImpact(opAtual, novaRevisao, comparison) {
      const analysis = {
        hasImpact: false,
        affectedSPs: [],
        affectedOPs: [],
        totalOPsImpacted: 0,
        newOPsNeeded: [],
        opsToCancel: [],
        summary: {
          newSPs: 0,
          modifiedSPs: 0,
          removedSPs: 0,
          newOPsCount: 0,
          cancelOPsCount: 0
        }
      };

      try {
        // Identificar Sub-Produtos (SP) na nova estrutura
        const spsNaNovaEstrutura = [];
        if (novaRevisao.componentes) {
          for (const componente of novaRevisao.componentes) {
            const produto = produtosMap[componente.componentId];
            if (produto && produto.tipo === 'SP') {
              spsNaNovaEstrutura.push({
                produto: produto,
                quantidade: componente.quantidade,
                unidade: componente.unidade
              });
            }
          }
        }

        // Identificar SPs na estrutura atual
        const spsNaEstruturaAtual = [];
        if (opAtual.materiaisNecessarios) {
          for (const material of opAtual.materiaisNecessarios) {
            const produto = produtosMap[material.produtoId];
            if (produto && produto.tipo === 'SP') {
              spsNaEstruturaAtual.push({
                produto: produto,
                quantidade: material.quantidade,
                unidade: material.unidade
              });
            }
          }
        }

        // Analisar mudanças nos SPs
        const spsAdicionados = [];
        const spsModificados = [];
        const spsRemovidos = [];

        // Verificar SPs adicionados ou modificados
        for (const spNovo of spsNaNovaEstrutura) {
          const spAtual = spsNaEstruturaAtual.find(sp => sp.produto.id === spNovo.produto.id);

          if (!spAtual) {
            // SP adicionado
            spsAdicionados.push(spNovo);
            analysis.summary.newSPs++;
          } else if (Math.abs((spAtual.quantidade || 0) - (spNovo.quantidade || 0)) > 0.001) {
            // SP modificado
            spsModificados.push({
              ...spNovo,
              quantidadeAnterior: spAtual.quantidade,
              diferenca: spNovo.quantidade - spAtual.quantidade
            });
            analysis.summary.modifiedSPs++;
          }
        }

        // Verificar SPs removidos
        for (const spAtual of spsNaEstruturaAtual) {
          const spNovo = spsNaNovaEstrutura.find(sp => sp.produto.id === spAtual.produto.id);
          if (!spNovo) {
            spsRemovidos.push(spAtual);
            analysis.summary.removedSPs++;
          }
        }

        // Se há mudanças em SPs, buscar outras OPs afetadas
        const todosSPsAfetados = [...spsAdicionados, ...spsModificados, ...spsRemovidos];

        if (todosSPsAfetados.length > 0) {
          analysis.hasImpact = true;
          analysis.affectedSPs = todosSPsAfetados;

          // Buscar outras OPs abertas que usam estes SPs
          for (const spAfetado of todosSPsAfetados) {
            const opsQueUsamSP = ordensProducao.filter(op => {
              if (op.id === opAtual.id) return false; // Excluir OP atual
              if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

              // Verificar se a OP usa este SP
              return op.materiaisNecessarios && op.materiaisNecessarios.some(material =>
                material.produtoId === spAfetado.produto.id
              );
            });

            for (const opAfetada of opsQueUsamSP) {
              if (!analysis.affectedOPs.find(op => op.id === opAfetada.id)) {
                const produto = produtosMap[opAfetada.produtoId];
                analysis.affectedOPs.push({
                  ...opAfetada,
                  produto: produto,
                  spsAfetados: [spAfetado]
                });
              } else {
                // Adicionar SP à lista de SPs afetados desta OP
                const opExistente = analysis.affectedOPs.find(op => op.id === opAfetada.id);
                if (!opExistente.spsAfetados.find(sp => sp.produto.id === spAfetado.produto.id)) {
                  opExistente.spsAfetados.push(spAfetado);
                }
              }
            }
          }

          // Analisar necessidade de novas OPs para SPs adicionados
          for (const spAdicionado of spsAdicionados) {
            const quantidadeNecessaria = spAdicionado.quantidade * opAtual.quantidade;

            // Verificar se já existe OP aberta para este SP
            const opExistente = ordensProducao.find(op =>
              op.produtoId === spAdicionado.produto.id &&
              op.status !== 'Concluída' &&
              op.status !== 'Cancelada'
            );

            if (!opExistente) {
              analysis.newOPsNeeded.push({
                produto: spAdicionado.produto,
                quantidadeNecessaria: quantidadeNecessaria,
                unidade: spAdicionado.unidade || 'PC',
                motivo: 'Novo sub-produto adicionado à estrutura',
                opOrigem: opAtual.numeroOP || opAtual.numero || 'OP'
              });
              analysis.summary.newOPsCount++;
            }
          }

          // Analisar OPs que podem ser canceladas para SPs removidos
          for (const spRemovido of spsRemovidos) {
            // Buscar OPs abertas que produzem este SP
            const opsDoSPRemovido = ordensProducao.filter(op =>
              op.produtoId === spRemovido.produto.id &&
              op.status !== 'Concluída' &&
              op.status !== 'Cancelada'
            );

            for (const opSP of opsDoSPRemovido) {
              // Verificar se este SP é usado em outras OPs abertas (além da atual)
              const outrasOPsQueUsamSP = ordensProducao.filter(op => {
                if (op.id === opAtual.id || op.id === opSP.id) return false;
                if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

                return op.materiaisNecessarios && op.materiaisNecessarios.some(material =>
                  material.produtoId === spRemovido.produto.id
                );
              });

              // Se não há outras OPs que usam este SP, pode ser candidata ao cancelamento
              if (outrasOPsQueUsamSP.length === 0) {
                analysis.opsToCancel.push({
                  ...opSP,
                  produto: produtosMap[opSP.produtoId],
                  motivo: 'Sub-produto removido da estrutura e não é usado em outras OPs',
                  spRemovido: spRemovido.produto,
                  opOrigem: opAtual.numeroOP || opAtual.numero || 'OP'
                });
                analysis.summary.cancelOPsCount++;
              }
            }
          }

          analysis.totalOPsImpacted = analysis.affectedOPs.length;
        }

      } catch (error) {
        console.error('Erro ao analisar impacto em cascata:', error);
      }

      return analysis;
    }

    function renderCascadeAnalysis(analysis) {
      const cascadeContent = document.getElementById('cascadeContent');

      let html = `
        <div class="cascade-section">
          <h4 style="color: #1761a0; margin-bottom: 15px;">📊 Resumo do Impacto</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
            <div class="summary-card" style="background: #e8f5e8; border-color: #4caf50;">
              <div class="summary-number">${analysis.summary.newSPs}</div>
              <div class="summary-label">SPs Adicionados</div>
            </div>
            <div class="summary-card" style="background: #e3f2fd; border-color: #2196f3;">
              <div class="summary-number">${analysis.summary.modifiedSPs}</div>
              <div class="summary-label">SPs Modificados</div>
            </div>
            <div class="summary-card" style="background: #ffebee; border-color: #f44336;">
              <div class="summary-number">${analysis.summary.removedSPs}</div>
              <div class="summary-label">SPs Removidos</div>
            </div>
            <div class="summary-card" style="background: #fff3e0; border-color: #ff9800;">
              <div class="summary-number">${analysis.totalOPsImpacted}</div>
              <div class="summary-label">OPs Afetadas</div>
            </div>
            <div class="summary-card" style="background: #f0f9ff; border-color: #0ea5e9;">
              <div class="summary-number">${analysis.summary.newOPsCount}</div>
              <div class="summary-label">Novas OPs</div>
            </div>
            <div class="summary-card" style="background: #fef2f2; border-color: #ef4444;">
              <div class="summary-number">${analysis.summary.cancelOPsCount}</div>
              <div class="summary-label">OPs p/ Cancelar</div>
            </div>
          </div>
      `;

      if (analysis.affectedOPs.length > 0) {
        html += `
          <h4 style="color: #1761a0; margin-bottom: 15px;">🔄 Ordens de Produção que serão Recalculadas</h4>
          <div class="cascade-ops">
        `;

        analysis.affectedOPs.forEach(op => {
          const numeroOP = op.numeroOP || op.numero || 'Sem número';
          const dataEntrega = op.dataEntrega ?
            new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

          html += `
            <div class="cascade-op">
              <div class="cascade-op-header">${numeroOP}</div>
              <div class="cascade-op-details">
                <strong>Produto:</strong> ${op.produto?.codigo || 'N/A'} - ${op.produto?.descricao || 'N/A'}<br>
                <strong>Quantidade:</strong> ${op.quantidade} ${getCorrectUnit(op)}<br>
                <strong>Status:</strong> ${op.status}<br>
                <strong>Entrega:</strong> ${dataEntrega}
              </div>
              <div class="cascade-sp-list">
                <strong>Sub-Produtos Afetados:</strong>
          `;

          op.spsAfetados.forEach(sp => {
            html += `
              <div class="cascade-sp-item">
                ${sp.produto.codigo} - ${sp.produto.descricao}
              </div>
            `;
          });

          html += `
              </div>
            </div>
          `;
        });

        html += `</div>`;
      }

      // Mostrar novas OPs necessárias
      if (analysis.newOPsNeeded && analysis.newOPsNeeded.length > 0) {
        html += `
          <h4 style="color: #0ea5e9; margin: 20px 0 15px 0;">🆕 Novas Ordens de Produção Necessárias</h4>
          <div class="new-ops-section" style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <p style="color: #0c4a6e; margin-bottom: 15px; font-weight: 500;">
              ⚠️ Os seguintes sub-produtos foram adicionados à estrutura e precisam de novas OPs:
            </p>
        `;

        analysis.newOPsNeeded.forEach(newOP => {
          html += `
            <div style="background: white; border: 1px solid #bae6fd; border-radius: 6px; padding: 12px; margin-bottom: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong style="color: #0c4a6e;">${newOP.produto.codigo} - ${newOP.produto.descricao}</strong><br>
                  <span style="color: #64748b; font-size: 14px;">
                    Quantidade necessária: <strong>${newOP.quantidadeNecessaria.toFixed(3)} ${newOP.unidade}</strong>
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Origem: ${newOP.opOrigem} | ${newOP.motivo}
                  </span>
                </div>
                <div style="background: #0ea5e9; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                  NOVA OP
                </div>
              </div>
            </div>
          `;
        });

        html += `</div>`;
      }

      // Mostrar OPs candidatas ao cancelamento
      if (analysis.opsToCancel && analysis.opsToCancel.length > 0) {
        html += `
          <h4 style="color: #ef4444; margin: 20px 0 15px 0;">🗑️ Ordens de Produção Candidatas ao Cancelamento</h4>
          <div class="cancel-ops-section" style="background: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <p style="color: #7f1d1d; margin-bottom: 15px; font-weight: 500;">
              ⚠️ Os seguintes sub-produtos foram removidos da estrutura e suas OPs podem ser canceladas:
            </p>
        `;

        analysis.opsToCancel.forEach(cancelOP => {
          const numeroOP = cancelOP.numeroOP || cancelOP.numero || 'Sem número';
          const dataEntrega = cancelOP.dataEntrega ?
            new Date(cancelOP.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

          html += `
            <div style="background: white; border: 1px solid #fecaca; border-radius: 6px; padding: 12px; margin-bottom: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong style="color: #7f1d1d;">${numeroOP}</strong><br>
                  <span style="color: #64748b; font-size: 14px;">
                    Produto: <strong>${cancelOP.produto?.codigo} - ${cancelOP.produto?.descricao}</strong>
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Qtde: ${cancelOP.quantidade} ${cancelOP.unidade || 'UN'} | Entrega: ${dataEntrega}
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Origem: ${cancelOP.opOrigem} | ${cancelOP.motivo}
                  </span>
                </div>
                <div style="background: #ef4444; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                  CANCELAR?
                </div>
              </div>
            </div>
          `;
        });

        html += `
            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 10px; margin-top: 15px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: #92400e; font-size: 16px;">⚠️</span>
                <span style="color: #92400e; font-size: 14px; font-weight: 500;">
                  ATENÇÃO: Verifique manualmente se essas OPs podem ser canceladas antes de prosseguir.
                </span>
              </div>
            </div>
          </div>
        `;
      }

      html += `</div>`;
      cascadeContent.innerHTML = html;
    }

    function renderMaterialsTable(materiais, type, diferencas = null) {
      if (materiais.length === 0) {
        return '<div style="text-align: center; color: #666; padding: 20px;">Nenhum material encontrado</div>';
      }

      let html = `
        <table class="materials-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Tipo</th>
              <th>Qtde</th>
              <th>Unidade</th>
              ${type === 'new' && diferencas ? '<th>Alteração</th>' : ''}
            </tr>
          </thead>
          <tbody>
      `;

      materiais.forEach(material => {
        const produto = produtosMap[material.produtoId] || {};
        const codigo = produto.codigo || material.codigo || 'N/A';
        const descricao = produto.descricao || material.descricao || 'N/A';
        const tipo = produto.tipo || material.tipo || 'N/A';
        const quantidade = (material.quantidade || 0).toFixed(3).replace('.', ',');
        const unidade = material.unidade || produto.unidade || 'PC';

        // Cor do tipo baseada no tipo de produto
        let tipoColor = '#6b7280';
        let tipoBg = '#f3f4f6';
        switch (tipo) {
          case 'MP':
            tipoColor = '#dc2626';
            tipoBg = '#fee2e2';
            break;
          case 'SP':
            tipoColor = '#2563eb';
            tipoBg = '#dbeafe';
            break;
          case 'PA':
            tipoColor = '#16a34a';
            tipoBg = '#dcfce7';
            break;
        }

        let rowClass = '';
        let changeInfo = '';

        if (type === 'new' && diferencas) {
          // Verificar se é novo, alterado ou removido
          const isAdicionado = diferencas.adicionados.some(m => m.produtoId === material.produtoId);
          const alterado = diferencas.alterados.find(m => m.produtoId === material.produtoId);

          if (isAdicionado) {
            rowClass = 'material-new';
            changeInfo = '<span style="color: #16a34a; font-weight: 600;">NOVO</span>';
          } else if (alterado) {
            const diferenca = alterado.diferenca;
            const sinal = diferenca > 0 ? '+' : '';
            const cor = diferenca > 0 ? '#16a34a' : '#dc2626';
            changeInfo = `<span style="color: ${cor}; font-weight: 600;">${sinal}${diferenca.toFixed(3).replace('.', ',')}</span>`;
          } else {
            changeInfo = '<span style="color: #6b7280;">-</span>';
          }
        }

        html += `
          <tr class="${rowClass}">
            <td>${codigo}</td>
            <td>${descricao}</td>
            <td><span style="background: ${tipoBg}; color: ${tipoColor}; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">${tipo}</span></td>
            <td>${quantidade}</td>
            <td>${unidade}</td>
            ${type === 'new' && diferencas ? `<td>${changeInfo}</td>` : ''}
          </tr>
        `;
      });

      // Adicionar materiais removidos na tabela da direita
      if (type === 'new' && diferencas && diferencas.removidos.length > 0) {
        diferencas.removidos.forEach(material => {
          const produto = produtosMap[material.produtoId] || {};
          const codigo = produto.codigo || material.codigo || 'N/A';
          const descricao = produto.descricao || material.descricao || 'N/A';
          const tipo = produto.tipo || material.tipo || 'N/A';
          const quantidade = (material.quantidade || 0).toFixed(3).replace('.', ',');
          const unidade = material.unidade || produto.unidade || 'PC';

          // Cor do tipo baseada no tipo de produto
          let tipoColor = '#6b7280';
          let tipoBg = '#f3f4f6';
          switch (tipo) {
            case 'MP':
              tipoColor = '#dc2626';
              tipoBg = '#fee2e2';
              break;
            case 'SP':
              tipoColor = '#2563eb';
              tipoBg = '#dbeafe';
              break;
            case 'PA':
              tipoColor = '#16a34a';
              tipoBg = '#dcfce7';
              break;
          }

          html += `
            <tr class="material-removed">
              <td>${codigo}</td>
              <td>${descricao}</td>
              <td><span style="background: ${tipoBg}; color: ${tipoColor}; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">${tipo}</span></td>
              <td>${quantidade}</td>
              <td>${unidade}</td>
              <td><span style="color: #dc2626; font-weight: 600;">REMOVIDO</span></td>
            </tr>
          `;
        });
      }

      html += `
          </tbody>
        </table>
      `;

      return html;
    }

    window.approveRecalculation = function() {
      // Garantir que a OP atual esteja selecionada se não há OPs selecionadas
      if (selectedOPs.length === 0 && opSelecionada) {
        selectedOPs = [opSelecionada.id];
      }

      // Esconder comparação e mostrar progresso
      document.querySelector('.comparison-section').style.display = 'none';
      startRecalculation();
    };

    window.rejectComparison = function() {
      // Voltar para seleção
      document.querySelector('.comparison-section').style.display = 'none';
      document.querySelector('.op-selection').style.display = 'block';
      document.querySelector('.revision-selection').style.display = 'block';
      document.getElementById('showComparison').style.display = 'inline-block';
      document.getElementById('startRecalculate').style.display = 'none';

      // Resetar botões
      updateRecalculateButton();
    };

    window.startRecalculation = async function() {
      // Garantir que temos uma OP selecionada (seja do array ou da OP atual)
      const opsParaRecalcular = selectedOPs.length > 0 ? selectedOPs : (opSelecionada ? [opSelecionada.id] : []);

      if (opsParaRecalcular.length === 0 || !selectedRevision) {
        alert('Erro: Não foi possível identificar a OP ou revisão de estrutura para recálculo.');
        return;
      }

      // Atualizar selectedOPs se necessário
      if (selectedOPs.length === 0 && opSelecionada) {
        selectedOPs = [opSelecionada.id];
      }

      const confirmMessage = `
🔄 RECÁLCULO DE ORDENS DE PRODUÇÃO

Você está prestes a recalcular ${selectedOPs.length} OP(s) usando a revisão ${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')} da estrutura.

⚠️ ATENÇÃO: Esta operação irá:
• Recalcular todos os materiais necessários
• Liberar empenhos antigos de estoque
• Criar novos empenhos baseados na nova estrutura
• Atualizar solicitações de compra vinculadas
• Registrar logs de auditoria

Esta ação NÃO PODE ser desfeita automaticamente.

Deseja continuar?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Mostrar seção de progresso
        document.querySelector('.progress-section').style.display = 'block';
        document.getElementById('startRecalculate').disabled = true;

        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressLog = document.getElementById('progressLog');

        progressLog.innerHTML = '';

        // Calcular total de OPs a processar (OP atual + OPs em cascata)
        const totalOPs = 1 + (cascadeAnalysis?.totalOPsImpacted || 0);
        let processedOPs = 0;

        function updateProgress(current, total, message) {
          const percentage = (current / total) * 100;
          progressFill.style.width = percentage + '%';
          progressText.textContent = `${current}/${total} OPs processadas (${percentage.toFixed(1)}%)`;

          const timestamp = new Date().toLocaleTimeString();
          progressLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
          progressLog.scrollTop = progressLog.scrollHeight;
        }

        updateProgress(0, totalOPs, 'Iniciando recálculo das ordens de produção...');

        const resultados = {
          sucesso: 0,
          erro: 0,
          detalhes: []
        };

        // Processar a OP atual
        try {
          const produto = produtosMap[opSelecionada.produtoId];
          const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';

          updateProgress(processedOPs, totalOPs, `Processando OP Principal: ${numeroOP} - ${produto?.codigo}...`);

          // Usar a revisão selecionada
          await recalcularOPComRevisao(opSelecionada, selectedRevision.data);

          resultados.sucesso++;
          resultados.detalhes.push({
            op: numeroOP,
            produto: produto?.codigo,
            status: 'Sucesso',
            tipo: 'Principal'
          });

          processedOPs++;
          updateProgress(processedOPs, totalOPs, `✅ OP Principal ${numeroOP} recalculada com sucesso`);

        } catch (error) {
          console.error(`Erro ao recalcular OP ${opSelecionada.id}:`, error);

          resultados.erro++;
          resultados.detalhes.push({
            op: opSelecionada.numeroOP || opSelecionada.numero || 'OP',
            produto: produtosMap[opSelecionada.produtoId]?.codigo || 'N/A',
            status: 'Erro',
            erro: error.message,
            tipo: 'Principal'
          });

          processedOPs++;
          updateProgress(processedOPs, totalOPs, `❌ Erro ao processar OP principal: ${error.message}`);
        }

        // Processar OPs em cascata se houver
        if (cascadeAnalysis && cascadeAnalysis.hasImpact && cascadeAnalysis.affectedOPs.length > 0) {
          updateProgress(processedOPs, totalOPs, `Iniciando recálculo em cascata de ${cascadeAnalysis.affectedOPs.length} OPs...`);

          for (const opAfetada of cascadeAnalysis.affectedOPs) {
            try {
              const produto = produtosMap[opAfetada.produtoId];
              const numeroOP = opAfetada.numeroOP || opAfetada.numero || 'Sem número';

              updateProgress(processedOPs, totalOPs, `Processando OP em Cascata: ${numeroOP} - ${produto?.codigo}...`);

              // Buscar a estrutura mais recente para esta OP
              const estruturaAtualizada = estruturas
                .filter(e => e.produtoPaiId === opAfetada.produtoId)
                .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0))[0];

              if (estruturaAtualizada) {
                await recalcularOPComRevisao(opAfetada, estruturaAtualizada);

                resultados.sucesso++;
                resultados.detalhes.push({
                  op: numeroOP,
                  produto: produto?.codigo,
                  status: 'Sucesso',
                  tipo: 'Cascata'
                });

                updateProgress(processedOPs + 1, totalOPs, `✅ OP Cascata ${numeroOP} recalculada com sucesso`);
              } else {
                throw new Error('Estrutura não encontrada para recálculo em cascata');
              }

            } catch (error) {
              console.error(`Erro ao recalcular OP em cascata ${opAfetada.id}:`, error);

              resultados.erro++;
              resultados.detalhes.push({
                op: opAfetada.numeroOP || opAfetada.numero || 'OP',
                produto: produtosMap[opAfetada.produtoId]?.codigo || 'N/A',
                status: 'Erro',
                erro: error.message,
                tipo: 'Cascata'
              });

              updateProgress(processedOPs + 1, totalOPs, `❌ Erro ao processar OP cascata: ${error.message}`);
            }

            processedOPs++;
          }
        }

        // Mostrar resultado final
        updateProgress(totalOPs, totalOPs, '🎉 Recálculo concluído!');

        const opsPrincipais = resultados.detalhes.filter(d => d.tipo === 'Principal');
        const opsCascata = resultados.detalhes.filter(d => d.tipo === 'Cascata');

        const resumo = `
✅ RECÁLCULO CONCLUÍDO

📊 Resumo:
• OP Principal: ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'} - ${opsPrincipais[0]?.status || 'N/A'}
• OPs em Cascata: ${opsCascata.length} processadas
• Total de Sucessos: ${resultados.sucesso}
• Total de Erros: ${resultados.erro}

${cascadeAnalysis?.hasImpact ? '🔄 Recálculo em cascata executado devido a alterações em Sub-Produtos (SP)' : ''}
${resultados.erro > 0 ? '⚠️ Verifique o log para detalhes dos erros.' : '🎉 Todas as OPs foram recalculadas com sucesso!'}`;

        alert(resumo);

        // Recarregar dados
        await window.onload();
        closeRecalculateModal();

      } catch (error) {
        console.error('Erro no processo de recálculo:', error);
        alert('❌ Erro no processo de recálculo: ' + error.message);
        document.getElementById('startRecalculate').disabled = false;
      }
    };

    async function recalcularOPComRevisao(op, revisaoEstrutura) {
      try {
        // 1. Calcular novos materiais baseado na revisão
        const novosMateriais = await calcularMateriaisComRevisao(revisaoEstrutura, op.quantidade);

        // 2. Liberar empenhos antigos
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          await liberarEmpenhosAntigos(op.materiaisNecessarios, op.armazemProducaoId || 'PROD1');
        }

        // 3. Criar novos empenhos
        const novosEmpenhos = await criarNovosEmpenhos(novosMateriais, op.armazemProducaoId || 'PROD1');

        // 4. Atualizar OP
        const opRef = doc(db, "ordensProducao", op.id);
        await updateDoc(opRef, {
          materiaisNecessarios: novosEmpenhos,
          dataUltimaRevisao: new Date(),
          revisaoEstrutura: revisaoEstrutura.revisaoAtual || 0,
          recalculadoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          historicoRevisoes: [...(op.historicoRevisoes || []), {
            data: new Date(),
            revisaoAnterior: op.revisaoEstrutura || 0,
            revisaoNova: revisaoEstrutura.revisaoAtual || 0,
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
            materiaisRecalculados: novosEmpenhos.length
          }]
        });

        // 5. Atualizar solicitações vinculadas
        await atualizarSolicitacoesVinculadas(op.id, novosMateriais);

        // 6. Registrar log
        await registrarLogRevisao(op, revisaoEstrutura, novosMateriais);

      } catch (error) {
        console.error(`Erro ao recalcular OP ${op.id} com revisão:`, error);
        throw error;
      }
    }

    async function calcularMateriaisComRevisao(revisaoEstrutura, quantidade) {
      const materiais = [];

      if (!revisaoEstrutura.componentes || revisaoEstrutura.componentes.length === 0) {
        return materiais;
      }

      for (const componente of revisaoEstrutura.componentes) {
        const quantidadeNecessaria = (componente.quantidade || 0) * quantidade;
        const produto = produtosMap[componente.componentId || componente.produtoId];

        if (produto && quantidadeNecessaria > 0) {
          console.log(`Adicionando material: ${produto.codigo} - Tipo: ${produto.tipo} - Qtd: ${quantidadeNecessaria}`);

          materiais.push({
            produtoId: componente.componentId || componente.produtoId,
            quantidade: quantidadeNecessaria,
            quantidadeUnitaria: componente.quantidade || 0,
            unidade: componente.unidade || produto.unidade || 'PC',
            codigo: produto.codigo,
            descricao: produto.descricao,
            tipo: produto.tipo // Adicionar tipo para debug
          });
        }
      }

      console.log(`Total de materiais calculados: ${materiais.length}`);
      console.log('Materiais por tipo:', materiais.reduce((acc, m) => {
        acc[m.tipo] = (acc[m.tipo] || 0) + 1;
        return acc;
      }, {}));

      return materiais;
    }

    async function registrarLogRevisao(op, revisaoEstrutura, materiais) {
      try {
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'RECALCULO_REVISAO',
          opId: op.id,
          numeroOP: op.numeroOP || op.numero,
          produtoId: op.produtoId,
          revisaoAnterior: op.revisaoEstrutura || 0,
          revisaoNova: revisaoEstrutura.revisaoAtual || 0,
          materiaisRecalculados: materiais.length,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataHora: new Date(),
          detalhes: {
            quantidadeOP: op.quantidade,
            componentesNaRevisao: revisaoEstrutura.componentes?.length || 0,
            operacoesNaRevisao: revisaoEstrutura.operacoes?.length || 0
          }
        });
      } catch (error) {
        console.error('Erro ao registrar log de revisão:', error);
        // Não falhar o processo principal
      }
    }
  </script>
</body>
</html>