<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Controle de Ordens Abertas - Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --border-color: #d4d4d4;
    }

    * { box-sizing: border-box; margin: 0; padding: 0; }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: #333;
    }

    .container {
      width: 95%;
      max-width: 1600px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header h1::before {
      content: '📋';
      font-size: 28px;
    }

    .dashboard-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      padding: 20px;
    }

    .card {
      background: linear-gradient(135deg, #ffffff, #f8f9fa);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .card-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }

    .card-number {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .card-label {
      color: #666;
      font-size: 14px;
    }

    .card-pendente { border-left: 4px solid var(--warning-color); }
    .card-andamento { border-left: 4px solid var(--primary-color); }
    .card-atrasada { border-left: 4px solid var(--danger-color); }
    .card-concluida { border-left: 4px solid var(--success-color); }

    .form-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin: 20px;
      background: white;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .filters-section {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      align-items: end;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    label {
      margin-bottom: 5px;
      color: #666;
      font-weight: 500;
      font-size: 14px;
    }

    input, select {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary { background-color: var(--primary-color); color: white; }
    .btn-success { background-color: var(--success-color); color: white; }
    .btn-danger { background-color: var(--danger-color); color: white; }
    .btn-warning { background-color: var(--warning-color); color: white; }
    .btn-secondary { background-color: #6c757d; color: white; }

    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      font-size: 13px;
    }

    .orders-table th,
    .orders-table td {
      padding: 10px 8px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .orders-table th {
      background-color: #f0f3f6;
      font-weight: 600;
      color: #666;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .orders-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      text-align: center;
      display: inline-block;
      white-space: nowrap;
    }

    .status-pendente { background-color: #fff3cd; color: #856404; }
    .status-em-andamento { background-color: #cce5ff; color: #004085; }
    .status-concluida { background-color: #d4edda; color: #155724; }
    .status-cancelada { background-color: #f8d7da; color: #721c24; }

    .priority-alta { background-color: #f8d7da; color: #721c24; }
    .priority-media { background-color: #fff3cd; color: #856404; }
    .priority-baixa { background-color: #d4edda; color: #155724; }

    .atrasada {
      background-color: #ffe6e6 !important;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .btn-action {
      padding: 4px 8px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      margin: 1px;
    }

    .btn-view { background-color: #17a2b8; color: white; }
    .btn-edit { background-color: #007bff; color: white; }
    .btn-start { background-color: var(--success-color); color: white; }
    .btn-complete { background-color: var(--warning-color); color: white; }

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
      margin-top: 5px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--success-color), var(--primary-color));
      transition: width 0.3s ease;
    }

    .table-responsive {
      overflow-x: auto;
      max-height: 600px;
      overflow-y: auto;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      z-index: 1001;
      display: none;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }

    .summary-section {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      border: 1px solid #2196f3;
      border-radius: 8px;
      padding: 15px;
      margin: 20px;
    }

    .summary-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
    }

    .summary-item {
      text-align: center;
      padding: 10px;
      background: white;
      border-radius: 6px;
    }

    .summary-number {
      font-size: 20px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .summary-label {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }

    @media (max-width: 768px) {
      .container { width: 100%; margin: 0; border-radius: 0; }
      .dashboard-cards { grid-template-columns: repeat(2, 1fr); }
      .filters-section { grid-template-columns: 1fr; }
      .orders-table { font-size: 11px; }
      .orders-table th, .orders-table td { padding: 6px 4px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Controle de Ordens Abertas</h1>
      <div>
        <button class="btn btn-primary" onclick="window.location.href='ordens_manutencao.html'">
          <i class="fas fa-plus"></i> Nova Ordem
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Dashboard Cards -->
    <div class="dashboard-cards">
      <div class="card card-pendente">
        <div class="card-icon" style="color: var(--warning-color);">⏳</div>
        <div class="card-number" id="totalPendentes">0</div>
        <div class="card-label">Pendentes</div>
      </div>
      <div class="card card-andamento">
        <div class="card-icon" style="color: var(--primary-color);">🔧</div>
        <div class="card-number" id="totalAndamento">0</div>
        <div class="card-label">Em Andamento</div>
      </div>
      <div class="card card-atrasada">
        <div class="card-icon" style="color: var(--danger-color);">⚠️</div>
        <div class="card-number" id="totalAtrasadas">0</div>
        <div class="card-label">Atrasadas</div>
      </div>
      <div class="card card-concluida">
        <div class="card-icon" style="color: var(--success-color);">✅</div>
        <div class="card-number" id="totalConcluidas">0</div>
        <div class="card-label">Concluídas Hoje</div>
      </div>
    </div>

    <!-- Resumo por Responsável -->
    <div class="summary-section">
      <div class="summary-title">
        <i class="fas fa-chart-bar"></i>
        Resumo por Responsável
      </div>
      <div class="summary-grid" id="summaryByResponsible">
      </div>
    </div>

    <!-- Filtros e Lista -->
    <div class="form-container">
      <h2 class="form-title">Ordens de Manutenção</h2>

      <div class="filters-section">
        <div class="filter-group">
          <label for="searchInput">Buscar</label>
          <input type="text" id="searchInput" placeholder="Código, recurso, responsável...">
        </div>
        <div class="filter-group">
          <label for="statusFilter">Status</label>
          <select id="statusFilter">
            <option value="">Todos</option>
            <option value="PENDENTE">Pendente</option>
            <option value="EM_ANDAMENTO">Em Andamento</option>
            <option value="CONCLUIDA">Concluída</option>
            <option value="CANCELADA">Cancelada</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="prioridadeFilter">Prioridade</label>
          <select id="prioridadeFilter">
            <option value="">Todas</option>
            <option value="ALTA">Alta</option>
            <option value="MEDIA">Média</option>
            <option value="BAIXA">Baixa</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="responsavelFilter">Responsável</label>
          <select id="responsavelFilter">
            <option value="">Todos</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="periodoFilter">Período</label>
          <select id="periodoFilter">
            <option value="">Todos</option>
            <option value="hoje">Hoje</option>
            <option value="semana">Esta Semana</option>
            <option value="mes">Este Mês</option>
            <option value="atrasadas">Atrasadas</option>
          </select>
        </div>
        <div class="filter-group">
          <button class="btn btn-primary" onclick="aplicarFiltros()">
            <i class="fas fa-filter"></i> Filtrar
          </button>
        </div>
      </div>

      <div class="table-responsive">
        <table class="orders-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Recurso/Equipamento</th>
              <th>Tipo</th>
              <th>Prioridade</th>
              <th>Status</th>
              <th>Responsável</th>
              <th>Data Prevista</th>
              <th>Data Início</th>
              <th>Progresso</th>
              <th>Tempo Gasto</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="ordersTableBody">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, getDocs, doc, updateDoc, query, where, orderBy, serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensList = [];
    let funcionariosList = [];
    let recursosList = [];
    let apontamentosList = [];

    async function loadData() {
      try {
        // Load all data in parallel
        const [ordensSnap, funcSnap, recursosSnap, apontamentosSnap] = await Promise.all([
          getDocs(collection(db, "ordensManutencao")),
          getDocs(collection(db, "funcionariosManutencao")),
          getDocs(collection(db, "recursos")),
          getDocs(collection(db, "apontamentosManutencao"))
        ]);

        ordensList = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        funcionariosList = funcSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursosList = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        apontamentosList = apontamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Process data
        enrichOrdersData();
        updateDashboard();
        updateResponsibleFilter();
        updateSummaryByResponsible();
        updateOrdersTable(ordensList);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        showNotification("Erro ao carregar dados", "error");
      }
    }

    function enrichOrdersData() {
      ordensList = ordensList.map(ordem => {
        // Add responsible info
        const responsavel = funcionariosList.find(f => f.id === ordem.fornecedorId);
        ordem.responsavelNome = responsavel ? responsavel.nome : 'Não definido';

        // Add resource info
        const recurso = recursosList.find(r => r.id === ordem.recursoId);
        ordem.recursoNome = recurso ? `${recurso.codigo} - ${recurso.maquina}` : 'Recurso não encontrado';

        // Calculate progress and time spent
        const apontamentos = apontamentosList.filter(a => a.ordemId === ordem.id);
        ordem.tempoGasto = apontamentos.reduce((total, apt) => total + (apt.tempoGasto || 0), 0);
        ordem.progresso = calculateProgress(ordem, apontamentos);

        // Check if overdue
        if (ordem.dataPrevista && ordem.status !== 'CONCLUIDA') {
          const dataPrevista = ordem.dataPrevista.toDate ? ordem.dataPrevista.toDate() : new Date(ordem.dataPrevista);
          ordem.atrasada = dataPrevista < new Date();
        }

        return ordem;
      });
    }

    function calculateProgress(ordem, apontamentos) {
      if (ordem.status === 'CONCLUIDA') return 100;
      if (ordem.status === 'PENDENTE') return 0;
      if (ordem.status === 'EM_ANDAMENTO') {
        // Calculate based on time spent vs estimated duration
        const estimatedHours = ordem.duracaoEstimada || 8; // Default 8 hours
        const spentHours = apontamentos.reduce((total, apt) => total + (apt.tempoGasto || 0), 0);
        return Math.min(90, Math.round((spentHours / estimatedHours) * 100));
      }
      return 0;
    }

    function updateDashboard() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const pendentes = ordensList.filter(o => o.status === 'PENDENTE').length;
      const andamento = ordensList.filter(o => o.status === 'EM_ANDAMENTO').length;
      const atrasadas = ordensList.filter(o => o.atrasada && o.status !== 'CONCLUIDA').length;
      const concluidasHoje = ordensList.filter(o => {
        if (o.status !== 'CONCLUIDA' || !o.ultimoApontamento) return false;
        const dataApontamento = o.ultimoApontamento.toDate ? o.ultimoApontamento.toDate() : new Date(o.ultimoApontamento);
        return dataApontamento >= today;
      }).length;

      document.getElementById('totalPendentes').textContent = pendentes;
      document.getElementById('totalAndamento').textContent = andamento;
      document.getElementById('totalAtrasadas').textContent = atrasadas;
      document.getElementById('totalConcluidas').textContent = concluidasHoje;
    }

    function updateResponsibleFilter() {
      const responsaveis = [...new Set(ordensList.map(o => o.responsavelNome))];
      const select = document.getElementById('responsavelFilter');
      
      select.innerHTML = '<option value="">Todos</option>' +
        responsaveis.map(resp => `<option value="${resp}">${resp}</option>`).join('');
    }

    function updateSummaryByResponsible() {
      const summary = {};
      
      ordensList.forEach(ordem => {
        const resp = ordem.responsavelNome;
        if (!summary[resp]) {
          summary[resp] = { total: 0, pendente: 0, andamento: 0, concluida: 0, atrasada: 0 };
        }
        summary[resp].total++;
        summary[resp][ordem.status.toLowerCase()]++;
        if (ordem.atrasada && ordem.status !== 'CONCLUIDA') {
          summary[resp].atrasada++;
        }
      });

      const container = document.getElementById('summaryByResponsible');
      container.innerHTML = Object.entries(summary).map(([resp, data]) => `
        <div class="summary-item">
          <div class="summary-number">${data.total}</div>
          <div class="summary-label">${resp}</div>
          <div style="font-size: 10px; margin-top: 5px;">
            <span style="color: var(--warning-color);">P:${data.pendente}</span> |
            <span style="color: var(--primary-color);">A:${data.andamento}</span> |
            <span style="color: var(--danger-color);">AT:${data.atrasada}</span>
          </div>
        </div>
      `).join('');
    }

    function updateOrdersTable(ordens) {
      const tbody = document.querySelector('#ordersTableBody');
      tbody.innerHTML = ordens.map(ordem => `
        <tr class="${ordem.atrasada ? 'atrasada' : ''}">
          <td>${ordem.codigo}</td>
          <td>${ordem.recursoNome}</td>
          <td>${ordem.tipoOrdem}</td>
          <td><span class="status-badge priority-${ordem.prioridade?.toLowerCase() || 'media'}">${ordem.prioridade || 'MÉDIA'}</span></td>
          <td><span class="status-badge status-${ordem.status.toLowerCase().replace('_', '-')}">${ordem.status.replace('_', ' ')}</span></td>
          <td>${ordem.responsavelNome}</td>
          <td>${formatDate(ordem.dataPrevista)} ${ordem.atrasada ? '⚠️' : ''}</td>
          <td>${formatDate(ordem.dataInicio)}</td>
          <td>
            <div style="display: flex; align-items: center; gap: 5px;">
              <span>${ordem.progresso}%</span>
              <div class="progress-bar" style="width: 60px;">
                <div class="progress-fill" style="width: ${ordem.progresso}%"></div>
              </div>
            </div>
          </td>
          <td>${ordem.tempoGasto.toFixed(1)}h</td>
          <td>
            <div class="action-buttons">
              <button onclick="visualizarOrdem('${ordem.id}')" class="btn-action btn-view" title="Visualizar">
                <i class="fas fa-eye"></i>
              </button>
              ${ordem.status === 'PENDENTE' ? `
                <button onclick="iniciarOrdem('${ordem.id}')" class="btn-action btn-start" title="Iniciar">
                  <i class="fas fa-play"></i>
                </button>
              ` : ''}
              ${ordem.status === 'EM_ANDAMENTO' ? `
                <button onclick="concluirOrdem('${ordem.id}')" class="btn-action btn-complete" title="Concluir">
                  <i class="fas fa-check"></i>
                </button>
              ` : ''}
              <button onclick="editarOrdem('${ordem.id}')" class="btn-action btn-edit" title="Editar">
                <i class="fas fa-edit"></i>
              </button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    function formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleDateString('pt-BR');
    }

    window.aplicarFiltros = function() {
      const status = document.getElementById('statusFilter').value;
      const prioridade = document.getElementById('prioridadeFilter').value;
      const responsavel = document.getElementById('responsavelFilter').value;
      const periodo = document.getElementById('periodoFilter').value;
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      let ordensFiltradas = ordensList.filter(ordem => {
        const matchStatus = !status || ordem.status === status;
        const matchPrioridade = !prioridade || ordem.prioridade === prioridade;
        const matchResponsavel = !responsavel || ordem.responsavelNome === responsavel;
        const matchSearch = !searchTerm || 
          ordem.codigo.toLowerCase().includes(searchTerm) ||
          ordem.recursoNome.toLowerCase().includes(searchTerm) ||
          ordem.responsavelNome.toLowerCase().includes(searchTerm);

        let matchPeriodo = true;
        if (periodo) {
          const hoje = new Date();
          const dataOrdem = ordem.dataPrevista ? (ordem.dataPrevista.toDate ? ordem.dataPrevista.toDate() : new Date(ordem.dataPrevista)) : null;
          
          switch(periodo) {
            case 'hoje':
              matchPeriodo = dataOrdem && dataOrdem.toDateString() === hoje.toDateString();
              break;
            case 'semana':
              const inicioSemana = new Date(hoje);
              inicioSemana.setDate(hoje.getDate() - hoje.getDay());
              matchPeriodo = dataOrdem && dataOrdem >= inicioSemana;
              break;
            case 'mes':
              matchPeriodo = dataOrdem && dataOrdem.getMonth() === hoje.getMonth() && dataOrdem.getFullYear() === hoje.getFullYear();
              break;
            case 'atrasadas':
              matchPeriodo = ordem.atrasada;
              break;
          }
        }

        return matchStatus && matchPrioridade && matchResponsavel && matchSearch && matchPeriodo;
      });

      updateOrdersTable(ordensFiltradas);
    };

    window.iniciarOrdem = async function(ordemId) {
      try {
        await updateDoc(doc(db, "ordensManutencao", ordemId), {
          status: 'EM_ANDAMENTO',
          dataInicio: serverTimestamp()
        });
        showNotification("Ordem iniciada com sucesso!", "success");
        loadData();
      } catch (error) {
        console.error("Erro ao iniciar ordem:", error);
        showNotification("Erro ao iniciar ordem", "error");
      }
    };

    window.concluirOrdem = async function(ordemId) {
      if (!confirm('Tem certeza que deseja concluir esta ordem?')) return;
      
      try {
        await updateDoc(doc(db, "ordensManutencao", ordemId), {
          status: 'CONCLUIDA',
          dataConclusao: serverTimestamp()
        });
        showNotification("Ordem concluída com sucesso!", "success");
        loadData();
      } catch (error) {
        console.error("Erro ao concluir ordem:", error);
        showNotification("Erro ao concluir ordem", "error");
      }
    };

    window.visualizarOrdem = function(ordemId) {
      // Redirect to view order details
      window.location.href = `ordens_manutencao.html?view=${ordemId}`;
    };

    window.editarOrdem = function(ordemId) {
      // Redirect to edit order
      window.location.href = `ordens_manutencao.html?edit=${ordemId}`;
    };

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, 3000);
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', () => {
      loadData();

      // Auto-refresh every 5 minutes
      setInterval(loadData, 5 * 60 * 1000);

      // Search input listener
      document.getElementById('searchInput').addEventListener('input', aplicarFiltros);
    });
  </script>
</body>
</html> 