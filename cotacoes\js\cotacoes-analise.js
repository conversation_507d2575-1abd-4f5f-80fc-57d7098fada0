// ===== ANÁLISE DE COTAÇÕES =====

let currentAnalysisQuotation = null;
let analysisData = null;
let selectedWinners = {};

// ===== ABRIR ANÁLISE DE COTAÇÃO =====
window.analyzeQuotation = function(quotationId) {
    const quotation = cotacoes.find(c => c.id === quotationId);
    if (!quotation) {
        showNotification('Cotação não encontrada', 'error');
        return;
    }

    if (!quotation.respostas || Object.keys(quotation.respostas).length === 0) {
        showNotification('Esta cotação ainda não possui respostas dos fornecedores', 'warning');
        return;
    }

    currentAnalysisQuotation = quotation;
    loadAnalysisData();
    openModal('analysisModal');
};

// ===== CARREGAR DADOS PARA ANÁLISE =====
function loadAnalysisData() {
    // Carregar informações básicas
    document.getElementById('analysisQuotationNumber').textContent = currentAnalysisQuotation.numero || 'S/N';
    document.getElementById('analysisStatus').textContent = getStatusText(currentAnalysisQuotation.status);
    document.getElementById('analysisStatus').className = `status ${currentAnalysisQuotation.status?.toLowerCase()}`;
    
    const deadline = currentAnalysisQuotation.dataLimite;
    document.getElementById('analysisDeadline').textContent = deadline ? 
        formatDate(deadline.toDate ? deadline.toDate() : new Date(deadline)) : 'Não definida';
    
    document.getElementById('analysisSuppliersCount').textContent = currentAnalysisQuotation.fornecedores?.length || 0;
    document.getElementById('analysisResponsesCount').textContent = Object.keys(currentAnalysisQuotation.respostas || {}).length;

    // Processar dados para análise
    processAnalysisData();
    
    // Carregar aba ativa
    showAnalysisTab('comparacao');
}

// ===== PROCESSAR DADOS PARA ANÁLISE =====
function processAnalysisData() {
    const quotation = currentAnalysisQuotation;
    const responses = quotation.respostas || {};
    
    analysisData = {
        items: [],
        suppliers: {},
        totals: {}
    };

    // Processar cada item da cotação
    quotation.itens.forEach((item, itemIndex) => {
        const itemAnalysis = {
            index: itemIndex,
            codigo: item.codigo,
            descricao: item.descricao,
            quantidadeSolicitada: item.quantidadeCompra || item.quantidade || 0,
            unidade: item.unidadeCompra || item.unidade || 'UN',
            suppliers: []
        };

        // Verificar respostas de cada fornecedor para este item
        Object.keys(responses).forEach(supplierId => {
            const response = responses[supplierId];
            const supplier = fornecedores.find(f => f.id === supplierId);
            
            if (!supplier) return;

            // Procurar este item na resposta do fornecedor
            const supplierItem = response.itens?.find(ri => ri.indiceItem === itemIndex);
            
            if (supplierItem) {
                const unitPrice = supplierItem.precoUnitario || 0;
                const quantity = supplierItem.quantidadeFornecida || supplierItem.quantidade || 0;
                const ipi = supplierItem.ipi || 0;
                const icms = supplierItem.icms || 0;
                
                const subtotal = quantity * unitPrice;
                const ipiValue = subtotal * (ipi / 100);
                const total = subtotal + ipiValue;

                itemAnalysis.suppliers.push({
                    id: supplierId,
                    nome: supplier.nome || supplier.razaoSocial || 'Nome não informado',
                    quantidadeFornecida: quantity,
                    precoUnitario: unitPrice,
                    ipi: ipi,
                    icms: icms,
                    subtotal: subtotal,
                    total: total,
                    prazoEntrega: response.prazoEntrega || 0,
                    condicaoPagamento: response.condicaoPagamento || '',
                    observacoes: supplierItem.observacaoItem || ''
                });
            }
        });

        // Ordenar fornecedores por preço unitário
        itemAnalysis.suppliers.sort((a, b) => a.precoUnitario - b.precoUnitario);
        
        // Marcar melhor preço
        if (itemAnalysis.suppliers.length > 0) {
            itemAnalysis.suppliers[0].isBestPrice = true;
        }

        analysisData.items.push(itemAnalysis);
    });

    // Processar dados por fornecedor
    Object.keys(responses).forEach(supplierId => {
        const response = responses[supplierId];
        const supplier = fornecedores.find(f => f.id === supplierId);
        
        if (!supplier) return;

        const supplierAnalysis = {
            id: supplierId,
            nome: supplier.nome || supplier.razaoSocial || 'Nome não informado',
            codigo: supplier.codigo || supplier.id,
            email: supplier.email || '',
            totalItens: response.itens?.length || 0,
            totalItensDisponiveis: quotation.itens.length,
            percentualResposta: ((response.itens?.length || 0) / quotation.itens.length * 100).toFixed(1),
            valorTotal: 0,
            prazoEntrega: response.prazoEntrega || 0,
            condicaoPagamento: response.condicaoPagamento || '',
            validadeProposta: response.validadeProposta || 0,
            observacoes: response.observacoes || '',
            itens: response.itens || []
        };

        // Calcular valor total do fornecedor
        supplierAnalysis.valorTotal = supplierAnalysis.itens.reduce((total, item) => {
            return total + (item.valorTotal || 0);
        }, 0);

        analysisData.suppliers[supplierId] = supplierAnalysis;
    });

    console.log('Dados de análise processados:', analysisData);
}

// ===== GERENCIAR ABAS DE ANÁLISE =====
window.showAnalysisTab = function(tabName, event) {
    // Ocultar todas as abas
    document.querySelectorAll('.analysis-tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remover classe active de todos os botões
    document.querySelectorAll('.analysis-tab').forEach(btn => {
        btn.classList.remove('active');
    });

    // Mostrar aba selecionada
    const selectedTab = document.getElementById(`analysisTab${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Ativar botão correspondente
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // Fallback: encontrar o botão pela aba
        const tabButton = document.querySelector(`[onclick*="showAnalysisTab('${tabName}')"]`);
        if (tabButton) {
            tabButton.classList.add('active');
        }
    }

    // Carregar conteúdo da aba
    switch(tabName) {
        case 'comparacao':
            loadComparisonTab();
            break;
        case 'fornecedores':
            loadSuppliersTab();
            break;
        case 'selecao':
            loadSelectionTab();
            break;
        case 'resumo':
            loadSummaryTab();
            break;
    }
};

// ===== CARREGAR ABA DE COMPARAÇÃO =====
function loadComparisonTab() {
    const tbody = document.getElementById('comparisonTableBody');
    tbody.innerHTML = '';

    analysisData.items.forEach(item => {
        if (item.suppliers.length === 0) return;

        const row = document.createElement('tr');
        row.className = 'comparison-row';
        
        // Criar células básicas do item
        const itemInfo = `
            <td style="vertical-align: top; padding: 15px;">
                <strong>${item.codigo || 'S/Código'}</strong>
            </td>
            <td style="vertical-align: top; padding: 15px;">
                ${item.descricao || 'Descrição não informada'}
            </td>
            <td style="vertical-align: top; padding: 15px; text-align: center;">
                ${parseFloat(item.quantidadeSolicitada).toFixed(3).replace('.', ',')}
            </td>
            <td style="vertical-align: top; padding: 15px; text-align: center;">
                ${item.unidade}
            </td>
        `;

        // Criar opções de fornecedores
        const suppliersHtml = item.suppliers.map(supplier => `
            <div class="supplier-option ${supplier.isBestPrice ? 'best-price' : ''}" 
                 onclick="selectSupplier(${item.index}, '${supplier.id}')"
                 data-item="${item.index}" data-supplier="${supplier.id}">
                <div class="supplier-info">
                    <div class="supplier-name">${supplier.nome}</div>
                    <div class="supplier-details">
                        Qtd: ${parseFloat(supplier.quantidadeFornecida).toFixed(3).replace('.', ',')} ${item.unidade} | 
                        Prazo: ${supplier.prazoEntrega} dias
                        ${supplier.observacoes ? ` | ${supplier.observacoes}` : ''}
                    </div>
                </div>
                <div class="price-info">
                    <div class="unit-price">R$ ${supplier.precoUnitario.toFixed(2).replace('.', ',')}</div>
                    <div class="total-price">Total: R$ ${supplier.total.toFixed(2).replace('.', ',')}</div>
                    ${supplier.isBestPrice ? '<div class="best-price-badge">Melhor Preço</div>' : ''}
                </div>
            </div>
        `).join('');

        row.innerHTML = itemInfo + `<td colspan="3" style="padding: 10px;">${suppliersHtml}</td>`;
        tbody.appendChild(row);
    });
}

// ===== SELECIONAR FORNECEDOR PARA UM ITEM =====
window.selectSupplier = function(itemIndex, supplierId) {
    // Remover seleção anterior para este item
    document.querySelectorAll(`[data-item="${itemIndex}"]`).forEach(option => {
        option.classList.remove('selected');
        option.querySelector('.selected-badge')?.remove();
    });

    // Adicionar nova seleção
    const selectedOption = document.querySelector(`[data-item="${itemIndex}"][data-supplier="${supplierId}"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected');
        
        // Adicionar badge de selecionado
        const priceInfo = selectedOption.querySelector('.price-info');
        const badge = document.createElement('div');
        badge.className = 'selected-badge';
        badge.textContent = 'Selecionado';
        priceInfo.appendChild(badge);
    }

    // Atualizar dados de seleção
    selectedWinners[itemIndex] = supplierId;
    
    // Atualizar resumo de seleção
    updateSelectionSummary();
};

// ===== SELECIONAR MELHORES PREÇOS AUTOMATICAMENTE =====
window.selectBestPrices = function() {
    selectedWinners = {};
    
    analysisData.items.forEach(item => {
        if (item.suppliers.length > 0) {
            const bestSupplier = item.suppliers[0]; // Já ordenado por preço
            selectedWinners[item.index] = bestSupplier.id;
        }
    });

    // Recarregar aba para mostrar seleções
    loadComparisonTab();
    
    // Aplicar seleções visuais
    Object.keys(selectedWinners).forEach(itemIndex => {
        const supplierId = selectedWinners[itemIndex];
        selectSupplier(parseInt(itemIndex), supplierId);
    });

    showNotification('Melhores preços selecionados automaticamente', 'success');
};

// ===== LIMPAR TODAS AS SELEÇÕES =====
window.clearAllSelections = function() {
    selectedWinners = {};
    
    // Remover seleções visuais
    document.querySelectorAll('.supplier-option').forEach(option => {
        option.classList.remove('selected');
        option.querySelector('.selected-badge')?.remove();
    });

    updateSelectionSummary();
    showNotification('Todas as seleções foram removidas', 'info');
};

// ===== ATUALIZAR RESUMO DE SELEÇÃO =====
function updateSelectionSummary() {
    const selectedCount = Object.keys(selectedWinners).length;
    const totalItems = analysisData.items.length;
    
    const summaryElement = document.getElementById('selectionSummary');
    if (selectedCount === 0) {
        summaryElement.textContent = 'Nenhum item selecionado';
    } else {
        summaryElement.textContent = `${selectedCount} de ${totalItems} itens selecionados`;
    }
}

// ===== CARREGAR ABA DE ANÁLISE POR FORNECEDOR =====
function loadSuppliersTab() {
    const container = document.getElementById('suppliersAnalysisContainer');
    container.innerHTML = '';

    Object.values(analysisData.suppliers).forEach(supplier => {
        const supplierCard = document.createElement('div');
        supplierCard.className = 'supplier-analysis-card';

        // Calcular métricas do fornecedor
        const totalValue = supplier.itens.reduce((sum, item) => sum + (item.valorTotal || 0), 0);
        const avgPrice = supplier.itens.length > 0 ?
            supplier.itens.reduce((sum, item) => sum + (item.precoUnitario || 0), 0) / supplier.itens.length : 0;

        // Contar quantos itens este fornecedor tem o melhor preço
        let bestPriceCount = 0;
        analysisData.items.forEach(item => {
            const supplierInItem = item.suppliers.find(s => s.id === supplier.id);
            if (supplierInItem && supplierInItem.isBestPrice) {
                bestPriceCount++;
            }
        });

        const competitivenessPercent = supplier.totalItens > 0 ?
            (bestPriceCount / supplier.totalItens * 100).toFixed(1) : 0;

        supplierCard.innerHTML = `
            <div class="supplier-analysis-header">
                <div>
                    <h4>${supplier.nome}</h4>
                    <small>${supplier.codigo} • ${supplier.email}</small>
                </div>
                <div class="supplier-status">
                    <span class="status-badge ${supplier.totalItens === supplier.totalItensDisponiveis ? 'complete' : 'partial'}">
                        ${supplier.totalItens === supplier.totalItensDisponiveis ? 'Resposta Completa' : 'Resposta Parcial'}
                    </span>
                </div>
            </div>

            <div class="supplier-analysis-body">
                <div class="analysis-metrics">
                    <div class="metric-card">
                        <div class="metric-value">${supplier.totalItens}</div>
                        <div class="metric-label">Itens Cotados</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${supplier.percentualResposta}%</div>
                        <div class="metric-label">Taxa Resposta</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R$ ${totalValue.toFixed(2).replace('.', ',')}</div>
                        <div class="metric-label">Valor Total</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R$ ${avgPrice.toFixed(2).replace('.', ',')}</div>
                        <div class="metric-label">Preço Médio</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${bestPriceCount}</div>
                        <div class="metric-label">Melhores Preços</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${competitivenessPercent}%</div>
                        <div class="metric-label">Competitividade</div>
                    </div>
                </div>

                <div class="supplier-details-section">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Condições Comerciais</h5>
                            <div class="detail-item">
                                <strong>Prazo de Entrega:</strong> ${supplier.prazoEntrega} dias
                            </div>
                            <div class="detail-item">
                                <strong>Condição de Pagamento:</strong> ${supplier.condicaoPagamento}
                            </div>
                            <div class="detail-item">
                                <strong>Validade da Proposta:</strong> ${supplier.validadeProposta} dias
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Observações</h5>
                            <div class="observations-text">
                                ${supplier.observacoes || 'Nenhuma observação fornecida'}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="supplier-items-section">
                    <h5>Itens Cotados</h5>
                    <div class="items-table-container">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Qtd. Solicitada</th>
                                    <th>Qtd. Fornecida</th>
                                    <th>Preço Unit.</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${supplier.itens.map(item => {
                                    const originalItem = analysisData.items.find(i => i.index === item.indiceItem);
                                    const supplierInItem = originalItem?.suppliers.find(s => s.id === supplier.id);
                                    const isBestPrice = supplierInItem?.isBestPrice || false;

                                    return `
                                        <tr class="${isBestPrice ? 'best-price-row' : ''}">
                                            <td>${item.codigo}</td>
                                            <td>${item.descricao}</td>
                                            <td>${parseFloat(item.quantidadeSolicitada || 0).toFixed(3).replace('.', ',')}</td>
                                            <td>${parseFloat(item.quantidadeFornecida || 0).toFixed(3).replace('.', ',')}</td>
                                            <td>R$ ${(item.precoUnitario || 0).toFixed(2).replace('.', ',')}</td>
                                            <td>R$ ${(item.valorTotal || 0).toFixed(2).replace('.', ',')}</td>
                                            <td>
                                                ${isBestPrice ? '<span class="best-price-badge">Melhor Preço</span>' : ''}
                                                ${item.observacaoItem ? '<i class="fas fa-info-circle" title="' + item.observacaoItem + '"></i>' : ''}
                                            </td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(supplierCard);
    });
}

// ===== CARREGAR ABA DE SELEÇÃO FINAL =====
function loadSelectionTab() {
    updateSelectionDisplay();
    calculateSelectionTotals();
}

function updateSelectionDisplay() {
    const container = document.getElementById('selectedItemsContainer');

    if (Object.keys(selectedWinners).length === 0) {
        container.innerHTML = `
            <div class="no-selection-message">
                <i class="fas fa-info-circle"></i>
                <h5>Nenhum item selecionado</h5>
                <p>Vá para a aba "Comparação por Item" para selecionar os vencedores de cada item.</p>
                <button class="btn btn-primary" onclick="showAnalysisTab('comparacao')">
                    <i class="fas fa-balance-scale"></i> Ir para Comparação
                </button>
            </div>
        `;
        return;
    }

    // Agrupar seleções por fornecedor
    const selectionsBySupplier = {};

    Object.keys(selectedWinners).forEach(itemIndex => {
        const supplierId = selectedWinners[itemIndex];
        const item = analysisData.items[parseInt(itemIndex)];
        const supplier = analysisData.suppliers[supplierId];
        const supplierItem = item.suppliers.find(s => s.id === supplierId);

        if (!selectionsBySupplier[supplierId]) {
            selectionsBySupplier[supplierId] = {
                supplier: supplier,
                items: []
            };
        }

        selectionsBySupplier[supplierId].items.push({
            item: item,
            supplierItem: supplierItem
        });
    });

    // Renderizar seleções agrupadas
    let html = '';
    Object.keys(selectionsBySupplier).forEach(supplierId => {
        const selection = selectionsBySupplier[supplierId];
        const totalValue = selection.items.reduce((sum, item) => sum + (item.supplierItem.total || 0), 0);

        html += `
            <div class="supplier-selection-card">
                <div class="supplier-selection-header">
                    <h5>${selection.supplier.nome}</h5>
                    <div class="selection-summary">
                        ${selection.items.length} itens • R$ ${totalValue.toFixed(2).replace('.', ',')}
                    </div>
                </div>
                <div class="supplier-selection-items">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Quantidade</th>
                                <th>Preço Unit.</th>
                                <th>Total</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${selection.items.map(item => `
                                <tr>
                                    <td>${item.item.codigo}</td>
                                    <td>${item.item.descricao}</td>
                                    <td>${parseFloat(item.supplierItem.quantidadeFornecida).toFixed(3).replace('.', ',')} ${item.item.unidade}</td>
                                    <td>R$ ${item.supplierItem.precoUnitario.toFixed(2).replace('.', ',')}</td>
                                    <td>R$ ${item.supplierItem.total.toFixed(2).replace('.', ',')}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="removeSelection(${item.item.index})" title="Remover seleção">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function calculateSelectionTotals() {
    let subtotal = 0;
    let totalIPI = 0;
    let totalICMS = 0;

    Object.keys(selectedWinners).forEach(itemIndex => {
        const supplierId = selectedWinners[itemIndex];
        const item = analysisData.items[parseInt(itemIndex)];
        const supplierItem = item.suppliers.find(s => s.id === supplierId);

        if (supplierItem) {
            subtotal += supplierItem.subtotal;
            totalIPI += supplierItem.subtotal * (supplierItem.ipi / 100);
            totalICMS += supplierItem.subtotal * (supplierItem.icms / 100);
        }
    });

    const grandTotal = subtotal + totalIPI;

    document.getElementById('selectionSubtotal').textContent = `R$ ${subtotal.toFixed(2).replace('.', ',')}`;
    document.getElementById('selectionIPI').textContent = `R$ ${totalIPI.toFixed(2).replace('.', ',')}`;
    document.getElementById('selectionICMS').textContent = `R$ ${totalICMS.toFixed(2).replace('.', ',')}`;
    document.getElementById('selectionTotal').textContent = `R$ ${grandTotal.toFixed(2).replace('.', ',')}`;
}

// ===== CARREGAR RESUMO FINAL =====
function loadSummaryTab() {
    const container = document.getElementById('finalSummaryContainer');

    // Calcular estatísticas gerais
    const totalItems = analysisData.items.length;
    const totalSuppliers = Object.keys(analysisData.suppliers).length;
    const selectedItems = Object.keys(selectedWinners).length;
    const selectionPercent = totalItems > 0 ? (selectedItems / totalItems * 100).toFixed(1) : 0;

    // Calcular economia (comparar com preços mais altos)
    let totalSavings = 0;
    let originalTotal = 0;
    let selectedTotal = 0;

    Object.keys(selectedWinners).forEach(itemIndex => {
        const item = analysisData.items[parseInt(itemIndex)];
        const selectedSupplierId = selectedWinners[itemIndex];
        const selectedSupplier = item.suppliers.find(s => s.id === selectedSupplierId);

        if (selectedSupplier && item.suppliers.length > 1) {
            const highestPrice = Math.max(...item.suppliers.map(s => s.total));
            const selectedPrice = selectedSupplier.total;

            originalTotal += highestPrice;
            selectedTotal += selectedPrice;
            totalSavings += (highestPrice - selectedPrice);
        }
    });

    const savingsPercent = originalTotal > 0 ? (totalSavings / originalTotal * 100).toFixed(1) : 0;

    // Agrupar por fornecedor para estatísticas
    const supplierStats = {};
    Object.keys(selectedWinners).forEach(itemIndex => {
        const supplierId = selectedWinners[itemIndex];
        if (!supplierStats[supplierId]) {
            supplierStats[supplierId] = {
                name: analysisData.suppliers[supplierId].nome,
                count: 0,
                value: 0
            };
        }

        const item = analysisData.items[parseInt(itemIndex)];
        const supplierItem = item.suppliers.find(s => s.id === supplierId);

        supplierStats[supplierId].count++;
        supplierStats[supplierId].value += supplierItem.total;
    });

    container.innerHTML = `
        <div class="summary-overview">
            <div class="row">
                <div class="col-md-3">
                    <div class="summary-stat-card">
                        <div class="stat-icon"><i class="fas fa-list"></i></div>
                        <div class="stat-content">
                            <div class="stat-value">${totalItems}</div>
                            <div class="stat-label">Total de Itens</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-stat-card">
                        <div class="stat-icon"><i class="fas fa-truck"></i></div>
                        <div class="stat-content">
                            <div class="stat-value">${totalSuppliers}</div>
                            <div class="stat-label">Fornecedores</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-stat-card">
                        <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                        <div class="stat-content">
                            <div class="stat-value">${selectedItems}</div>
                            <div class="stat-label">Itens Selecionados</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-stat-card">
                        <div class="stat-icon"><i class="fas fa-percentage"></i></div>
                        <div class="stat-content">
                            <div class="stat-value">${selectionPercent}%</div>
                            <div class="stat-label">Progresso</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${selectedItems > 0 ? `
            <div class="summary-financial">
                <h4><i class="fas fa-calculator"></i> Resumo Financeiro</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="financial-card">
                            <h5>Valor Total Selecionado</h5>
                            <div class="financial-value">R$ ${selectedTotal.toFixed(2).replace('.', ',')}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="financial-card savings">
                            <h5>Economia Estimada</h5>
                            <div class="financial-value">R$ ${totalSavings.toFixed(2).replace('.', ',')}</div>
                            <div class="financial-percent">${savingsPercent}% de economia</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="summary-suppliers">
                <h4><i class="fas fa-chart-pie"></i> Distribuição por Fornecedor</h4>
                <div class="suppliers-distribution">
                    ${Object.keys(supplierStats).map(supplierId => {
                        const stat = supplierStats[supplierId];
                        const percent = selectedItems > 0 ? (stat.count / selectedItems * 100).toFixed(1) : 0;
                        return `
                            <div class="supplier-distribution-item">
                                <div class="supplier-info">
                                    <strong>${stat.name}</strong>
                                    <small>${stat.count} itens (${percent}%)</small>
                                </div>
                                <div class="supplier-value">
                                    R$ ${stat.value.toFixed(2).replace('.', ',')}
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${percent}%"></div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>

            <div class="summary-recommendations">
                <h4><i class="fas fa-lightbulb"></i> Recomendações</h4>
                <div class="recommendations-list">
                    ${generateRecommendations(supplierStats, totalItems, selectedItems)}
                </div>
            </div>
        ` : `
            <div class="no-selection-summary">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Análise Incompleta</h4>
                <p>Selecione os vencedores na aba "Comparação por Item" para ver o resumo completo.</p>
                <button class="btn btn-primary" onclick="showAnalysisTab('comparacao')">
                    <i class="fas fa-balance-scale"></i> Fazer Seleções
                </button>
            </div>
        `}
    `;
}

// ===== GERAR RECOMENDAÇÕES =====
function generateRecommendations(supplierStats, totalItems, selectedItems) {
    const recommendations = [];

    // Verificar se há itens não selecionados
    if (selectedItems < totalItems) {
        recommendations.push({
            type: 'warning',
            text: `${totalItems - selectedItems} itens ainda não foram selecionados. Complete a análise para otimizar a compra.`
        });
    }

    // Verificar concentração em poucos fornecedores
    const supplierCount = Object.keys(supplierStats).length;
    if (supplierCount === 1 && selectedItems > 1) {
        recommendations.push({
            type: 'info',
            text: 'Todos os itens foram selecionados de um único fornecedor. Considere diversificar para reduzir riscos.'
        });
    }

    // Verificar fornecedor com muitos itens
    Object.keys(supplierStats).forEach(supplierId => {
        const stat = supplierStats[supplierId];
        const percent = (stat.count / selectedItems) * 100;
        if (percent > 70 && supplierCount > 1) {
            recommendations.push({
                type: 'info',
                text: `${stat.name} foi selecionado para ${percent.toFixed(1)}% dos itens. Verifique se a concentração é adequada.`
            });
        }
    });

    // Recomendação geral
    if (selectedItems === totalItems) {
        recommendations.push({
            type: 'success',
            text: 'Análise completa! Todos os itens foram selecionados. Você pode prosseguir para a geração dos pedidos de compra.'
        });
    }

    return recommendations.map(rec => `
        <div class="recommendation-item ${rec.type}">
            <i class="fas fa-${rec.type === 'success' ? 'check-circle' : rec.type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${rec.text}
        </div>
    `).join('');
}

// ===== FUNÇÕES AUXILIARES =====
window.removeSelection = function(itemIndex) {
    delete selectedWinners[itemIndex];

    // Remover seleção visual na aba de comparação
    document.querySelectorAll(`[data-item="${itemIndex}"]`).forEach(option => {
        option.classList.remove('selected');
        option.querySelector('.selected-badge')?.remove();
    });

    // Atualizar displays
    updateSelectionSummary();
    updateSelectionDisplay();
    calculateSelectionTotals();

    showNotification('Seleção removida', 'info');
};

// ===== FUNÇÕES DE AÇÃO =====
window.exportAnalysis = function() {
    if (!currentAnalysisQuotation) {
        showNotification('Nenhuma análise carregada', 'error');
        return;
    }

    // Gerar dados para exportação
    const exportData = {
        cotacao: {
            numero: currentAnalysisQuotation.numero,
            data: formatDate(currentAnalysisQuotation.dataCriacao),
            status: currentAnalysisQuotation.status
        },
        analise: analysisData,
        selecoes: selectedWinners,
        dataExportacao: new Date().toLocaleString('pt-BR')
    };

    // Criar e baixar arquivo JSON
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `analise-cotacao-${currentAnalysisQuotation.numero || 'sem-numero'}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showNotification('Análise exportada com sucesso', 'success');
};

window.saveSelectionDraft = async function() {
    if (Object.keys(selectedWinners).length === 0) {
        showNotification('Nenhuma seleção para salvar', 'warning');
        return;
    }

    try {
        // Preparar dados da seleção
        const selectionData = {
            cotacaoId: currentAnalysisQuotation.id,
            selecoes: selectedWinners,
            dataSelecao: Timestamp.now(),
            usuario: currentUser.nome || 'Usuário',
            status: 'RASCUNHO'
        };

        // Salvar no Firestore (simulado por enquanto)
        const selectionKey = `selection_${currentAnalysisQuotation.id}`;
        localStorage.setItem(selectionKey, JSON.stringify(selectionData));

        // Atualizar status da cotação
        await updateDoc(doc(db, "cotacoes", currentAnalysisQuotation.id), {
            selecaoRascunho: selectionData,
            ultimaAtualizacao: Timestamp.now()
        });

        showNotification('Seleção salva como rascunho', 'success');

    } catch (error) {
        console.error('Erro ao salvar seleção:', error);
        showNotification('Erro ao salvar seleção: ' + error.message, 'error');
    }
};

window.generatePurchaseOrders = async function() {
    if (Object.keys(selectedWinners).length === 0) {
        showNotification('Selecione pelo menos um item antes de gerar pedidos', 'warning');
        return;
    }

    // Confirmar geração
    const confirmGenerate = confirm(
        `Gerar pedidos de compra para ${Object.keys(selectedWinners).length} itens selecionados?\n\n` +
        'Esta ação criará pedidos de compra baseados nas seleções feitas.'
    );

    if (!confirmGenerate) {
        return;
    }

    try {
        // Agrupar seleções por fornecedor
        const ordersBySupplier = {};

        Object.keys(selectedWinners).forEach(itemIndex => {
            const supplierId = selectedWinners[itemIndex];
            const item = analysisData.items[parseInt(itemIndex)];
            const supplier = analysisData.suppliers[supplierId];
            const supplierItem = item.suppliers.find(s => s.id === supplierId);

            if (!ordersBySupplier[supplierId]) {
                ordersBySupplier[supplierId] = {
                    fornecedor: supplier,
                    itens: [],
                    valorTotal: 0
                };
            }

            ordersBySupplier[supplierId].itens.push({
                codigo: item.codigo,
                descricao: item.descricao,
                quantidade: supplierItem.quantidadeFornecida,
                unidade: item.unidade,
                precoUnitario: supplierItem.precoUnitario,
                ipi: supplierItem.ipi,
                icms: supplierItem.icms,
                valorTotal: supplierItem.total
            });

            ordersBySupplier[supplierId].valorTotal += supplierItem.total;
        });

        // Gerar pedidos de compra
        const pedidosGerados = [];

        for (const supplierId of Object.keys(ordersBySupplier)) {
            const order = ordersBySupplier[supplierId];

            const pedido = {
                numero: `PC-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
                dataCriacao: Timestamp.now(),
                status: 'PENDENTE',
                cotacaoId: currentAnalysisQuotation.id,
                cotacaoNumero: currentAnalysisQuotation.numero,
                fornecedorId: supplierId,
                fornecedorNome: order.fornecedor.nome,
                itens: order.itens,
                valorTotal: order.valorTotal,
                condicoesPagamento: order.fornecedor.condicaoPagamento,
                prazoEntrega: order.fornecedor.prazoEntrega,
                observacoes: `Pedido gerado automaticamente da análise da cotação ${currentAnalysisQuotation.numero}`,
                criadoPor: currentUser.nome || 'Sistema'
            };

            // Salvar no Firestore
            try {
                const docRef = await addDoc(collection(db, "pedidosCompra"), pedido);
                pedido.id = docRef.id;
                console.log('Pedido de compra salvo com sucesso:', pedido.numero, 'ID:', docRef.id);
                pedidosGerados.push(pedido);
            } catch (error) {
                console.error('Erro ao salvar pedido:', pedido.numero, error);
                throw new Error(`Erro ao salvar pedido ${pedido.numero}: ${error.message}`);
            }
        }

        // Atualizar status da cotação
        await updateDoc(doc(db, "cotacoes", currentAnalysisQuotation.id), {
            status: 'FECHADA',
            dataFechamento: Timestamp.now(),
            pedidosGerados: pedidosGerados.map(p => p.numero),
            selecaoFinal: selectedWinners,
            ultimaAtualizacao: Timestamp.now()
        });

        // Mostrar resultado
        const suppliersCount = Object.keys(ordersBySupplier).length;
        const itemsCount = Object.keys(selectedWinners).length;

        showNotification(
            `${pedidosGerados.length} pedidos de compra gerados com sucesso!\n` +
            `${itemsCount} itens distribuídos entre ${suppliersCount} fornecedores.`,
            'success',
            5000
        );

        // Fechar modal e recarregar cotações
        closeModal('analysisModal');
        await loadQuotations();

    } catch (error) {
        console.error('Erro ao gerar pedidos:', error);
        showNotification('Erro ao gerar pedidos de compra: ' + error.message, 'error');
    }
};

// ===== FUNÇÃO AUXILIAR PARA FORMATAÇÃO =====
function formatDate(timestamp) {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('pt-BR');
}
