<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Central de Estoque</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 40px;
        }

        .module-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #3498db;
        }

        .module-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .module-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .module-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
            text-align: center;
        }

        .module-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            transform: translateY(-2px);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
            transform: translateY(-2px);
        }

        .stats-section {
            background: #f8f9fa;
            padding: 30px 40px;
            border-top: 1px solid #e9ecef;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .modules-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-btn">← Voltar</a>

    <div class="container">
        <div class="header">
            <h1>📦 Central de Estoque</h1>
            <p>Sistema unificado de gestão de estoque - Todas as funcionalidades em um só lugar</p>
        </div>

        <div class="modules-grid">
            <!-- Editor Principal -->
            <div class="module-card">
                <div class="module-icon">🎛️</div>
                <div class="module-title">Editor de Saldos</div>
                <div class="module-description">
                    Interface principal para visualizar, editar e analisar saldos de estoque. 
                    Inclui diagnóstico de problemas, relatórios e correções automáticas.
                </div>
                <div class="module-actions">
                    <a href="editor_saldos_estoque.html" class="btn btn-primary">🎛️ Abrir Editor Principal</a>
                </div>
            </div>

            <!-- Movimentações -->
            <div class="module-card">
                <div class="module-icon">🚚</div>
                <div class="module-title">Movimentações</div>
                <div class="module-description">
                    Transferências entre armazéns, movimentações via ordem de produção 
                    e retorno de sobras.
                </div>
                <div class="module-actions">
                    <a href="movimentacao_armazem.html" class="btn btn-success">🚚 Movimentações</a>
                </div>
            </div>

            <!-- Ajustes -->
            <div class="module-card">
                <div class="module-icon">🔧</div>
                <div class="module-title">Ajustes de Estoque</div>
                <div class="module-description">
                    Correções pontuais de saldos, ajustes de inventário 
                    e regularizações de estoque.
                </div>
                <div class="module-actions">
                    <a href="ajuste_estoque.html" class="btn btn-warning">🔧 Ajustes</a>
                </div>
            </div>

            <!-- Gestão Avançada -->
            <div class="module-card">
                <div class="module-icon">📊</div>
                <div class="module-title">Gestão Avançada</div>
                <div class="module-description">
                    Dashboard executivo, análises avançadas e 
                    ferramentas de gestão estratégica.
                </div>
                <div class="module-actions">
                    <a href="gestao_estoque.html" class="btn btn-primary">📊 Gestão Avançada</a>
                </div>
            </div>

            <!-- Problemas e Diagnósticos -->
            <div class="module-card">
                <div class="module-icon">🔍</div>
                <div class="module-title">Análise de Problemas</div>
                <div class="module-description">
                    Identificação e correção de problemas de saldo, 
                    inconsistências e análises detalhadas.
                </div>
                <div class="module-actions">
                    <a href="listar_problemas_saldos.html" class="btn btn-warning">🔍 Analisar Problemas</a>
                </div>
            </div>

            <!-- Configuração Inicial -->
            <div class="module-card">
                <div class="module-icon">🆕</div>
                <div class="module-title">Configuração Inicial</div>
                <div class="module-description">
                    Ferramenta especializada para configurar saldos iniciais
                    em novos clientes e implementações.
                </div>
                <div class="module-actions">
                    <a href="saldos_iniciais.html" class="btn btn-success">🆕 Saldos Iniciais</a>
                </div>
            </div>

            <!-- Ferramentas Administrativas -->
            <div class="module-card">
                <div class="module-icon">⚙️</div>
                <div class="module-title">Ferramentas Admin</div>
                <div class="module-description">
                    Zerar estoques, backup e restauração de dados,
                    ferramentas avançadas de manutenção.
                </div>
                <div class="module-actions">
                    <a href="zerar_estoques_saldos_iniciais.html" class="btn btn-danger">⚙️ Ferramentas Admin</a>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h3 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">📈 Estatísticas do Sistema</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalProdutos">-</div>
                    <div class="stat-label">Produtos Cadastrados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalArmazens">-</div>
                    <div class="stat-label">Armazéns Ativos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalEstoques">-</div>
                    <div class="stat-label">Registros de Estoque</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="valorTotal">-</div>
                    <div class="stat-label">Valor Total Estoque</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Carregar estatísticas
        async function carregarEstatisticas() {
            try {
                const [produtosSnap, armazensSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Calcular valor total do estoque
                let valorTotal = 0;
                estoques.forEach(estoque => {
                    const produto = produtos.find(p => p.id === estoque.produtoId);
                    if (produto && estoque.saldo > 0) {
                        valorTotal += (estoque.saldo || 0) * (produto.valorUnitario || 0);
                    }
                });

                // Atualizar interface
                document.getElementById('totalProdutos').textContent = produtos.length.toLocaleString();
                document.getElementById('totalArmazens').textContent = armazensSnap.size.toLocaleString();
                document.getElementById('totalEstoques').textContent = estoques.length.toLocaleString();
                document.getElementById('valorTotal').textContent = 
                    'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });

            } catch (error) {
                console.error('Erro ao carregar estatísticas:', error);
                document.querySelectorAll('.stat-number').forEach(el => el.textContent = 'Erro');
            }
        }

        // Carregar ao inicializar
        document.addEventListener('DOMContentLoaded', carregarEstatisticas);
    </script>
</body>
</html>
