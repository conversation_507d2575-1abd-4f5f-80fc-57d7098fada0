// ===================================================================
// CONFIGURAÇÃO CENTRALIZADA DO FIREBASE - WiZAR ERP
// ===================================================================
// Este arquivo centraliza TODAS as configurações do Firebase
// Todos os outros arquivos devem importar daqui
// ===================================================================

// Import the functions you need from the SDKs
import { initializeApp, getApps, getApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getFirestore, doc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { getStorage } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

// ===================================================================
// CONFIGURAÇÃO ÚNICA DO FIREBASE - BANCO-MRP
// ===================================================================
// ⚠️  IMPORTANTE: Esta é a ÚNICA configuração válida do sistema
// ⚠️  NÃO duplicar esta configuração em outros arquivos
// ===================================================================
const firebaseConfig = {
      apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
      authDomain: "banco-mrp.firebaseapp.com", 
      projectId: "banco-mrp",
      storageBucket: "banco-mrp.firebasestorage.app",
      messagingSenderId: "740147152218",
      appId: "1:740147152218:web:2d301340bf314e68d75f63",
      measurementId: "G-YNNQ1VX1EH"
};

// ===================================================================
// INICIALIZAÇÃO DO FIREBASE
// ===================================================================
// Initialize Firebase only if no apps exist (evita duplicação)
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

// Initialize services
const db = getFirestore(app);
const storage = getStorage(app);
const auth = getAuth(app);

// ===================================================================
// EXPORTS CENTRALIZADOS
// ===================================================================
// Todos os arquivos devem importar daqui:
// import { db, storage, auth, firebaseConfig } from './firebase-config.js';
// ===================================================================
export {
  db,
  storage,
  auth,
  firebaseConfig,
  app
};

// ===================================================================
// FUNÇÕES UTILITÁRIAS CENTRALIZADAS
// ===================================================================

/**
 * Função para verificar se o Firebase está inicializado
 */
export function isFirebaseInitialized() {
  return getApps().length > 0;
}

/**
 * Função para obter informações do projeto
 */
export function getProjectInfo() {
  return {
    projectId: firebaseConfig.projectId,
    authDomain: firebaseConfig.authDomain,
    storageBucket: firebaseConfig.storageBucket
  };
}

// ===================================================================
// LOG DE INICIALIZAÇÃO
// ===================================================================
console.log('🔥 Firebase inicializado centralmente:', {
  projectId: firebaseConfig.projectId,
  timestamp: new Date().toISOString(),
  apps: getApps().length
});
