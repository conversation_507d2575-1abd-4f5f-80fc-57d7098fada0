<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saldos Iniciais - Estoque</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --header-bg: #354a5f;
        }

        * { box-sizing: border-box; margin: 0; padding: 0; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .form-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .table th {
            background-color: var(--secondary-color);
            font-weight: 600;
        }

        .status-message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 800px;
            border-radius: 5px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .error-list {
            margin-top: 15px;
            padding: 10px;
            background-color: #fff3f3;
            border: 1px solid #ffcdd2;
            border-radius: 4px;
        }

        .error-item {
            color: #d32f2f;
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #ffcdd2;
        }

        .error-item:last-child {
            border-bottom: none;
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: right;
        }

        .modal-buttons button {
            margin-left: 10px;
        }
    </style>

    <script type="module" src="js/main.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Saldos Iniciais - Estoque</h1>
            <button class="btn btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>

        <div class="form-section">
            <h2>Importação de Saldos</h2>
            <div class="form-group">
                <label>Arquivo Excel:</label>
                <input type="file" id="excelFile" accept=".xlsx,.xls" class="form-control" onchange="handleFileSelect(event)">
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="importarSaldos()">Importar Saldos</button>
                <button class="btn btn-secondary" onclick="exportarModelo()">Exportar Modelo Excel</button>
            </div>
            <div id="importStatus" class="status-message"></div>
        </div>

        <div class="form-section">
            <h2>Cadastro Manual</h2>
            <div class="form-group">
                <label>Produto:</label>
                <select id="produto" class="form-control" onchange="atualizarUnidade()"></select>
            </div>
            <div class="form-group">
                <label>Quantidade:</label>
                <input type="number" id="quantidade" class="form-control" step="0.001" min="0">
            </div>
            <div class="form-group">
                <label>Armazém:</label>
                <select id="armazem" class="form-control"></select>
            </div>
            <div class="form-group">
                <label>Lote:</label>
                <input type="text" id="lote" class="form-control">
            </div>
            <div class="form-group">
                <label>Preço Unitário:</label>
                <input type="number" id="precoUnitario" class="form-control" step="0.01" min="0">
            </div>
            <button class="btn btn-primary" onclick="cadastrarSaldo()">Cadastrar</button>
        </div>

        <div class="form-section">
            <h2>Saldos Cadastrados</h2>
            <button class="btn btn-primary" onclick="exportarSaldosIniciais()">Exportar Saldos Iniciais</button>
            <table class="table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Tipo</th>
                        <th>Unidade</th>
                        <th>Armazém</th>
                        <th>Lote</th>
                        <th>Quantidade</th>
                        <th>Preço Unitário</th>
                        <th>Data Cadastro</th>
                    </tr>
                </thead>
                <tbody id="saldosTable"></tbody>
            </table>
        </div>
    </div>

    <!-- Modal de Validação -->
    <div id="validationModal" class="modal">
        <div class="modal-content">
            <h3>Validação do Arquivo Excel</h3>
            <div id="validationResults"></div>
            <div class="modal-buttons">
                <button onclick="closeValidationModal()" class="btn btn-secondary">Fechar</button>
                <button onclick="processValidatedFile()" class="btn btn-primary" id="processButton" style="display: none;">Processar Arquivo</button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs,
            query,
            where,
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let estoques = [];
        let currentFileData = null; // Variável para armazenar os dados do arquivo atual

        window.onload = async function() {
            await carregarDados();
            popularSelects();
            carregarSaldos();
        };

        async function carregarDados() {
            try {
                const [produtosSnap, armazensSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                mostrarMensagem("Erro ao carregar dados", "error");
            }
        }

        function popularSelects() {
            const produtoSelect = document.getElementById('produto');
            const armazemSelect = document.getElementById('armazem');

            produtoSelect.innerHTML = '<option value="">Selecione um produto...</option>';
            produtos.forEach(produto => {
                produtoSelect.innerHTML += `
                    <option value="${produto.id}">
                        ${produto.codigo} - ${produto.descricao}
                    </option>`;
            });

            armazemSelect.innerHTML = '<option value="">Selecione um armazém...</option>';
            armazens.forEach(armazem => {
                armazemSelect.innerHTML += `
                    <option value="${armazem.id}">
                        ${armazem.codigo} - ${armazem.descricao}
                    </option>`;
            });
        }

        window.atualizarUnidade = function() {
            const produtoId = document.getElementById('produto').value;
            const produto = produtos.find(p => p.id === produtoId);
            if (produto) {
                const qtdInput = document.getElementById('quantidade');
                qtdInput.placeholder = `Quantidade em ${produto.unidade}`;
            }
        };

        window.cadastrarSaldo = async function() {
            const produtoId = document.getElementById('produto').value;
            const armazemId = document.getElementById('armazem').value;
            const quantidade = parseFloat(document.getElementById('quantidade').value);
            const lote = document.getElementById('lote').value;

            if (!produtoId || !armazemId || !quantidade || quantidade <= 0) {
                mostrarMensagem("Preencha todos os campos corretamente", "error");
                return;
            }

            try {
                const produto = produtos.find(p => p.id === produtoId);
                const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);

                if (estoque) {
                    mostrarMensagem("Já existe saldo para este produto neste armazém", "error");
                    return;
                }

                const precoUnitario = parseFloat(document.getElementById('precoUnitario').value) || 0;
                const saldoInicial = {
                    produtoId,
                    armazemId,
                    saldo: quantidade,
                    saldoReservado: 0,
                    lote,
                    precoUnitario,
                    dataCadastro: Timestamp.now()
                };

                await addDoc(collection(db, "estoques"), saldoInicial);

                await addDoc(collection(db, "movimentacoesEstoque"), {
                    produtoId,
                    tipo: 'ENTRADA',
                    quantidade,
                    tipoDocumento: 'SALDO_INICIAL',
                    numeroDocumento: 'SI-' + new Date().getTime(),
                    observacoes: 'Saldo inicial do produto',
                    dataHora: Timestamp.now(),
                    armazemId,
                    lote
                });

                mostrarMensagem("Saldo cadastrado com sucesso!", "success");
                await carregarDados();
                carregarSaldos();
                limparFormulario();
            } catch (error) {
                console.error("Erro ao cadastrar saldo:", error);
                mostrarMensagem("Erro ao cadastrar saldo", "error");
            }
        };

        window.importarSaldos = async function() {
            const fileInput = document.getElementById('excelFile');
            const file = fileInput.files[0];

            if (!file) {
                mostrarMensagem("Selecione um arquivo Excel", "error");
                return;
            }

            try {
                const data = await lerArquivoExcel(file);
                console.log("Dados lidos do Excel:", data);

                let sucessos = 0;
                let erros = 0;
                let errosDetalhados = [];

                const colunasNecessarias = ['Código', 'Quantidade', 'Armazém', 'Lote', 'Preço Unitário'];
                const primeiraLinha = data[0];
                console.log("Primeira linha do arquivo:", primeiraLinha);

                const colunasFaltantes = colunasNecessarias.filter(col => !(col in primeiraLinha));
                if (colunasFaltantes.length > 0) {
                    console.error("Colunas faltando:", colunasFaltantes);
                    throw new Error(`Colunas obrigatórias faltando: ${colunasFaltantes.join(', ')}`);
                }

                for (let i = 0; i < data.length; i++) {
                    const row = data[i];
                    console.log(`Processando linha ${i + 1}:`, row);

                    try {
                        const codigoProduto = String(row['Código']).trim().toUpperCase();
                        console.log(`Buscando produto com código: ${codigoProduto}`);
                        const produto = produtos.find(p => p.codigo.toUpperCase() === codigoProduto);

                        if (!produto) {
                            const erro = `Linha ${i + 1}: Produto não encontrado: ${codigoProduto}`;
                            console.error(erro);
                            errosDetalhados.push(erro);
                            erros++;
                            continue;
                        }

                        const codigoArmazem = String(row['Armazém']).trim().toUpperCase();
                        console.log(`Buscando armazém com código: ${codigoArmazem}`);
                        const armazem = armazens.find(a => a.codigo.toUpperCase() === codigoArmazem);

                        if (!armazem) {
                            const erro = `Linha ${i + 1}: Armazém não encontrado: ${codigoArmazem}`;
                            console.error(erro);
                            errosDetalhados.push(erro);
                            erros++;
                            continue;
                        }

                        const quantidade = parseFloat(String(row['Quantidade']).replace(',', '.'));
                        if (isNaN(quantidade) || quantidade <= 0) {
                            const erro = `Linha ${i + 1}: Quantidade inválida para produto ${codigoProduto}: ${row['Quantidade']}`;
                            console.error(erro);
                            errosDetalhados.push(erro);
                            erros++;
                            continue;
                        }

                        const estoque = estoques.find(e => 
                            e.produtoId === produto.id && e.armazemId === armazem.id);

                        if (estoque) {
                            const erro = `Linha ${i + 1}: Já existe saldo para o produto ${codigoProduto} no armazém ${codigoArmazem}`;
                            console.error(erro);
                            errosDetalhados.push(erro);
                            erros++;
                            continue;
                        }

                        const precoUnitario = parseFloat(String(row['Preço Unitário'] || '0').replace(',', '.'));

                        console.log(`Criando estoque para produto ${codigoProduto} no armazém ${codigoArmazem}`);

                        await addDoc(collection(db, "estoques"), {
                            produtoId: produto.id,
                            armazemId: armazem.id,
                            saldo: quantidade,
                            saldoReservado: 0,
                            lote: String(row['Lote'] || 'INICIAL').trim(),
                            precoUnitario: isNaN(precoUnitario) ? 0 : precoUnitario,
                            dataCadastro: Timestamp.now()
                        });

                        await addDoc(collection(db, "movimentacoesEstoque"), {
                            produtoId: produto.id,
                            tipo: 'ENTRADA',
                            quantidade,
                            tipoDocumento: 'SALDO_INICIAL',
                            numeroDocumento: 'SI-' + new Date().getTime(),
                            observacoes: 'Importação de saldo inicial',
                            dataHora: Timestamp.now(),
                            armazemId: armazem.id,
                            lote: String(row['Lote'] || 'INICIAL').trim()
                        });

                        console.log(`Linha ${i + 1} processada com sucesso`);
                        sucessos++;
                    } catch (error) {
                        console.error(`Erro ao processar linha ${i + 1}:`, error);
                        errosDetalhados.push(`Linha ${i + 1}: ${error.message}`);
                        erros++;
                    }
                }

                let mensagem = `Importação concluída: ${sucessos} sucessos, ${erros} erros`;
                if (erros > 0) {
                    mensagem += '\n\nDetalhes dos erros:\n' + errosDetalhados.slice(0, 10).join('\n');
                    if (errosDetalhados.length > 10) {
                        mensagem += `\n... e mais ${errosDetalhados.length - 10} erros`;
                    }
                }
                console.log("Resumo da importação:", mensagem);
                mostrarMensagem(mensagem, sucessos > 0 ? "success" : "error");

                await carregarDados();
                carregarSaldos();
                fileInput.value = '';
            } catch (error) {
                console.error("Erro na importação:", error);
                mostrarMensagem("Erro ao processar arquivo: " + error.message, "error");
            }
        };

        async function lerArquivoExcel(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = e.target.result;
                        const workbook = XLSX.read(data, { type: 'binary' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        const json = XLSX.utils.sheet_to_json(worksheet);
                        resolve(json);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = reject;
                reader.readAsBinaryString(file);
            });
        }

        async function carregarSaldos() {
            const tableBody = document.getElementById('saldosTable');
            tableBody.innerHTML = '';

            estoques.forEach(estoque => {
                const produto = produtos.find(p => p.id === estoque.produtoId);
                const armazem = armazens.find(a => a.id === estoque.armazemId);
                if (!produto || !armazem) return;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${produto.codigo}</td>
                    <td>${produto.descricao}</td>
                    <td>${produto.tipo}</td>
                    <td>${produto.unidade}</td>
                    <td>${armazem.codigo}</td>
                    <td>${estoque.lote || '-'}</td>
                    <td>${estoque.saldo.toFixed(3)}</td>
                    <td>R$ ${estoque.precoUnitario ? estoque.precoUnitario.toFixed(2) : '0.00'}</td>
                    <td>${estoque.dataCadastro ? new Date(estoque.dataCadastro.seconds * 1000).toLocaleString() : '-'}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        function limparFormulario() {
            document.getElementById('produto').value = '';
            document.getElementById('quantidade').value = '';
            document.getElementById('armazem').value = '';
            document.getElementById('lote').value = '';
        }

        function mostrarMensagem(mensagem, tipo) {
            const statusElement = document.getElementById('importStatus');
            statusElement.textContent = mensagem;
            statusElement.className = `status-message ${tipo}`;
            statusElement.style.display = 'block';
            statusElement.style.whiteSpace = 'pre-line';
            setTimeout(() => statusElement.style.display = 'none', 10000);
        }

        window.exportarModelo = async function() {
            try {
                // Buscar produtos do Firestore usando módulos
                const produtosRef = collection(db, 'produtos');
                const q = query(produtosRef, where('status', '==', 'ativo'));
                const snapshot = await getDocs(q);

                if (snapshot.empty) {
                    mostrarMensagem("Nenhum produto ativo encontrado", "warning");
                    return;
                }

                // Preparar dados dos produtos
                const dados = snapshot.docs.map(doc => {
                    const produto = doc.data();
                    return {
                        'Código': produto.codigo || '',
                        'Quantidade': '0.000',
                        'Armazém': 'ALM001',
                        'Lote': 'LOTE001',
                        'Preço Unitário': '0.00'
                    };
                });

                // Criar planilha com os produtos
                const ws = XLSX.utils.json_to_sheet(dados);

                // Criar planilha de instruções
                const wsInstrucoes = XLSX.utils.aoa_to_sheet([
                    ['INSTRUÇÕES PARA IMPORTAÇÃO DE SALDOS'],
                    [''],
                    ['Coluna Código:'],
                    ['- Deve conter o código do produto cadastrado no sistema'],
                    ['- Exemplo: "PROD001"'],
                    [''],
                    ['Coluna Quantidade:'],
                    ['- Deve ser um número maior ou igual a zero'],
                    ['- Use ponto como separador decimal'],
                    ['- Exemplo: "100.000"'],
                    [''],
                    ['Coluna Armazém:'],
                    ['- Deve conter o código do armazém cadastrado no sistema'],
                    ['- Exemplo: "ALM001"'],
                    ['- Códigos disponíveis:'],
                    ...armazens.map(a => [`- ${a.codigo} (${a.descricao})`]),
                    [''],
                    ['Coluna Lote:'],
                    ['- Identificação do lote do produto'],
                    ['- Exemplo: "LOTE001"'],
                    [''],
                    ['Coluna Preço Unitário:'],
                    ['- Valor unitário do produto'],
                    ['- Use ponto como separador decimal'],
                    ['- Exemplo: "10.50"']
                ]);

                // Configurar larguras das colunas
                const wscols = [
                    {wch: 15},
                    {wch: 15},
                    {wch: 15},
                    {wch: 15},
                    {wch: 15}
                ];
                ws['!cols'] = wscols;
                wsInstrucoes['!cols'] = [{wch: 50}];

                // Criar workbook e adicionar planilhas
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, wsInstrucoes, "Instruções");
                XLSX.utils.book_append_sheet(wb, ws, "Modelo");

                // Formatar cabeçalhos
                const range = XLSX.utils.decode_range(ws['!ref']);
                for(let C = range.s.c; C <= range.e.c; ++C) {
                    const cell = XLSX.utils.encode_cell({r: 0, c: C});
                    if(!ws[cell]) ws[cell] = { v: "" };
                    ws[cell].s = {
                        font: { bold: true },
                        fill: { fgColor: { rgb: "CCCCCC" } }
                    };
                }

                // Formatar título das instruções
                wsInstrucoes['A1'].s = {
                    font: { bold: true, color: { rgb: "0854A0" } },
                    alignment: { horizontal: "center" }
                };

                // Exportar arquivo
                XLSX.writeFile(wb, "modelo_importacao_saldos.xlsx");
                mostrarMensagem("Modelo exportado com sucesso!", "success");
            } catch (error) {
                console.error("Erro ao exportar modelo:", error);
                mostrarMensagem("Erro ao exportar modelo: " + error.message, "error");
            }
        };

        // Função global para lidar com a seleção de arquivo
        window.handleFileSelect = async function(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const data = await lerArquivoExcel(file);
                currentFileData = data; // Armazena os dados para uso posterior
                const validationResults = validateExcelData(data);

                if (validationResults.hasErrors) {
                    showValidationModal(validationResults);
                } else {
                    // Se não houver erros, processa o arquivo diretamente
                    importarSaldos();
                }
            } catch (error) {
                mostrarMensagem('Erro ao ler o arquivo: ' + error.message, 'error');
            }
        };

        // Função global para processar o arquivo validado
        window.processValidatedFile = function() {
            closeValidationModal();
            if (currentFileData) {
                importarSaldos();
            }
        };

        // Função global para fechar o modal
        window.closeValidationModal = function() {
            const modal = document.getElementById('validationModal');
            modal.style.display = 'none';
        };

        // Função global para mostrar o modal
        window.showValidationModal = function(validationResults) {
            const modal = document.getElementById('validationModal');
            const resultsDiv = document.getElementById('validationResults');
            const processButton = document.getElementById('processButton');

            let html = '<div class="error-list">';
            if (validationResults.errors.length > 0) {
                html += '<h4>Erros encontrados:</h4>';
                validationResults.errors.forEach(error => {
                    html += `<div class="error-item">${error}</div>`;
                });
            } else {
                html += '<div class="error-item">Nenhum erro encontrado!</div>';
            }
            html += '</div>';

            resultsDiv.innerHTML = html;
            processButton.style.display = validationResults.errors.length === 0 ? 'inline-block' : 'none';
            modal.style.display = 'block';
        };

        // Função para validar os dados do Excel
        function validateExcelData(data) {
            const errors = [];
            const requiredColumns = ['Código', 'Quantidade', 'Armazém'];
            const validArmazens = new Set(armazens.map(a => a.codigo.toUpperCase()));

            // Verifica se há dados
            if (!data || data.length === 0) {
                errors.push('O arquivo está vazio');
                return { hasErrors: true, errors };
            }

            // Verifica as colunas obrigatórias
            const firstRow = data[0];
            const missingColumns = requiredColumns.filter(col => !(col in firstRow));
            if (missingColumns.length > 0) {
                errors.push(`Colunas obrigatórias ausentes: ${missingColumns.join(', ')}`);
            }

            // Valida cada linha
            data.forEach((row, index) => {
                const lineNumber = index + 2; // +2 porque a primeira linha é o cabeçalho e o índice começa em 0

                // Verifica código do produto
                if (!row['Código']) {
                    errors.push(`Linha ${lineNumber}: Código do produto não informado`);
                } else {
                    const codigoProduto = String(row['Código']).trim().toUpperCase();
                    const produto = produtos.find(p => p.codigo.toUpperCase() === codigoProduto);
                    if (!produto) {
                        errors.push(`Linha ${lineNumber}: Produto não encontrado: ${codigoProduto}`);
                    }
                }

                // Verifica quantidade
                const quantidade = parseFloat(String(row['Quantidade']).replace(',', '.'));
                if (isNaN(quantidade) || quantidade < 0) {
                    errors.push(`Linha ${lineNumber}: Quantidade inválida`);
                }

                // Verifica armazém
                if (!row['Armazém']) {
                    errors.push(`Linha ${lineNumber}: Armazém não informado`);
                } else {
                    const codigoArmazem = String(row['Armazém']).trim().toUpperCase();
                    if (!validArmazens.has(codigoArmazem)) {
                        errors.push(`Linha ${lineNumber}: Armazém não encontrado: ${codigoArmazem}`);
                    }
                }
            });

            return {
                hasErrors: errors.length > 0,
                errors,
                validArmazens: Array.from(validArmazens)
            };
        }

        // Fecha o modal quando clicar fora dele
        window.onclick = function(event) {
            const modal = document.getElementById('validationModal');
            if (event.target == modal) {
                closeValidationModal();
            }
        }

        window.exportarSaldosIniciais = function() {
            // Monta os dados reais dos saldos iniciais
            const dados = estoques.map(estoque => {
                const produto = produtos.find(p => p.id === estoque.produtoId) || {};
                const armazem = armazens.find(a => a.id === estoque.armazemId) || {};
                return {
                    'Código': produto.codigo || '',
                    'Descrição': produto.descricao || '',
                    'Tipo': produto.tipo || '',
                    'Unidade': produto.unidade || '',
                    'Armazém': armazem.codigo || '',
                    'Lote': estoque.lote || '',
                    'Quantidade': estoque.saldo != null ? estoque.saldo.toFixed(3) : '',
                    'Preço Unitário': estoque.precoUnitario != null ? estoque.precoUnitario.toFixed(2) : '',
                    'Data Cadastro': estoque.dataCadastro ? new Date(estoque.dataCadastro.seconds * 1000).toLocaleString() : ''
                };
            });
            if (dados.length === 0) {
                mostrarMensagem('Nenhum saldo inicial cadastrado para exportar.', 'warning');
                return;
            }
            const ws = XLSX.utils.json_to_sheet(dados);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Saldos Iniciais');
            XLSX.writeFile(wb, 'saldos_iniciais_exportados.xlsx');
            mostrarMensagem('Saldos iniciais exportados com sucesso!', 'success');
        };
    </script>
</body>
</html>
