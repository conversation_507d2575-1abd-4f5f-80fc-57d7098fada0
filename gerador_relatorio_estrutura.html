<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Gerador de Relatório - Estrutura do Banco de Dados</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .controls {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }

        .progress-bar {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .results {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border: 1px solid #e9ecef;
            display: none;
        }

        .collection-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .collection-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .collection-content {
            padding: 20px;
        }

        .field-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .field-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
        }

        .field-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .field-type {
            color: #7f8c8d;
            font-size: 12px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .report-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-database"></i>
                Gerador de Relatório - Estrutura do Banco
            </h1>
            <p>Analise completa das coleções e campos do Firebase Firestore</p>
        </div>

        <div class="controls">
            <h3><i class="fas fa-cogs"></i> Controles</h3>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="analisarEstrutura()">
                    <i class="fas fa-search"></i>
                    Analisar Estrutura
                </button>
                <button class="btn btn-success" onclick="exportarRelatorio()" id="exportBtn" style="display: none;">
                    <i class="fas fa-download"></i>
                    Exportar Relatório
                </button>
                <button class="btn btn-warning" onclick="copiarRelatorio()" id="copyBtn" style="display: none;">
                    <i class="fas fa-copy"></i>
                    Copiar para Clipboard
                </button>
            </div>
            
            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>
        </div>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalCollections">0</div>
                <div class="stat-label">Coleções</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalDocuments">0</div>
                <div class="stat-label">Documentos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalFields">0</div>
                <div class="stat-label">Campos Únicos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="analysisTime">0s</div>
                <div class="stat-label">Tempo de Análise</div>
            </div>
        </div>

        <div class="results" id="results">
            <h3><i class="fas fa-list"></i> Estrutura das Coleções</h3>
            <div id="collectionsContainer"></div>
        </div>

        <div class="export-area" id="exportArea" style="display: none;">
            <h3><i class="fas fa-file-export"></i> Relatório Exportável</h3>
            <p>Copie o conteúdo abaixo para compartilhar:</p>
            <div class="report-content" id="reportContent"></div>
            <button class="btn btn-primary" onclick="toggleReportContent()">
                <i class="fas fa-eye"></i>
                <span id="toggleText">Mostrar Relatório</span>
            </button>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            doc,
            getDoc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let estruturaCompleta = {};
        let relatorioTexto = '';

        window.analisarEstrutura = async function() {
            const startTime = Date.now();
            
            try {
                document.getElementById('progress').style.display = 'block';
                document.getElementById('results').style.display = 'none';
                document.getElementById('stats').style.display = 'none';
                document.getElementById('exportArea').style.display = 'none';
                
                updateProgress(0, 'Iniciando análise...');

                // Lista de coleções conhecidas do seu sistema
                const colecoes = [
                    'produtos', 'armazens', 'estoques', 'ordensProducao',
                    'solicitacoesCompra', 'cotacoes', 'pedidosCompra',
                    'fornecedores', 'usuarios', 'parametros',
                    'transferenciasArmazem', 'movimentacoesEstoque',
                    'familias', 'grupos', 'operacoes', 'setores',
                    'recebimentos', 'inspecoes', 'qualidade',
                    'manutencao', 'ordensServico', 'equipamentos'
                ];

                estruturaCompleta = {};
                let totalDocuments = 0;
                let totalFields = new Set();

                for (let i = 0; i < colecoes.length; i++) {
                    const nomeColecao = colecoes[i];
                    const progresso = ((i + 1) / colecoes.length) * 100;
                    
                    updateProgress(progresso, `Analisando ${nomeColecao}...`);
                    
                    try {
                        const snapshot = await getDocs(collection(db, nomeColecao));
                        const documentos = snapshot.docs;
                        
                        if (documentos.length > 0) {
                            estruturaCompleta[nomeColecao] = {
                                totalDocumentos: documentos.length,
                                campos: {},
                                exemplos: {}
                            };

                            // Analisar campos de todos os documentos
                            documentos.forEach(doc => {
                                const data = doc.data();
                                analisarCampos(data, estruturaCompleta[nomeColecao].campos, '');
                                
                                // Guardar exemplo do primeiro documento
                                if (Object.keys(estruturaCompleta[nomeColecao].exemplos).length === 0) {
                                    estruturaCompleta[nomeColecao].exemplos = data;
                                }
                            });

                            totalDocuments += documentos.length;
                            Object.keys(estruturaCompleta[nomeColecao].campos).forEach(campo => {
                                totalFields.add(`${nomeColecao}.${campo}`);
                            });
                        }
                    } catch (error) {
                        console.warn(`Coleção ${nomeColecao} não encontrada ou sem acesso:`, error);
                    }
                }

                const endTime = Date.now();
                const analysisTime = ((endTime - startTime) / 1000).toFixed(1);

                // Atualizar estatísticas
                document.getElementById('totalCollections').textContent = Object.keys(estruturaCompleta).length;
                document.getElementById('totalDocuments').textContent = totalDocuments;
                document.getElementById('totalFields').textContent = totalFields.size;
                document.getElementById('analysisTime').textContent = analysisTime + 's';

                // Mostrar resultados
                renderizarResultados();
                gerarRelatorioTexto();

                document.getElementById('progress').style.display = 'none';
                document.getElementById('stats').style.display = 'grid';
                document.getElementById('results').style.display = 'block';
                document.getElementById('exportArea').style.display = 'block';
                document.getElementById('exportBtn').style.display = 'inline-flex';
                document.getElementById('copyBtn').style.display = 'inline-flex';

            } catch (error) {
                console.error('Erro na análise:', error);
                alert('Erro ao analisar estrutura: ' + error.message);
                document.getElementById('progress').style.display = 'none';
            }
        };

        function analisarCampos(obj, campos, prefixo) {
            Object.keys(obj).forEach(key => {
                const valor = obj[key];
                const nomeCompleto = prefixo ? `${prefixo}.${key}` : key;
                
                if (valor === null) {
                    campos[nomeCompleto] = 'null';
                } else if (Array.isArray(valor)) {
                    campos[nomeCompleto] = 'array';
                    if (valor.length > 0 && typeof valor[0] === 'object' && valor[0] !== null) {
                        analisarCampos(valor[0], campos, nomeCompleto + '[0]');
                    }
                } else if (typeof valor === 'object' && valor !== null) {
                    if (valor.seconds !== undefined) {
                        campos[nomeCompleto] = 'timestamp';
                    } else {
                        campos[nomeCompleto] = 'object';
                        analisarCampos(valor, campos, nomeCompleto);
                    }
                } else {
                    campos[nomeCompleto] = typeof valor;
                }
            });
        }

        function updateProgress(percent, message) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
            progressBar.textContent = Math.round(percent) + '%';
        }

        function renderizarResultados() {
            const container = document.getElementById('collectionsContainer');
            container.innerHTML = '';

            Object.keys(estruturaCompleta).sort().forEach(nomeColecao => {
                const colecao = estruturaCompleta[nomeColecao];
                
                const div = document.createElement('div');
                div.className = 'collection-item';
                
                div.innerHTML = `
                    <div class="collection-header">
                        <span><i class="fas fa-folder"></i> ${nomeColecao}</span>
                        <span>${colecao.totalDocumentos} documentos</span>
                    </div>
                    <div class="collection-content">
                        <div class="field-list">
                            ${Object.keys(colecao.campos).sort().map(campo => `
                                <div class="field-item">
                                    <div class="field-name">${campo}</div>
                                    <div class="field-type">${colecao.campos[campo]}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
                
                container.appendChild(div);
            });
        }

        function gerarRelatorioTexto() {
            const agora = new Date().toLocaleString('pt-BR');
            
            relatorioTexto = `# 📊 RELATÓRIO DE ESTRUTURA DO BANCO DE DADOS
## Sistema de Gestão Empresarial

**Data de Geração:** ${agora}
**Total de Coleções:** ${Object.keys(estruturaCompleta).length}
**Total de Documentos:** ${document.getElementById('totalDocuments').textContent}
**Total de Campos Únicos:** ${document.getElementById('totalFields').textContent}

---

## 📋 ESTRUTURA DETALHADA DAS COLEÇÕES

`;

            Object.keys(estruturaCompleta).sort().forEach(nomeColecao => {
                const colecao = estruturaCompleta[nomeColecao];
                
                relatorioTexto += `### 📁 ${nomeColecao.toUpperCase()}
**Documentos:** ${colecao.totalDocumentos}
**Campos:**
`;
                
                Object.keys(colecao.campos).sort().forEach(campo => {
                    relatorioTexto += `- \`${campo}\` (${colecao.campos[campo]})\n`;
                });
                
                relatorioTexto += '\n---\n\n';
            });

            relatorioTexto += `## 📈 ESTATÍSTICAS RESUMIDAS

| Métrica | Valor |
|---------|-------|
| Coleções Analisadas | ${Object.keys(estruturaCompleta).length} |
| Total de Documentos | ${document.getElementById('totalDocuments').textContent} |
| Campos Únicos | ${document.getElementById('totalFields').textContent} |
| Tempo de Análise | ${document.getElementById('analysisTime').textContent} |

---

## 🔍 OBSERVAÇÕES

- Este relatório foi gerado automaticamente
- Inclui apenas coleções com documentos existentes
- Campos aninhados são representados com notação de ponto
- Arrays são analisados pelo primeiro elemento quando aplicável

---

**Gerado por:** Sistema de Análise de Estrutura de Banco
**Versão:** 1.0.0`;

            document.getElementById('reportContent').textContent = relatorioTexto;
        }

        window.exportarRelatorio = function() {
            const blob = new Blob([relatorioTexto], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `estrutura_banco_${new Date().toISOString().split('T')[0]}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        window.copiarRelatorio = function() {
            navigator.clipboard.writeText(relatorioTexto).then(() => {
                alert('Relatório copiado para o clipboard!');
            }).catch(err => {
                console.error('Erro ao copiar:', err);
                alert('Erro ao copiar relatório');
            });
        };

        window.toggleReportContent = function() {
            const content = document.getElementById('reportContent');
            const toggleText = document.getElementById('toggleText');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleText.textContent = 'Ocultar Relatório';
            } else {
                content.style.display = 'none';
                toggleText.textContent = 'Mostrar Relatório';
            }
        };
    </script>
</body>
</html>
