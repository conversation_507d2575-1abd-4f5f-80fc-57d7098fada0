<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Correção Universal de Dados</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .correction-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }
        .correction-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #dc3545;
            transition: transform 0.2s ease;
        }
        .tool-card:hover {
            transform: translateY(-2px);
        }
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .tool-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        .tool-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #dc3545;
        }
        .tool-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .tool-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }
        .batch-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .results-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .problem-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .problem-item.fixed {
            background: #d4edda;
            border-color: #28a745;
        }
        .problem-item.critical {
            background: #f8d7da;
            border-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="correction-container">
        <div class="correction-header">
            <h1>🔧 Correção Universal de Dados</h1>
            <p>Ferramenta avançada para correção e manutenção do banco de dados</p>
            <p><strong>⚠️ ATENÇÃO:</strong> Sempre faça backup antes de executar correções</p>
        </div>

        <!-- Ferramentas de Correção -->
        <div class="tools-grid">
            <!-- Correção de Saldos -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">💰</div>
                    <div class="tool-title">Correção de Saldos</div>
                </div>
                <div class="tool-description">
                    Corrige saldos undefined, null ou inconsistentes nos estoques.
                </div>
                <div class="tool-controls">
                    <div class="input-group">
                        <label>Código do produto (opcional):</label>
                        <input type="text" id="saldoProduto" placeholder="Digite código específico ou deixe vazio para todos">
                    </div>
                    <button onclick="corrigirSaldos()" class="btn btn-primary">🔧 Corrigir Saldos</button>
                    <button onclick="verificarSaldos()" class="btn btn-warning">🔍 Apenas Verificar</button>
                </div>
            </div>

            <!-- Limpeza de Registros Órfãos -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🗑️</div>
                    <div class="tool-title">Limpeza de Órfãos</div>
                </div>
                <div class="tool-description">
                    Remove registros que não possuem referência válida para produtos.
                </div>
                <div class="tool-controls">
                    <div class="input-group">
                        <label>Coleção:</label>
                        <select id="orfaoColecao">
                            <option value="estoques">Estoques</option>
                            <option value="estoqueQualidade">Estoque Qualidade</option>
                            <option value="movimentacoesEstoque">Movimentações</option>
                        </select>
                    </div>
                    <button onclick="limparOrfaos()" class="btn btn-danger">🗑️ Limpar Órfãos</button>
                    <button onclick="identificarOrfaos()" class="btn btn-warning">🔍 Identificar</button>
                </div>
            </div>

            <!-- Correção de Campos Undefined -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">❓</div>
                    <div class="tool-title">Campos Undefined</div>
                </div>
                <div class="tool-description">
                    Corrige campos com valores undefined, null ou vazios.
                </div>
                <div class="tool-controls">
                    <div class="input-group">
                        <label>Valor padrão para números:</label>
                        <input type="number" id="valorPadraoNumero" value="0" step="0.01">
                    </div>
                    <div class="input-group">
                        <label>Valor padrão para texto:</label>
                        <input type="text" id="valorPadraoTexto" value="N/A">
                    </div>
                    <button onclick="corrigirUndefined()" class="btn btn-primary">🔧 Corrigir</button>
                </div>
            </div>

            <!-- Sincronização de Dados -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔄</div>
                    <div class="tool-title">Sincronização</div>
                </div>
                <div class="tool-description">
                    Sincroniza dados entre coleções relacionadas.
                </div>
                <div class="tool-controls">
                    <button onclick="sincronizarProdutos()" class="btn btn-primary">🔄 Produtos ↔ Estoques</button>
                    <button onclick="sincronizarMovimentacoes()" class="btn btn-primary">🔄 Movimentações</button>
                </div>
            </div>

            <!-- Recálculo de Totais -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🧮</div>
                    <div class="tool-title">Recálculo de Totais</div>
                </div>
                <div class="tool-description">
                    Recalcula totais e valores derivados.
                </div>
                <div class="tool-controls">
                    <button onclick="recalcularSaldos()" class="btn btn-primary">🧮 Saldos Totais</button>
                    <button onclick="recalcularValores()" class="btn btn-primary">💰 Valores</button>
                </div>
            </div>

            <!-- Correção de Datas -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📅</div>
                    <div class="tool-title">Correção de Datas</div>
                </div>
                <div class="tool-description">
                    Corrige formatos de data inconsistentes.
                </div>
                <div class="tool-controls">
                    <div class="input-group">
                        <label>Coleção:</label>
                        <select id="dataColecao">
                            <option value="solicitacoesCompra">Solicitações</option>
                            <option value="cotacoes">Cotações</option>
                            <option value="pedidosCompra">Pedidos</option>
                            <option value="movimentacoesEstoque">Movimentações</option>
                        </select>
                    </div>
                    <button onclick="corrigirDatas()" class="btn btn-primary">📅 Corrigir Datas</button>
                </div>
            </div>
        </div>

        <!-- Ações em Lote -->
        <div class="batch-section">
            <h3>⚡ Ações em Lote</h3>
            <p>Execute múltiplas correções de uma vez (use com extremo cuidado):</p>
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="correcaoCompleta()" class="btn btn-danger">🚨 Correção Completa</button>
                <button onclick="manutencaoPreventiva()" class="btn btn-warning">🛠️ Manutenção Preventiva</button>
                <button onclick="otimizacaoPerformance()" class="btn btn-success">⚡ Otimização</button>
            </div>
        </div>

        <!-- Log de Atividades -->
        <div class="log-area" id="logArea"></div>

        <!-- Resultados -->
        <div class="results-section" id="resultsSection" style="display: none;">
            <h3>📋 Resultados das Correções</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            addDoc,
            query,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let correctionResults = [];

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += '<div class="' + type + '">[' + timestamp + '] ' + message + '</div>';
            logArea.scrollTop = logArea.scrollHeight;
        }

        function addResult(type, title, description, details = []) {
            correctionResults.push({
                type: type,
                title: title,
                description: description,
                details: details,
                timestamp: new Date()
            });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const section = document.getElementById('resultsSection');
            const content = document.getElementById('resultsContent');
            
            if (correctionResults.length === 0) {
                section.style.display = 'none';
                return;
            }
            
            section.style.display = 'block';
            
            let html = '';
            correctionResults.forEach(function(result) {
                const className = result.type === 'error' ? 'critical' : 
                                result.type === 'success' ? 'fixed' : '';
                
                html += '<div class="problem-item ' + className + '">' +
                    '<h4>' + result.title + '</h4>' +
                    '<p>' + result.description + '</p>' +
                    '<small>Executado em: ' + result.timestamp.toLocaleString() + '</small>';
                
                if (result.details.length > 0) {
                    html += '<details style="margin-top: 10px;">' +
                        '<summary>Ver detalhes (' + result.details.length + ' itens)</summary>' +
                        '<ul style="margin: 10px 0; padding-left: 20px;">';
                    
                    result.details.forEach(function(detail) {
                        html += '<li>' + detail + '</li>';
                    });
                    
                    html += '</ul></details>';
                }
                
                html += '</div>';
            });
            
            content.innerHTML = html;
        }

        // Função global para verificar saldos
        window.verificarSaldos = async function() {
            log('🔍 Verificando consistência dos saldos...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                let detalhes = [];

                for (const produto of produtos) {
                    const estoqueItem = estoques.find(e => e.produtoId === produto.id);
                    const qualidadeItem = qualidade.find(q => q.produtoId === produto.id);

                    if (estoqueItem && (estoqueItem.saldo === undefined || estoqueItem.saldo === null)) {
                        problemas++;
                        detalhes.push(produto.codigo + ': Saldo estoque undefined');
                    }

                    if (qualidadeItem && (qualidadeItem.saldo === undefined || qualidadeItem.saldo === null)) {
                        problemas++;
                        detalhes.push(produto.codigo + ': Saldo qualidade undefined');
                    }
                }

                log('📊 Verificação concluída: ' + problemas + ' problemas encontrados', problemas > 0 ? 'warning' : 'success');
                addResult(problemas > 0 ? 'warning' : 'info', 'Verificação de Saldos',
                    problemas + ' problemas encontrados em ' + produtos.length + ' produtos', detalhes);

            } catch (error) {
                log('❌ Erro na verificação: ' + error.message, 'error');
            }
        };

        // Função global para corrigir saldos
        window.corrigirSaldos = async function() {
            const produtoFiltro = document.getElementById('saldoProduto').value.trim();

            if (!confirm('Confirma a correção de saldos? Esta ação pode alterar dados importantes.')) {
                return;
            }

            log('🔧 Iniciando correção de saldos...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let produtosFiltrados = produtos;
                if (produtoFiltro) {
                    produtosFiltrados = produtos.filter(p =>
                        p.codigo && p.codigo.toLowerCase().includes(produtoFiltro.toLowerCase())
                    );
                }

                log('Processando ' + produtosFiltrados.length + ' produtos...', 'info');

                let correcoes = 0;
                let detalhes = [];

                for (const produto of produtosFiltrados) {
                    const estoqueItem = estoques.find(e => e.produtoId === produto.id);
                    const qualidadeItem = qualidade.find(q => q.produtoId === produto.id);

                    if (estoqueItem && (estoqueItem.saldo === undefined || estoqueItem.saldo === null)) {
                        await updateDoc(doc(db, "estoques", estoqueItem.id), {
                            saldo: 0,
                            corrigidoEm: Timestamp.now(),
                            corrigidoPor: currentUser.nome
                        });
                        correcoes++;
                        detalhes.push(produto.codigo + ': Saldo estoque corrigido para 0');
                    }

                    if (qualidadeItem && (qualidadeItem.saldo === undefined || qualidadeItem.saldo === null)) {
                        await updateDoc(doc(db, "estoqueQualidade", qualidadeItem.id), {
                            saldo: 0,
                            corrigidoEm: Timestamp.now(),
                            corrigidoPor: currentUser.nome
                        });
                        correcoes++;
                        detalhes.push(produto.codigo + ': Saldo qualidade corrigido para 0');
                    }
                }

                log('✅ Correção concluída: ' + correcoes + ' saldos corrigidos', 'success');
                addResult('success', 'Correção de Saldos',
                    correcoes + ' saldos foram corrigidos com sucesso', detalhes);

            } catch (error) {
                log('❌ Erro na correção de saldos: ' + error.message, 'error');
                addResult('error', 'Erro na Correção de Saldos', error.message);
            }
        };

        // Função global para identificar órfãos
        window.identificarOrfaos = async function() {
            const colecao = document.getElementById('orfaoColecao').value;

            log('🔍 Identificando órfãos em ' + colecao + '...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const colecaoSnap = await getDocs(collection(db, colecao));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const registros = colecaoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const produtoIds = new Set(produtos.map(p => p.id));
                let orfaos = 0;
                let detalhes = [];

                for (const registro of registros) {
                    if (registro.produtoId && !produtoIds.has(registro.produtoId)) {
                        orfaos++;
                        detalhes.push('Órfão encontrado: ' + registro.id + ' (produto: ' + registro.produtoId + ')');
                    }
                }

                log('📊 Identificação concluída: ' + orfaos + ' órfãos encontrados', orfaos > 0 ? 'warning' : 'success');
                addResult(orfaos > 0 ? 'warning' : 'info', 'Identificação de Órfãos',
                    orfaos + ' registros órfãos encontrados na coleção ' + colecao, detalhes);

            } catch (error) {
                log('❌ Erro na identificação: ' + error.message, 'error');
            }
        };

        // Função global para limpar órfãos
        window.limparOrfaos = async function() {
            const colecao = document.getElementById('orfaoColecao').value;

            if (!confirm('Confirma a limpeza de órfãos na coleção ' + colecao + '? Esta ação é irreversível.')) {
                return;
            }

            log('🗑️ Iniciando limpeza de órfãos em ' + colecao + '...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const colecaoSnap = await getDocs(collection(db, colecao));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const registros = colecaoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const produtoIds = new Set(produtos.map(p => p.id));
                let removidos = 0;
                let detalhes = [];

                for (const registro of registros) {
                    if (registro.produtoId && !produtoIds.has(registro.produtoId)) {
                        await deleteDoc(doc(db, colecao, registro.id));
                        removidos++;
                        detalhes.push('Removido registro órfão: ' + registro.id + ' (produto: ' + registro.produtoId + ')');
                    }
                }

                log('✅ Limpeza concluída: ' + removidos + ' registros órfãos removidos', 'success');
                addResult('success', 'Limpeza de Órfãos',
                    removidos + ' registros órfãos removidos da coleção ' + colecao, detalhes);

            } catch (error) {
                log('❌ Erro na limpeza: ' + error.message, 'error');
                addResult('error', 'Erro na Limpeza de Órfãos', error.message);
            }
        };

        // Função global para corrigir undefined
        window.corrigirUndefined = async function() {
            const valorNumero = parseFloat(document.getElementById('valorPadraoNumero').value) || 0;
            const valorTexto = document.getElementById('valorPadraoTexto').value || 'N/A';

            if (!confirm('Confirma a correção de campos undefined? Esta ação afetará múltiplas coleções.')) {
                return;
            }

            log('❓ Iniciando correção de campos undefined...', 'info');

            try {
                const colecoes = ['produtos', 'estoques', 'estoqueQualidade', 'movimentacoesEstoque'];
                let totalCorrecoes = 0;
                let detalhes = [];

                for (const nomeColecao of colecoes) {
                    const snap = await getDocs(collection(db, nomeColecao));
                    const registros = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                    for (const registro of registros) {
                        let updates = {};
                        let temUpdates = false;

                        const camposNumericos = ['saldo', 'quantidade', 'valorUnitario', 'estoqueMinimo', 'estoqueMaximo'];
                        camposNumericos.forEach(campo => {
                            if (registro[campo] === undefined || registro[campo] === null || isNaN(registro[campo])) {
                                updates[campo] = valorNumero;
                                temUpdates = true;
                            }
                        });

                        const camposTexto = ['status', 'observacoes', 'usuario'];
                        camposTexto.forEach(campo => {
                            if (registro[campo] === undefined || registro[campo] === null || registro[campo] === '') {
                                updates[campo] = valorTexto;
                                temUpdates = true;
                            }
                        });

                        if (temUpdates) {
                            updates.corrigidoEm = Timestamp.now();
                            updates.corrigidoPor = currentUser.nome;

                            await updateDoc(doc(db, nomeColecao, registro.id), updates);
                            totalCorrecoes++;
                            detalhes.push(nomeColecao + '/' + registro.id + ': ' + (Object.keys(updates).length - 2) + ' campos corrigidos');
                        }
                    }
                }

                log('✅ Correção concluída: ' + totalCorrecoes + ' registros corrigidos', 'success');
                addResult('success', 'Correção de Campos Undefined',
                    totalCorrecoes + ' registros corrigidos em ' + colecoes.length + ' coleções', detalhes);

            } catch (error) {
                log('❌ Erro na correção: ' + error.message, 'error');
                addResult('error', 'Erro na Correção de Undefined', error.message);
            }
        };

        // Função global para sincronizar produtos
        window.sincronizarProdutos = async function() {
            if (!confirm('Confirma a sincronização entre produtos e estoques?')) {
                return;
            }

            log('🔄 Iniciando sincronização produtos ↔ estoques...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const estoquesSnap = await getDocs(collection(db, "estoques"));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let criados = 0;
                let detalhes = [];

                for (const produto of produtos) {
                    const temEstoque = estoques.some(e => e.produtoId === produto.id);

                    if (!temEstoque) {
                        await addDoc(collection(db, "estoques"), {
                            produtoId: produto.id,
                            saldo: 0,
                            armazemId: produto.armazemPadraoId || '',
                            criadoEm: Timestamp.now(),
                            criadoPor: currentUser.nome,
                            sincronizado: true
                        });
                        criados++;
                        detalhes.push('Estoque criado para produto: ' + produto.codigo);
                    }
                }

                log('✅ Sincronização concluída: ' + criados + ' estoques criados', 'success');
                addResult('success', 'Sincronização Produtos ↔ Estoques',
                    criados + ' registros de estoque criados', detalhes);

            } catch (error) {
                log('❌ Erro na sincronização: ' + error.message, 'error');
                addResult('error', 'Erro na Sincronização', error.message);
            }
        };

        // Função global para sincronizar movimentações
        window.sincronizarMovimentacoes = async function() {
            if (!confirm('Confirma a sincronização de movimentações?')) {
                return;
            }

            log('🔄 Iniciando sincronização de movimentações...', 'info');

            try {
                const movSnap = await getDocs(collection(db, "movimentacoesEstoque"));
                const movimentacoes = movSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let corrigidas = 0;
                let detalhes = [];

                for (const mov of movimentacoes) {
                    let updates = {};
                    let temUpdates = false;

                    if (!mov.dataMovimentacao) {
                        updates.dataMovimentacao = Timestamp.now();
                        temUpdates = true;
                    }

                    if (!mov.usuario) {
                        updates.usuario = currentUser.nome;
                        temUpdates = true;
                    }

                    if (!mov.tipo) {
                        updates.tipo = 'AJUSTE';
                        temUpdates = true;
                    }

                    if (temUpdates) {
                        updates.sincronizadoEm = Timestamp.now();
                        await updateDoc(doc(db, "movimentacoesEstoque", mov.id), updates);
                        corrigidas++;
                        detalhes.push('Movimentação ' + mov.id + ': ' + (Object.keys(updates).length - 1) + ' campos corrigidos');
                    }
                }

                log('✅ Sincronização concluída: ' + corrigidas + ' movimentações corrigidas', 'success');
                addResult('success', 'Sincronização de Movimentações',
                    corrigidas + ' movimentações sincronizadas', detalhes);

            } catch (error) {
                log('❌ Erro na sincronização: ' + error.message, 'error');
                addResult('error', 'Erro na Sincronização de Movimentações', error.message);
            }
        };

        // Função global para recalcular saldos
        window.recalcularSaldos = async function() {
            if (!confirm('Confirma o recálculo de saldos? Esta operação pode demorar.')) {
                return;
            }

            log('🧮 Iniciando recálculo de saldos...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const movSnap = await getDocs(collection(db, "movimentacoesEstoque"));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const movimentacoes = movSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let recalculados = 0;
                let detalhes = [];

                for (const produto of produtos) {
                    const movsProduto = movimentacoes.filter(m => m.produtoId === produto.id);

                    let saldoCalculado = 0;
                    movsProduto.forEach(mov => {
                        if (mov.tipo === 'ENTRADA') {
                            saldoCalculado += mov.quantidade || 0;
                        } else if (mov.tipo === 'SAIDA') {
                            saldoCalculado -= mov.quantidade || 0;
                        }
                    });

                    const estoquesSnap = await getDocs(query(collection(db, "estoques"), where("produtoId", "==", produto.id)));

                    if (!estoquesSnap.empty) {
                        const estoqueDoc = estoquesSnap.docs[0];
                        const estoqueAtual = estoqueDoc.data();

                        if (estoqueAtual.saldo !== saldoCalculado) {
                            await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                                saldo: saldoCalculado,
                                saldoAnterior: estoqueAtual.saldo,
                                recalculadoEm: Timestamp.now(),
                                recalculadoPor: currentUser.nome
                            });
                            recalculados++;
                            detalhes.push(produto.codigo + ': ' + estoqueAtual.saldo + ' → ' + saldoCalculado);
                        }
                    }
                }

                log('✅ Recálculo concluído: ' + recalculados + ' saldos atualizados', 'success');
                addResult('success', 'Recálculo de Saldos',
                    recalculados + ' saldos recalculados baseados nas movimentações', detalhes);

            } catch (error) {
                log('❌ Erro no recálculo: ' + error.message, 'error');
                addResult('error', 'Erro no Recálculo de Saldos', error.message);
            }
        };

        // Função global para recalcular valores
        window.recalcularValores = async function() {
            if (!confirm('Confirma o recálculo de valores?')) {
                return;
            }

            log('💰 Iniciando recálculo de valores...', 'info');

            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const estoquesSnap = await getDocs(collection(db, "estoques"));

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let atualizados = 0;
                let detalhes = [];

                for (const estoque of estoques) {
                    const produto = produtos.find(p => p.id === estoque.produtoId);

                    if (produto && produto.valorUnitario) {
                        const valorTotal = estoque.saldo * produto.valorUnitario;

                        await updateDoc(doc(db, "estoques", estoque.id), {
                            valorUnitario: produto.valorUnitario,
                            valorTotal: valorTotal,
                            valorRecalculadoEm: Timestamp.now()
                        });

                        atualizados++;
                        detalhes.push(produto.codigo + ': R$ ' + valorTotal.toFixed(2));
                    }
                }

                log('✅ Recálculo concluído: ' + atualizados + ' valores atualizados', 'success');
                addResult('success', 'Recálculo de Valores',
                    atualizados + ' valores recalculados', detalhes);

            } catch (error) {
                log('❌ Erro no recálculo: ' + error.message, 'error');
                addResult('error', 'Erro no Recálculo de Valores', error.message);
            }
        };

        // Função global para corrigir datas
        window.corrigirDatas = async function() {
            const colecao = document.getElementById('dataColecao').value;

            if (!confirm('Confirma a correção de datas na coleção ' + colecao + '?')) {
                return;
            }

            log('📅 Iniciando correção de datas em ' + colecao + '...', 'info');

            try {
                const snap = await getDocs(collection(db, colecao));
                const registros = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let corrigidas = 0;
                let detalhes = [];

                for (const registro of registros) {
                    let updates = {};
                    let temUpdates = false;

                    const camposData = ['dataCriacao', 'dataAprovacao', 'dataMovimentacao', 'ultimaAtualizacao'];

                    camposData.forEach(campo => {
                        if (registro[campo] && typeof registro[campo] === 'string') {
                            try {
                                const data = new Date(registro[campo]);
                                if (!isNaN(data.getTime())) {
                                    updates[campo] = Timestamp.fromDate(data);
                                    temUpdates = true;
                                }
                            } catch (e) {
                                updates[campo] = Timestamp.now();
                                temUpdates = true;
                            }
                        } else if (!registro[campo] && campo === 'dataCriacao') {
                            updates[campo] = Timestamp.now();
                            temUpdates = true;
                        }
                    });

                    if (temUpdates) {
                        updates.datasCorrigidasEm = Timestamp.now();
                        await updateDoc(doc(db, colecao, registro.id), updates);
                        corrigidas++;
                        detalhes.push(registro.id + ': ' + (Object.keys(updates).length - 1) + ' datas corrigidas');
                    }
                }

                log('✅ Correção concluída: ' + corrigidas + ' registros com datas corrigidas', 'success');
                addResult('success', 'Correção de Datas',
                    corrigidas + ' registros com datas corrigidas na coleção ' + colecao, detalhes);

            } catch (error) {
                log('❌ Erro na correção de datas: ' + error.message, 'error');
                addResult('error', 'Erro na Correção de Datas', error.message);
            }
        };

        // Função global para correção completa
        window.correcaoCompleta = async function() {
            if (!confirm('⚠️ ATENÇÃO: Esta operação executará TODAS as correções disponíveis.\n\nIsso pode alterar muitos dados e demorar vários minutos.\n\nTem certeza que deseja continuar?')) {
                return;
            }

            log('🚨 Iniciando correção completa do sistema...', 'warning');

            try {
                await verificarSaldos();
                await corrigirSaldos();
                await corrigirUndefined();
                await sincronizarProdutos();
                await sincronizarMovimentacoes();
                await recalcularSaldos();
                await recalcularValores();

                log('✅ Correção completa finalizada com sucesso!', 'success');
                addResult('success', 'Correção Completa', 'Todas as correções foram executadas com sucesso');

            } catch (error) {
                log('❌ Erro na correção completa: ' + error.message, 'error');
                addResult('error', 'Erro na Correção Completa', error.message);
            }
        };

        // Função global para manutenção preventiva
        window.manutencaoPreventiva = async function() {
            if (!confirm('Executar manutenção preventiva? Isso incluirá verificações e pequenas correções.')) {
                return;
            }

            log('🛠️ Iniciando manutenção preventiva...', 'info');

            try {
                await verificarSaldos();
                await identificarOrfaos();

                log('✅ Manutenção preventiva concluída', 'success');
                addResult('success', 'Manutenção Preventiva', 'Verificações preventivas executadas');

            } catch (error) {
                log('❌ Erro na manutenção: ' + error.message, 'error');
            }
        };

        // Função global para otimização de performance
        window.otimizacaoPerformance = async function() {
            if (!confirm('Executar otimização de performance?')) {
                return;
            }

            log('⚡ Iniciando otimização de performance...', 'info');

            try {
                const colecoes = ['estoques', 'estoqueQualidade', 'movimentacoesEstoque'];

                for (const col of colecoes) {
                    document.getElementById('orfaoColecao').value = col;
                    await limparOrfaos();
                }

                log('✅ Otimização concluída', 'success');
                addResult('success', 'Otimização de Performance', 'Sistema otimizado com sucesso');

            } catch (error) {
                log('❌ Erro na otimização: ' + error.message, 'error');
            }
        };

        // Inicialização
        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de correção universal carregado', 'info');
            log('⚠️ ATENÇÃO: Sempre faça backup antes de executar correções', 'warning');
        };
    </script>
</body>
</html>
