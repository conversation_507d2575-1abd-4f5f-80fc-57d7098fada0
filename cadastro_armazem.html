<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🏭 Cadastro de Armazéns</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* ========================================
       🎨 CSS PADRONIZADO - CADASTRO ARMAZÉM
       Baseado em: gestao_compras_integrada.html
       ======================================== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .form-container {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }

    .form-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-col {
      flex: 1;
    }

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
      display: block;
    }

    .form-control {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .warehouses-table {
      width: 100%;
      border-collapse: collapse;
    }

    .warehouses-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .warehouses-table th:hover {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .warehouses-table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .warehouses-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .edit-btn {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .edit-btn:hover, .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .search-container {
      margin-bottom: 20px;
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .search-container input {
      flex: 1;
      min-width: 250px;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .search-container input:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .required::after {
      content: " *";
      color: #e74c3c;
      font-weight: bold;
    }

    .sort-indicator {
      margin-left: 8px;
      font-size: 14px;
      color: #ecf0f1;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 1000;
      display: none;
      animation: slideIn 0.3s ease;
    }

    .notification-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    }

    .notification-error {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    /* Responsividade */
    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .table-container {
        overflow-x: auto;
      }

      .search-container {
        flex-direction: column;
      }

      .search-container input {
        min-width: auto;
      }

      .form-actions {
        flex-direction: column;
      }

      .action-buttons {
        justify-content: center;
      }

      .form-row {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-warehouse"></i>
        Cadastro de Armazéns
      </h1>
      <div class="header-actions">
        <button class="btn btn-warning" onclick="window.location.href='index.html'">
          <i class="fas fa-home"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <div id="notification" class="notification"></div>

      <!-- Formulário de Cadastro -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-plus-circle"></i>
          Cadastrar Novo Armazém
        </h2>
        <form id="warehouseForm" onsubmit="registerWarehouse(event)">
          <input type="hidden" id="editingId">

          <div class="form-row">
            <div class="form-group">
              <label for="warehouseCode" class="required">Código</label>
              <input type="text" id="warehouseCode" class="form-control" required maxlength="5">
            </div>
            <div class="form-group">
              <label for="warehouseName" class="required">Nome</label>
              <input type="text" id="warehouseName" class="form-control" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="warehouseType" class="required">Tipo de Armazém</label>
              <select id="warehouseType" class="form-control" required>
                <option value="">Selecione o tipo...</option>
                <option value="RECEBIMENTO">🔄 Recebimento</option>
                <option value="QUALIDADE">✅ Qualidade</option>
                <option value="ALMOXARIFADO">📦 Almoxarifado</option>
                <option value="PRODUCAO">🏭 Produção</option>
                <option value="EXPEDICAO">🚚 Expedição</option>
                <option value="DEVOLUCAO">↩️ Devolução</option>
                <option value="BLOQUEADO">🚫 Bloqueado</option>
                <option value="CONSIGNADO">🤝 Consignado</option>
                <option value="RESERVADO">🔒 Reservado</option>
              </select>
            </div>
            <div class="form-group">
              <label for="warehouseOwnership" class="required">Tipo de Propriedade</label>
              <select id="warehouseOwnership" class="form-control" required>
                <option value="">Selecione...</option>
                <option value="PROPRIO">🏠 Próprio</option>
                <option value="TERCEIRO">🏢 Terceiro</option>
                <option value="FILIAL">🏪 Filial</option>
                <option value="CONSIGNADO">🤝 Consignado</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="warehouseAddress" class="required">Endereço/Localização</label>
            <input type="text" id="warehouseAddress" class="form-control" placeholder="🗺️ Endereço, cidade, UF ou referência interna" required>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="warehouseActive" class="required">Status</label>
              <select id="warehouseActive" class="form-control" required>
                <option value="true">✅ Ativo</option>
                <option value="false">❌ Inativo</option>
              </select>
            </div>
            <div class="form-group">
              <label for="warehouseDescription">Descrição</label>
              <textarea id="warehouseDescription" class="form-control" rows="3" placeholder="📝 Descrição do armazém (opcional)"></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="resetForm()" id="cancelButton" style="display: none;">
              <i class="fas fa-times"></i>
              Cancelar
            </button>
            <button type="button" class="btn btn-secondary" onclick="resetForm()">
              <i class="fas fa-eraser"></i>
              Limpar
            </button>
            <button type="submit" class="btn btn-success" id="submitButton">
              <i class="fas fa-save"></i>
              Cadastrar Armazém
            </button>
          </div>
        </form>
      </div>

      <!-- Lista de Armazéns -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-list"></i>
          Armazéns Cadastrados
        </h2>

        <div class="search-container">
          <input type="text" id="searchCodigo" class="form-control" placeholder="🔍 Pesquisar por Código" oninput="filterWarehouses()">
          <input type="text" id="searchNome" class="form-control" placeholder="🔍 Pesquisar por Nome" oninput="filterWarehouses()">
        </div>

        <div class="table-container">
          <table class="warehouses-table">
            <thead>
              <tr>
                <th onclick="sortTable('codigo')">
                  <i class="fas fa-code"></i>
                  Código
                  <span id="sortCodigo" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('nome')">
                  <i class="fas fa-tag"></i>
                  Nome
                  <span id="sortNome" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('tipo')">
                  <i class="fas fa-layer-group"></i>
                  Tipo
                  <span id="sortTipo" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('propriedade')">
                  <i class="fas fa-building"></i>
                  Propriedade
                  <span id="sortPropriedade" class="sort-indicator"></span>
                </th>
                <th>
                  <i class="fas fa-map-marker-alt"></i>
                  Endereço
                </th>
                <th onclick="sortTable('ativo')">
                  <i class="fas fa-toggle-on"></i>
                  Status
                  <span id="sortAtivo" class="sort-indicator"></span>
                </th>
                <th>
                  <i class="fas fa-comment"></i>
                  Descrição
                </th>
                <th>
                  <i class="fas fa-cogs"></i>
                  Ações
                </th>
              </tr>
            </thead>
            <tbody id="warehousesTableBody">
              <tr>
                <td colspan="8" style="text-align: center; padding: 40px;">
                  <i class="fas fa-spinner fa-spin"></i>
                  Carregando armazéns...
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let armazens = [];
    let filteredArmazens = [];
    let editingId = null;
    let sortDirection = 'asc';
    let currentSortColumn = '';

    // Função global para ordenação
    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      if (sortBy === 'codigo') {
        filteredArmazens.sort((a, b) => sortDirection === 'asc'
          ? a.codigo.localeCompare(b.codigo)
          : b.codigo.localeCompare(a.codigo));
      } else if (sortBy === 'nome') {
        filteredArmazens.sort((a, b) => sortDirection === 'asc'
          ? a.nome.localeCompare(b.nome)
          : b.nome.localeCompare(a.nome));
      } else if (sortBy === 'tipo') {
        filteredArmazens.sort((a, b) => sortDirection === 'asc'
          ? a.tipo.localeCompare(b.tipo)
          : b.tipo.localeCompare(a.tipo));
      } else if (sortBy === 'propriedade') {
        filteredArmazens.sort((a, b) => sortDirection === 'asc'
          ? (a.propriedade || '').localeCompare(b.propriedade || '')
          : (b.propriedade || '').localeCompare(a.propriedade || ''));
      } else if (sortBy === 'ativo') {
        filteredArmazens.sort((a, b) => sortDirection === 'asc'
          ? a.ativo - b.ativo
          : b.ativo - a.ativo);
      }

      updateSortIndicators(sortBy, sortDirection);
      displayWarehouses();
    };

    // Função para atualizar os indicadores visuais
    function updateSortIndicators(column, direction) {
      document.getElementById('sortCodigo').innerHTML = '';
      document.getElementById('sortNome').innerHTML = '';
      document.getElementById('sortTipo').innerHTML = '';
      document.getElementById('sortPropriedade').innerHTML = '';
      document.getElementById('sortAtivo').innerHTML = '';

      let indicatorId = `sort${column.charAt(0).toUpperCase() + column.slice(1)}`;
      if (column === 'codigo') indicatorId = 'sortCodigo';
      
      const indicatorElement = document.getElementById(indicatorId);
      if (indicatorElement) {
        indicatorElement.innerHTML = direction === 'asc' ? '▲' : '▼';
      }
    }

    // Função de filtro
    window.filterWarehouses = function() {
      const searchCodigo = document.getElementById('searchCodigo').value.toLowerCase();
      const searchNome = document.getElementById('searchNome').value.toLowerCase();

      filteredArmazens = armazens.filter(armazem => {
        const matchCodigo = !searchCodigo || armazem.codigo.toLowerCase().includes(searchCodigo);
        const matchNome = !searchNome || armazem.nome.toLowerCase().includes(searchNome);
        return matchCodigo && matchNome;
      });

      displayWarehouses();
    };

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }

      const currentUser = JSON.parse(userSession);
      if (currentUser.nivel < 9) {
        showNotification('Acesso restrito. Apenas administradores podem gerenciar armazéns.', 'error');
        window.location.href = 'index.html';
        return;
      }

      setupRealTimeListener();
    };

    function setupRealTimeListener() {
      try {
        onSnapshot(collection(db, "armazens"), snap => {
          armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          filteredArmazens = [...armazens];
          loadWarehouses();
        });
      } catch (error) {
        console.error("Erro ao carregar armazéns:", error);
        showNotification("Erro ao carregar armazéns.", "error");
      }
    }

    function loadWarehouses() {
      filteredArmazens.sort((a, b) => a.codigo.localeCompare(b.codigo));
      displayWarehouses();
    }

    function displayWarehouses() {
      const tableBody = document.getElementById('warehousesTableBody');
      
      if (filteredArmazens.length === 0) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px; color: #7f8c8d;">
              <i class="fas fa-search"></i><br>
              Nenhum armazém encontrado
            </td>
          </tr>
        `;
        return;
      }

      tableBody.innerHTML = filteredArmazens.map(armazem => `
        <tr>
          <td><strong>${armazem.codigo}</strong></td>
          <td>${armazem.nome}</td>
          <td>
            <span style="display: inline-flex; align-items: center; gap: 8px;">
              ${getTipoIcon(armazem.tipo)} ${formatTipo(armazem.tipo)}
            </span>
          </td>
          <td>
            <span style="display: inline-flex; align-items: center; gap: 8px;">
              ${getPropriedadeIcon(armazem.propriedade)} ${formatPropriedade(armazem.propriedade)}
            </span>
          </td>
          <td style="max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="${armazem.endereco || '-'}">
            ${armazem.endereco || '-'}
          </td>
          <td>
            <span style="background: ${armazem.ativo ? '#d4edda' : '#f8d7da'}; color: ${armazem.ativo ? '#155724' : '#721c24'}; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
              ${armazem.ativo ? '✅ Ativo' : '❌ Inativo'}
            </span>
          </td>
          <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="${armazem.descricao || '-'}">
            ${armazem.descricao || '-'}
          </td>
          <td>
            <div class="action-buttons">
              <button class="edit-btn" onclick="editWarehouse('${armazem.id}')" title="Editar armazém">
                <i class="fas fa-edit"></i> Editar
              </button>
              <button class="delete-btn" onclick="deleteWarehouse('${armazem.id}')" title="Excluir armazém">
                <i class="fas fa-trash"></i> Excluir
              </button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    function getTipoIcon(tipo) {
      const icons = {
        'RECEBIMENTO': '🔄',
        'QUALIDADE': '✅',
        'ALMOXARIFADO': '📦',
        'PRODUCAO': '🏭',
        'EXPEDICAO': '🚚',
        'DEVOLUCAO': '↩️',
        'BLOQUEADO': '🚫',
        'CONSIGNADO': '🤝',
        'RESERVADO': '🔒'
      };
      return icons[tipo] || '📦';
    }

    function getPropriedadeIcon(propriedade) {
      const icons = {
        'PROPRIO': '🏠',
        'TERCEIRO': '🏢',
        'FILIAL': '🏪',
        'CONSIGNADO': '🤝'
      };
      return icons[propriedade] || '🏢';
    }

    function formatTipo(tipo) {
      return tipo ? tipo.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) : '-';
    }

    function formatPropriedade(propriedade) {
      return propriedade ? propriedade.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) : '-';
    }

    window.editWarehouse = function(id) {
      const armazem = armazens.find(a => a.id === id);
      if (!armazem) return;

      editingId = id;
      document.getElementById('editingId').value = id;
      document.getElementById('warehouseCode').value = armazem.codigo;
      document.getElementById('warehouseName').value = armazem.nome;
      document.getElementById('warehouseType').value = armazem.tipo;
      document.getElementById('warehouseOwnership').value = armazem.propriedade || '';
      document.getElementById('warehouseAddress').value = armazem.endereco || '';
      document.getElementById('warehouseActive').value = armazem.ativo ? 'true' : 'false';
      document.getElementById('warehouseDescription').value = armazem.descricao || '';
      
      // Atualizar interface para modo de edição
      document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Atualizar Armazém';
      document.getElementById('cancelButton').style.display = 'inline-flex';
      
      // Scroll para o formulário
      document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
    };

    window.deleteWarehouse = async function(id) {
      if (!confirm('Tem certeza que deseja excluir este armazém?')) return;

      try {
        await deleteDoc(doc(db, "armazens", id));
        showNotification('Armazém excluído com sucesso!', 'success');
      } catch (error) {
        console.error("Erro ao excluir armazém:", error);
        showNotification("Erro ao excluir armazém: " + error.message, "error");
      }
    };

    window.resetForm = function() {
      editingId = null;
      document.getElementById('warehouseForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Cadastrar Armazém';
      document.getElementById('cancelButton').style.display = 'none';
      
      // Limpar bordas de erro
      document.getElementById('warehouseCode').style.borderColor = '';
    };

    window.registerWarehouse = async function(event) {
      event.preventDefault();

      const codigo = document.getElementById('warehouseCode').value.toUpperCase();
      const nome = document.getElementById('warehouseName').value;
      const tipo = document.getElementById('warehouseType').value;
      const propriedade = document.getElementById('warehouseOwnership').value;
      const endereco = document.getElementById('warehouseAddress').value;
      const ativo = document.getElementById('warehouseActive').value === 'true';
      const descricao = document.getElementById('warehouseDescription').value || '';

      // Validar código duplicado
      const existingWarehouse = armazens.find(a => a.codigo === codigo && a.id !== editingId);
      if (existingWarehouse) {
        showNotification('Já existe um armazém com este código.', 'error');
        document.getElementById('warehouseCode').style.borderColor = '#e74c3c';
        return;
      }

      try {
        const armazem = {
          codigo,
          nome,
          tipo,
          propriedade,
          endereco,
          ativo,
          descricao
        };

        if (editingId) {
          await updateDoc(doc(db, "armazens", editingId), armazem);
          showNotification('Armazém atualizado com sucesso!', 'success');
        } else {
          await addDoc(collection(db, "armazens"), armazem);
          showNotification('Armazém cadastrado com sucesso!', 'success');
        }

        resetForm();
      } catch (error) {
        console.error("Erro ao salvar armazém:", error);
        showNotification("Erro ao salvar armazém: " + error.message, "error");
      }
    };

    function showNotification(message, type = 'success', duration = 3000) {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = `notification notification-${type}`;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, duration);
    }
  </script>
</body>
</html>
```