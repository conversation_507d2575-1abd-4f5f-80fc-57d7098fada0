<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 OCR Sistema Inteligente - Leitura de Imagens</title>
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4.1.1/dist/tesseract.min.js"></script>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            color: var(--dark-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .upload-area {
            background: rgba(255, 255, 255, 0.95);
            border: 3px dashed var(--primary-color);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--secondary-color);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: var(--secondary-color);
            background: rgba(46, 204, 113, 0.1);
        }

        .upload-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #666;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--secondary-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .processing-area {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .processing-area.show {
            display: block;
        }

        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .results-area {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .results-area.show {
            display: block;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .extracted-text {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }

        .data-card h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-label {
            font-weight: 600;
            color: #666;
        }

        .data-value {
            color: var(--dark-color);
            font-weight: 500;
        }

        .action-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .type-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .type-option {
            padding: 10px 20px;
            border: 2px solid var(--primary-color);
            border-radius: 25px;
            background: white;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .type-option.active {
            background: var(--primary-color);
            color: white;
        }

        .type-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .paste-area {
            background: rgba(255, 255, 255, 0.95);
            border: 2px dashed var(--warning-color);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
        }

        .paste-area:hover {
            border-color: var(--secondary-color);
            background: rgba(255, 255, 255, 1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .type-selector {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-secondary back-button" onclick="window.location.href='index.html'">
        ← Voltar ao Menu
    </button>

    <div class="container">
        <div class="header">
            <h1>📸 OCR Sistema Inteligente</h1>
            <p>Leitura Automática de Imagens com Inteligência Artificial</p>
            
            <!-- Seletor de Tipo de Documento -->
            <div class="type-selector">
                <div class="type-option active" data-type="auto">🤖 Detecção Automática</div>
                <div class="type-option" data-type="nota-fiscal">📄 Nota Fiscal</div>
                <div class="type-option" data-type="orcamento">💰 Orçamento</div>
                <div class="type-option" data-type="documento">📋 Documento Geral</div>
                <div class="type-option" data-type="tabela">📊 Tabela/Lista</div>
            </div>
        </div>

        <!-- Área de Upload -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📸</div>
            <div class="upload-text">Arraste uma imagem aqui ou clique para selecionar</div>
            <div class="upload-subtext">Suporta: JPG, PNG, PDF, WEBP (máx. 10MB)</div>
            <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;">
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                    📁 Selecionar Arquivo
                </button>
                <button class="btn btn-warning" onclick="abrirCamera()">
                    📷 Usar Câmera
                </button>
            </div>
        </div>

        <!-- Área de Colar Imagem -->
        <div class="paste-area" onclick="colarImagem()">
            <div style="font-size: 2rem; margin-bottom: 10px;">📋</div>
            <div style="font-weight: 600; margin-bottom: 5px;">Colar Imagem da Área de Transferência</div>
            <div style="color: #666;">Ctrl+V ou clique aqui após copiar uma imagem</div>
        </div>

        <!-- Área de Processamento -->
        <div class="processing-area" id="processingArea">
            <h3>🔄 Processando Imagem...</h3>
            <img id="imagePreview" class="image-preview" alt="Prévia da Imagem">
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill">0%</div>
                </div>
            </div>
            
            <div id="processingStatus">Iniciando processamento...</div>
        </div>

        <!-- Área de Resultados -->
        <div class="results-area" id="resultsArea">
            <h3>✅ Dados Extraídos com Sucesso!</h3>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('texto')">📝 Texto Extraído</button>
                <button class="tab" onclick="showTab('dados')">📊 Dados Estruturados</button>
                <button class="tab" onclick="showTab('acoes')">⚡ Ações Rápidas</button>
            </div>

            <div id="tab-texto" class="tab-content active">
                <div class="extracted-text" id="extractedText"></div>
            </div>

            <div id="tab-dados" class="tab-content">
                <div class="data-grid" id="dataGrid"></div>
            </div>

            <div id="tab-acoes" class="tab-content">
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="criarPedidoCompra()">
                        🛒 Criar Pedido de Compra
                    </button>
                    <button class="btn btn-primary" onclick="cadastrarFornecedor()">
                        🏭 Cadastrar Fornecedor
                    </button>
                    <button class="btn btn-warning" onclick="adicionarProdutos()">
                        📦 Adicionar Produtos
                    </button>
                    <button class="btn btn-danger" onclick="exportarDados()">
                        💾 Exportar Dados
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let extractedData = {};
        let currentDocumentType = 'auto';
        let rawText = '';

        // Verificar autenticação
        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            setupEventListeners();
        };

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // Drag and Drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    processFile(files[0]);
                }
            });

            // File Input
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    processFile(e.target.files[0]);
                }
            });

            // Seletor de tipo
            document.querySelectorAll('.type-option').forEach(option => {
                option.addEventListener('click', () => {
                    document.querySelectorAll('.type-option').forEach(o => o.classList.remove('active'));
                    option.classList.add('active');
                    currentDocumentType = option.dataset.type;
                });
            });

            // Colar imagem
            document.addEventListener('paste', (e) => {
                const items = e.clipboardData.items;
                for (let item of items) {
                    if (item.type.indexOf('image') !== -1) {
                        const file = item.getAsFile();
                        processFile(file);
                        break;
                    }
                }
            });
        }

        async function processFile(file) {
            if (!file) return;

            // Validar arquivo
            if (file.size > 10 * 1024 * 1024) {
                alert('Arquivo muito grande! Máximo 10MB.');
                return;
            }

            const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];
            if (!validTypes.includes(file.type)) {
                alert('Tipo de arquivo não suportado!');
                return;
            }

            // Mostrar área de processamento
            document.getElementById('processingArea').classList.add('show');
            document.getElementById('resultsArea').classList.remove('show');

            // Mostrar prévia da imagem
            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('imagePreview').src = e.target.result;
            };
            reader.readAsDataURL(file);

            // Processar com OCR
            await performOCR(file);
        }

        async function performOCR(file) {
            const progressFill = document.getElementById('progressFill');
            const processingStatus = document.getElementById('processingStatus');

            try {
                processingStatus.textContent = 'Inicializando OCR...';
                progressFill.style.width = '10%';
                progressFill.textContent = '10%';

                // Configurar Tesseract
                const worker = await Tesseract.createWorker('por', 1, {
                    logger: m => {
                        if (m.status === 'recognizing text') {
                            const progress = Math.round(m.progress * 80) + 10;
                            progressFill.style.width = progress + '%';
                            progressFill.textContent = progress + '%';
                            processingStatus.textContent = `Reconhecendo texto... ${progress}%`;
                        }
                    }
                });

                processingStatus.textContent = 'Extraindo texto da imagem...';

                // Executar OCR
                const { data: { text } } = await worker.recognize(file);
                rawText = text;

                progressFill.style.width = '90%';
                progressFill.textContent = '90%';
                processingStatus.textContent = 'Analisando dados extraídos...';

                // Analisar texto extraído
                await analyzeExtractedText(text);

                progressFill.style.width = '100%';
                progressFill.textContent = '100%';
                processingStatus.textContent = 'Processamento concluído!';

                await worker.terminate();

                // Mostrar resultados
                setTimeout(() => {
                    document.getElementById('processingArea').classList.remove('show');
                    document.getElementById('resultsArea').classList.add('show');
                }, 1000);

            } catch (error) {
                console.error('Erro no OCR:', error);
                processingStatus.textContent = 'Erro no processamento: ' + error.message;
                alert('Erro ao processar imagem: ' + error.message);
            }
        }

        async function analyzeExtractedText(text) {
            // Limpar e normalizar texto
            const cleanText = text.replace(/\s+/g, ' ').trim();

            // Detectar tipo de documento automaticamente se necessário
            if (currentDocumentType === 'auto') {
                currentDocumentType = detectDocumentType(cleanText);
            }

            // Extrair dados baseado no tipo
            switch (currentDocumentType) {
                case 'nota-fiscal':
                    extractedData = extractNotaFiscalData(cleanText);
                    break;
                case 'orcamento':
                    extractedData = extractOrcamentoData(cleanText);
                    break;
                case 'documento':
                    extractedData = extractDocumentData(cleanText);
                    break;
                case 'tabela':
                    extractedData = extractTableData(cleanText);
                    break;
                default:
                    extractedData = extractGenericData(cleanText);
            }

            // Atualizar interface
            updateResults();
        }

        function detectDocumentType(text) {
            const lowerText = text.toLowerCase();

            if (lowerText.includes('nota fiscal') || lowerText.includes('cnpj') || lowerText.includes('danfe')) {
                return 'nota-fiscal';
            }

            if (lowerText.includes('orçamento') || lowerText.includes('cotação') || lowerText.includes('proposta')) {
                return 'orcamento';
            }

            if (lowerText.includes('tabela') || text.includes('|') || text.split('\n').length > 10) {
                return 'tabela';
            }

            return 'documento';
        }

        function extractNotaFiscalData(text) {
            const data = {
                tipo: 'Nota Fiscal',
                numero: extractPattern(text, /n[úu]mero[:\s]*(\d+)/i),
                serie: extractPattern(text, /s[ée]rie[:\s]*(\d+)/i),
                cnpj: extractPattern(text, /cnpj[:\s]*([\d\.\-\/]+)/i),
                razaoSocial: extractPattern(text, /raz[ãa]o social[:\s]*([^\n]+)/i),
                valor: extractPattern(text, /total[:\s]*r?\$?\s*([\d\.,]+)/i),
                data: extractPattern(text, /data[:\s]*(\d{2}\/\d{2}\/\d{4})/i),
                chaveAcesso: extractPattern(text, /chave[:\s]*(\d{44})/i),
                itens: extractItensNotaFiscal(text)
            };

            return data;
        }

        function extractOrcamentoData(text) {
            const data = {
                tipo: 'Orçamento',
                numero: extractPattern(text, /or[çc]amento[:\s]*(\d+)/i),
                fornecedor: extractPattern(text, /fornecedor[:\s]*([^\n]+)/i),
                contato: extractPattern(text, /contato[:\s]*([^\n]+)/i),
                telefone: extractPattern(text, /telefone[:\s]*([\d\(\)\-\s]+)/i),
                email: extractPattern(text, /e?-?mail[:\s]*([^\s\n]+@[^\s\n]+)/i),
                valorTotal: extractPattern(text, /total[:\s]*r?\$?\s*([\d\.,]+)/i),
                validade: extractPattern(text, /validade[:\s]*(\d{2}\/\d{2}\/\d{4})/i),
                itens: extractItensOrcamento(text)
            };

            return data;
        }

        function extractDocumentData(text) {
            const data = {
                tipo: 'Documento Geral',
                emails: extractAllPatterns(text, /([^\s\n]+@[^\s\n]+\.[^\s\n]+)/g),
                telefones: extractAllPatterns(text, /(\(\d{2}\)\s?\d{4,5}-?\d{4})/g),
                cnpjs: extractAllPatterns(text, /([\d\.\-\/]{14,18})/g),
                cpfs: extractAllPatterns(text, /([\d\.\-]{11,14})/g),
                datas: extractAllPatterns(text, /(\d{2}\/\d{2}\/\d{4})/g),
                valores: extractAllPatterns(text, /r?\$?\s*([\d\.,]+)/gi),
                ceps: extractAllPatterns(text, /(\d{5}-?\d{3})/g)
            };

            return data;
        }

        function extractTableData(text) {
            const lines = text.split('\n').filter(line => line.trim());
            const data = {
                tipo: 'Tabela/Lista',
                totalLinhas: lines.length,
                colunas: detectColumns(lines),
                dados: parseTableData(lines)
            };

            return data;
        }

        function extractGenericData(text) {
            return {
                tipo: 'Dados Gerais',
                palavrasChave: extractKeywords(text),
                numeros: extractAllPatterns(text, /(\d+)/g),
                emails: extractAllPatterns(text, /([^\s\n]+@[^\s\n]+)/g),
                telefones: extractAllPatterns(text, /([\d\(\)\-\s]{10,})/g)
            };
        }

        // Funções auxiliares
        function extractPattern(text, pattern) {
            const match = text.match(pattern);
            return match ? match[1].trim() : null;
        }

        function extractAllPatterns(text, pattern) {
            const matches = text.match(pattern);
            return matches ? [...new Set(matches)] : [];
        }

        function extractItensNotaFiscal(text) {
            // Lógica para extrair itens de nota fiscal
            const lines = text.split('\n');
            const itens = [];

            for (let line of lines) {
                if (line.match(/\d+\s+[A-Z]/)) {
                    const parts = line.split(/\s+/);
                    if (parts.length >= 4) {
                        itens.push({
                            codigo: parts[0],
                            descricao: parts.slice(1, -2).join(' '),
                            quantidade: parts[parts.length - 2],
                            valor: parts[parts.length - 1]
                        });
                    }
                }
            }

            return itens;
        }

        function extractItensOrcamento(text) {
            // Lógica similar para orçamentos
            return extractItensNotaFiscal(text);
        }

        function detectColumns(lines) {
            if (lines.length === 0) return [];

            const firstLine = lines[0];
            const separators = ['\t', '|', ';', ','];

            for (let sep of separators) {
                if (firstLine.includes(sep)) {
                    return firstLine.split(sep).map(col => col.trim());
                }
            }

            return ['Dados'];
        }

        function parseTableData(lines) {
            const data = [];

            for (let line of lines) {
                const separators = ['\t', '|', ';', ','];
                let parsed = false;

                for (let sep of separators) {
                    if (line.includes(sep)) {
                        data.push(line.split(sep).map(cell => cell.trim()));
                        parsed = true;
                        break;
                    }
                }

                if (!parsed) {
                    data.push([line.trim()]);
                }
            }

            return data;
        }

        function extractKeywords(text) {
            const words = text.toLowerCase().split(/\s+/);
            const stopWords = ['o', 'a', 'de', 'da', 'do', 'e', 'em', 'para', 'com', 'por'];
            const keywords = words.filter(word =>
                word.length > 3 && !stopWords.includes(word)
            );

            return [...new Set(keywords)].slice(0, 10);
        }

        function updateResults() {
            // Atualizar texto extraído
            document.getElementById('extractedText').textContent = rawText;

            // Atualizar dados estruturados
            updateDataGrid();
        }

        function updateDataGrid() {
            const dataGrid = document.getElementById('dataGrid');
            dataGrid.innerHTML = '';

            if (!extractedData || Object.keys(extractedData).length === 0) {
                dataGrid.innerHTML = '<p>Nenhum dado estruturado encontrado.</p>';
                return;
            }

            // Criar cards para cada categoria de dados
            Object.entries(extractedData).forEach(([key, value]) => {
                if (value && (typeof value === 'string' || Array.isArray(value) || typeof value === 'object')) {
                    const card = createDataCard(key, value);
                    dataGrid.appendChild(card);
                }
            });
        }

        function createDataCard(title, data) {
            const card = document.createElement('div');
            card.className = 'data-card';

            const titleElement = document.createElement('h4');
            titleElement.textContent = formatTitle(title);
            card.appendChild(titleElement);

            if (Array.isArray(data)) {
                data.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'data-item';
                    itemElement.innerHTML = `<span class="data-value">${item}</span>`;
                    card.appendChild(itemElement);
                });
            } else if (typeof data === 'object') {
                Object.entries(data).forEach(([key, value]) => {
                    if (value) {
                        const itemElement = document.createElement('div');
                        itemElement.className = 'data-item';
                        itemElement.innerHTML = `
                            <span class="data-label">${formatTitle(key)}:</span>
                            <span class="data-value">${value}</span>
                        `;
                        card.appendChild(itemElement);
                    }
                });
            } else {
                const itemElement = document.createElement('div');
                itemElement.className = 'data-item';
                itemElement.innerHTML = `<span class="data-value">${data}</span>`;
                card.appendChild(itemElement);
            }

            return card;
        }

        function formatTitle(title) {
            return title.replace(/([A-Z])/g, ' $1')
                       .replace(/^./, str => str.toUpperCase())
                       .replace(/\b\w/g, l => l.toUpperCase());
        }

        // Funções de interface
        window.showTab = function(tabName) {
            // Remover active de todas as tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Ativar tab selecionada
            document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`tab-${tabName}`).classList.add('active');
        };

        window.abrirCamera = function() {
            // Implementar captura de câmera
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    // Criar modal com câmera
                    const modal = createCameraModal(stream);
                    document.body.appendChild(modal);
                })
                .catch(error => {
                    alert('Erro ao acessar câmera: ' + error.message);
                });
        };

        window.colarImagem = function() {
            // Simular Ctrl+V
            document.execCommand('paste');
        };

        // Funções de ação
        window.criarPedidoCompra = async function() {
            if (!extractedData || !extractedData.itens) {
                alert('Nenhum item encontrado para criar pedido de compra.');
                return;
            }

            try {
                const pedido = {
                    numeroPedido: `PC-OCR-${Date.now()}`,
                    fornecedor: extractedData.fornecedor || extractedData.razaoSocial || 'Fornecedor OCR',
                    dataPedido: Timestamp.now(),
                    status: 'RASCUNHO',
                    valorTotal: parseFloat(extractedData.valorTotal?.replace(/[^\d,]/g, '').replace(',', '.')) || 0,
                    observacoes: `Pedido criado via OCR - Tipo: ${extractedData.tipo}`,
                    criadoPor: currentUser.nome,
                    origem: 'OCR_SISTEMA',
                    itens: extractedData.itens.map(item => ({
                        descricao: item.descricao,
                        quantidade: parseFloat(item.quantidade) || 1,
                        valorUnitario: parseFloat(item.valor?.replace(/[^\d,]/g, '').replace(',', '.')) || 0,
                        codigo: item.codigo || ''
                    }))
                };

                await addDoc(collection(db, "pedidosCompra"), pedido);
                alert(`Pedido de compra ${pedido.numeroPedido} criado com sucesso!`);

            } catch (error) {
                console.error('Erro ao criar pedido:', error);
                alert('Erro ao criar pedido de compra: ' + error.message);
            }
        };

        window.cadastrarFornecedor = async function() {
            if (!extractedData.cnpj && !extractedData.razaoSocial) {
                alert('Dados insuficientes para cadastrar fornecedor.');
                return;
            }

            try {
                const fornecedor = {
                    codigo: `FORN-OCR-${Date.now()}`,
                    nome: extractedData.razaoSocial || extractedData.fornecedor || 'Fornecedor OCR',
                    cnpj: extractedData.cnpj || '',
                    telefone: extractedData.telefone || '',
                    email: extractedData.email || '',
                    endereco: '',
                    contato: extractedData.contato || '',
                    status: 'ATIVO',
                    dataCadastro: Timestamp.now(),
                    criadoPor: currentUser.nome,
                    origem: 'OCR_SISTEMA'
                };

                await addDoc(collection(db, "fornecedores"), fornecedor);
                alert(`Fornecedor ${fornecedor.nome} cadastrado com sucesso!`);

            } catch (error) {
                console.error('Erro ao cadastrar fornecedor:', error);
                alert('Erro ao cadastrar fornecedor: ' + error.message);
            }
        };

        window.adicionarProdutos = async function() {
            if (!extractedData.itens || extractedData.itens.length === 0) {
                alert('Nenhum produto encontrado para adicionar.');
                return;
            }

            try {
                let produtosCriados = 0;

                for (const item of extractedData.itens) {
                    if (item.descricao) {
                        const produto = {
                            codigo: item.codigo || `PROD-OCR-${Date.now()}-${produtosCriados}`,
                            descricao: item.descricao,
                            unidade: 'UN',
                            precoUnitario: parseFloat(item.valor?.replace(/[^\d,]/g, '').replace(',', '.')) || 0,
                            status: 'ATIVO',
                            dataCadastro: Timestamp.now(),
                            criadoPor: currentUser.nome,
                            origem: 'OCR_SISTEMA',
                            categoria: 'OCR_IMPORTADO'
                        };

                        await addDoc(collection(db, "produtos"), produto);
                        produtosCriados++;
                    }
                }

                alert(`${produtosCriados} produtos adicionados com sucesso!`);

            } catch (error) {
                console.error('Erro ao adicionar produtos:', error);
                alert('Erro ao adicionar produtos: ' + error.message);
            }
        };

        window.exportarDados = function() {
            const dataToExport = {
                timestamp: new Date().toISOString(),
                documentType: currentDocumentType,
                rawText: rawText,
                extractedData: extractedData
            };

            const dataStr = JSON.stringify(dataToExport, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ocr_dados_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        };

        function createCameraModal(stream) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
            `;

            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.style.cssText = 'width: 100%; max-width: 500px; border-radius: 8px;';

            const captureBtn = document.createElement('button');
            captureBtn.textContent = '📸 Capturar';
            captureBtn.className = 'btn btn-primary';
            captureBtn.style.margin = '10px';

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '❌ Fechar';
            closeBtn.className = 'btn btn-secondary';
            closeBtn.style.margin = '10px';

            captureBtn.onclick = () => {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                canvas.getContext('2d').drawImage(video, 0, 0);

                canvas.toBlob(blob => {
                    processFile(blob);
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);
                });
            };

            closeBtn.onclick = () => {
                stream.getTracks().forEach(track => track.stop());
                document.body.removeChild(modal);
            };

            content.appendChild(video);
            content.appendChild(document.createElement('br'));
            content.appendChild(captureBtn);
            content.appendChild(closeBtn);
            modal.appendChild(content);

            return modal;
        }
    </script>
</body>
</html>
