/* Estilos específicos para configurações e parâmetros */

/* Estilos para seções do formulário */
.form-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
}

/* Estilos para tabelas */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.table th, .table td {
  padding: 12px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
}

.table tr:hover {
  background-color: #f8f9fa;
}

/* Estilos para mensagens de status */
.status-message {
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.status-message.show {
  display: block;
}

.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Estilos para grupos de botões */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* Estilos para botões específicos */
.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Responsividade */
@media (max-width: 768px) {
  .form-section {
    padding: 10px;
  }

  .button-group {
    flex-direction: column;
  }

  .button-group button {
    width: 100%;
  }

  .table {
    display: block;
    overflow-x: auto;
  }
}

:root {
    --primary-color: #0854a0;
    --primary-hover: #0a4d8c;
    --secondary-color: #f0f3f6;
    --border-color: #d4d4d4;
    --text-color: #333;
    --text-secondary: #666;
    --success-color: #107e3e;
    --danger-color: #dc3545;
    --header-bg: #354a5f;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f7f7f7;
    color: var(--text-color);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
    background-color: var(--header-bg);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    margin: -20px -20px 20px -20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 24px;
    font-weight: 500;
}

/* Tabs */
.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.tab-button {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background-color: var(--secondary-color);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Form Containers */
.form-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.form-title {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.form-row {
    display: flex;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.form-col {
    flex: 1;
    min-width: 250px;
}

/* Form Fields */
label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-secondary);
    font-weight: 500;
}

input[type="text"],
input[type="number"],
select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

/* Switches */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--success-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.switch-container {
    display: flex;
    align-items: center;
}

.switch-label {
    margin-left: 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Help Tooltips */
.help-container {
    position: relative;
    display: inline-block;
    margin-left: 8px;
    vertical-align: middle;
}

.help-button {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    border: none;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    line-height: 1;
    transition: background-color 0.2s;
}

.help-button:hover {
    background-color: #2980b9;
}

.help-tooltip {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    width: 300px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-size: 13px;
    line-height: 1.5;
    text-align: left;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
}

.help-button:hover + .help-tooltip,
.help-tooltip:hover {
    display: block;
}

.help-tooltip strong {
    color: #2c3e50;
}

/* Legend */
.legenda {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.legenda-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.legenda-cor {
    width: 20px;
    height: 20px;
    border: 2px solid var(--success-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast.success {
    background-color: #28a745;
}

.toast.error {
    background-color: #dc3545;
}

.toast.warning {
    background-color: #ffc107;
    color: #212529;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        width: 95%;
        margin: 20px auto;
    }

    .form-row {
        flex-direction: column;
    }

    .form-col {
        width: 100%;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1;
        text-align: center;
        padding: 8px;
    }
} 