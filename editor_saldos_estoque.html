<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Editor de Saldos de Estoque</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .diagnostico-section {
            padding: 20px 30px;
            background: #fff3cd;
            border-bottom: 1px solid #ffeaa7;
            margin-bottom: 0;
        }

        .diagnostico-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .problemas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .problema-card {
            background: white;
            border: 2px solid #fcd34d;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .problema-card:hover {
            border-color: #f59e0b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
        }

        .problema-card.active {
            border-color: #dc2626;
            background: #fef2f2;
        }

        .problema-numero {
            font-size: 1.8em;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 5px;
        }

        .problema-label {
            font-size: 0.9em;
            color: #374151;
            font-weight: 500;
        }

        .problema-descricao {
            font-size: 0.8em;
            color: #6b7280;
            margin-top: 5px;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-2px);
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            min-width: 100px;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #4f46e5;
        }

        .stat-label {
            font-size: 0.9em;
            color: #6b7280;
            margin-top: 5px;
        }

        .table-container {
            overflow-x: auto;
            max-height: 70vh;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: #f8f9fa;
            color: #374151;
            font-weight: 600;
            padding: 15px 10px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 12px 10px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .editable-cell {
            position: relative;
        }

        .saldo-input {
            width: 80px;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            text-align: right;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .saldo-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
        }

        .saldo-input.changed {
            background-color: #fef3c7;
            border-color: #f59e0b;
        }

        .saldo-negativo {
            color: #dc2626;
            font-weight: bold;
        }

        .saldo-positivo {
            color: #059669;
            font-weight: bold;
        }

        .saldo-zero {
            color: #6b7280;
        }

        .linha-problema {
            background-color: #fef2f2 !important;
            border-left: 4px solid #dc2626;
        }

        .linha-problema:hover {
            background-color: #fee2e2 !important;
        }

        .problema-badge {
            display: inline-block;
            background: #dc2626;
            color: white;
            font-size: 0.7em;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 5px;
            font-weight: bold;
        }

        .problema-badge.negativo { background: #dc2626; }
        .problema-badge.zerado { background: #6b7280; }
        .problema-badge.inconsistente { background: #f59e0b; }
        .problema-badge.sem-movimento { background: #8b5cf6; }
        .problema-badge.critico { background: #ef4444; }

        .save-btn {
            padding: 4px 8px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .save-btn.show {
            opacity: 1;
        }

        .save-btn:hover {
            background: #059669;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-radius: 50%;
            border-top-color: #4f46e5;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin: 20px 30px;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .produto-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .produto-codigo {
            font-weight: bold;
            color: #374151;
        }

        .produto-descricao {
            font-size: 0.9em;
            color: #6b7280;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .armazem-header {
            text-align: center;
            font-size: 0.9em;
            padding: 8px 5px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .stats {
                justify-content: center;
                flex-wrap: wrap;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 Editor de Saldos de Estoque</h1>
            <p>Visualize e edite os saldos de produtos por armazém</p>
        </div>

        <div class="diagnostico-section">
            <div class="diagnostico-title">
                🔍 Diagnóstico de Problemas de Estoque
            </div>

            <div class="problemas-grid">
                <div class="problema-card" onclick="filtrarProblema('negativos')" id="card-negativos">
                    <div class="problema-numero" id="count-negativos">0</div>
                    <div class="problema-label">Saldos Negativos</div>
                    <div class="problema-descricao">Produtos com estoque negativo</div>
                </div>

                <div class="problema-card" onclick="filtrarProblema('zerados')" id="card-zerados">
                    <div class="problema-numero" id="count-zerados">0</div>
                    <div class="problema-label">Estoques Zerados</div>
                    <div class="problema-descricao">Produtos sem estoque</div>
                </div>

                <div class="problema-card" onclick="filtrarProblema('inconsistentes')" id="card-inconsistentes">
                    <div class="problema-numero" id="count-inconsistentes">0</div>
                    <div class="problema-label">Inconsistências</div>
                    <div class="problema-descricao">Diferenças entre armazéns</div>
                </div>

                <div class="problema-card" onclick="filtrarProblema('semMovimento')" id="card-semMovimento">
                    <div class="problema-numero" id="count-semMovimento">0</div>
                    <div class="problema-label">Sem Movimento</div>
                    <div class="problema-descricao">Produtos parados há muito tempo</div>
                </div>

                <div class="problema-card" onclick="filtrarProblema('criticos')" id="card-criticos">
                    <div class="problema-numero" id="count-criticos">0</div>
                    <div class="problema-label">Estoque Crítico</div>
                    <div class="problema-descricao">Abaixo do mínimo</div>
                </div>

                <div class="problema-card" onclick="filtrarProblema('todos')" id="card-todos">
                    <div class="problema-numero" id="count-todos">0</div>
                    <div class="problema-label">Todos os Produtos</div>
                    <div class="problema-descricao">Limpar filtros</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="🔍 Buscar por código ou descrição do produto...">
                <span class="search-icon">🔍</span>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalProdutos">0</div>
                    <div class="stat-label">Produtos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalArmazens">0</div>
                    <div class="stat-label">Armazéns</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="alteracoesPendentes">0</div>
                    <div class="stat-label">Pendentes</div>
                </div>
            </div>

            <button class="btn btn-success" onclick="salvarTodasAlteracoes()" id="btnSalvarTodas" disabled>
                💾 Salvar Todas
            </button>

            <button class="btn btn-warning" onclick="recarregarDados()">
                🔄 Recarregar
            </button>

            <select id="filtroArmazem" onchange="aplicarFiltros()" class="btn" style="background: white; border: 2px solid #e9ecef;">
                <option value="">Todos os Armazéns</option>
            </select>

            <select id="filtroSaldo" onchange="aplicarFiltros()" class="btn" style="background: white; border: 2px solid #e9ecef;">
                <option value="">Todos os Saldos</option>
                <option value="positivo">Saldo Positivo</option>
                <option value="zero">Saldo Zero</option>
                <option value="negativo">Saldo Negativo</option>
            </select>

            <button class="btn btn-primary" onclick="exportarCSV()">
                📊 Exportar CSV
            </button>

            <button class="btn btn-warning" onclick="gerarRelatorioProblemas()">
                📋 Relatório de Problemas
            </button>

            <button class="btn btn-primary" onclick="abrirRelatorioCompleto()">
                📈 Relatório Completo
            </button>

            <button class="btn btn-success" onclick="abrirSaldosPorArmazem()">
                🏪 Saldos por Armazém
            </button>

            <button class="btn btn-warning" onclick="corrigirProblemasAutomatico()">
                🔧 Corrigir Problemas
            </button>
        </div>

        <div id="alertContainer"></div>

        <div class="table-container">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <div>Carregando dados do estoque...</div>
            </div>
            
            <table id="estoqueTable" style="display: none;">
                <thead>
                    <tr>
                        <th style="width: 200px;">Produto</th>
                        <th style="width: 80px;">Tipo</th>
                        <th style="width: 60px;">Unidade</th>
                        <!-- Colunas de armazéns serão adicionadas dinamicamente -->
                    </tr>
                </thead>
                <tbody id="estoqueTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            updateDoc,
            addDoc,
            query,
            orderBy
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let estoques = [];
        let alteracoesPendentes = new Map();
        let filtroAtivo = null;
        let problemasDetectados = {
            negativos: [],
            zerados: [],
            inconsistentes: [],
            semMovimento: [],
            criticos: []
        };

        // Carregar dados iniciais
        async function carregarDados() {
            try {
                showLoading(true);
                
                // Carregar produtos
                const produtosSnap = await getDocs(query(collection(db, "produtos"), orderBy("codigo")));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Carregar armazéns
                const armazensSnap = await getDocs(query(collection(db, "armazens"), orderBy("nome")));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Carregar estoques
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                console.log(`Carregados: ${produtos.length} produtos, ${armazens.length} armazéns, ${estoques.length} registros de estoque`);
                
                criarTabela();
                analisarProblemas();
                atualizarEstatisticas();
                showLoading(false);
                
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showAlert('Erro ao carregar dados: ' + error.message, 'error');
                showLoading(false);
            }
        }

        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
            document.getElementById('estoqueTable').style.display = show ? 'none' : 'table';
        }

        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        function criarTabela() {
            const table = document.getElementById('estoqueTable');
            const thead = table.querySelector('thead tr');
            const tbody = document.getElementById('estoqueTableBody');

            // Limpar colunas de armazéns existentes
            while (thead.children.length > 3) {
                thead.removeChild(thead.lastChild);
            }

            // Adicionar colunas de armazéns
            armazens.forEach(armazem => {
                const th = document.createElement('th');
                th.className = 'armazem-header';
                th.style.width = '100px';
                th.innerHTML = `
                    <div>${armazem.nome}</div>
                    <div style="font-size: 0.8em; font-weight: normal; color: #6b7280;">${armazem.tipo || 'Geral'}</div>
                `;
                thead.appendChild(th);
            });

            // Preencher filtro de armazéns
            const filtroArmazem = document.getElementById('filtroArmazem');
            filtroArmazem.innerHTML = '<option value="">Todos os Armazéns</option>';
            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = armazem.nome;
                filtroArmazem.appendChild(option);
            });
            
            // Limpar tbody
            tbody.innerHTML = '';
            
            // Criar linhas para cada produto
            produtos.forEach(produto => {
                const tr = document.createElement('tr');
                tr.dataset.produtoId = produto.id;
                
                // Coluna do produto
                const tdProduto = document.createElement('td');

                // Verificar problemas do produto
                const badges = [];
                if (problemasDetectados.negativos.includes(produto.id)) {
                    badges.push('<span class="problema-badge negativo">NEG</span>');
                }
                if (problemasDetectados.zerados.includes(produto.id)) {
                    badges.push('<span class="problema-badge zerado">ZERO</span>');
                }
                if (problemasDetectados.inconsistentes.includes(produto.id)) {
                    badges.push('<span class="problema-badge inconsistente">INC</span>');
                }
                if (problemasDetectados.semMovimento.includes(produto.id)) {
                    badges.push('<span class="problema-badge sem-movimento">SEM MOV</span>');
                }
                if (problemasDetectados.criticos.includes(produto.id)) {
                    badges.push('<span class="problema-badge critico">CRIT</span>');
                }

                // Adicionar classe de problema se houver algum
                if (badges.length > 0) {
                    tr.classList.add('linha-problema');
                }

                tdProduto.innerHTML = `
                    <div class="produto-info">
                        <div class="produto-codigo">${produto.codigo}${badges.join('')}</div>
                        <div class="produto-descricao" title="${produto.descricao}">${produto.descricao}</div>
                    </div>
                `;
                tr.appendChild(tdProduto);
                
                // Coluna do tipo
                const tdTipo = document.createElement('td');
                tdTipo.textContent = produto.tipo || '-';
                tr.appendChild(tdTipo);
                
                // Coluna da unidade
                const tdUnidade = document.createElement('td');
                tdUnidade.textContent = produto.unidade || '-';
                tr.appendChild(tdUnidade);
                
                // Colunas de saldo por armazém
                armazens.forEach(armazem => {
                    const tdSaldo = document.createElement('td');
                    tdSaldo.className = 'editable-cell';
                    
                    // Buscar saldo atual
                    const registroEstoque = estoques.find(e => 
                        e.produtoId === produto.id && e.armazemId === armazem.id
                    );
                    
                    const saldoAtual = registroEstoque ? (registroEstoque.saldo || 0) : 0;
                    
                    tdSaldo.innerHTML = `
                        <input type="number" 
                               class="saldo-input ${getSaldoClass(saldoAtual)}" 
                               value="${saldoAtual}" 
                               step="0.01"
                               data-produto-id="${produto.id}"
                               data-armazem-id="${armazem.id}"
                               data-registro-id="${registroEstoque ? registroEstoque.id : ''}"
                               data-valor-original="${saldoAtual}"
                               onchange="marcarAlteracao(this)"
                               onkeypress="handleKeyPress(event, this)">
                        <button class="save-btn" onclick="salvarSaldo(this)" title="Salvar alteração">💾</button>
                    `;
                    
                    tr.appendChild(tdSaldo);
                });
                
                tbody.appendChild(tr);
            });
        }

        function getSaldoClass(saldo) {
            if (saldo < 0) return 'saldo-negativo';
            if (saldo > 0) return 'saldo-positivo';
            return 'saldo-zero';
        }

        window.marcarAlteracao = function(input) {
            const valorOriginal = parseFloat(input.dataset.valorOriginal);
            const valorAtual = parseFloat(input.value) || 0;
            const chave = `${input.dataset.produtoId}-${input.dataset.armazemId}`;
            
            if (valorAtual !== valorOriginal) {
                input.classList.add('changed');
                input.nextElementSibling.classList.add('show');
                alteracoesPendentes.set(chave, {
                    input: input,
                    valorNovo: valorAtual,
                    valorOriginal: valorOriginal
                });
            } else {
                input.classList.remove('changed');
                input.nextElementSibling.classList.remove('show');
                alteracoesPendentes.delete(chave);
            }
            
            // Atualizar classe do saldo
            input.className = input.className.replace(/saldo-(negativo|positivo|zero)/, '');
            input.classList.add(getSaldoClass(valorAtual));
            
            atualizarEstatisticas();
        };

        window.handleKeyPress = function(event, input) {
            if (event.key === 'Enter') {
                salvarSaldo(input.nextElementSibling);
            }
        };

        window.salvarSaldo = async function(button) {
            const input = button.previousElementSibling;
            const produtoId = input.dataset.produtoId;
            const armazemId = input.dataset.armazemId;
            const registroId = input.dataset.registroId;
            const novoSaldo = parseFloat(input.value) || 0;

            try {
                button.disabled = true;
                button.textContent = '⏳';

                if (registroId) {
                    // Atualizar registro existente
                    await updateDoc(doc(db, "estoques", registroId), {
                        saldo: novoSaldo,
                        ultimaMovimentacao: new Date()
                    });
                } else {
                    // Criar novo registro se não existir
                    const novoRegistro = {
                        produtoId: produtoId,
                        armazemId: armazemId,
                        saldo: novoSaldo,
                        saldoReservado: 0,
                        ultimaMovimentacao: new Date()
                    };

                    const docRef = await addDoc(collection(db, "estoques"), novoRegistro);
                    input.dataset.registroId = docRef.id;

                    // Adicionar aos dados locais
                    estoques.push({ id: docRef.id, ...novoRegistro });
                }

                // Atualizar dados locais
                input.dataset.valorOriginal = novoSaldo;
                input.classList.remove('changed');
                button.classList.remove('show');

                const chave = `${produtoId}-${armazemId}`;
                alteracoesPendentes.delete(chave);

                showAlert('Saldo salvo com sucesso!', 'success');

                // Reanalisar problemas após alteração
                analisarProblemas();
                atualizarEstatisticas();

            } catch (error) {
                console.error('Erro ao salvar saldo:', error);
                showAlert('Erro ao salvar saldo: ' + error.message, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '💾';
            }
        };

        window.salvarTodasAlteracoes = async function() {
            if (alteracoesPendentes.size === 0) return;
            
            const btnSalvar = document.getElementById('btnSalvarTodas');
            btnSalvar.disabled = true;
            btnSalvar.textContent = '⏳ Salvando...';
            
            let sucessos = 0;
            let erros = 0;
            
            for (const [chave, alteracao] of alteracoesPendentes) {
                try {
                    await salvarSaldo(alteracao.input.nextElementSibling);
                    sucessos++;
                } catch (error) {
                    erros++;
                    console.error('Erro ao salvar:', chave, error);
                }
            }
            
            showAlert(`Salvamento concluído: ${sucessos} sucessos, ${erros} erros`, erros > 0 ? 'warning' : 'success');
            
            btnSalvar.disabled = false;
            btnSalvar.textContent = '💾 Salvar Todas';
        };

        window.recarregarDados = function() {
            alteracoesPendentes.clear();
            filtroAtivo = null;

            // Remover classe active de todos os cards
            document.querySelectorAll('.problema-card').forEach(card => {
                card.classList.remove('active');
            });

            carregarDados();
        };

        function analisarProblemas() {
            problemasDetectados = {
                negativos: [],
                zerados: [],
                inconsistentes: [],
                semMovimento: [],
                criticos: []
            };

            const hoje = new Date();
            const trintaDiasAtras = new Date(hoje.getTime() - (30 * 24 * 60 * 60 * 1000));

            produtos.forEach(produto => {
                const saldosPorArmazem = [];
                let temSaldoNegativo = false;
                let temSaldoZero = false;
                let temMovimentoRecente = false;
                let saldoTotal = 0;

                armazens.forEach(armazem => {
                    const registro = estoques.find(e =>
                        e.produtoId === produto.id && e.armazemId === armazem.id
                    );

                    const saldo = registro ? (registro.saldo || 0) : 0;
                    saldosPorArmazem.push(saldo);
                    saldoTotal += saldo;

                    if (saldo < 0) temSaldoNegativo = true;
                    if (saldo === 0) temSaldoZero = true;

                    // Verificar movimento recente
                    if (registro && registro.ultimaMovimentacao) {
                        const dataMovimento = registro.ultimaMovimentacao.toDate ?
                            registro.ultimaMovimentacao.toDate() :
                            new Date(registro.ultimaMovimentacao);

                        if (dataMovimento > trintaDiasAtras) {
                            temMovimentoRecente = true;
                        }
                    }
                });

                // Classificar problemas
                if (temSaldoNegativo) {
                    problemasDetectados.negativos.push(produto.id);
                }

                if (saldoTotal === 0) {
                    problemasDetectados.zerados.push(produto.id);
                }

                // Inconsistências: diferenças muito grandes entre armazéns
                const maxSaldo = Math.max(...saldosPorArmazem);
                const minSaldo = Math.min(...saldosPorArmazem);
                if (maxSaldo > 0 && (maxSaldo - minSaldo) > (maxSaldo * 0.5)) {
                    problemasDetectados.inconsistentes.push(produto.id);
                }

                // Sem movimento recente
                if (!temMovimentoRecente && saldoTotal > 0) {
                    problemasDetectados.semMovimento.push(produto.id);
                }

                // Estoque crítico (menos de 10% do máximo histórico ou menos de 5 unidades)
                if (saldoTotal > 0 && saldoTotal < 5) {
                    problemasDetectados.criticos.push(produto.id);
                }
            });

            // Atualizar contadores na interface
            document.getElementById('count-negativos').textContent = problemasDetectados.negativos.length;
            document.getElementById('count-zerados').textContent = problemasDetectados.zerados.length;
            document.getElementById('count-inconsistentes').textContent = problemasDetectados.inconsistentes.length;
            document.getElementById('count-semMovimento').textContent = problemasDetectados.semMovimento.length;
            document.getElementById('count-criticos').textContent = problemasDetectados.criticos.length;
            document.getElementById('count-todos').textContent = produtos.length;
        }

        // Função de filtros (DEFINIDA ANTES DE SER USADA)
        window.aplicarFiltros = function() {
            const termo = document.getElementById('searchInput').value.toLowerCase();
            const filtroArmazem = document.getElementById('filtroArmazem').value;
            const filtroSaldo = document.getElementById('filtroSaldo').value;
            const linhas = document.querySelectorAll('#estoqueTableBody tr');

            linhas.forEach(linha => {
                const produtoId = linha.dataset.produtoId;
                const codigo = linha.querySelector('.produto-codigo').textContent.toLowerCase();
                const descricao = linha.querySelector('.produto-descricao').textContent.toLowerCase();

                // Filtro de busca
                const matchBusca = codigo.includes(termo) || descricao.includes(termo);

                // Filtro de problemas
                let matchProblema = true;
                if (filtroAtivo && filtroAtivo !== 'todos') {
                    matchProblema = problemasDetectados[filtroAtivo].includes(produtoId);
                }

                // Filtro de saldo
                let matchSaldo = true;
                if (filtroSaldo) {
                    const inputs = linha.querySelectorAll('.saldo-input');
                    const saldos = Array.from(inputs).map(input => parseFloat(input.value) || 0);

                    switch (filtroSaldo) {
                        case 'positivo':
                            matchSaldo = saldos.some(s => s > 0);
                            break;
                        case 'zero':
                            matchSaldo = saldos.some(s => s === 0);
                            break;
                        case 'negativo':
                            matchSaldo = saldos.some(s => s < 0);
                            break;
                    }
                }

                // Filtro de armazém (mostrar apenas coluna específica se selecionado)
                if (filtroArmazem) {
                    // Ocultar outras colunas de armazém
                    const cells = linha.querySelectorAll('td');
                    cells.forEach((cell, index) => {
                        if (index > 2) { // Após as colunas fixas
                            const input = cell.querySelector('.saldo-input');
                            if (input && input.dataset.armazemId !== filtroArmazem) {
                                cell.style.display = 'none';
                            } else {
                                cell.style.display = '';
                            }
                        }
                    });

                    // Também ocultar headers
                    const headers = document.querySelectorAll('#estoqueTable thead th');
                    headers.forEach((header, index) => {
                        if (index > 2) {
                            const armazemIndex = index - 3;
                            if (armazens[armazemIndex] && armazens[armazemIndex].id !== filtroArmazem) {
                                header.style.display = 'none';
                            } else {
                                header.style.display = '';
                            }
                        }
                    });
                } else {
                    // Mostrar todas as colunas
                    linha.querySelectorAll('td').forEach(cell => cell.style.display = '');
                    document.querySelectorAll('#estoqueTable thead th').forEach(header => header.style.display = '');
                }

                linha.style.display = (matchBusca && matchSaldo && matchProblema) ? '' : 'none';
            });

            // Atualizar contador de produtos visíveis
            const linhasVisiveis = document.querySelectorAll('#estoqueTableBody tr[style=""]').length;
            const linhasOcultas = document.querySelectorAll('#estoqueTableBody tr[style*="none"]').length;

            // Mostrar informação sobre filtros ativos
            if (filtroAtivo && filtroAtivo !== 'todos') {
                const tipoProblema = {
                    'negativos': 'Saldos Negativos',
                    'zerados': 'Estoques Zerados',
                    'inconsistentes': 'Inconsistências',
                    'semMovimento': 'Sem Movimento',
                    'criticos': 'Estoque Crítico'
                };

                showAlert(`Filtro ativo: ${tipoProblema[filtroAtivo]} - ${linhasVisiveis} produtos encontrados`, 'warning');
            }
        };

        window.filtrarProblema = function(tipo) {
            // Remover classe active de todos os cards
            document.querySelectorAll('.problema-card').forEach(card => {
                card.classList.remove('active');
            });

            // Ativar card selecionado
            document.getElementById(`card-${tipo}`).classList.add('active');

            filtroAtivo = tipo;
            aplicarFiltros();
        };

        function atualizarEstatisticas() {
            document.getElementById('totalProdutos').textContent = produtos.length;
            document.getElementById('totalArmazens').textContent = armazens.length;
            document.getElementById('alteracoesPendentes').textContent = alteracoesPendentes.size;

            const btnSalvarTodas = document.getElementById('btnSalvarTodas');
            btnSalvarTodas.disabled = alteracoesPendentes.size === 0;
        }

        // Event listeners para filtros
        document.getElementById('searchInput').addEventListener('input', aplicarFiltros);

        window.exportarCSV = function() {
            const linhas = ['Código,Descrição,Tipo,Unidade,' + armazens.map(a => a.nome).join(',')];

            produtos.forEach(produto => {
                const linha = [
                    produto.codigo,
                    `"${produto.descricao}"`,
                    produto.tipo || '',
                    produto.unidade || ''
                ];

                armazens.forEach(armazem => {
                    const registro = estoques.find(e =>
                        e.produtoId === produto.id && e.armazemId === armazem.id
                    );
                    linha.push(registro ? (registro.saldo || 0) : 0);
                });

                linhas.push(linha.join(','));
            });

            const csv = linhas.join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `estoque_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('Arquivo CSV exportado com sucesso!', 'success');
        };

        window.gerarRelatorioProblemas = function() {
            const linhas = ['Tipo de Problema,Código,Descrição,Detalhes'];

            // Saldos negativos
            problemasDetectados.negativos.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    const saldosNegativos = [];
                    armazens.forEach(armazem => {
                        const registro = estoques.find(e =>
                            e.produtoId === produtoId && e.armazemId === armazem.id
                        );
                        const saldo = registro ? (registro.saldo || 0) : 0;
                        if (saldo < 0) {
                            saldosNegativos.push(`${armazem.nome}: ${saldo}`);
                        }
                    });

                    linhas.push(`Saldo Negativo,${produto.codigo},"${produto.descricao}","${saldosNegativos.join('; ')}"`);
                }
            });

            // Estoques zerados
            problemasDetectados.zerados.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    linhas.push(`Estoque Zerado,${produto.codigo},"${produto.descricao}","Sem estoque em todos os armazéns"`);
                }
            });

            // Inconsistências
            problemasDetectados.inconsistentes.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    const saldos = [];
                    armazens.forEach(armazem => {
                        const registro = estoques.find(e =>
                            e.produtoId === produtoId && e.armazemId === armazem.id
                        );
                        const saldo = registro ? (registro.saldo || 0) : 0;
                        saldos.push(`${armazem.nome}: ${saldo}`);
                    });

                    linhas.push(`Inconsistência,${produto.codigo},"${produto.descricao}","${saldos.join('; ')}"`);
                }
            });

            // Sem movimento
            problemasDetectados.semMovimento.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    linhas.push(`Sem Movimento,${produto.codigo},"${produto.descricao}","Sem movimentação nos últimos 30 dias"`);
                }
            });

            // Estoque crítico
            problemasDetectados.criticos.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    let saldoTotal = 0;
                    armazens.forEach(armazem => {
                        const registro = estoques.find(e =>
                            e.produtoId === produtoId && e.armazemId === armazem.id
                        );
                        saldoTotal += registro ? (registro.saldo || 0) : 0;
                    });

                    linhas.push(`Estoque Crítico,${produto.codigo},"${produto.descricao}","Saldo total: ${saldoTotal}"`);
                }
            });

            const csv = linhas.join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `problemas_estoque_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('Relatório de problemas exportado com sucesso!', 'success');
        };

        // ===== NOVAS FUNCIONALIDADES CONSOLIDADAS =====

        window.abrirRelatorioCompleto = function() {
            // Gerar relatório completo similar ao relatorio_estoque_simples.html
            const dadosRelatorio = [];

            produtos.forEach(produto => {
                const estoquesItem = estoques.filter(e => e.produtoId === produto.id);

                if (estoquesItem.length === 0) {
                    dadosRelatorio.push({
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        unidade: produto.unidade || 'PC',
                        saldoTotal: 0,
                        valorTotal: 0,
                        status: 'SEM_ESTOQUE'
                    });
                } else {
                    const saldoTotal = estoquesItem.reduce((sum, e) => sum + (e.saldo || 0), 0);
                    const valorUnitario = produto.valorUnitario || 0;

                    dadosRelatorio.push({
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        unidade: produto.unidade || 'PC',
                        saldoTotal: saldoTotal,
                        valorTotal: saldoTotal * valorUnitario,
                        status: saldoTotal > 0 ? 'OK' : 'ZERADO'
                    });
                }
            });

            // Criar janela de relatório
            const relatorioWindow = window.open('', '_blank', 'width=1200,height=800');
            relatorioWindow.document.write(`
                <html>
                <head>
                    <title>Relatório Completo de Estoque</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .status-ok { color: green; font-weight: bold; }
                        .status-zerado { color: orange; font-weight: bold; }
                        .status-sem-estoque { color: red; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <h1>📊 Relatório Completo de Estoque</h1>
                    <p>Gerado em: ${new Date().toLocaleString()}</p>
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Unidade</th>
                                <th>Saldo Total</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${dadosRelatorio.map(item => `
                                <tr>
                                    <td>${item.codigo}</td>
                                    <td>${item.descricao}</td>
                                    <td>${item.unidade}</td>
                                    <td>${item.saldoTotal.toLocaleString()}</td>
                                    <td>R$ ${item.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</td>
                                    <td class="status-${item.status.toLowerCase().replace('_', '-')}">${item.status}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </body>
                </html>
            `);
            relatorioWindow.document.close();
        };

        window.abrirSaldosPorArmazem = function() {
            // Gerar relatório de saldos por armazém
            const dadosPorArmazem = [];

            produtos.forEach(produto => {
                armazens.forEach(armazem => {
                    const estoque = estoques.find(e =>
                        e.produtoId === produto.id && e.armazemId === armazem.id
                    );

                    if (estoque && estoque.saldo > 0) {
                        dadosPorArmazem.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            armazem: armazem.nome,
                            saldo: estoque.saldo || 0,
                            unidade: produto.unidade || 'PC'
                        });
                    }
                });
            });

            // Criar janela de relatório
            const relatorioWindow = window.open('', '_blank', 'width=1000,height=600');
            relatorioWindow.document.write(`
                <html>
                <head>
                    <title>Saldos por Armazém</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <h1>🏪 Saldos por Armazém</h1>
                    <p>Gerado em: ${new Date().toLocaleString()}</p>
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Armazém</th>
                                <th>Saldo</th>
                                <th>Unidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${dadosPorArmazem.map(item => `
                                <tr>
                                    <td>${item.codigo}</td>
                                    <td>${item.descricao}</td>
                                    <td>${item.armazem}</td>
                                    <td>${item.saldo.toLocaleString()}</td>
                                    <td>${item.unidade}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </body>
                </html>
            `);
            relatorioWindow.document.close();
        };

        window.corrigirProblemasAutomatico = async function() {
            if (!confirm('🔧 Deseja corrigir automaticamente os saldos undefined para 0?')) {
                return;
            }

            let corrigidos = 0;

            try {
                for (const estoque of estoques) {
                    if (estoque.saldo === undefined || estoque.saldo === null) {
                        await updateDoc(doc(db, "estoques", estoque.id), {
                            saldo: 0,
                            ultimaMovimentacao: new Date()
                        });
                        corrigidos++;
                    }
                }

                showAlert(`✅ ${corrigidos} problemas corrigidos automaticamente!`, 'success');
                recarregarDados();

            } catch (error) {
                console.error('Erro na correção automática:', error);
                showAlert('Erro na correção automática: ' + error.message, 'error');
            }
        };

        // Inicializar
        carregarDados();
    </script>
</body>
</html>
