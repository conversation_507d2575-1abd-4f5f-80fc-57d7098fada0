<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Item 100101</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .negative {
            color: red;
            font-weight: bold;
        }
        .positive {
            color: green;
            font-weight: bold;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Item 100101 - Investigação de Saldo</h1>
        
        <div class="section">
            <h3>Controles</h3>
            <button class="btn" onclick="investigarItem()">🔍 Investigar Item 100101</button>
            <button class="btn" onclick="limparLog()">🗑️ Limpar Log</button>
        </div>

        <div class="section">
            <h3>📋 Informações do Produto</h3>
            <div id="produtoInfo">Clique em "Investigar" para carregar...</div>
        </div>

        <div class="section">
            <h3>📦 Registros de Estoque</h3>
            <div id="estoqueInfo">Clique em "Investigar" para carregar...</div>
        </div>

        <div class="section">
            <h3>🔄 Movimentações</h3>
            <div id="movimentacoesInfo">Clique em "Investigar" para carregar...</div>
        </div>

        <div class="section">
            <h3>📊 Análise de Saldo</h3>
            <div id="analiseInfo">Clique em "Investigar" para carregar...</div>
        </div>

        <div class="section">
            <h3>📝 Log de Debug</h3>
            <div id="logArea" class="log">Aguardando investigação...</div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            query,
            where,
            orderBy
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        window.limparLog = function() {
            document.getElementById('logArea').textContent = 'Log limpo...\n';
        };

        window.investigarItem = async function() {
            try {
                log('🔍 Iniciando investigação do item 100101...');
                
                // 1. Buscar produto
                log('📋 Buscando informações do produto...');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                const produto = produtos.find(p => p.codigo === '100101');
                if (!produto) {
                    log('❌ Produto 100101 não encontrado!');
                    return;
                }
                
                log(`✅ Produto encontrado: ${produto.descricao}`);
                
                // Mostrar info do produto
                document.getElementById('produtoInfo').innerHTML = `
                    <table class="data-table">
                        <tr><th>ID</th><td>${produto.id}</td></tr>
                        <tr><th>Código</th><td>${produto.codigo}</td></tr>
                        <tr><th>Descrição</th><td>${produto.descricao}</td></tr>
                        <tr><th>Unidade</th><td>${produto.unidade}</td></tr>
                        <tr><th>Tipo</th><td>${produto.tipo}</td></tr>
                    </table>
                `;

                // 2. Buscar todos os registros de estoque
                log('📦 Buscando registros de estoque...');
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                const estoquesItem = estoques.filter(e => e.produtoId === produto.id);
                log(`📦 Encontrados ${estoquesItem.length} registros de estoque para o item`);
                
                // Buscar armazéns
                const armazensSnap = await getDocs(collection(db, "armazens"));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                let estoqueHtml = '<table class="data-table"><tr><th>ID Registro</th><th>Armazém</th><th>Saldo</th><th>Saldo Reservado</th><th>Última Movimentação</th></tr>';
                let saldoTotal = 0;
                
                estoquesItem.forEach(est => {
                    const armazem = armazens.find(a => a.id === est.armazemId);
                    const saldo = est.saldo || 0;
                    saldoTotal += saldo;
                    
                    const saldoClass = saldo < 0 ? 'negative' : saldo > 0 ? 'positive' : '';
                    
                    estoqueHtml += `
                        <tr>
                            <td>${est.id}</td>
                            <td>${armazem ? armazem.nome : est.armazemId}</td>
                            <td class="${saldoClass}">${saldo}</td>
                            <td>${est.saldoReservado || 0}</td>
                            <td>${est.ultimaMovimentacao ? new Date(est.ultimaMovimentacao.seconds * 1000).toLocaleString() : '-'}</td>
                        </tr>
                    `;
                    
                    log(`📦 Registro ${est.id}: Saldo ${saldo} no armazém ${armazem ? armazem.nome : est.armazemId}`);
                });
                
                estoqueHtml += `<tr style="background-color: #f0f0f0; font-weight: bold;"><td colspan="2">TOTAL</td><td class="${saldoTotal < 0 ? 'negative' : 'positive'}">${saldoTotal}</td><td colspan="2">-</td></tr></table>`;
                document.getElementById('estoqueInfo').innerHTML = estoqueHtml;
                
                log(`📊 Saldo total calculado: ${saldoTotal}`);

                // 3. Buscar movimentações
                log('🔄 Buscando movimentações...');
                const movSnap = await getDocs(collection(db, "movimentacoesEstoque"));
                const movimentacoes = movSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                const movsItem = movimentacoes.filter(m => m.produtoId === produto.id);
                log(`🔄 Encontradas ${movsItem.length} movimentações para o item`);
                
                let movHtml = '<table class="data-table"><tr><th>Data</th><th>Tipo</th><th>Quantidade</th><th>Armazém</th><th>Documento</th></tr>';
                let saldoCalculado = 0;
                
                // Ordenar por data
                movsItem.sort((a, b) => {
                    const dateA = a.dataMovimentacao ? a.dataMovimentacao.seconds : 0;
                    const dateB = b.dataMovimentacao ? b.dataMovimentacao.seconds : 0;
                    return dateA - dateB;
                });
                
                movsItem.forEach(mov => {
                    const armazem = armazens.find(a => a.id === mov.armazemId);
                    const quantidade = mov.quantidade || 0;
                    
                    if (mov.tipo === 'ENTRADA') {
                        saldoCalculado += quantidade;
                    } else if (mov.tipo === 'SAIDA') {
                        saldoCalculado -= quantidade;
                    }
                    
                    const tipoClass = mov.tipo === 'ENTRADA' ? 'positive' : 'negative';
                    const quantidadeDisplay = mov.tipo === 'ENTRADA' ? `+${quantidade}` : `-${quantidade}`;
                    
                    movHtml += `
                        <tr>
                            <td>${mov.dataMovimentacao ? new Date(mov.dataMovimentacao.seconds * 1000).toLocaleString() : '-'}</td>
                            <td>${mov.tipo}</td>
                            <td class="${tipoClass}">${quantidadeDisplay}</td>
                            <td>${armazem ? armazem.nome : mov.armazemId}</td>
                            <td>${mov.numeroDocumento || '-'}</td>
                        </tr>
                    `;
                });
                
                movHtml += `<tr style="background-color: #f0f0f0; font-weight: bold;"><td colspan="2">SALDO CALCULADO</td><td class="${saldoCalculado < 0 ? 'negative' : 'positive'}">${saldoCalculado}</td><td colspan="2">-</td></tr></table>`;
                document.getElementById('movimentacoesInfo').innerHTML = movHtml;
                
                log(`🔄 Saldo calculado pelas movimentações: ${saldoCalculado}`);

                // 4. Análise final
                const diferenca = saldoTotal - saldoCalculado;
                let analiseHtml = `
                    <table class="data-table">
                        <tr><th>Saldo nos Registros de Estoque</th><td class="${saldoTotal < 0 ? 'negative' : 'positive'}">${saldoTotal}</td></tr>
                        <tr><th>Saldo Calculado pelas Movimentações</th><td class="${saldoCalculado < 0 ? 'negative' : 'positive'}">${saldoCalculado}</td></tr>
                        <tr><th>Diferença</th><td class="${diferenca !== 0 ? 'negative' : 'positive'}">${diferenca}</td></tr>
                        <tr><th>Status</th><td>${diferenca === 0 ? '✅ Consistente' : '❌ Inconsistente'}</td></tr>
                    </table>
                `;
                
                if (diferenca !== 0) {
                    analiseHtml += `<p style="color: red; font-weight: bold;">⚠️ PROBLEMA DETECTADO: Há inconsistência entre os saldos!</p>`;
                    log(`❌ INCONSISTÊNCIA DETECTADA: Diferença de ${diferenca}`);
                } else {
                    analiseHtml += `<p style="color: green; font-weight: bold;">✅ Saldos consistentes!</p>`;
                    log(`✅ Saldos consistentes`);
                }
                
                document.getElementById('analiseInfo').innerHTML = analiseHtml;
                
                log('🔍 Investigação concluída!');
                
            } catch (error) {
                log(`❌ Erro na investigação: ${error.message}`);
                console.error('Erro:', error);
            }
        };
    </script>
</body>
</html>
