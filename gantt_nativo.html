<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Gantt Chart - Ordens de Produção</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .gantt-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
            transform-origin: top left;
        }

        /* Container pai para controlar overflow do zoom */
        .gantt-wrapper {
            overflow: auto;
            max-height: 80vh;
            border-radius: 10px;
            margin-top: 20px;
        }

        /* Seletor de BOM */
        .bom-selector {
            display: inline-flex;
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .bom-selector select {
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
        }

        .bom-selector select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .gantt-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 2px solid #dee2e6;
            font-weight: bold;
            display: grid;
            grid-template-columns: 300px 1fr;
        }

        .gantt-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            max-height: 600px;
            overflow-y: auto;
        }

        .task-list {
            border-right: 2px solid #dee2e6;
        }

        .timeline {
            overflow-x: auto;
            position: relative;
        }

        .task-row {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            min-height: 50px;
            transition: background-color 0.3s ease;
        }

        .task-row:hover {
            background-color: #f8f9fa;
        }

        .task-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .task-info {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 4px;
        }

        .timeline-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .day-column {
            min-width: 40px;
            padding: 8px 4px;
            text-align: center;
            border-right: 1px solid #e9ecef;
            font-size: 0.8em;
            font-weight: 500;
        }

        .weekend {
            background-color: #f1f3f4;
            color: #6c757d;
        }

        .timeline-row {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            min-height: 50px;
            position: relative;
            align-items: center;
        }

        .timeline-cell {
            min-width: 40px;
            height: 50px;
            border-right: 1px solid #e9ecef;
            position: relative;
        }

        .task-bar {
            position: absolute;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            top: 50%;
            transform: translateY(-50%);
        }

        .task-bar:hover {
            transform: translateY(-50%) scale(1.05);
            z-index: 5;
        }

        .task-bar.normal {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .task-bar.critical {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: 2px solid #c0392b;
            animation: critical-pulse 2s infinite;
            box-shadow: 0 0 15px rgba(192, 57, 43, 0.5);
        }

        .task-bar.critical::after {
            content: "🔥";
            position: absolute;
            right: -8px;
            top: -8px;
            background: #c0392b;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        @keyframes critical-pulse {
            0% { box-shadow: 0 0 15px rgba(192, 57, 43, 0.5); }
            50% { box-shadow: 0 0 25px rgba(192, 57, 43, 0.8); }
            100% { box-shadow: 0 0 15px rgba(192, 57, 43, 0.5); }
        }

        .task-bar.completed {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .task-bar.in-progress {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .task-bar.high-priority {
            background: linear-gradient(135deg, #fd7e14, #e55a00);
            border: 1px solid #d63384;
        }

        .task-bar.low-priority {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        /* Estilos por nível da estrutura */
        .task-bar.level-0 {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            border-left: 4px solid #0D47A1;
        }

        .task-bar.level-1 {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            border-left: 4px solid #4a148c;
        }

        .task-bar.level-2 {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            border-left: 4px solid #1b5e20;
        }

        .task-bar.level-3 {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            border-left: 4px solid #e65100;
        }

        .task-bar.level-4 {
            background: linear-gradient(135deg, #e91e63, #c2185b);
            border-left: 4px solid #880e4f;
        }

        /* Tarefas virtuais */
        .task-bar.virtual-task {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: 2px dashed #343a40;
            opacity: 0.8;
        }

        .task-bar.virtual-task::after {
            content: '👻';
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
        }

        .task-row.child {
            margin-left: 20px;
            border-left: 3px solid #007bff;
            padding-left: 15px;
            background: #f8f9fa;
        }

        .task-name.child {
            font-size: 0.9em;
            color: #495057;
        }

        .legend {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .legend h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .legend-items {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 10px;
        }

        .stats {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .tooltip {
            position: absolute;
            background: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8em;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .tooltip.show {
            opacity: 1;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #2c3e50;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Gantt Chart - Ordens de Produção</h1>
            <p>Análise de Caminho Crítico - Integrado com Firebase</p>
        </div>

        <div class="alert alert-info">
            <strong>🏭 Sistema Integrado!</strong> Carregue suas Ordens de Produção reais do Firebase e analise o caminho crítico automaticamente.
        </div>

        <div class="controls">
            <div class="data-source-controls" style="display: inline-flex; gap: 10px; margin-right: 20px; align-items: center;">
                <label style="font-weight: bold; color: #2c3e50;">📊 Fonte de Dados:</label>
                <button class="btn btn-primary" onclick="carregarOPsReais()" id="btnOPsReais">
                    🏭 OPs Reais
                </button>
                <button class="btn btn-info" onclick="carregarBOM()" id="btnBOM">
                    📋 BOM/Estruturas
                </button>
            </div>

            <!-- Seletor de Produto para BOM -->
            <div class="bom-selector" id="bomSelector" style="display: none; margin-right: 15px; align-items: center; gap: 10px;">
                <label style="font-weight: bold; color: #2c3e50;">🏭 Produto:</label>
                <select id="seletorProdutoBOM" onchange="selecionarProdutoBOM()" style="padding: 8px; min-width: 200px;">
                    <option value="">Selecione um produto...</option>
                </select>
                <button class="btn btn-success" onclick="gerarBOMSelecionado()" id="btnGerarBOM" disabled>
                    📋 Gerar BOM
                </button>
            </div>

            <button class="btn btn-success" onclick="toggleFluxo()" id="btnFluxo">
                <i class="fas fa-exchange-alt"></i> 🔄 Fluxo: Normal
            </button>

            <div class="zoom-controls" style="display: inline-flex; gap: 5px; margin: 0 10px; align-items: center;">
                <button class="btn btn-secondary" onclick="zoomOut()" title="Zoom Out">
                    <i class="fas fa-search-minus"></i> 🔍-
                </button>
                <span id="zoomLevel" style="padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-weight: bold; min-width: 50px; text-align: center;">100%</span>
                <button class="btn btn-secondary" onclick="zoomIn()" title="Zoom In">
                    <i class="fas fa-search-plus"></i> 🔍+
                </button>
                <button class="btn btn-secondary" onclick="resetZoom()" title="Reset Zoom">
                    <i class="fas fa-expand"></i> 📐
                </button>
            </div>

            <button class="btn btn-danger" onclick="calcularCaminhoCritico()">🔥 Caminho Crítico</button>
            <button class="btn btn-warning" onclick="toggleMostrarMP()" id="btnMostrarMP">
                📦 Ocultar MP
            </button>
            <select id="filtroOP" onchange="filtrarPorOP()" style="margin-left: 20px; padding: 8px;">
                <option value="">Todas as OPs</option>
            </select>
            <button class="btn" onclick="voltarMenu()" style="margin-left: auto;">← Voltar ao Menu</button>
        </div>

        <div class="gantt-wrapper">
            <div class="gantt-container" id="ganttContainer">
                <div class="gantt-header">
                    <div>📋 Tarefas</div>
                    <div>📅 Cronograma</div>
                </div>
                <div class="gantt-content">
                    <div class="task-list" id="taskList">
                        <!-- Tarefas serão inseridas aqui -->
                    </div>
                    <div class="timeline" id="timeline">
                        <!-- Timeline será inserida aqui -->
                    </div>
                </div>
            </div>
        </div>

        <div class="legend">
            <h3>📋 Legenda</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #007bff, #0056b3);"></div>
                    <span>Tarefa Normal</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24);"></div>
                    <span>🔥 Caminho Crítico</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #28a745, #1e7e34);"></div>
                    <span>✅ Concluída</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #ffc107, #e0a800);"></div>
                    <span>⏳ Em Progresso</span>
                </div>
            </div>
        </div>

        <div class="stats" id="statsContainer" style="display: none;">
            <h3>📊 Estatísticas do Projeto</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTasks">0</div>
                    <div class="stat-label">Total de Tarefas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="criticalTasks">0</div>
                    <div class="stat-label">Tarefas Críticas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="projectDuration">0</div>
                    <div class="stat-label">Duração (dias)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completedTasks">0</div>
                    <div class="stat-label">Concluídas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tooltip -->
    <div class="tooltip" id="tooltip"></div>

    <!-- Firebase -->
    <script type="module">
        // ===================================================================
        // GANTT CHART NATIVO - INTEGRADO COM FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            query,
            where,
            orderBy
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let tasks = [];
        let criticalPath = [];
        let projectStartDate = new Date();
        let projectEndDate = new Date();
        let ordensProducao = [];
        let produtos = [];
        let estruturas = [];
        let allOPs = [];

        // Controles de visualização
        let fluxoInvertido = false; // false = Normal (PA->SP->MP), true = Invertido (MP->SP->PA)
        let zoomLevel = 100; // Nível de zoom em porcentagem
        let minZoom = 25; // Zoom mínimo (25%)
        let maxZoom = 200; // Zoom máximo (200%)
        let mostrarMP = false; // false = Ocultar MP, true = Mostrar MP
        let fonteDados = 'ops'; // 'ops' = OPs Reais, 'bom' = BOM/Estruturas

        // ===================================================================
        // CARREGAMENTO DE DADOS REAIS DO FIREBASE
        // ===================================================================

        window.carregarOPsReais = async function() {
            try {
                fonteDados = 'ops';
                atualizarBotoesDataSource();
                showAlert('🔄 Carregando OPs do Firebase...', 'info');

                // Carregar dados do Firebase
                await carregarDadosFirebase();

                // Converter OPs para tarefas do Gantt
                await converterOPsParaTarefas();

                // Renderizar Gantt
                renderizarGantt();

                showAlert('✅ OPs reais carregadas com sucesso!', 'success');

            } catch (error) {
                console.error('Erro ao carregar OPs:', error);
                showAlert('❌ Erro ao carregar OPs: ' + error.message, 'warning');
            }
        };

        window.carregarBOM = async function() {
            try {
                fonteDados = 'bom';
                atualizarBotoesDataSource();
                showAlert('🔄 Carregando estruturas BOM...', 'info');

                // Carregar dados do Firebase
                await carregarDadosFirebase();

                // Mostrar seletor de produtos
                mostrarSeletorBOM();

                showAlert('✅ Estruturas carregadas! Selecione um produto para gerar o BOM.', 'success');

            } catch (error) {
                console.error('Erro ao carregar BOM:', error);
                showAlert('❌ Erro ao carregar estruturas: ' + error.message, 'warning');
            }
        };

        function atualizarBotoesDataSource() {
            const btnOPs = document.getElementById('btnOPsReais');
            const btnBOM = document.getElementById('btnBOM');
            const bomSelector = document.getElementById('bomSelector');

            if (fonteDados === 'ops') {
                btnOPs.className = 'btn btn-primary';
                btnBOM.className = 'btn btn-outline-info';
                bomSelector.style.display = 'none';
            } else {
                btnOPs.className = 'btn btn-outline-primary';
                btnBOM.className = 'btn btn-info';
                bomSelector.style.display = 'inline-flex';
            }
        }

        async function carregarDadosFirebase() {
            console.log('📊 Carregando dados do Firebase...');

            // Carregar produtos
            const produtosSnap = await getDocs(collection(db, "produtos"));
            produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // Carregar estruturas
            const estruturasSnap = await getDocs(collection(db, "estruturas"));
            estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // Carregar ordens de produção (consulta simples sem índice)
            console.log('📊 Carregando todas as OPs...');
            const opsSnap = await getDocs(collection(db, "ordensProducao"));
            const todasOPs = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            console.log(`📊 Total de OPs encontradas: ${todasOPs.length}`);

            // Filtrar apenas OPs ativas (sem usar query complexa)
            ordensProducao = todasOPs.filter(op => {
                const status = op.status;
                return status === "Pendente" ||
                       status === "Em Produção" ||
                       status === "Firme" ||
                       status === "Planejada" ||
                       status === "Em Andamento";
            });

            console.log(`📊 OPs ativas filtradas: ${ordensProducao.length}`);

            // Ordenar por nível e data
            ordensProducao.sort((a, b) => {
                if ((a.nivel || 0) !== (b.nivel || 0)) return (a.nivel || 0) - (b.nivel || 0);
                const dateA = a.dataCriacao?.seconds || 0;
                const dateB = b.dataCriacao?.seconds || 0;
                return dateA - dateB;
            });

            allOPs = [...ordensProducao];

            console.log(`📊 Carregados: ${produtos.length} produtos, ${estruturas.length} estruturas, ${ordensProducao.length} OPs`);

            // Atualizar filtro de OPs
            atualizarFiltroOPs();
        }

        async function converterOPsParaTarefas() {
            console.log('🔄 Convertendo OPs para tarefas do Gantt usando estruturas...');

            tasks = [];
            const hoje = new Date();

            // Filtrar apenas OPs pai (nível 0)
            const opsPai = ordensProducao.filter(op => (op.nivel || 0) === 0);
            console.log(`📊 Encontradas ${opsPai.length} OPs pai (nível 0)`);

            for (let i = 0; i < opsPai.length; i++) {
                const op = opsPai[i];
                const produto = produtos.find(p => p.id === op.produtoId);

                if (!produto) {
                    console.warn(`⚠️ Produto não encontrado para OP ${op.numero || op.id}`);
                    continue;
                }

                console.log(`🏭 Processando OP: ${op.numero || 'OP'} - ${produto.codigo}`);

                // Explodir estrutura para criar hierarquia de tarefas
                await explodirEstruturaProduto(op, produto, i);
            }

            console.log(`✅ Total de tarefas criadas: ${tasks.length}`);

            // Aplicar ordenação baseada no fluxo
            if (fluxoInvertido) {
                inverterOrdemTarefas();
            }

            calcularDatasFinais();
        }

        // Função removida - agora usa converterBOMEspecifico para produtos selecionados

        function mostrarSeletorBOM() {
            const seletor = document.getElementById('seletorProdutoBOM');
            seletor.innerHTML = '<option value="">Selecione um produto...</option>';

            // Buscar produtos que possuem estrutura (PA e SP)
            const produtosComEstrutura = produtos.filter(produto =>
                (produto.tipo === 'PA' || produto.tipo === 'SP') &&
                estruturas.some(e => e.produtoPaiId === produto.id)
            );

            console.log(`📋 Encontrados ${produtosComEstrutura.length} produtos com estrutura`);

            // Ordenar por código
            produtosComEstrutura.sort((a, b) => a.codigo.localeCompare(b.codigo));

            produtosComEstrutura.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `${produto.codigo} - ${produto.descricao} (${produto.tipo})`;
                seletor.appendChild(option);
            });

            // Limpar Gantt atual
            tasks = [];
            renderizarGantt();
        }

        window.selecionarProdutoBOM = function() {
            const seletor = document.getElementById('seletorProdutoBOM');
            const btnGerar = document.getElementById('btnGerarBOM');

            if (seletor.value) {
                btnGerar.disabled = false;
                btnGerar.className = 'btn btn-success';

                const produto = produtos.find(p => p.id === seletor.value);
                if (produto) {
                    showAlert(`📋 Produto selecionado: ${produto.codigo}. Clique em "Gerar BOM" para visualizar.`, 'info');
                }
            } else {
                btnGerar.disabled = true;
                btnGerar.className = 'btn btn-secondary';
            }
        };

        window.gerarBOMSelecionado = async function() {
            const seletor = document.getElementById('seletorProdutoBOM');
            const produtoId = seletor.value;

            if (!produtoId) {
                showAlert('⚠️ Selecione um produto primeiro!', 'warning');
                return;
            }

            const produto = produtos.find(p => p.id === produtoId);
            if (!produto) {
                showAlert('❌ Produto não encontrado!', 'error');
                return;
            }

            try {
                showAlert(`🔄 Gerando BOM para ${produto.codigo}...`, 'info');

                // Converter BOM específico
                await converterBOMEspecifico(produto);

                // Renderizar Gantt
                renderizarGantt();

                showAlert(`✅ BOM gerado para ${produto.codigo}! ${tasks.length} itens encontrados.`, 'success');

            } catch (error) {
                console.error('Erro ao gerar BOM:', error);
                showAlert('❌ Erro ao gerar BOM: ' + error.message, 'error');
            }
        };

        function atualizarFiltroBOM() {
            const filtro = document.getElementById('filtroOP');
            filtro.innerHTML = '<option value="">Todas as Estruturas</option>';

            const produtosPA = produtos.filter(p => p.tipo === 'PA');
            const produtosComEstrutura = produtosPA.filter(pa =>
                estruturas.some(e => e.produtoPaiId === pa.id)
            );

            produtosComEstrutura.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `BOM-${produto.codigo} - ${produto.descricao}`;
                filtro.appendChild(option);
            });
        }

        async function explodirEstruturaProduto(opPai, produto, taskIndex) {
            const hoje = new Date();

            // Criar tarefa principal (OP Pai)
            const duracaoPai = calcularDuracaoOP(opPai, produto);
            const dataInicioPai = opPai.dataInicio ?
                new Date(opPai.dataInicio.seconds * 1000) :
                new Date(hoje.getTime() + taskIndex * 24 * 60 * 60 * 1000);

            const taskPai = {
                id: tasks.length + 1,
                opId: opPai.id,
                name: `${opPai.numero || 'OP'} - ${produto.codigo}`,
                description: produto.descricao,
                duration: duracaoPai,
                startDate: dataInicioPai,
                dependencies: [],
                status: determinarStatus(opPai.status),
                progress: calcularProgressoOP(opPai),
                quantidade: opPai.quantidade,
                dataEntrega: opPai.dataEntrega ? new Date(opPai.dataEntrega.seconds * 1000) : null,
                nivel: 0,
                prioridade: opPai.prioridade || 'normal',
                tipo: produto.tipo,
                isParent: true
            };

            tasks.push(taskPai);

            // Buscar estrutura do produto
            const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);

            if (estrutura && estrutura.componentes && estrutura.componentes.length > 0) {
                console.log(`📋 Explodindo estrutura de ${produto.codigo} - ${estrutura.componentes.length} componentes`);

                // Explodir componentes recursivamente
                await explodirComponentes(estrutura.componentes, opPai, 1, taskPai.id);
            }
        }

        async function explodirComponentes(componentes, opPai, nivel, parentTaskId) {
            for (const componente of componentes) {
                const componenteProduto = produtos.find(p => p.id === componente.componentId);

                if (!componenteProduto) {
                    console.warn(`⚠️ Componente não encontrado: ${componente.componentId}`);
                    continue;
                }

                const quantidadeComponente = opPai.quantidade * componente.quantidade;

                // Buscar OP filha correspondente (se existir)
                const opFilha = ordensProducao.find(op =>
                    op.produtoId === componente.componentId &&
                    (op.nivel || 0) === nivel &&
                    (op.produtoPaiId === opPai.produtoId || op.opPaiId === opPai.id)
                );

                // Calcular duração baseada na estrutura ou OP filha
                const duracao = opFilha ?
                    calcularDuracaoOP(opFilha, componenteProduto) :
                    calcularDuracaoEstrutura(componenteProduto, quantidadeComponente);

                // Data de início (dependente da tarefa pai)
                const taskPai = tasks.find(t => t.id === parentTaskId);
                const dataInicio = new Date(taskPai.startDate.getTime() + (nivel - 1) * 24 * 60 * 60 * 1000);

                const taskComponente = {
                    id: tasks.length + 1,
                    opId: opFilha?.id || `virtual-${componente.componentId}-${nivel}`,
                    name: `${'  '.repeat(nivel)}└ ${componenteProduto.codigo}`,
                    description: componenteProduto.descricao,
                    duration: duracao,
                    startDate: dataInicio,
                    dependencies: [parentTaskId],
                    status: opFilha ? determinarStatus(opFilha.status) : 'normal',
                    progress: opFilha ? calcularProgressoOP(opFilha) : 0,
                    quantidade: quantidadeComponente,
                    dataEntrega: opFilha?.dataEntrega ? new Date(opFilha.dataEntrega.seconds * 1000) : null,
                    nivel: nivel,
                    prioridade: opFilha?.prioridade || 'normal',
                    tipo: componenteProduto.tipo,
                    isChild: true,
                    parentTaskId: parentTaskId,
                    isVirtual: !opFilha // Indica se é uma tarefa virtual (sem OP real)
                };

                // Só adicionar se não for MP ou se MP estiver habilitado
                if (componenteProduto.tipo !== 'MP' || mostrarMP) {
                    tasks.push(taskComponente);
                }

                // Se é SP (Semi-Produto), explodir sua estrutura recursivamente
                if (componenteProduto.tipo === 'SP' && nivel < 5) { // Limitar a 5 níveis
                    const estruturaComponente = estruturas.find(e => e.produtoPaiId === componente.componentId);

                    if (estruturaComponente && estruturaComponente.componentes) {
                        console.log(`📋 Explodindo sub-estrutura de ${componenteProduto.codigo} (nível ${nivel})`);
                        await explodirComponentes(estruturaComponente.componentes, opPai, nivel + 1, taskComponente.id);
                    }
                }
            }
        }

        function determinarStatus(status) {
            if (status === 'Concluída' || status === 'Concluida') return 'completed';
            if (status === 'Em Produção' || status === 'Em Producao') return 'in-progress';
            if (status === 'Firme') return 'in-progress';
            return 'normal';
        }

        function calcularDuracaoEstrutura(produto, quantidade) {
            // Buscar estrutura do produto para calcular tempo
            const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);

            if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
                const tempoTotal = estrutura.operacoes.reduce((total, operacao) => {
                    return total + (operacao.tempoSetup || 0) + (operacao.tempoCiclo || 0) * quantidade;
                }, 0);

                return Math.max(1, Math.ceil(tempoTotal / (8 * 60))); // minutos para dias
            }

            // Duração padrão baseada no tipo
            if (produto.tipo === 'PA') return 5;
            if (produto.tipo === 'SP') return 3;
            return 1; // MP
        }

        function calcularDuracaoOP(op, produto) {
            // Buscar estrutura do produto
            const estrutura = estruturas.find(e => e.produtoPaiId === op.produtoId);

            if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
                // Somar tempo de todas as operações
                const tempoTotal = estrutura.operacoes.reduce((total, operacao) => {
                    return total + (operacao.tempoSetup || 0) + (operacao.tempoCiclo || 0) * op.quantidade;
                }, 0);

                // Converter para dias (assumindo 8 horas por dia)
                return Math.max(1, Math.ceil(tempoTotal / (8 * 60))); // minutos para dias
            }

            // Duração padrão baseada no tipo de produto
            if (produto.tipo === 'PA') return 5; // Produto acabado
            if (produto.tipo === 'SP') return 3; // Semi-produto
            return 2; // Padrão
        }

        function calcularProgressoOP(op) {
            if (op.status === 'Concluída') return 100;
            if (op.status === 'Em Produção') return 50;
            if (op.status === 'Firme') return 25;
            return 0;
        }

        function calcularDependencias(op, opsPai, currentIndex) {
            // Buscar OPs filhas que esta OP depende
            const opsFilhas = ordensProducao.filter(opFilha =>
                opFilha.nivel > 0 &&
                opFilha.produtoPaiId === op.produtoId
            );

            if (opsFilhas.length > 0) {
                // Se tem filhas, depende da conclusão delas
                return []; // Será calculado depois quando adicionarmos as filhas
            }

            // Dependência sequencial simples (cada OP depende da anterior)
            return currentIndex > 0 ? [currentIndex] : [];
        }

        // Função removida - agora a explosão de estruturas é feita diretamente na converterOPsParaTarefas

        function atualizarFiltroOPs() {
            const filtro = document.getElementById('filtroOP');
            filtro.innerHTML = '<option value="">Todas as OPs</option>';

            const opsPai = allOPs.filter(op => (op.nivel || 0) === 0);
            opsPai.forEach(op => {
                const produto = produtos.find(p => p.id === op.produtoId);
                const option = document.createElement('option');
                option.value = op.id;
                option.textContent = `${op.numero || 'OP'} - ${produto?.codigo || 'N/A'}`;
                filtro.appendChild(option);
            });
        }

        window.filtrarPorOP = function() {
            const filtroValue = document.getElementById('filtroOP').value;

            if (fonteDados === 'ops') {
                // Filtro para OPs
                if (!filtroValue) {
                    ordensProducao = [...allOPs];
                } else {
                    const opSelecionada = allOPs.find(op => op.id === filtroValue);
                    if (opSelecionada) {
                        ordensProducao = allOPs.filter(op =>
                            op.id === filtroValue ||
                            op.produtoPaiId === opSelecionada.produtoId ||
                            op.opPaiId === filtroValue ||
                            (op.nivel > 0 && op.produtoPaiId === opSelecionada.produtoId)
                        );
                    } else {
                        ordensProducao = [...allOPs];
                    }
                }

                // Reconverter e renderizar
                converterOPsParaTarefas().then(() => {
                    renderizarGantt();
                    showAlert(`📊 Filtro aplicado: ${ordensProducao.length} OPs encontradas`, 'info');
                });
            } else {
                // Filtro para BOM
                if (!filtroValue) {
                    // Limpar Gantt no modo BOM quando não há filtro
                    tasks = [];
                    renderizarGantt();
                    showAlert('📋 Selecione um produto específico para gerar o BOM', 'info');
                } else {
                    // Filtrar estrutura específica
                    const produto = produtos.find(p => p.id === filtroValue);
                    if (produto) {
                        converterBOMEspecifico(produto).then(() => {
                            renderizarGantt();
                            showAlert(`📊 Mostrando estrutura de ${produto.codigo}`, 'info');
                        });
                    }
                }
            }
        };

        async function converterBOMEspecifico(produto) {
            console.log(`🔄 Convertendo BOM específico: ${produto.codigo}`);

            tasks = [];
            const hoje = new Date();

            // Criar OP virtual para o produto específico
            const opVirtual = {
                id: `bom-${produto.id}`,
                numero: `BOM-${produto.codigo}`,
                produtoId: produto.id,
                quantidade: 1,
                status: 'Planejada',
                nivel: 0
            };

            // Explodir estrutura
            await explodirEstruturaProduto(opVirtual, produto, 0);

            // Aplicar ordenação baseada no fluxo
            if (fluxoInvertido) {
                inverterOrdemTarefas();
            }

            calcularDatasFinais();
        }

        // ===================================================================
        // CONTROLES DE FLUXO E ZOOM
        // ===================================================================

        function inverterOrdemTarefas() {
            console.log('🔄 Invertendo ordem das tarefas...');

            // Separar tarefas por nível
            const tarefasPorNivel = {};
            tasks.forEach(task => {
                const nivel = task.nivel || 0;
                if (!tarefasPorNivel[nivel]) {
                    tarefasPorNivel[nivel] = [];
                }
                tarefasPorNivel[nivel].push(task);
            });

            // Reorganizar: MP (nível mais alto) -> SP -> PA (nível 0)
            const niveisOrdenados = Object.keys(tarefasPorNivel)
                .map(n => parseInt(n))
                .sort((a, b) => b - a); // Ordem decrescente

            tasks = [];
            let newId = 1;

            niveisOrdenados.forEach(nivel => {
                // Ordenar tarefas do mesmo nível por tipo (MP -> SP -> PA)
                const tarefasNivel = tarefasPorNivel[nivel].sort((a, b) => {
                    const ordemTipo = { 'MP': 1, 'SP': 2, 'PA': 3 };
                    return (ordemTipo[a.tipo] || 4) - (ordemTipo[b.tipo] || 4);
                });

                tarefasNivel.forEach(task => {
                    task.id = newId++;
                    // Recalcular dependências no fluxo invertido
                    task.dependencies = calcularDependenciasInvertidas(task, tasks);
                    tasks.push(task);
                });
            });

            console.log(`✅ Ordem invertida aplicada: ${tasks.length} tarefas reorganizadas`);
        }

        function calcularDependenciasInvertidas(task, tarefasExistentes) {
            // No fluxo invertido, tarefas de nível menor dependem das de nível maior
            const dependencias = [];

            tarefasExistentes.forEach(existingTask => {
                // Se a tarefa atual é de nível menor e do mesmo produto pai
                if (existingTask.nivel > task.nivel &&
                    existingTask.parentTaskId === task.parentTaskId) {
                    dependencias.push(existingTask.id);
                }
            });

            return dependencias;
        }

        window.toggleFluxo = function() {
            if (tasks.length === 0) {
                showAlert('⚠️ Carregue as OPs primeiro!', 'warning');
                return;
            }

            fluxoInvertido = !fluxoInvertido;
            const btnFluxo = document.getElementById('btnFluxo');

            if (fluxoInvertido) {
                btnFluxo.innerHTML = '<i class="fas fa-exchange-alt"></i> 🔄 Fluxo: Invertido';
                btnFluxo.className = 'btn btn-warning';
                showAlert('🔄 Fluxo invertido: MP → SP → PA', 'info');
            } else {
                btnFluxo.innerHTML = '<i class="fas fa-exchange-alt"></i> 🔄 Fluxo: Normal';
                btnFluxo.className = 'btn btn-info';
                showAlert('🔄 Fluxo normal: PA → SP → MP', 'info');
            }

            // Reconverter e renderizar
            if (fonteDados === 'ops') {
                converterOPsParaTarefas().then(() => {
                    renderizarGantt();
                });
            } else {
                // No modo BOM, não reconverte automaticamente
                showAlert('📋 Selecione um produto específico para gerar o BOM', 'info');
            }
        };

        window.toggleMostrarMP = function() {
            if (tasks.length === 0) {
                showAlert('⚠️ Carregue os dados primeiro!', 'warning');
                return;
            }

            mostrarMP = !mostrarMP;
            const btnMostrarMP = document.getElementById('btnMostrarMP');

            if (mostrarMP) {
                btnMostrarMP.innerHTML = '📦 Mostrar MP';
                btnMostrarMP.className = 'btn btn-success';
                showAlert('📦 Matérias-primas agora são exibidas', 'info');
            } else {
                btnMostrarMP.innerHTML = '📦 Ocultar MP';
                btnMostrarMP.className = 'btn btn-warning';
                showAlert('📦 Matérias-primas foram ocultadas', 'info');
            }

            // Reconverter e renderizar
            if (fonteDados === 'ops') {
                converterOPsParaTarefas().then(() => {
                    renderizarGantt();
                });
            } else {
                // No modo BOM, não reconverte automaticamente
                showAlert('📋 Selecione um produto específico para gerar o BOM', 'info');
            }
        };

        window.zoomIn = function() {
            if (zoomLevel < maxZoom) {
                zoomLevel += 25;
                aplicarZoom();
            }
        };

        window.zoomOut = function() {
            if (zoomLevel > minZoom) {
                zoomLevel -= 25;
                aplicarZoom();
            }
        };

        window.resetZoom = function() {
            zoomLevel = 100;
            aplicarZoom();
        };

        function aplicarZoom() {
            const ganttContainer = document.getElementById('ganttContainer');

            // Aplicar zoom
            ganttContainer.style.transform = `scale(${zoomLevel / 100})`;

            // Atualizar indicador de zoom
            document.getElementById('zoomLevel').textContent = `${zoomLevel}%`;

            // Feedback visual
            if (zoomLevel < 100) {
                showAlert(`🔍 Zoom aplicado: ${zoomLevel}% - Visão panorâmica ativa`, 'info');
            } else if (zoomLevel > 100) {
                showAlert(`🔍 Zoom aplicado: ${zoomLevel}% - Visão detalhada ativa`, 'info');
            } else {
                showAlert('📐 Zoom resetado para 100%', 'info');
            }
        }

        // ===================================================================
        // FUNÇÕES AUXILIARES
        // ===================================================================

        // ===================================================================
        // CÁLCULOS DE DATAS
        // ===================================================================

        function calcularDatasFinais() {
            tasks.forEach(task => {
                task.endDate = new Date(task.startDate.getTime() + (task.duration - 1) * 24 * 60 * 60 * 1000);
            });

            projectStartDate = new Date(Math.min(...tasks.map(t => t.startDate.getTime())));
            projectEndDate = new Date(Math.max(...tasks.map(t => t.endDate.getTime())));
        }

        // ===================================================================
        // RENDERIZAÇÃO DO GANTT
        // ===================================================================

        function renderizarGantt() {
            renderizarListaTarefas();
            renderizarTimeline();
            atualizarEstatisticas();
        }

        function renderizarListaTarefas() {
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = '';

            tasks.forEach(task => {
                const taskRow = document.createElement('div');
                taskRow.className = 'task-row';

                // Indentação baseada no nível da estrutura
                const nivel = task.nivel || 0;
                const indentPixels = nivel * 25; // 25px por nível

                // Cores por nível (similar ao relatorio_explosao_estruturas.html)
                const levelColors = [
                    '#2196F3', // Nível 0 - Azul
                    '#9c27b0', // Nível 1 - Roxo
                    '#4caf50', // Nível 2 - Verde
                    '#ff9800', // Nível 3 - Laranja
                    '#e91e63'  // Nível 4 - Rosa
                ];

                const borderColor = levelColors[nivel] || '#6c757d';

                let indentStyle = '';
                if (nivel > 0) {
                    indentStyle = `
                        margin-left: ${indentPixels}px;
                        border-left: 3px solid ${borderColor};
                        padding-left: 15px;
                        background: linear-gradient(90deg, ${borderColor}10, transparent);
                    `;
                }

                // Ícones por tipo de produto
                const typeIcons = {
                    'PA': '🏭', // Produto Acabado
                    'SP': '⚙️', // Semi-Produto
                    'MP': '📦'  // Matéria Prima
                };

                // Determinar ícone do status
                let statusIcon = '⏳';
                if (task.status === 'completed') statusIcon = '✅';
                else if (task.status === 'in-progress') statusIcon = '🔄';
                else if (task.status === 'critical') statusIcon = '🔥';

                // Ícone do nível
                const levelIcons = ['🔷', '🔸', '🔹', '🔺', '🔻'];
                const levelIcon = levelIcons[nivel] || '🔸';

                // Informações adicionais
                let extraInfo = `${task.duration} dias • ${task.progress}% concluído`;

                if (task.quantidade) {
                    extraInfo += ` • Qtd: ${task.quantidade.toFixed(3)}`;
                }

                if (task.dataEntrega) {
                    const diasRestantes = Math.ceil((task.dataEntrega - new Date()) / (24 * 60 * 60 * 1000));
                    extraInfo += ` • Entrega: ${diasRestantes} dias`;
                }

                if (task.prioridade && task.prioridade !== 'normal') {
                    extraInfo += ` • ${task.prioridade.toUpperCase()}`;
                }

                // Indicador se é tarefa virtual (sem OP real)
                const virtualIndicator = task.isVirtual ? ' 👻' : '';

                taskRow.innerHTML = `
                    <div style="${indentStyle}">
                        <div class="task-name">
                            ${levelIcon} ${statusIcon} ${typeIcons[task.tipo] || '📋'} ${task.name}
                            ${task.status === 'critical' ? ' 🔥' : ''}${virtualIndicator}
                        </div>
                        <div class="task-info">
                            <span style="color: ${borderColor}; font-weight: 600;">Nível ${nivel}</span> • ${extraInfo}
                            ${task.description ? `<br><small>${task.description}</small>` : ''}
                            ${task.isVirtual ? '<br><small style="color: #6c757d;">⚠️ Tarefa virtual (sem OP correspondente)</small>' : ''}
                        </div>
                    </div>
                `;
                taskList.appendChild(taskRow);
            });
        }

        function renderizarTimeline() {
            const timeline = document.getElementById('timeline');
            timeline.innerHTML = '';

            // Calcular número de dias
            const totalDays = Math.ceil((projectEndDate - projectStartDate) / (24 * 60 * 60 * 1000)) + 1;
            
            // Criar header da timeline
            const timelineHeader = document.createElement('div');
            timelineHeader.className = 'timeline-header';
            
            for (let i = 0; i < totalDays; i++) {
                const currentDate = new Date(projectStartDate.getTime() + i * 24 * 60 * 60 * 1000);
                const dayColumn = document.createElement('div');
                dayColumn.className = 'day-column';
                
                const dayOfWeek = currentDate.getDay();
                if (dayOfWeek === 0 || dayOfWeek === 6) {
                    dayColumn.classList.add('weekend');
                }
                
                dayColumn.innerHTML = `
                    <div>${currentDate.getDate()}</div>
                    <div style="font-size: 0.7em;">${currentDate.toLocaleDateString('pt-BR', { weekday: 'short' })}</div>
                `;
                timelineHeader.appendChild(dayColumn);
            }
            timeline.appendChild(timelineHeader);

            // Criar linhas das tarefas
            tasks.forEach(task => {
                const timelineRow = document.createElement('div');
                timelineRow.className = 'timeline-row';

                // Criar células vazias
                for (let i = 0; i < totalDays; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'timeline-cell';
                    timelineRow.appendChild(cell);
                }

                // Calcular posição da barra da tarefa
                const startDay = Math.floor((task.startDate - projectStartDate) / (24 * 60 * 60 * 1000));
                const taskWidth = task.duration * 40; // 40px por dia
                const taskLeft = startDay * 40;

                // Criar barra da tarefa
                const taskBar = document.createElement('div');
                let taskClasses = `task-bar ${task.status}`;

                // Adicionar classe de prioridade
                if (task.prioridade === 'alta') {
                    taskClasses += ' high-priority';
                } else if (task.prioridade === 'baixa') {
                    taskClasses += ' low-priority';
                }

                // Adicionar classe baseada no nível
                if (task.nivel > 0) {
                    taskClasses += ` level-${task.nivel}`;
                }

                // Adicionar classe para tarefas virtuais
                if (task.isVirtual) {
                    taskClasses += ' virtual-task';
                }

                taskBar.className = taskClasses;
                taskBar.style.left = taskLeft + 'px';
                taskBar.style.width = taskWidth + 'px';

                // Altura da barra baseada no nível (tarefas pai são mais altas)
                const barHeight = task.nivel === 0 ? '24px' : '20px';
                taskBar.style.height = barHeight;

                // Texto da barra (mais compacto)
                let displayText = task.name;
                if (task.name.includes('└')) {
                    displayText = task.name.split('└')[1].trim();
                }

                // Adicionar ícone do tipo
                const typeIcons = { 'PA': '🏭', 'SP': '⚙️', 'MP': '📦' };
                const typeIcon = typeIcons[task.tipo] || '';

                taskBar.textContent = `${typeIcon} ${displayText}`;

                // Estilo adicional para tarefas virtuais
                if (task.isVirtual) {
                    taskBar.style.opacity = '0.7';
                    taskBar.style.borderStyle = 'dashed';
                }

                // Adicionar eventos de tooltip
                taskBar.addEventListener('mouseenter', (e) => showTooltip(e, task));
                taskBar.addEventListener('mouseleave', hideTooltip);

                timelineRow.appendChild(taskBar);
                timeline.appendChild(timelineRow);
            });
        }

        // ===================================================================
        // CÁLCULO DO CAMINHO CRÍTICO
        // ===================================================================

        function calcularCaminhoCritico() {
            console.log('🔥 Calculando caminho crítico...');

            if (tasks.length === 0) {
                showAlert('⚠️ Carregue dados primeiro!', 'warning');
                return;
            }

            // Implementação simplificada do algoritmo CPM
            const taskMap = {};
            tasks.forEach(task => {
                taskMap[task.id] = {
                    ...task,
                    earliestStart: 0,
                    earliestFinish: 0,
                    latestStart: 0,
                    latestFinish: 0,
                    slack: 0
                };
            });

            // Forward pass - calcular earliest start/finish
            tasks.forEach(task => {
                const currentTask = taskMap[task.id];
                
                if (task.dependencies.length === 0) {
                    currentTask.earliestStart = 0;
                } else {
                    currentTask.earliestStart = Math.max(
                        ...task.dependencies.map(depId => taskMap[depId].earliestFinish)
                    );
                }
                
                currentTask.earliestFinish = currentTask.earliestStart + task.duration;
            });

            // Encontrar duração total do projeto
            const projectDuration = Math.max(...Object.values(taskMap).map(t => t.earliestFinish));

            // Backward pass - calcular latest start/finish
            const reversedTasks = [...tasks].reverse();
            reversedTasks.forEach(task => {
                const currentTask = taskMap[task.id];
                
                // Encontrar sucessores
                const successors = tasks.filter(t => t.dependencies.includes(task.id));
                
                if (successors.length === 0) {
                    currentTask.latestFinish = projectDuration;
                } else {
                    currentTask.latestFinish = Math.min(
                        ...successors.map(succ => taskMap[succ.id].latestStart)
                    );
                }
                
                currentTask.latestStart = currentTask.latestFinish - task.duration;
                currentTask.slack = currentTask.latestStart - currentTask.earliestStart;
            });

            // Identificar tarefas críticas
            criticalPath = [];
            Object.values(taskMap).forEach(task => {
                if (task.slack === 0) {
                    criticalPath.push(task.id);
                    // Atualizar status da tarefa original
                    const originalTask = tasks.find(t => t.id === task.id);
                    if (originalTask) {
                        originalTask.status = 'critical';
                    }
                }
            });

            // Re-renderizar com tarefas críticas destacadas
            renderizarGantt();

            // Mostrar resultado
            const criticalCount = criticalPath.length;
            const totalTasks = tasks.length;
            
            showAlert(
                `🔥 CAMINHO CRÍTICO CALCULADO!\n\n` +
                `📊 Tarefas críticas: ${criticalCount} de ${totalTasks}\n` +
                `⏱️ Duração do projeto: ${projectDuration} dias\n\n` +
                `As tarefas críticas estão destacadas em vermelho com animação.`,
                'success'
            );

            console.log('🎯 Tarefas críticas:', criticalPath);
            console.log('⏱️ Duração total:', projectDuration, 'dias');
        }

        // ===================================================================
        // FUNÇÕES AUXILIARES
        // ===================================================================

        function showTooltip(event, task) {
            const tooltip = document.getElementById('tooltip');

            // Ícones por tipo
            const typeIcons = { 'PA': '🏭', 'SP': '⚙️', 'MP': '📦' };
            const typeIcon = typeIcons[task.tipo] || '📋';

            // Ícones de nível
            const levelIcons = ['🔷', '🔸', '🔹', '🔺', '🔻'];
            const levelIcon = levelIcons[task.nivel] || '🔸';

            let tooltipContent = `
                <div style="border-bottom: 1px solid #ddd; padding-bottom: 8px; margin-bottom: 8px;">
                    <strong>${levelIcon} ${typeIcon} ${task.name}</strong>
                    ${task.isVirtual ? ' <span style="color: #6c757d;">👻 Virtual</span>' : ''}
                </div>
                ${task.description ? `<div style="color: #666; margin-bottom: 8px;">${task.description}</div>` : ''}

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                    <div><strong>Duração:</strong> ${task.duration} dias</div>
                    <div><strong>Progresso:</strong> ${task.progress}%</div>
                    <div><strong>Início:</strong> ${task.startDate.toLocaleDateString('pt-BR')}</div>
                    <div><strong>Fim:</strong> ${task.endDate.toLocaleDateString('pt-BR')}</div>
                </div>
            `;

            // Informações específicas das OPs
            if (task.quantidade) {
                tooltipContent += `<div style="margin-top: 8px;"><strong>Quantidade:</strong> ${task.quantidade.toFixed(3)}</div>`;
            }

            if (task.dataEntrega) {
                const diasRestantes = Math.ceil((task.dataEntrega - new Date()) / (24 * 60 * 60 * 1000));
                tooltipContent += `<div><strong>Entrega:</strong> ${task.dataEntrega.toLocaleDateString('pt-BR')} (${diasRestantes} dias)</div>`;
            }

            // Informações da estrutura
            tooltipContent += `
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd;">
                    <div><strong>Nível da Estrutura:</strong> ${task.nivel}</div>
                    <div><strong>Tipo:</strong> ${task.tipo} ${typeIcon}</div>
                    ${task.isParent ? '<div style="color: #2196F3;"><strong>📋 Tarefa Principal</strong></div>' : ''}
                    ${task.isChild ? '<div style="color: #9c27b0;"><strong>🔗 Componente</strong></div>' : ''}
                </div>
            `;

            if (task.prioridade && task.prioridade !== 'normal') {
                tooltipContent += `<div style="margin-top: 4px;"><strong>Prioridade:</strong> <span style="color: #ff9800;">${task.prioridade.toUpperCase()}</span></div>`;
            }

            if (task.status === 'critical') {
                tooltipContent += '<div style="margin-top: 8px; color: #e74c3c; font-weight: bold;">🔥 TAREFA CRÍTICA</div>';
            }

            if (task.isVirtual) {
                tooltipContent += '<div style="margin-top: 8px; color: #6c757d; font-style: italic;">⚠️ Esta tarefa foi criada automaticamente baseada na estrutura do produto, mas não possui OP correspondente no sistema.</div>';
            }

            tooltip.innerHTML = tooltipContent;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.classList.add('show');
        }

        function hideTooltip() {
            document.getElementById('tooltip').classList.remove('show');
        }

        function atualizarEstatisticas() {
            const totalTasks = tasks.length;
            const criticalTasks = tasks.filter(t => t.status === 'critical').length;
            const completedTasks = tasks.filter(t => t.status === 'completed').length;
            const projectDuration = Math.ceil((projectEndDate - projectStartDate) / (24 * 60 * 60 * 1000)) + 1;

            document.getElementById('totalTasks').textContent = totalTasks;
            document.getElementById('criticalTasks').textContent = criticalTasks;
            document.getElementById('projectDuration').textContent = projectDuration;
            document.getElementById('completedTasks').textContent = completedTasks;
            
            document.getElementById('statsContainer').style.display = 'block';
        }

        function showAlert(message, type) {
            // Remover alertas existentes
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => {
                if (alert.textContent.includes('Funcionando Offline')) return;
                alert.remove();
            });

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.controls'));
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        window.expandirTudo = function() {
            if (tasks.length === 0) {
                showAlert('⚠️ Carregue as OPs primeiro!', 'warning');
                return;
            }
            showAlert('ℹ️ Todas as OPs já estão visíveis nesta visualização.', 'info');
        }

        window.voltarMenu = function() {
            if (confirm('Deseja voltar ao menu principal?')) {
                window.location.href = 'ordens_producao.html';
            }
        }

        // ===================================================================
        // INICIALIZAÇÃO
        // ===================================================================

        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 Gantt Chart Integrado carregado com sucesso!');
            showAlert('🏭 Sistema pronto! Escolha: "🏭 OPs Reais" para ordens de produção ou "📋 BOM/Estruturas" para visualizar estruturas de produtos.', 'info');
        });

        // Tornar funções globais para compatibilidade
        window.calcularCaminhoCritico = calcularCaminhoCritico;
        window.showAlert = showAlert;
        window.renderizarGantt = renderizarGantt;
        window.calcularDatasFinais = calcularDatasFinais;
        window.renderizarListaTarefas = renderizarListaTarefas;
        window.renderizarTimeline = renderizarTimeline;
        window.atualizarEstatisticas = atualizarEstatisticas;
        window.showTooltip = showTooltip;
        window.hideTooltip = hideTooltip;

        // Funções de controle
        window.toggleFluxo = toggleFluxo;
        window.zoomIn = zoomIn;
        window.zoomOut = zoomOut;
        window.resetZoom = resetZoom;
        window.toggleMostrarMP = toggleMostrarMP;
        window.carregarBOM = carregarBOM;
        window.selecionarProdutoBOM = selecionarProdutoBOM;
        window.gerarBOMSelecionado = gerarBOMSelecionado;

    </script>
</body>
</html>
