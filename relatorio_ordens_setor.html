<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de Ordens por Setor</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --light-bg: #f8f9fa;
      --white: #ffffff;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
      --border-radius: 8px;
      --transition: all 0.3s ease;
    }

    @page {
      size: A4 portrait;
      margin: 15mm;
    }

    @media print {
      body {
        margin: 0;
        padding: 0;
      }
      .no-print {
        display: none;
      }
      .order-page {
        page-break-after: always;
        padding: 0 !important;
        margin: 0 !important;
        box-shadow: none !important;
      }
      .order-page:last-child {
        page-break-after: avoid;
      }
      .order-warning {
        display: none;
      }
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: var(--text-color);
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: var(--white);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      overflow: hidden;
      animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .header {
      background: linear-gradient(135deg, var(--header-bg), var(--primary-color));
      color: var(--white);
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.05) 10px,
        rgba(255,255,255,0.05) 20px
      );
      animation: float 20s linear infinite;
    }

    @keyframes float {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .header h1 {
      font-size: 2rem;
      font-weight: 600;
      margin: 0;
      position: relative;
      z-index: 1;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .header .btn-secondary {
      position: relative;
      z-index: 1;
    }

    .filters {
      margin: 30px;
      padding: 25px;
      background: linear-gradient(135deg, var(--light-bg), #e9ecef);
      border-radius: var(--border-radius);
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid rgba(255,255,255,0.8);
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--text-color);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .filter-group select,
    .filter-group input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      font-size: 1rem;
      transition: var(--transition);
      background: var(--white);
    }

    .filter-group select:focus,
    .filter-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-2px);
    }

    .filter-group input::placeholder {
      color: var(--text-secondary);
      font-style: italic;
    }

    .action-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 1px solid var(--border-color);
    }

    button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      min-width: 140px;
    }

    button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.3s, height 0.3s;
    }

    button:hover::before {
      width: 300px;
      height: 300px;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-hover);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--success-color), #28a745);
      color: var(--white);
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, var(--success-hover), #218838);
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d, #5a6268);
      color: var(--white);
    }

    .btn-secondary:hover {
      background: linear-gradient(135deg, #5a6268, #495057);
    }

    .order-page {
      background: var(--white);
      margin: 30px;
      padding: 25px;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
      transition: var(--transition);
    }

    .order-page:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-2px);
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid var(--primary-color);
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 20px;
      border-radius: var(--border-radius);
      margin: -25px -25px 20px -25px;
    }

    .logo {
      width: 120px;
      height: auto;
      filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
    }

    .order-title {
      text-align: center;
      flex-grow: 1;
      margin: 0 20px;
    }

    .order-title h1 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--primary-color);
      text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .order-title h2 {
      margin: 8px 0;
      font-size: 1.4rem;
      font-weight: 600;
      color: var(--text-color);
    }

    .order-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: var(--border-radius);
      border: 1px solid var(--border-color);
    }

    .info-item {
      background: var(--white);
      border: 1px solid var(--border-color);
      padding: 12px;
      border-radius: 6px;
      transition: var(--transition);
    }

    .info-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .info-item strong {
      display: block;
      font-size: 0.75rem;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
      font-weight: 600;
    }

    .info-item span {
      display: block;
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-color);
    }

    .section {
      margin-bottom: 25px;
      background: var(--white);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
      border: 1px solid var(--border-color);
    }

    .section-title {
      background: linear-gradient(135deg, var(--primary-color), #0a4d8c);
      color: var(--white);
      padding: 15px 20px;
      font-weight: 600;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin: 0;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 0.9rem;
      background: var(--white);
    }

    th, td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }

    th {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      font-weight: 600;
      color: var(--text-color);
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
      border-bottom: 2px solid var(--primary-color);
    }

    tbody tr {
      transition: var(--transition);
    }

    tbody tr:hover {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      transform: scale(1.01);
    }

    tbody tr:nth-child(even) {
      background: rgba(8, 84, 160, 0.02);
    }

    .signatures {
      margin-top: 30px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: var(--border-radius);
      border: 1px solid var(--border-color);
    }

    .signature {
      text-align: center;
      padding: 15px;
      background: var(--white);
      border-radius: 6px;
      border: 1px solid var(--border-color);
    }

    .signature-line {
      width: 100%;
      border-top: 2px solid var(--primary-color);
      margin-top: 30px;
      padding-top: 8px;
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: var(--transition);
    }

    .status-badge:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .status-pendente {
      background: linear-gradient(135deg, #ffc107, #e0a800);
      color: #000;
    }

    .status-em-producao {
      background: linear-gradient(135deg, #17a2b8, #138496);
      color: #fff;
    }

    .status-concluida {
      background: linear-gradient(135deg, #28a745, #1e7e34);
      color: #fff;
    }

    .status-cancelada {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: #fff;
    }

    .order-warning {
      margin: 30px;
      padding: 20px;
      background: linear-gradient(135deg, #fff3e0, #ffe0b3);
      border: 2px solid var(--warning-color);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
    }

    .order-warning strong {
      color: var(--warning-color);
      font-size: 1.1rem;
      display: block;
      margin-bottom: 8px;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
      }

      .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header h1 {
        font-size: 1.5rem;
      }

      .filter-row {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        flex-direction: column;
      }

      .order-info {
        grid-template-columns: 1fr;
      }

      .signatures {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📊 Relatório de Ordens por Setor</h1>
      <button class="btn-secondary" onclick="window.location.href='index.html'">
        ← Voltar
      </button>
    </div>
    <div class="no-print">
      <div class="filters">
        <div class="filter-row">
          <div class="filter-group">
            <label>🔍 Buscar por Número/Produto:</label>
            <input type="text" id="searchInput" placeholder="Digite o número da OP ou código do produto..." oninput="generateReport()">
          </div>
          <div class="filter-group">
            <label>📋 Status:</label>
            <select id="statusFilter" onchange="generateReport()">
              <option value="">Todos os status</option>
              <option value="Pendente">⏳ Pendente</option>
              <option value="Em Produção">🔄 Em Produção</option>
              <option value="Concluída">✅ Concluída</option>
              <option value="Cancelada">❌ Cancelada</option>
            </select>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-group">
            <label>📅 Período Inicial:</label>
            <input type="date" id="startDate" onchange="generateReport()">
          </div>
          <div class="filter-group">
            <label>📅 Período Final:</label>
            <input type="date" id="endDate" onchange="generateReport()">
          </div>
          <div class="filter-group">
            <label>🏭 Setor:</label>
            <select id="setorFilter" onchange="generateReport()">
              <option value="">Todos os setores</option>
            </select>
          </div>
        </div>
      </div>
      <div class="action-buttons">
        <button class="btn-primary" onclick="generateReport()">
          📊 Gerar Relatório
        </button>
        <button class="btn-secondary" onclick="window.print()">
          🖨️ Imprimir
        </button>
      </div>
    </div>

    <div id="reportContent"></div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs,
      query,
      where,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let recursos = [];
    let operacoes = [];
    let ordensProducao = [];
    let estoques = [];

    window.onload = async function() {
      await loadData();
      updateSetorFilter();
    };

    async function loadData() {
      try {
        const [produtosSnap, estruturasSnap, recursosSnap, operacoesSnap, ordensSnap, estoquesSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "recursos")),
          getDocs(collection(db, "operacoes")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "estoques"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function updateSetorFilter() {
      const select = document.getElementById('setorFilter');
      const setores = new Set();

      recursos.forEach(recurso => {
        if (recurso.setor) {
          setores.add(recurso.setor);
        }
      });

      Array.from(setores).sort().forEach(setor => {
        select.innerHTML += `<option value="${setor}">${setor}</option>`;
      });
    }

    function createOrderPage(order) {
      const produto = produtos.find(p => p.id === order.produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === order.produtoId);

      const orderPage = document.createElement('div');
      orderPage.className = 'order-page';

      orderPage.innerHTML = `
        <div class="order-header">
          <img src="https://www.naliteck.com.br/img/logo.png" alt="Logo" class="logo">
          <div class="order-title">
            <h1>ORDEM DE PRODUÇÃO</h1>
            <h2>${order.numero}</h2>
          </div>
          <div style="text-align: right; font-size: 10px;">
            <strong>Data: </strong>${new Date().toLocaleDateString()}<br>
            <strong>Hora: </strong>${new Date().toLocaleTimeString()}
          </div>
        </div>

        <div class="order-info">
          <div class="info-item">
            <strong>Produto:</strong>
            <span>${produto.codigo} - ${produto.descricao}</span>
          </div>
          <div class="info-item">
            <strong>Tipo:</strong>
            <span>${produto.tipo}</span>
          </div>
          <div class="info-item">
            <strong>Quantidade:</strong>
            <span>${order.quantidade} ${produto.unidade}</span>
          </div>
          <div class="info-item">
            <strong>Status:</strong>
            <span class="status-badge status-${order.status.toLowerCase()}">${order.status}</span>
          </div>
          <div class="info-item">
            <strong>Data de Criação:</strong>
            <span>${new Date(order.dataCriacao.seconds * 1000).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <strong>Data de Entrega:</strong>
            <span>${new Date(order.dataEntrega.seconds * 1000).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <strong>Prioridade:</strong>
            <span>${order.prioridade || 'Normal'}</span>
          </div>
        </div>

        ${order.materiaisNecessarios ? `
          <div class="section">
            <div class="section-title">LISTA DE MATERIAIS</div>
            <table>
              <thead>
                <tr>
                  <th>Código</th>
                  <th>Descrição</th>
                  <th>Tipo</th>
                  <th>Quantidade</th>
                  <th>Unidade</th>
                </tr>
              </thead>
              <tbody>
                ${order.materiaisNecessarios.map(material => {
                  const materialProduto = produtos.find(p => p.id === material.produtoId);
                  return `
                    <tr>
                      <td>${materialProduto.codigo}</td>
                      <td>${materialProduto.descricao}</td>
                      <td>${materialProduto.tipo}</td>
                      <td>${material.quantidade}</td>
                      <td>${materialProduto.unidade}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        ` : ''}

        ${estrutura && estrutura.operacoes ? `
          <div class="section">
            <div class="section-title">ROTEIRO DE PRODUÇÃO</div>
            <table>
              <thead>
                <tr>
                  <th>Seq.</th>
                  <th>Operação</th>
                  <th>Recurso</th>
                  <th>Tempo (min)</th>
                  <th>Descrição</th>
                  <th>Status</th>
                  <th>Data Início</th>
                  <th>Data Fim</th>
                </tr>
              </thead>
              <tbody>
                ${estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(op => {
                  const operacao = operacoes.find(o => o.id === op.operacaoId);
                  const recurso = recursos.find(r => r.id === op.recursoId);
                  return `
                    <tr>
                      <td>${op.sequencia}</td>
                      <td>${operacao.operacao}</td>
                      <td>${recurso.codigo} - ${recurso.maquina}</td>
                      <td>${op.tempo}</td>
                      <td>${op.descricao || ''}</td>
                      <td></td>
                      <td></td>
                      <td></td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        ` : ''}

        <div class="signatures">
          <div class="signature">
            <div class="signature-line">Produção</div>
          </div>
          <div class="signature">
            <div class="signature-line">Qualidade</div>
          </div>
          <div class="signature">
            <div class="signature-line">Supervisor</div>
          </div>
        </div>
      `;

      return orderPage;
    }

    window.generateReport = async function() {
      const searchInput = document.getElementById('searchInput').value.toLowerCase();
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      const status = document.getElementById('statusFilter').value;
      const setorFilter = document.getElementById('setorFilter').value;

      let filteredOrders = [...ordensProducao];

      if (searchInput) {
        filteredOrders = filteredOrders.filter(op => {
          const produto = produtos.find(p => p.id === op.produtoId);
          return op.numero.toLowerCase().includes(searchInput) || 
                 produto.codigo.toLowerCase().includes(searchInput) ||
                 produto.descricao.toLowerCase().includes(searchInput);
        });
      }

      if (startDate) {
        filteredOrders = filteredOrders.filter(op => 
          op.dataEntrega.seconds >= new Date(startDate).getTime() / 1000
        );
      }

      if (endDate) {
        filteredOrders = filteredOrders.filter(op => 
          op.dataEntrega.seconds <= new Date(endDate + 'T23:59:59').getTime() / 1000
        );
      }

      if (status) {
        filteredOrders = filteredOrders.filter(op => op.status === status);
      }

      const reportContent = document.getElementById('reportContent');
      reportContent.innerHTML = '';

      filteredOrders.forEach(ordem => {
        const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
        if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
          const operacoes = [...estrutura.operacoes].sort((a, b) => a.sequencia - b.sequencia);
          const primeiraOperacao = operacoes[0];
          const recurso = recursos.find(r => r.id === primeiraOperacao.recursoId);

          if (recurso && (!setorFilter || recurso.setor === setorFilter)) {
            if (verificarSaldoMateriais(ordem)) {
              reportContent.appendChild(createOrderPage(ordem));
            } else {
              const warningDiv = document.createElement('div');
              warningDiv.className = 'order-warning';
              warningDiv.innerHTML = `
                <div style="padding: 10px; margin: 10px 0; background-color: #fff3e5; border: 1px solid #e9730c; border-radius: 4px;">
                  <strong>OP ${ordem.numero} - Bloqueada para Impressão</strong><br>
                  Saldo insuficiente de matéria prima
                </div>
              `;
              reportContent.appendChild(warningDiv);
            }
          }
        }
      });
    };

    // Função para verificar saldo dos materiais tipo MP
    function verificarSaldoMateriais(ordem) {
      if (!ordem.materiaisNecessarios) return true;

      // Verifica apenas materiais do tipo MP
      for (const material of ordem.materiaisNecessarios) {
        const produto = produtos.find(p => p.id === material.produtoId);

        // Se não for MP, ignora a verificação
        if (!produto || produto.tipo !== 'MP') continue;

        const estoque = estoques.find(e => e.produtoId === material.produtoId);
        const saldoDisponivel = estoque ? estoque.saldo : 0;
        const quantidadeNecessaria = material.quantidade;

        if (saldoDisponivel < quantidadeNecessaria) {
          return false;
        }
      }

      return true;
    }

    // Função para verificar se pode imprimir a OP
    function verificarBloqueioImpressao(ordem) {
      const temSaldoSuficiente = verificarSaldoMateriais(ordem);

      if (!temSaldoSuficiente) {
        alert('Não é possível imprimir esta OP pois não há saldo suficiente de matéria prima.');
        return false;
      }

      return true;
    }

    // Modificar a função de impressão para usar a verificação
    async function imprimirOP(ordem) {
      if (!verificarBloqueioImpressao(ordem)) {
        return;
      }

      // ... resto do código de impressão existente ...
    }
  </script>
</body>
</html>