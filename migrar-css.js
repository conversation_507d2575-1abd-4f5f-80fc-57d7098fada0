/**
 * Script para migração automática dos arquivos HTML para o novo sistema CSS padronizado
 * Baseado no design da tela de cotações
 */

const fs = require('fs');
const path = require('path');

// Mapeamento de classes antigas para novas
const classMappings = {
    // Containers
    'container': 'container',
    
    // Headers
    'header': 'header',
    'page-header': 'header',
    'header-actions': 'header-actions',
    
    // Botõ<PERSON>
    'btn-primary': 'btn btn-primary',
    'btn-success': 'btn btn-success',
    'btn-warning': 'btn btn-warning',
    'btn-danger': 'btn btn-danger',
    'btn-info': 'btn btn-info',
    'btn-secondary': 'btn btn-secondary',
    
    // Formulários
    'form-group': 'form-group',
    'form-control': 'form-control',
    'form-row': 'form-row',
    'form-col': 'form-col',
    
    // Tabelas
    'table': 'table',
    'table-container': 'table-container',
    
    // Modais
    'modal': 'modal',
    'modal-content': 'modal-content',
    'modal-header': 'modal-header',
    'modal-body': 'modal-body',
    
    // Status
    'status-pendente': 'status aberta',
    'status-aprovada': 'status aprovada',
    'status-rejeitada': 'status fechada',
    
    // Alertas
    'alert-info': 'alert alert-info',
    'alert-success': 'alert alert-success',
    'alert-warning': 'alert alert-warning',
    'alert-danger': 'alert alert-danger'
};

// CSS inline que deve ser removido (padrões comuns)
const inlineStylesToRemove = [
    'margin: 0',
    'padding: 0',
    'box-sizing: border-box',
    'font-family: \'Segoe UI\'',
    'background-color: #f7f7f7',
    'color: #333',
    'display: flex',
    'justify-content: space-between',
    'align-items: center'
];

// Variáveis CSS antigas para novas
const cssVariableMappings = {
    '--primary-color: #0854a0': '--primary-color: #3498db',
    '--success-color: #107e3e': '--success-color: #27ae60',
    '--danger-color: #bb0000': '--danger-color: #e74c3c',
    '--warning-color: #e9730c': '--warning-color: #f39c12',
    '--header-bg: #354a5f': '--bg-header: linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
};

/**
 * Migra um arquivo HTML para o novo sistema CSS
 */
function migrateHTMLFile(filePath) {
    console.log(`Migrando arquivo: ${filePath}`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        // 1. Adicionar referência ao novo CSS se não existir
        if (!content.includes('sistema-padronizado.css')) {
            const headCloseTag = '</head>';
            const cssLink = '    <link rel="stylesheet" href="styles/sistema-padronizado.css">\n';
            
            if (content.includes(headCloseTag)) {
                content = content.replace(headCloseTag, cssLink + headCloseTag);
                modified = true;
                console.log('  ✓ Adicionada referência ao CSS padronizado');
            }
        }
        
        // 2. Substituir classes CSS
        Object.entries(classMappings).forEach(([oldClass, newClass]) => {
            const regex = new RegExp(`class=["']([^"']*\\s)?${oldClass}(\\s[^"']*)?["']`, 'g');
            const newContent = content.replace(regex, (match, prefix = '', suffix = '') => {
                return `class="${prefix}${newClass}${suffix}"`.replace(/\s+/g, ' ').trim();
            });
            
            if (newContent !== content) {
                content = newContent;
                modified = true;
                console.log(`  ✓ Substituída classe: ${oldClass} → ${newClass}`);
            }
        });
        
        // 3. Remover estilos inline comuns
        inlineStylesToRemove.forEach(style => {
            const regex = new RegExp(`style=["'][^"']*${style.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^"']*["']`, 'g');
            const newContent = content.replace(regex, (match) => {
                // Remove apenas o estilo específico, mantém outros estilos
                return match.replace(style + ';', '').replace(style, '');
            });
            
            if (newContent !== content) {
                content = newContent;
                modified = true;
                console.log(`  ✓ Removido estilo inline: ${style}`);
            }
        });
        
        // 4. Atualizar variáveis CSS
        Object.entries(cssVariableMappings).forEach(([oldVar, newVar]) => {
            if (content.includes(oldVar)) {
                content = content.replace(new RegExp(oldVar, 'g'), newVar);
                modified = true;
                console.log(`  ✓ Atualizada variável CSS: ${oldVar} → ${newVar}`);
            }
        });
        
        // 5. Adicionar classes utilitárias onde apropriado
        // Exemplo: substituir style="text-align: center" por class="text-center"
        const utilityMappings = {
            'style="text-align: center"': 'class="text-center"',
            'style="text-align: right"': 'class="text-right"',
            'style="display: none"': 'class="d-none"',
            'style="display: flex"': 'class="d-flex"'
        };
        
        Object.entries(utilityMappings).forEach(([oldStyle, newClass]) => {
            if (content.includes(oldStyle)) {
                content = content.replace(new RegExp(oldStyle, 'g'), newClass);
                modified = true;
                console.log(`  ✓ Convertido para classe utilitária: ${oldStyle} → ${newClass}`);
            }
        });
        
        // 6. Salvar arquivo se foi modificado
        if (modified) {
            // Criar backup
            const backupPath = filePath + '.backup';
            fs.copyFileSync(filePath, backupPath);
            console.log(`  ✓ Backup criado: ${backupPath}`);
            
            // Salvar arquivo modificado
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`  ✓ Arquivo migrado com sucesso!`);
            
            return true;
        } else {
            console.log('  - Nenhuma modificação necessária');
            return false;
        }
        
    } catch (error) {
        console.error(`  ✗ Erro ao migrar arquivo: ${error.message}`);
        return false;
    }
}

/**
 * Migra todos os arquivos HTML do diretório
 */
function migrateAllFiles() {
    console.log('=== MIGRAÇÃO PARA SISTEMA CSS PADRONIZADO ===\n');
    
    const currentDir = process.cwd();
    const files = fs.readdirSync(currentDir);
    
    const htmlFiles = files.filter(file => 
        file.endsWith('.html') && 
        !file.includes('padronizado') && 
        !file.includes('backup')
    );
    
    console.log(`Encontrados ${htmlFiles.length} arquivos HTML para migração:\n`);
    
    let migratedCount = 0;
    
    htmlFiles.forEach(file => {
        const filePath = path.join(currentDir, file);
        if (migrateHTMLFile(filePath)) {
            migratedCount++;
        }
        console.log(''); // Linha em branco
    });
    
    console.log('=== RESUMO DA MIGRAÇÃO ===');
    console.log(`Total de arquivos: ${htmlFiles.length}`);
    console.log(`Arquivos migrados: ${migratedCount}`);
    console.log(`Arquivos sem alteração: ${htmlFiles.length - migratedCount}`);
    
    if (migratedCount > 0) {
        console.log('\n✓ Migração concluída com sucesso!');
        console.log('✓ Backups criados para todos os arquivos modificados');
        console.log('✓ Agora você pode testar as páginas e ajustar conforme necessário');
    } else {
        console.log('\n- Nenhum arquivo precisou ser migrado');
    }
}

/**
 * Função para reverter migração usando backups
 */
function revertMigration() {
    console.log('=== REVERTENDO MIGRAÇÃO ===\n');
    
    const currentDir = process.cwd();
    const files = fs.readdirSync(currentDir);
    
    const backupFiles = files.filter(file => file.endsWith('.backup'));
    
    if (backupFiles.length === 0) {
        console.log('Nenhum arquivo de backup encontrado.');
        return;
    }
    
    backupFiles.forEach(backupFile => {
        const originalFile = backupFile.replace('.backup', '');
        const backupPath = path.join(currentDir, backupFile);
        const originalPath = path.join(currentDir, originalFile);
        
        try {
            fs.copyFileSync(backupPath, originalPath);
            fs.unlinkSync(backupPath);
            console.log(`✓ Revertido: ${originalFile}`);
        } catch (error) {
            console.error(`✗ Erro ao reverter ${originalFile}: ${error.message}`);
        }
    });
    
    console.log('\n✓ Migração revertida com sucesso!');
}

// Executar migração se chamado diretamente
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--revert')) {
        revertMigration();
    } else if (args.includes('--help')) {
        console.log('Uso:');
        console.log('  node migrar-css.js          # Migrar todos os arquivos HTML');
        console.log('  node migrar-css.js --revert # Reverter migração usando backups');
        console.log('  node migrar-css.js --help   # Mostrar esta ajuda');
    } else {
        migrateAllFiles();
    }
}

module.exports = {
    migrateHTMLFile,
    migrateAllFiles,
    revertMigration
};
