// Sistema de alertas de estoque
import { db } from '../firebase-config.js';
import { collection, query, where, getDocs, addDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class AlertasEstoque {
    constructor() {
        this.estoqueCollection = collection(db, 'estoques');
        this.alertasCollection = collection(db, 'alertasEstoque');
        this.produtosCollection = collection(db, 'produtos');
    }

    // Verifica produtos abaixo do estoque mínimo
    async verificarEstoqueMinimo() {
        try {
            const produtos = await getDocs(this.produtosCollection);
            const alertas = [];

            for (const produtoDoc of produtos.docs) {
                const produto = { id: produtoDoc.id, ...produtoDoc.data() };
                
                if (!produto.estoqueMinimo) continue;

                // Busca saldo atual em todos os armazéns
                const estoquesQuery = query(this.estoqueCollection, 
                    where('produtoId', '==', produto.id)
                );
                const estoques = await getDocs(estoquesQuery);
                
                const saldoTotal = estoques.docs.reduce((total, doc) => 
                    total + (doc.data().saldo || 0), 0);

                if (saldoTotal <= produto.estoqueMinimo) {
                    // Cria alerta
                    const alerta = {
                        produtoId: produto.id,
                        produtoCodigo: produto.codigo,
                        produtoDescricao: produto.descricao,
                        estoqueMinimo: produto.estoqueMinimo,
                        saldoAtual: saldoTotal,
                        dataAlerta: Timestamp.now(),
                        status: 'PENDENTE'
                    };

                    await addDoc(this.alertasCollection, alerta);
                    alertas.push(alerta);
                }
            }

            return alertas;
        } catch (error) {
            console.error('Erro ao verificar estoque mínimo:', error);
            throw error;
        }
    }

    // Busca alertas pendentes
    async buscarAlertasPendentes() {
        try {
            const q = query(this.alertasCollection, 
                where('status', '==', 'PENDENTE')
            );
            const querySnapshot = await getDocs(q);
            
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Erro ao buscar alertas:', error);
            throw error;
        }
    }

    // Gera sugestão de compra baseada no histórico
    async gerarSugestaoCompra(produtoId) {
        try {
            // Busca histórico de consumo dos últimos 3 meses
            const tresMesesAtras = new Date();
            tresMesesAtras.setMonth(tresMesesAtras.getMonth() - 3);

            const movimentacoesCollection = collection(db, 'movimentacoesEstoque');
            const q = query(movimentacoesCollection,
                where('produtoId', '==', produtoId),
                where('tipo', '==', 'SAIDA'),
                where('dataMovimentacao', '>=', tresMesesAtras)
            );

            const movimentacoes = await getDocs(q);
            
            // Calcula média de consumo mensal
            const consumoTotal = movimentacoes.docs.reduce((total, doc) => 
                total + doc.data().quantidade, 0);
            const consumoMedioPorMes = consumoTotal / 3;

            // Busca dados do produto
            const produtoDoc = await getDocs(query(this.produtosCollection, 
                where('id', '==', produtoId)));
            const produto = produtoDoc.docs[0].data();

            // Calcula quantidade sugerida (3 meses de estoque)
            const quantidadeSugerida = Math.ceil(consumoMedioPorMes * 3);

            return {
                produtoId,
                produtoCodigo: produto.codigo,
                produtoDescricao: produto.descricao,
                consumoMedioPorMes,
                quantidadeSugerida,
                baseadoEm: {
                    periodo: '3 meses',
                    consumoTotal,
                    numeroMovimentacoes: movimentacoes.size
                }
            };
        } catch (error) {
            console.error('Erro ao gerar sugestão de compra:', error);
            throw error;
        }
    }
}
