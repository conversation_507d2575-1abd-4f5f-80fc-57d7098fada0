<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Análise Específica de Solicitações</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .analysis-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .analysis-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-sample {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .btn-analyze {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .field-analysis {
            background: #e7f3ff;
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #007bff; font-weight: bold; }
    </style>
</head>
<body>
    <div class="analysis-container">
        <h1>🔍 Análise Específica de Solicitações</h1>
        
        <div class="analysis-card">
            <h3>🎯 Objetivo</h3>
            <p>Vamos analisar <strong>exatamente</strong> como suas solicitações estão estruturadas para adaptar o dashboard.</p>
            <button onclick="analisarSolicitacoes()" class="btn-analyze">🔍 Analisar Solicitações Detalhadamente</button>
        </div>

        <div id="logArea" class="log-area"></div>
        <div id="analysisResults"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logArea.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema carregado. Vamos analisar as solicitações!', 'info');
        };

        window.analisarSolicitacoes = async function() {
            log('🔄 Iniciando análise detalhada das solicitações...', 'info');
            
            try {
                // Buscar solicitações SEM filtro de data (pode ter nome diferente)
                log('Tentando buscar solicitações sem filtro de data...', 'info');
                const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));

                const todasSolicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const solicitacoes = todasSolicitacoes.slice(0, 5); // Pegar apenas 5 para análise

                log(`✅ Total de solicitações na coleção: ${todasSolicitacoes.length}`, 'success');
                log(`✅ Analisando ${solicitacoes.length} solicitações`, 'success');

                if (solicitacoes.length === 0) {
                    log('❌ Nenhuma solicitação encontrada', 'error');
                    return;
                }

                let html = `
                    <div class="analysis-card">
                        <h3>📊 Análise Detalhada das Solicitações</h3>
                        <p><strong>Total analisado:</strong> ${solicitacoes.length} solicitações</p>
                `;

                // Analisar cada solicitação
                solicitacoes.forEach((sol, index) => {
                    log(`Analisando solicitação ${index + 1}: ${sol.id}`, 'info');
                    
                    html += `
                        <div class="field-analysis">
                            <h4>📄 Solicitação ${index + 1} (ID: ${sol.id.substring(0, 8)}...)</h4>
                            <div class="data-sample">
                                <strong>TODOS OS CAMPOS ENCONTRADOS:</strong><br>
                    `;

                    // Mostrar TODOS os campos
                    Object.keys(sol).forEach(key => {
                        const value = sol[key];
                        let tipo = typeof value;
                        let preview = '';

                        if (Array.isArray(value)) {
                            tipo = `Array[${value.length}]`;
                            preview = ` → ${value.length} itens`;
                            if (value.length > 0) {
                                preview += ` (primeiro: ${JSON.stringify(value[0]).substring(0, 50)}...)`;
                            }
                        } else if (value && typeof value === 'object' && value.seconds) {
                            tipo = 'Timestamp';
                            preview = ` → ${new Date(value.seconds * 1000).toLocaleString()}`;
                        } else if (value && typeof value === 'object') {
                            tipo = 'Object';
                            preview = ` → {${Object.keys(value).join(', ')}}`;
                        } else {
                            preview = ` → ${String(value).substring(0, 50)}`;
                        }

                        html += `&nbsp;&nbsp;• <strong>${key}:</strong> ${tipo}${preview}<br>`;
                    });

                    html += `
                            </div>
                        </div>
                    `;

                    // Verificar campos que podem conter produtos/materiais
                    const camposPossiveis = ['itens', 'items', 'produtos', 'materiais', 'detalhes', 'requisicoes', 'solicitacoes'];
                    let campoEncontrado = null;

                    camposPossiveis.forEach(campo => {
                        if (sol[campo]) {
                            campoEncontrado = campo;
                            log(`✅ Campo '${campo}' encontrado na solicitação ${index + 1}`, 'success');
                        }
                    });

                    if (!campoEncontrado) {
                        log(`⚠️ Nenhum campo de itens encontrado na solicitação ${index + 1}`, 'warning');
                        
                        // Verificar se há campos que são arrays
                        Object.keys(sol).forEach(key => {
                            if (Array.isArray(sol[key]) && sol[key].length > 0) {
                                log(`🔍 Campo array encontrado: '${key}' com ${sol[key].length} itens`, 'info');
                                campoEncontrado = key;
                            }
                        });
                    }

                    if (campoEncontrado) {
                        html += `
                            <div style="background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0;">
                                <h5>✅ Campo de itens encontrado: "${campoEncontrado}"</h5>
                                <div class="data-sample">
                                    <strong>Estrutura dos itens:</strong><br>
                        `;

                        const itens = sol[campoEncontrado];
                        if (Array.isArray(itens) && itens.length > 0) {
                            const primeiroItem = itens[0];
                            Object.keys(primeiroItem).forEach(key => {
                                const value = primeiroItem[key];
                                let tipo = typeof value;
                                if (value && typeof value === 'object' && value.seconds) tipo = 'Timestamp';
                                if (Array.isArray(value)) tipo = `Array[${value.length}]`;
                                
                                html += `&nbsp;&nbsp;&nbsp;&nbsp;• <strong>${key}:</strong> ${tipo} → ${String(value).substring(0, 30)}<br>`;
                            });
                        }

                        html += '</div></div>';
                    }
                });

                // Resumo e recomendações
                html += `
                    <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                        <h4>💡 Próximos Passos</h4>
                        <p>Com base nesta análise, vou adaptar o dashboard para usar a estrutura correta dos seus dados.</p>
                        <p><strong>Me informe:</strong> Qual campo contém os produtos/materiais solicitados?</p>
                    </div>
                </div>
                `;

                document.getElementById('analysisResults').innerHTML = html;
                log('✅ Análise detalhada concluída', 'success');

            } catch (error) {
                log(`❌ Erro na análise: ${error.message}`, 'error');
                console.error('Erro detalhado:', error);
            }
        };
    </script>
</body>
</html>
